"""
Web 应用核心模块

提供统一的 WebApplication 核心类,基于 FastAPI 实现配置驱动的非阻塞 Web 应用功能.

主要功能:
- WebApplication - 统一Web应用核心类(配置驱动的背压控制和智能调度)
- 非阻塞服务器启动机制
- 配置驱动的组件初始化
- FastAPI集成和配置
- 控制器注册管理
- 异常处理器管理
- 中间件管理
- 背压控制器集成(可配置)
- 智能调度器集成(可配置)
"""

import asyncio
import contextlib
import time
from enum import Enum
from typing import TYPE_CHECKING, Any, Dict, Optional

# FastAPI和uvicorn是Web模块的必需依赖
import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from loguru import logger

from ..errors import (handle_context_exceptions, performance_monitor,
                      retry_with_backoff, timeout_handler)
from .exceptions import GlobalExceptionHandler
from .middleware import MiddlewareManager, ResponseMiddleware
from .params import ParameterBinder
from .properties import WebProperties
from .registry import ControllerRegistry
from .response import ApiResponse, BusinessError, ValidationError
from .router import RouteHandler

# 类型检查时导入,避免循环导入
if TYPE_CHECKING:
    from miniboot.context import ApplicationContext


class WebApplicationState(Enum):
    """Web应用状态枚举"""

    UNINITIALIZED = "uninitialized"  # 初始状态,未初始化
    INITIALIZING = "initializing"  # 正在初始化组件
    INITIALIZED = "initialized"  # 初始化完成,但服务器未启动
    STARTING = "starting"  # 正在启动服务器
    RUNNING = "running"  # 服务器正在运行
    STOPPING = "stopping"  # 正在停止服务器
    STOPPED = "stopped"  # 服务器已停止


class WebApplication:
    """统一的Web应用核心类

    基于FastAPI实现的配置驱动Web应用,支持:
    - 非阻塞服务器启动
    - 配置驱动的背压控制(可选)
    - 配置驱动的智能调度(可选)
    - 控制器注册、中间件管理、异常处理等功能
    """

    def __init__(self, context: Optional["ApplicationContext"] = None, properties: Optional[WebProperties] = None):
        """初始化统一Web应用

        Args:
            context: 应用上下文
            properties: Web配置属性,如果为None则使用默认配置
        """
        self.context = context
        self.properties = properties or WebProperties()

        # 启动时间记录
        self._init_start_time = time.time()

        # FastAPI应用
        self.fastapi_app: Optional[FastAPI] = None

        # 背压控制器(根据配置启用)
        self.backpressure_controller: Optional[Any] = None

        # 智能调度器(根据配置启用)
        self.smart_scheduler: Optional[Any] = None

        # 控制器注册表
        self.controller_registry: Optional[ControllerRegistry] = None

        # 中间件管理器
        self.middleware_manager: Optional[MiddlewareManager] = None

        # 路由处理器
        self.route_handler: Optional[RouteHandler] = None

        # 参数绑定器
        self.parameter_binder: Optional[ParameterBinder] = None

        # 全局异常处理器
        self.exception_handler: Optional[GlobalExceptionHandler] = None

        # 响应处理中间件
        self.response_middleware: Optional[ResponseMiddleware] = None

        # 服务器实例和状态
        self._server: Optional[uvicorn.Server] = None
        self._server_task: Optional[asyncio.Task] = None
        self._state = WebApplicationState.UNINITIALIZED

        # 性能指标
        self._startup_metrics = {"init_time": 0.0, "component_init_time": 0.0, "server_start_time": 0.0}

    @handle_context_exceptions
    @timeout_handler(timeout_seconds=60.0, timeout_message="Web application initialization timed out")
    @performance_monitor(slow_threshold=5.0)
    async def initialize(self) -> FastAPI:
        """异步初始化Web应用 - 配置驱动的功能启用"""
        if self._state != WebApplicationState.UNINITIALIZED:
            return self.fastapi_app

        logger.info("🚀 Initializing web application with configuration-driven features")
        component_start_time = time.time()

        try:
            # 1. 根据配置初始化背压控制器
            if self.properties.backpressure.enabled:
                await self._init_backpressure()
                logger.info("✅ Backpressure controller enabled and initialized")
            else:
                logger.debug("⚪ Backpressure controller disabled by configuration")

            # 2. 根据配置初始化智能调度器
            if self.properties.async_optimization.enabled:
                await self._init_scheduler()
                logger.info("✅ Smart scheduler enabled and initialized")
            else:
                logger.debug("⚪ Smart scheduler disabled by configuration")

            # 3. 根据配置初始化智能组件
            await self._init_components()

            # 4. 创建FastAPI应用
            self.fastapi_app = await self._build_app()

            # 5. 智能注册控制器(根据配置优化)
            await self._register_controllers()

            # 6. 初始化响应处理和异常管理
            await self._setup_handlers()

            # 7. 配置中间件
            await self._config_middleware()

            # 8. 设置静态文件服务
            self._setup_static_files()

            # 记录初始化时间
            self._startup_metrics["component_init_time"] = time.time() - component_start_time
            self._startup_metrics["init_time"] = time.time() - self._init_start_time
            self._state = WebApplicationState.INITIALIZED

            logger.info(f"🎉 Web application initialized successfully in {self._startup_metrics['init_time']:.3f}s")
            return self.fastapi_app

        except Exception as e:
            logger.error(f"❌ Failed to initialize web application: {e}")
            raise

    async def _init_backpressure(self):
        """初始化背压控制器"""
        try:
            from .backpressure.controller import BackpressureController

            # 创建背压控制器实例
            self.backpressure_controller = BackpressureController(self.properties.backpressure)

            # 异步初始化控制器
            await self.backpressure_controller.initialize_async()

            logger.debug(f"✅ Backpressure controller initialized with strategy: {self.properties.backpressure.strategy}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize backpressure controller: {e}")
            # 如果初始化失败,设置为 None 以禁用背压控制
            self.backpressure_controller = None
            raise

    async def _init_scheduler(self):
        """初始化智能调度器"""
        try:
            from .scheduling.scheduler import TaskScheduler

            # 创建智能调度器实例
            self.smart_scheduler = TaskScheduler(self.properties.async_optimization)

            # 异步初始化调度器
            await self.smart_scheduler.initialize()

            logger.debug(f"✅ Smart scheduler initialized with intelligent_scheduling: {self.properties.async_optimization.intelligent_scheduling}")

        except Exception as e:
            logger.error(f"❌ Failed to initialize smart scheduler: {e}")
            # 如果初始化失败,设置为 None 以禁用智能调度
            self.smart_scheduler = None
            raise

    async def _init_components(self):
        """初始化智能组件"""
        try:
            # 统一创建自适应组件(根据 smart_scheduler 自动选择模式)
            intelligent_mode = (
                self.properties.async_optimization.enabled
                and self.properties.async_optimization.intelligent_scheduling
                and self.smart_scheduler is not None
            )

            # 创建自适应控制器注册器
            self.controller_registry = ControllerRegistry(
                app=None,  # 稍后设置
                smart_scheduler=self.smart_scheduler if intelligent_mode else None,
            )

            # 创建自适应中间件管理器
            self.middleware_manager = MiddlewareManager(smart_scheduler=self.smart_scheduler if intelligent_mode else None)

            # 自动注册默认中间件
            self._setup_middlewares()

            # 创建路由处理器(始终启用,因为它是独立的智能组件)
            self.route_handler = RouteHandler(self.smart_scheduler) if intelligent_mode else None

            # 创建参数绑定器(始终启用,因为它是独立的智能组件)
            self.parameter_binder = ParameterBinder(self.smart_scheduler) if intelligent_mode else None

            mode_name = "intelligent" if intelligent_mode else "traditional"
            logger.debug(f"✅ Adaptive components initialized in {mode_name} mode")

        except Exception as e:
            logger.error(f"❌ Failed to initialize adaptive components: {e}")
            # 回退到传统模式(不传递 smart_scheduler)
            self.controller_registry = ControllerRegistry()
            self.middleware_manager = MiddlewareManager()

            # 自动注册默认中间件（回退模式）
            self._setup_middlewares()

            self.route_handler = None
            self.parameter_binder = None
            logger.debug("⚠️ Fallback to traditional mode")

    def _setup_middlewares(self):
        """注册默认中间件：logging, cors, compression, response"""
        try:
            from .middleware import (CompressionMiddleware, CorsMiddleware,
                                     LoggingMiddleware)

            # 批量注册默认中间件
            middlewares = [
                LoggingMiddleware(),
                CorsMiddleware(),
                CompressionMiddleware(),
                ResponseMiddleware()
            ]

            for middleware in middlewares:
                self.middleware_manager.register_middleware(middleware)

            logger.info("✅ Default middlewares registered: logging, cors, compression, response")

        except Exception as e:
            logger.warning(f"Failed to register default middlewares: {e}")

    async def _setup_handlers(self):
        """初始化响应处理和异常管理"""
        try:
            logger.debug("🔧 Initializing response and exception handling...")

            # 初始化全局异常处理器
            self.exception_handler = GlobalExceptionHandler(include_traceback=getattr(self.properties, "debug", False), log_exceptions=True)
            logger.debug("✅ Global exception handler initialized")

            # 初始化响应处理中间件
            self.response_middleware = ResponseMiddleware(auto_wrap_response=True, add_request_id=True, add_response_time=True)
            logger.debug("✅ Response middleware initialized")

        except Exception as e:
            logger.error(f"❌ Failed to initialize response and exception handling: {e}")
            # 使用默认配置
            self.exception_handler = GlobalExceptionHandler()
            self.response_middleware = ResponseMiddleware()
            logger.debug("⚠️ Using default response and exception handling")

    async def _build_app(self) -> FastAPI:
        """创建FastAPI应用"""
        app = FastAPI(
            title=self.properties.title,
            description=self.properties.description,
            version=self.properties.version,
            docs_url="/docs" if self.properties.docs.enabled else None,
            redoc_url="/redoc" if self.properties.docs.enabled else None,
        )

        # 添加中间件
        await self._setup_middleware(app)

        # 设置全局异常处理器
        if self.exception_handler:
            await self._setup_exceptions(app)

        logger.debug("FastAPI application created")
        return app

    async def _setup_middleware(self, app: FastAPI):
        """统一添加中间件"""
        # 添加背压控制中间件
        if self.backpressure_controller:
            await self._setup_backpressure(app)

        # 添加智能调度中间件
        if self.smart_scheduler:
            await self._setup_scheduler(app)

        # 添加响应处理中间件
        if self.response_middleware:
            await self._setup_response(app)

    def _is_smart_mode(self) -> bool:
        """检查是否为智能模式"""
        return (
            self.properties.async_optimization.enabled
            and self.properties.async_optimization.intelligent_scheduling
            and self.smart_scheduler is not None
        )

    async def _setup_backpressure(self, app: FastAPI):
        """添加背压控制中间件"""
        from starlette.middleware.base import BaseHTTPMiddleware
        from starlette.responses import JSONResponse

        from .backpressure.controller import ProtectionAction

        class BackpressureMiddleware(BaseHTTPMiddleware):
            def __init__(self, app, backpressure_controller):
                super().__init__(app)
                self.backpressure_controller = backpressure_controller

            async def dispatch(self, request: Request, call_next):
                # 构建请求上下文
                request_context = {
                    "method": request.method,
                    "url": str(request.url),
                    "headers": dict(request.headers),
                    "client": request.client.host if request.client else None,
                    "timestamp": time.time(),
                }

                try:
                    # 检查是否需要背压保护
                    decision = await self.backpressure_controller.should_protect_request(request_context)

                    if decision.action == ProtectionAction.REJECT:
                        # 拒绝请求
                        return JSONResponse(
                            status_code=503, content={"error": "Service temporarily unavailable", "reason": decision.reason, "retry_after": 30}
                        )
                    elif decision.action == ProtectionAction.THROTTLE:
                        # 限流处理 - 添加延迟
                        import asyncio

                        await asyncio.sleep(0.1)

                    # 正常处理请求
                    response = await call_next(request)
                    return response

                except Exception as e:
                    logger.error(f"Error in backpressure middleware: {e}")
                    # 如果背压控制出错,继续正常处理请求
                    return await call_next(request)

        # 添加中间件到应用
        app.add_middleware(BackpressureMiddleware, backpressure_controller=self.backpressure_controller)
        logger.debug("✅ Backpressure middleware added to FastAPI application")

    async def _setup_scheduler(self, app: FastAPI):
        """添加智能调度中间件"""
        from starlette.middleware.base import BaseHTTPMiddleware
        from starlette.responses import JSONResponse

        from .scheduling.scheduler import TaskRequest

        class SmartSchedulingMiddleware(BaseHTTPMiddleware):
            def __init__(self, app, smart_scheduler):
                super().__init__(app)
                self.smart_scheduler = smart_scheduler

            async def dispatch(self, request: Request, call_next):
                # 检查是否启用智能调度
                if not self.smart_scheduler or not self.smart_scheduler.is_running():
                    return await call_next(request)

                # 构建任务请求上下文
                request_context = {
                    "method": request.method,
                    "url": str(request.url),
                    "headers": dict(request.headers),
                    "client": request.client.host if request.client else None,
                    "timestamp": time.time(),
                }

                try:
                    # 创建任务请求
                    task_request = TaskRequest(task_id=f"request_{id(request)}", function=call_next, args=(request,), metadata=request_context)

                    # 使用智能调度器处理请求
                    response = await self.smart_scheduler.schedule_task(task_request)
                    return response

                except Exception as e:
                    logger.error(f"Error in smart scheduling middleware: {e}")
                    # 如果智能调度出错,回退到正常处理
                    return await call_next(request)

        # 添加中间件到应用
        app.add_middleware(SmartSchedulingMiddleware, smart_scheduler=self.smart_scheduler)
        logger.debug("✅ Smart scheduling middleware added to FastAPI application")

    async def _setup_response(self, app: FastAPI):
        """添加响应处理中间件"""
        from starlette.middleware.base import BaseHTTPMiddleware

        class ResponseHandlingMiddleware(BaseHTTPMiddleware):
            def __init__(self, app, response_middleware):
                super().__init__(app)
                self.response_middleware = response_middleware

            async def dispatch(self, request: Request, call_next):
                try:
                    # 使用响应中间件处理请求
                    return await self.response_middleware(request, call_next)
                except Exception as e:
                    logger.error(f"Error in response middleware: {e}")
                    # 如果响应中间件出错,继续正常处理
                    return await call_next(request)

        # 添加中间件到应用
        app.add_middleware(ResponseHandlingMiddleware, response_middleware=self.response_middleware)
        logger.debug("✅ Response handling middleware added to FastAPI application")

    async def _setup_exceptions(self, app: FastAPI):
        """设置全局异常处理器"""

        @app.exception_handler(Exception)
        async def global_exception_handler(request: Request, exc: Exception):
            """全局异常处理器"""
            try:
                return await self.exception_handler.handle_exception(request, exc)
            except Exception as e:
                logger.error(f"Exception handler failed: {e}")
                # 创建回退响应
                from .response import ApiResponse

                api_response = ApiResponse.internal_error(message="系统异常", request_id=getattr(request.state, "request_id", None))
                return JSONResponse(status_code=500, content=api_response.dict())

        # 注册特定异常处理器
        @app.exception_handler(BusinessError)
        async def business_error_handler(request: Request, exc: BusinessError):
            """业务异常处理器"""
            request_id = getattr(request.state, "request_id", None)
            api_response = exc.to_response(request_id)
            return JSONResponse(status_code=api_response.code, content=api_response.dict())

        @app.exception_handler(ValidationError)
        async def validation_error_handler(request: Request, exc: ValidationError):
            """校验异常处理器"""
            request_id = getattr(request.state, "request_id", None)
            api_response = exc.to_response(request_id)
            return JSONResponse(status_code=api_response.code, content=api_response.dict())

        logger.debug("✅ Global exception handlers registered")

    async def _register_controllers(self):
        """智能注册控制器(根据配置优化)"""
        # 控制器注册表已在 _init_components 中初始化

        # 设置FastAPI应用到控制器注册器
        if hasattr(self.controller_registry, "set_app"):
            self.controller_registry.set_app(self.fastapi_app)
        elif hasattr(self.controller_registry, "app"):
            self.controller_registry.app = self.fastapi_app

        # 如果控制器注册器支持异步注册到应用,则使用异步方式
        if hasattr(self.controller_registry, "apply_to_app"):
            logger.debug("Using intelligent controller registration")
            await self.controller_registry.apply_to_app(self.fastapi_app)
        else:
            logger.debug("Using standard controller registration")

    async def _config_middleware(self):
        """配置中间件"""
        # 中间件管理器已在 _init_components 中初始化

        # 使用统一的配置方式(自适应)
        self.middleware_manager.configure_all(self.fastapi_app, self.properties)
        mode = (
            "intelligent" if (hasattr(self.middleware_manager, "_intelligent_mode") and self.middleware_manager._intelligent_mode) else "traditional"
        )
        logger.debug(f"Using {mode} middleware configuration")

        logger.debug("Middlewares configured")



    def _setup_static_files(self):
        """设置静态文件服务"""
        if not self.properties.static.enabled:
            return

        static_dir = self.properties.static.directory
        mount_path = self.properties.static.mount_path

        # 检查静态文件目录是否存在
        from pathlib import Path

        static_path = Path(static_dir)
        if self.properties.static.check_dir and not static_path.exists():
            if static_path.is_absolute():
                # 绝对路径,尝试创建目录
                try:
                    static_path.mkdir(parents=True, exist_ok=True)
                    logger.info(f"Created static directory: {static_dir}")
                except OSError as e:
                    logger.warning(f"Failed to create static directory {static_dir}: {e}")
                    return
            else:
                # 相对路径,记录警告但不创建
                logger.warning(f"Static directory does not exist: {static_dir}")
                return

        try:
            # 挂载静态文件服务
            self.fastapi_app.mount(mount_path, StaticFiles(directory=static_dir, html=self.properties.static.html), name="static")
            logger.info(f"Mounted static files: {mount_path} -> {static_dir}")
        except Exception as e:
            logger.error(f"Failed to mount static files: {e}")



    def register_controller(self, controller: Any, controller_name: Optional[str] = None):
        """注册控制器

        Args:
            controller: 控制器实例
            controller_name: 控制器名称,如果为None则使用类名

        Raises:
            RuntimeError: 如果应用未初始化或注册失败
        """
        if self._state == WebApplicationState.UNINITIALIZED or not self.controller_registry:
            raise RuntimeError("WebApplication must be initialized before registering controllers. Call await app.initialize() first.")

        if controller_name is None:
            controller_name = controller.__class__.__name__

        try:
            # 使用统一的注册方法(自适应)
            success = self.controller_registry.register(controller, controller_name)

            if success:
                logger.info(f"✅ Registered controller: {controller_name}")
            else:
                raise RuntimeError(f"Controller registration returned False: {controller_name}")

        except Exception as e:
            logger.error(f"❌ Failed to register controller {controller_name}: {e}")
            raise RuntimeError(f"Failed to register controller {controller_name}: {e}") from e

    async def register_controller_async(self, controller: Any, controller_name: Optional[str] = None):
        """异步注册控制器(推荐用于智能调度)

        Args:
            controller: 控制器实例
            controller_name: 控制器名称,如果为None则使用类名

        Raises:
            RuntimeError: 如果应用未初始化或注册失败
        """
        if self._state == WebApplicationState.UNINITIALIZED or not self.controller_registry:
            raise RuntimeError("WebApplication must be initialized before registering controllers. Call await app.initialize() first.")

        if controller_name is None:
            controller_name = controller.__class__.__name__

        try:
            # 使用统一的异步注册方法(自适应)
            if hasattr(self.controller_registry, "register_async"):
                # 支持异步注册
                success = await self.controller_registry.register_async(controller, controller_name)
                mode = "auto" if hasattr(self.controller_registry, "_auto_mode") and self.controller_registry._auto_mode else "traditional"
                if success:
                    logger.debug(f"✅ Controller '{controller_name}' registered in {mode} mode")
            else:
                # 回退到同步注册
                success = self.controller_registry.register(controller, controller_name)
                if success:
                    logger.debug(f"✅ Controller '{controller_name}' registered")

            if success:
                logger.info(f"✅ Async controller '{controller_name}' registered")
            else:
                raise RuntimeError(f"Controller registration returned False: {controller_name}")

        except Exception as e:
            logger.error(f"❌ Failed to register controller {controller_name}: {e}")
            raise RuntimeError(f"Failed to register controller {controller_name}: {e}") from e

    def get_controller_registry(self) -> ControllerRegistry:
        """获取控制器注册表

        Returns:
            控制器注册表实例
        """
        return self.controller_registry

    def get_registered_controllers(self) -> dict[str, Any]:
        """获取已注册的控制器

        Returns:
            控制器字典
        """
        return self.controller_registry.get_controllers()

    def get_registered_routes(self) -> list[Any]:
        """获取已注册的路由

        Returns:
            路由列表
        """
        return self.controller_registry.get_routes()

    def get_middleware_manager(self) -> MiddlewareManager:
        """获取中间件管理器

        Returns:
            中间件管理器实例
        """
        return self.middleware_manager

    def register_middleware(self, middleware) -> bool:
        """注册中间件

        Args:
            middleware: 中间件实例

        Returns:
            是否注册成功
        """
        return self.middleware_manager.register_middleware(middleware)

    def enable_middleware(self, name: str) -> bool:
        """启用中间件

        Args:
            name: 中间件名称

        Returns:
            是否成功
        """
        return self.middleware_manager.enable_middleware(name)

    def disable_middleware(self, name: str) -> bool:
        """禁用中间件

        Args:
            name: 中间件名称

        Returns:
            是否成功
        """
        return self.middleware_manager.disable_middleware(name)

    def add_middleware(self, middleware_class, **kwargs):
        """添加中间件

        Args:
            middleware_class: 中间件类
            **kwargs: 中间件参数
        """
        if self.fastapi_app:
            self.fastapi_app.add_middleware(middleware_class, **kwargs)
            logger.debug(f"Added middleware: {middleware_class.__name__}")
        else:
            logger.warning("FastAPI app not initialized, middleware will be added later")

    def add_exception_handler(self, exception_class, handler):
        """添加异常处理器

        Args:
            exception_class: 异常类
            handler: 处理函数
        """
        if self.fastapi_app:
            self.fastapi_app.add_exception_handler(exception_class, handler)
            logger.debug(f"Added exception handler for: {exception_class.__name__}")
        else:
            logger.warning("FastAPI app not initialized, exception handler will be added later")

    @handle_context_exceptions
    @timeout_handler(timeout_seconds=30.0, timeout_message="Web server startup timed out")
    @retry_with_backoff(max_attempts=3, base_delay=1.0, strategy="exponential", exceptions=(OSError, ConnectionError))
    async def start_async(self) -> bool:
        """非阻塞启动Web服务器

        Returns:
            bool: 启动是否成功
        """
        if not self.properties.enabled:
            logger.debug("Web application is disabled")
            return False

        if self._state in (WebApplicationState.STARTING, WebApplicationState.RUNNING):
            logger.warning("Web server is already starting or running")
            return False

        self._state = WebApplicationState.STARTING
        server_start_time = time.time()

        try:
            # 确保应用已初始化
            if self._state == WebApplicationState.UNINITIALIZED:
                await self.initialize()

            # 创建服务器配置
            config = uvicorn.Config(
                app=self.fastapi_app,
                host=str(self.properties.host),
                port=int(self.properties.port),
                log_level=str(self.properties.log_level).lower(),
                access_log=bool(self.properties.logger.enabled),
            )

            self._server = uvicorn.Server(config)

            # 非阻塞启动:在后台任务中启动服务器
            logger.info(f"🚀 Starting web server on {self.properties.host}:{self.properties.port}")
            self._server_task = asyncio.create_task(self._server.serve())

            # 等待服务器启动完成(非阻塞检查)
            startup_timeout = 5.0  # 5秒超时
            start_check_time = time.time()

            while not self._server.started and (time.time() - start_check_time) < startup_timeout:
                await asyncio.sleep(0.01)  # 10ms检查间隔

            if self._server.started:
                self._startup_metrics["server_start_time"] = time.time() - server_start_time
                self._state = WebApplicationState.RUNNING

                total_startup_time = time.time() - self._init_start_time
                logger.info(f"🎉 Web server started successfully in {total_startup_time:.3f}s (< 200ms target)")
                return True
            else:
                logger.error("❌ Web server failed to start within timeout")
                self._state = WebApplicationState.INITIALIZED
                return False

        except Exception as e:
            logger.error(f"❌ Failed to start web server: {e}")
            self._state = WebApplicationState.INITIALIZED
            raise

    @handle_context_exceptions
    @timeout_handler(timeout_seconds=60.0, timeout_message="Web server start timed out")
    async def start_server(self):
        """启动Web服务器(阻塞式)"""
        if not await self.start_async():
            return

        # 等待服务器任务完成(阻塞式)
        if self._server_task:
            try:
                await self._server_task
            except asyncio.CancelledError:
                logger.debug("Web server task was cancelled")
            except Exception as e:
                logger.error(f"Web server task failed: {e}")
                raise

    @handle_context_exceptions
    @timeout_handler(timeout_seconds=30.0, timeout_message="Web server shutdown timed out")
    async def stop(self):
        """停止Web服务器"""
        if self._state != WebApplicationState.RUNNING:
            logger.debug("Web server is not running")
            return

        self._state = WebApplicationState.STOPPING

        if self._server:
            logger.info("🛑 Stopping web server...")
            self._server.should_exit = True

            # 等待服务器停止
            if self._server_task and not self._server_task.done():
                self._server_task.cancel()
                with contextlib.suppress(asyncio.CancelledError):
                    await self._server_task

            # 清理资源
            await self._cleanup()

            self._server = None
            self._server_task = None
            self._state = WebApplicationState.STOPPED

            logger.info("✅ Web server stopped gracefully")

    def get_status(self) -> Dict[str, Any]:
        """获取智能组件状态

        Returns:
            智能组件状态信息
        """
        status = {
            "application_state": self._state.value,
            "is_running": self.is_running(),
            "is_initialized": self.is_initialized(),
            "smart_components_enabled": False,
            "controller_registry": {"type": "traditional", "status": "unknown"},
            "middleware_manager": {"type": "traditional", "status": "unknown"},
            "route_handler": {"type": "traditional", "status": "unknown"},
            "parameter_binder": {"type": "traditional", "status": "unknown"},
            "exception_handler": {"type": "traditional", "status": "unknown"},
            "response_middleware": {"type": "traditional", "status": "unknown"},
        }

        try:
            # 检查控制器注册器
            if self.controller_registry:
                is_auto_mode = hasattr(self.controller_registry, "_auto_mode") and self.controller_registry._auto_mode
                if is_auto_mode:
                    status["smart_components_enabled"] = True
                    status["controller_registry"] = {
                        "type": "auto",
                        "status": "active",
                        "statistics": self.controller_registry.get_stats() if hasattr(self.controller_registry, "get_stats") else {},
                    }
                else:
                    status["controller_registry"] = {
                        "type": "traditional",
                        "status": "active",
                        "controllers_count": len(self.controller_registry.get_controllers()),
                    }

            # 检查中间件管理器
            if self.middleware_manager:
                is_intelligent = hasattr(self.middleware_manager, "_intelligent_mode") and self.middleware_manager._intelligent_mode
                if is_intelligent:
                    status["smart_components_enabled"] = True
                    status["middleware_manager"] = {
                        "type": "intelligent",
                        "status": "active",
                        "statistics": self.middleware_manager.get_stats() if hasattr(self.middleware_manager, "get_stats") else {},
                    }
                else:
                    status["middleware_manager"] = {
                        "type": "traditional",
                        "status": "active",
                        "middlewares_count": len(self.middleware_manager.get_all_middlewares()),
                    }

            # 检查路由处理器
            if self.route_handler:
                status["smart_components_enabled"] = True
                status["route_handler"] = {
                    "type": "intelligent",
                    "status": "active",
                    "global_metrics": self.route_handler.get_global_metrics() if hasattr(self.route_handler, "get_global_metrics") else {},
                    "optimization_report": self.route_handler.get_optimization_report()
                    if hasattr(self.route_handler, "get_optimization_report")
                    else {},
                }
            else:
                status["route_handler"] = {"type": "traditional", "status": "disabled"}

            # 检查参数绑定器
            if self.parameter_binder:
                status["smart_components_enabled"] = True
                status["parameter_binder"] = {
                    "type": "intelligent",
                    "status": "active",
                    "statistics": self.parameter_binder.get_statistics() if hasattr(self.parameter_binder, "get_statistics") else {},
                    "performance": self.parameter_binder.get_parameter_performance()
                    if hasattr(self.parameter_binder, "get_parameter_performance")
                    else {},
                }
            else:
                status["parameter_binder"] = {"type": "traditional", "status": "disabled"}

            # 检查全局异常处理器
            if self.exception_handler:
                status["exception_handler"] = {
                    "type": "smart",
                    "status": "active",
                    "include_traceback": self.exception_handler.include_traceback,
                    "log_exceptions": self.exception_handler.log_exceptions,
                    "handlers_count": len(self.exception_handler._handlers),
                }
            else:
                status["exception_handler"] = {"type": "traditional", "status": "disabled"}

            # 检查响应处理中间件
            if self.response_middleware:
                status["response_middleware"] = {
                    "type": "smart",
                    "status": "active",
                    "auto_wrap_response": self.response_middleware.auto_wrap_response,
                    "add_request_id": self.response_middleware.add_request_id,
                    "add_response_time": self.response_middleware.add_response_time,
                    "cors_enabled": self.response_middleware.cors_enabled,
                }
            else:
                status["response_middleware"] = {"type": "traditional", "status": "disabled"}

        except Exception as e:
            status["error"] = str(e)

        return status

    async def _cleanup(self):
        """清理资源"""
        try:
            # 清理背压控制器
            if self.backpressure_controller:
                await self.backpressure_controller.cleanup_async()
                logger.debug("✅ Backpressure controller cleaned up")

            # 清理智能调度器
            if self.smart_scheduler:
                await self.smart_scheduler.cleanup_async()
                logger.debug("✅ Smart scheduler cleaned up")

            # 清理自适应组件
            if hasattr(self.controller_registry, "cleanup_async"):
                await self.controller_registry.cleanup_async()
                logger.debug("✅ Controller registry cleaned up")

            if hasattr(self.middleware_manager, "cleanup_async"):
                await self.middleware_manager.cleanup_async()
                logger.debug("✅ Middleware manager cleaned up")

            # 等待一小段时间确保所有后台任务都已停止
            await asyncio.sleep(0.1)

        except Exception as e:
            logger.error(f"Error during resource cleanup: {e}")

    def is_running(self) -> bool:
        """检查服务器是否正在运行"""
        return self._state == WebApplicationState.RUNNING

    def is_initialized(self) -> bool:
        """检查应用是否已初始化"""
        return self._state in (
            WebApplicationState.INITIALIZED,
            WebApplicationState.STARTING,
            WebApplicationState.RUNNING,
            WebApplicationState.STOPPING,
            WebApplicationState.STOPPED,
        )

    def get_state(self) -> WebApplicationState:
        """获取当前应用状态"""
        return self._state

    def get_metrics(self) -> dict:
        """获取启动性能指标"""
        return self._startup_metrics.copy()

    def run(self, host: Optional[str] = None, port: Optional[int] = None, non_blocking: bool = False):
        """运行Web应用

        Args:
            host: 主机地址,覆盖配置中的host
            port: 端口号,覆盖配置中的port
            non_blocking: 是否非阻塞运行
        """
        if not self.properties.enabled:
            logger.debug("Web application is disabled")
            return

        # 覆盖配置
        if host is not None:
            self.properties.host = host
        if port is not None:
            self.properties.port = port

        if non_blocking:
            # 非阻塞运行:启动后立即返回
            logger.info(f"Starting web application (non-blocking) on {self.properties.host}:{self.properties.port}")
            try:
                # 检查是否有运行中的事件循环
                loop = asyncio.get_running_loop()
                asyncio.create_task(self.start_async())
            except RuntimeError:
                # 没有运行中的事件循环,创建新的事件循环
                logger.warning("No running event loop found, creating new event loop for non-blocking start")
                asyncio.run(self.start_async())
        else:
            # 阻塞运行:传统方式
            logger.info(f"Running web application on {self.properties.host}:{self.properties.port}")

            if self._state != WebApplicationState.UNINITIALIZED:
                # 如果已初始化,使用现有的FastAPI应用
                uvicorn.run(
                    self.fastapi_app,
                    host=str(self.properties.host),
                    port=int(self.properties.port),
                    log_level=str(self.properties.log_level).lower(),
                    access_log=bool(self.properties.logger.enabled),
                )
            else:
                # 如果未初始化,使用异步启动
                asyncio.run(self.start_server())

    def get_app(self) -> FastAPI:
        """获取FastAPI应用实例

        Returns:
            FastAPI应用实例

        Raises:
            RuntimeError: 如果应用未初始化
        """
        if self.fastapi_app:
            return self.fastapi_app
        else:
            raise RuntimeError("FastAPI app not initialized. Call initialize() first.")

    def get_backpressure_status(self) -> dict[str, Any]:
        """获取背压控制状态

        Returns:
            背压控制状态信息
        """
        if not self.backpressure_controller:
            return {"enabled": False, "reason": "Backpressure controller not initialized"}

        try:
            return {
                "enabled": True,
                "status": self.backpressure_controller.get_status_summary(),
                "protection_enabled": self.backpressure_controller.is_protection_enabled(),
                "strategy": self.properties.backpressure.strategy.value if self.properties.backpressure.strategy else "unknown",
            }
        except Exception as e:
            return {"enabled": True, "error": str(e), "status": "error"}

    def get_smart_scheduler_status(self) -> dict[str, Any]:
        """获取智能调度器状态

        Returns:
            智能调度器状态信息
        """
        if not self.smart_scheduler:
            return {"enabled": False, "reason": "Smart scheduler not initialized"}

        try:
            return {
                "enabled": True,
                "status": self.smart_scheduler.get_status_summary(),
                "is_running": self.smart_scheduler.is_running(),
                "intelligent_scheduling": self.properties.async_optimization.intelligent_scheduling,
                "performance_profiling": self.properties.async_optimization.performance_profiling,
                "adaptive_learning": self.properties.async_optimization.adaptive_learning,
            }
        except Exception as e:
            return {"enabled": True, "error": str(e), "status": "error"}

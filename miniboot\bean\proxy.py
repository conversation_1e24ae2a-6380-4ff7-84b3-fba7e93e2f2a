#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 统一Bean代理系统
"""

import asyncio
import inspect
import threading
from concurrent.futures import ThreadPoolExecutor
from functools import wraps
from typing import Any, Callable, Dict, List, Optional, Union
from weakref import WeakKeyDictionary


class ProxyPerformanceLevel:
    """代理性能级别配置"""

    BASIC = "basic"          # 基础性能：简单缓存
    OPTIMIZED = "optimized"  # 优化性能：高级缓存和检测
    MAXIMUM = "maximum"      # 最大性能：所有优化特性


class MethodCache:
    """高性能方法缓存，支持LRU淘汰策略"""

    def __init__(self, max_size: int = 256):
        self.max_size = max_size
        self.cache: Dict[tuple, Any] = {}
        self.access_order: Dict[tuple, int] = {}
        self.access_counter = 0
        self._lock = threading.RLock()

    def get(self, key: tuple) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key in self.cache:
                self.access_order[key] = self.access_counter
                self.access_counter += 1
                return self.cache[key]
            return None

    def put(self, key: tuple, value: Any) -> None:
        """存储缓存值"""
        with self._lock:
            if len(self.cache) >= self.max_size and key not in self.cache:
                # LRU淘汰
                oldest_key = min(self.access_order.keys(), key=lambda k: self.access_order[k])
                del self.cache[oldest_key]
                del self.access_order[oldest_key]

            self.cache[key] = value
            self.access_order[key] = self.access_counter
            self.access_counter += 1

    def clear(self) -> None:
        """清空缓存"""
        with self._lock:
            self.cache.clear()
            self.access_order.clear()
            self.access_counter = 0


class AttributeInspector:
    """属性检查器，缓存对象的属性信息"""

    def __init__(self, obj: Any):
        self.obj = obj
        self._attr_cache: Dict[str, bool] = {}
        self._callable_cache: Dict[str, bool] = {}
        self._async_cache: Dict[str, bool] = {}
        self._lock = threading.RLock()

    def has_attribute(self, name: str) -> bool:
        """检查是否有指定属性"""
        with self._lock:
            if name not in self._attr_cache:
                self._attr_cache[name] = hasattr(self.obj, name)
            return self._attr_cache[name]

    def is_callable(self, name: str) -> bool:
        """检查属性是否可调用"""
        with self._lock:
            if name not in self._callable_cache:
                if self.has_attribute(name):
                    self._callable_cache[name] = callable(getattr(self.obj, name))
                else:
                    self._callable_cache[name] = False
            return self._callable_cache[name]

    def is_async_method(self, name: str) -> bool:
        """检查方法是否为异步方法"""
        with self._lock:
            if name not in self._async_cache:
                if self.has_attribute(name) and self.is_callable(name):
                    method = getattr(self.obj, name)
                    self._async_cache[name] = inspect.iscoroutinefunction(method)
                else:
                    self._async_cache[name] = False
            return self._async_cache[name]

    def get_attribute(self, name: str) -> Any:
        """获取属性值"""
        return getattr(self.obj, name)


class BeanProxy:
    """统一Bean代理

    自动适配同步/异步环境，支持多种性能级别配置。
    整合了智能代理和组合代理的功能，提供统一的代理接口。

    核心特性：
    1. 环境自适应：自动检测同步/异步环境
    2. 性能分级：支持三种性能级别配置
    3. 智能缓存：高效的方法和属性缓存
    4. 线程安全：完整的多线程支持
    5. 统计监控：详细的性能统计信息
    """

    # 共享线程池
    _thread_pool: Optional[ThreadPoolExecutor] = None
    _thread_pool_lock = threading.Lock()

    # 全局代理统计
    _global_stats = {
        'total_proxies': 0,
        'active_proxies': 0,
        'total_method_calls': 0,
        'cache_hits': 0
    }
    _stats_lock = threading.RLock()

    @classmethod
    def get_thread_pool(cls) -> ThreadPoolExecutor:
        """获取共享线程池"""
        if cls._thread_pool is None:
            with cls._thread_pool_lock:
                if cls._thread_pool is None:
                    cls._thread_pool = ThreadPoolExecutor(
                        max_workers=4,
                        thread_name_prefix="BeanProxy"
                    )
        return cls._thread_pool

    @classmethod
    def get_global_stats(cls) -> Dict[str, int]:
        """获取全局统计信息"""
        with cls._stats_lock:
            return cls._global_stats.copy()

    def __init__(
        self,
        target_bean: Any,
        bean_name: str = "unknown",
        bean_factory: Optional["BeanFactory"] = None,
        performance_level: str = ProxyPerformanceLevel.OPTIMIZED,
        cache_size: int = 256
    ):
        """初始化Bean代理

        Args:
            target_bean: 目标Bean实例
            bean_name: Bean名称
            bean_factory: Bean工厂实例（用于获取其他Bean）
            performance_level: 性能级别
            cache_size: 缓存大小
        """
        self._target_bean = target_bean
        self._bean_name = bean_name
        self._bean_factory = bean_factory
        self._performance_level = performance_level

        # 初始化缓存
        if performance_level == ProxyPerformanceLevel.BASIC:
            self._method_cache: Union[Dict, MethodCache] = {}
        else:
            self._method_cache = MethodCache(cache_size)

        # 初始化属性检查器
        if performance_level in [ProxyPerformanceLevel.OPTIMIZED, ProxyPerformanceLevel.MAXIMUM]:
            self._inspector = AttributeInspector(target_bean)
        else:
            self._inspector = None

        # 环境检测优化（最大性能模式）
        if performance_level == ProxyPerformanceLevel.MAXIMUM:
            self._env_cache_ttl = 100
            self._env_cache_count = 0
            self._last_env_result = self._detect_async_environment()

        # 实例统计信息
        self._stats = {
            'method_calls': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'async_calls': 0,
            'sync_calls': 0
        }

        # 更新全局统计
        with self._stats_lock:
            self._global_stats['total_proxies'] += 1
            self._global_stats['active_proxies'] += 1

    def __getattr__(self, name: str) -> Any:
        """动态获取属性或方法"""
        self._stats['method_calls'] += 1

        with self._stats_lock:
            self._global_stats['total_method_calls'] += 1

        # 根据性能级别选择策略
        if self._performance_level == ProxyPerformanceLevel.BASIC:
            return self._get_attribute_basic(name)
        elif self._performance_level == ProxyPerformanceLevel.OPTIMIZED:
            return self._get_attribute_optimized(name)
        else:  # MAXIMUM
            return self._get_attribute_maximum(name)

    def _get_attribute_basic(self, name: str) -> Any:
        """基础性能模式的属性获取"""
        is_async = self._detect_async_environment()
        cache_key = (name, is_async)

        # 简单缓存检查
        if isinstance(self._method_cache, dict) and cache_key in self._method_cache:
            self._stats['cache_hits'] += 1
            with self._stats_lock:
                self._global_stats['cache_hits'] += 1
            return self._method_cache[cache_key]

        # 获取属性
        result = self._get_attribute_core(name, is_async)

        # 简单缓存
        if isinstance(self._method_cache, dict):
            self._method_cache[cache_key] = result

        self._stats['cache_misses'] += 1
        return result

    def _get_attribute_optimized(self, name: str) -> Any:
        """优化性能模式的属性获取"""
        is_async = self._detect_async_environment()
        cache_key = (name, is_async)

        # 高性能缓存检查
        if isinstance(self._method_cache, MethodCache):
            cached_result = self._method_cache.get(cache_key)
            if cached_result is not None:
                self._stats['cache_hits'] += 1
                with self._stats_lock:
                    self._global_stats['cache_hits'] += 1
                return cached_result

        # 使用属性检查器优化获取
        result = self._get_attribute_with_inspector(name, is_async)

        # 高性能缓存
        if isinstance(self._method_cache, MethodCache):
            self._method_cache.put(cache_key, result)

        self._stats['cache_misses'] += 1
        return result

    def _get_attribute_maximum(self, name: str) -> Any:
        """最大性能模式的属性获取"""
        # 使用缓存的环境检测结果
        is_async = self._get_cached_async_environment()
        cache_key = (name, is_async)

        # 高性能缓存检查
        if isinstance(self._method_cache, MethodCache):
            cached_result = self._method_cache.get(cache_key)
            if cached_result is not None:
                self._stats['cache_hits'] += 1
                with self._stats_lock:
                    self._global_stats['cache_hits'] += 1
                return cached_result

        # 使用属性检查器优化获取
        result = self._get_attribute_with_inspector(name, is_async)

        # 高性能缓存
        if isinstance(self._method_cache, MethodCache):
            self._method_cache.put(cache_key, result)

        self._stats['cache_misses'] += 1
        return result

    def _get_attribute_core(self, name: str, is_async: bool) -> Any:
        """核心属性获取逻辑"""
        if not hasattr(self._target_bean, name):
            raise AttributeError(f"'{self._bean_name}' object has no attribute '{name}'")

        attr = getattr(self._target_bean, name)

        # 如果不是方法，直接返回
        if not callable(attr):
            return attr

        # 包装方法以支持环境适配
        return self._wrap_method(attr, name, is_async)

    def _get_attribute_with_inspector(self, name: str, is_async: bool) -> Any:
        """使用属性检查器优化的属性获取"""
        if not self._inspector.has_attribute(name):
            raise AttributeError(f"'{self._bean_name}' object has no attribute '{name}'")

        attr = self._inspector.get_attribute(name)

        # 如果不是方法，直接返回
        if not self._inspector.is_callable(name):
            return attr

        # 包装方法以支持环境适配
        return self._wrap_method(attr, name, is_async)

    def _wrap_method(self, method: Callable, method_name: str, is_async: bool) -> Callable:
        """包装方法以支持环境适配"""
        is_async_method = inspect.iscoroutinefunction(method)

        if is_async and not is_async_method:
            # 在异步环境中调用同步方法
            return self._create_async_wrapper(method, method_name)
        elif not is_async and is_async_method:
            # 在同步环境中调用异步方法
            return self._create_sync_wrapper(method, method_name)
        else:
            # 环境匹配，直接返回
            return method

    def _create_async_wrapper(self, sync_method: Callable, method_name: str) -> Callable:
        """创建异步包装器，在线程池中执行同步方法"""
        @wraps(sync_method)
        async def async_wrapper(*args, **kwargs):
            self._stats['async_calls'] += 1
            loop = asyncio.get_running_loop()
            thread_pool = self.get_thread_pool()

            try:
                result = await loop.run_in_executor(
                    thread_pool,
                    lambda: sync_method(*args, **kwargs)
                )
                return result
            except Exception as e:
                raise RuntimeError(
                    f"Error executing sync method '{method_name}' in async context: {str(e)}"
                ) from e

        return async_wrapper

    def _create_sync_wrapper(self, async_method: Callable, method_name: str) -> Callable:
        """创建同步包装器，使用asyncio.run执行异步方法"""
        @wraps(async_method)
        def sync_wrapper(*args, **kwargs):
            self._stats['sync_calls'] += 1

            try:
                # 检查是否已经在事件循环中
                try:
                    loop = asyncio.get_running_loop()
                    # 如果已经在事件循环中，不能使用asyncio.run
                    raise RuntimeError(
                        f"Cannot call async method '{method_name}' from within an async context. "
                        f"Use 'await' instead."
                    )
                except RuntimeError:
                    # 没有运行的事件循环，可以使用asyncio.run
                    return asyncio.run(async_method(*args, **kwargs))
            except Exception as e:
                raise RuntimeError(
                    f"Error executing async method '{method_name}' in sync context: {str(e)}"
                ) from e

        return sync_wrapper

    def _detect_async_environment(self) -> bool:
        """检测是否在异步环境中"""
        try:
            loop = asyncio.get_running_loop()
            return loop.is_running()
        except RuntimeError:
            return False

    def _get_cached_async_environment(self) -> bool:
        """获取缓存的异步环境检测结果（最大性能模式）"""
        self._env_cache_count += 1

        # 每100次调用重新检测一次
        if self._env_cache_count >= self._env_cache_ttl:
            self._last_env_result = self._detect_async_environment()
            self._env_cache_count = 0

        return self._last_env_result

    def get_target_bean(self) -> Any:
        """获取目标Bean实例"""
        return self._target_bean

    def get_bean_name(self) -> str:
        """获取Bean名称"""
        return self._bean_name

    def get_performance_level(self) -> str:
        """获取性能级别"""
        return self._performance_level

    def get_stats(self) -> Dict[str, int]:
        """获取代理统计信息"""
        return self._stats.copy()

    def clear_cache(self) -> None:
        """清空缓存"""
        if isinstance(self._method_cache, MethodCache):
            self._method_cache.clear()
        elif isinstance(self._method_cache, dict):
            self._method_cache.clear()

        if self._inspector:
            # 重新创建属性检查器以清空其缓存
            self._inspector = AttributeInspector(self._target_bean)

    def __del__(self):
        """析构函数，更新全局统计"""
        try:
            with self._stats_lock:
                self._global_stats['active_proxies'] -= 1
        except:
            # 忽略析构时的异常
            pass

    def __str__(self) -> str:
        """字符串表示"""
        return f"BeanProxy(name='{self._bean_name}', level={self._performance_level})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (
            f"BeanProxy(bean_name='{self._bean_name}', "
            f"target_bean={type(self._target_bean).__name__}, "
            f"performance_level='{self._performance_level}')"
        )

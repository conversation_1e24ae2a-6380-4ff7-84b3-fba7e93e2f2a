#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 异步环境信息端点 - 高性能异步实现

提供高性能的异步环境信息端点,支持敏感信息保护、配置过滤和智能环境管理.

核心特性:
- 异步环境变量收集
- 敏感信息自动过滤和保护
- 配置信息分类管理
- 安全的环境信息暴露
- 智能缓存策略
"""

import asyncio
import os
import re
import time
from datetime import datetime
from enum import Enum
from typing import Any, Dict, List, Optional, Set

from loguru import logger

from miniboot.utils import cached, timeout

from .base import BaseEndpoint, EndpointOperation, OperationType


class SensitivityLevel(Enum):
    """敏感级别枚举"""

    PUBLIC = "public"  # 公开信息
    INTERNAL = "internal"  # 内部信息
    CONFIDENTIAL = "confidential"  # 机密信息
    SECRET = "secret"  # 秘密信息


class EnvironmentFilter:
    """环境变量过滤器"""

    # 敏感信息关键词模式
    SENSITIVE_PATTERNS = {
        SensitivityLevel.SECRET: [
            r".*password.*",
            r".*passwd.*",
            r".*pwd.*",
            r".*secret.*",
            r".*key.*",
            r".*token.*",
            r".*auth.*",
            r".*credential.*",
            r".*private.*",
            r".*api_key.*",
            r".*access_key.*",
            r".*secret_key.*",
        ],
        SensitivityLevel.CONFIDENTIAL: [
            r".*database.*",
            r".*db_.*",
            r".*connection.*",
            r".*host.*",
            r".*port.*",
            r".*url.*",
            r".*endpoint.*",
            r".*server.*",
        ],
        SensitivityLevel.INTERNAL: [r".*config.*", r".*setting.*", r".*option.*", r".*env.*", r".*profile.*"],
    }

    # 安全的公开环境变量
    SAFE_PUBLIC_VARS = {
        "PATH",
        "PYTHONPATH",
        "LANG",
        "LC_ALL",
        "TZ",
        "USER",
        "USERNAME",
        "HOME",
        "USERPROFILE",
        "TEMP",
        "TMP",
        "SHELL",
        "TERM",
        "JAVA_HOME",
        "PYTHON_HOME",
        "NODE_HOME",
        "VIRTUAL_ENV",
        "CONDA_DEFAULT_ENV",
        "CONDA_PREFIX",
    }

    def __init__(self, show_sensitive: bool = False, max_sensitivity: SensitivityLevel = SensitivityLevel.INTERNAL):
        """初始化环境过滤器

        Args:
            show_sensitive: 是否显示敏感信息
            max_sensitivity: 最大敏感级别
        """
        self.show_sensitive = show_sensitive
        self.max_sensitivity = max_sensitivity
        self._compiled_patterns = self._compile_patterns()

    def _compile_patterns(self) -> Dict[SensitivityLevel, List[re.Pattern]]:
        """编译正则表达式模式"""
        compiled = {}
        for level, patterns in self.SENSITIVE_PATTERNS.items():
            compiled[level] = [re.compile(pattern, re.IGNORECASE) for pattern in patterns]
        return compiled

    def classify_variable(self, var_name: str) -> SensitivityLevel:
        """分类环境变量的敏感级别"""
        var_lower = var_name.lower()

        # 检查是否为安全的公开变量
        if var_name.upper() in self.SAFE_PUBLIC_VARS:
            return SensitivityLevel.PUBLIC

        # 按敏感级别检查
        for level in [SensitivityLevel.SECRET, SensitivityLevel.CONFIDENTIAL, SensitivityLevel.INTERNAL]:
            patterns = self._compiled_patterns.get(level, [])
            for pattern in patterns:
                if pattern.match(var_lower):
                    return level

        return SensitivityLevel.PUBLIC

    def should_include(self, var_name: str, sensitivity: SensitivityLevel) -> bool:
        """判断是否应该包含该变量"""
        if not self.show_sensitive and sensitivity in [SensitivityLevel.SECRET, SensitivityLevel.CONFIDENTIAL]:
            return False

        # 检查最大敏感级别
        sensitivity_order = [SensitivityLevel.PUBLIC, SensitivityLevel.INTERNAL, SensitivityLevel.CONFIDENTIAL, SensitivityLevel.SECRET]

        return sensitivity_order.index(sensitivity) <= sensitivity_order.index(self.max_sensitivity)

    def mask_value(self, value: str, sensitivity: SensitivityLevel) -> str:
        """掩码敏感值"""
        if sensitivity == SensitivityLevel.PUBLIC:
            return value
        elif sensitivity == SensitivityLevel.INTERNAL:
            return value if self.show_sensitive else f"{value[:3]}***{value[-3:]}" if len(value) > 6 else "***"
        elif sensitivity == SensitivityLevel.CONFIDENTIAL:
            return value if self.show_sensitive else f"{value[:2]}***" if len(value) > 2 else "***"
        else:  # SECRET
            return value if self.show_sensitive else "***HIDDEN***"


class AsyncEnvironmentCollector:
    """异步环境信息收集器"""

    def __init__(self, env_filter: EnvironmentFilter, timeout: float = 5.0):
        """初始化异步环境收集器

        Args:
            env_filter: 环境过滤器
            timeout: 收集超时时间(秒)
        """
        self.env_filter = env_filter
        self.timeout = timeout

    @timeout(5.0)
    async def collect_environment_async(self) -> Dict[str, Any]:
        """异步收集环境信息"""
        try:
            loop = asyncio.get_event_loop()

            # 并发收集不同类型的环境信息
            env_vars_task = loop.run_in_executor(None, self._collect_environment_variables)
            system_info_task = loop.run_in_executor(None, self._collect_system_info)
            python_info_task = loop.run_in_executor(None, self._collect_python_info)

            # 等待所有任务完成
            env_vars, system_info, python_info = await asyncio.gather(env_vars_task, system_info_task, python_info_task, return_exceptions=True)

            result = {
                "timestamp": datetime.now().isoformat(),
                "environment_variables": env_vars if not isinstance(env_vars, Exception) else {"error": str(env_vars)},
                "system_info": system_info if not isinstance(system_info, Exception) else {"error": str(system_info)},
                "python_info": python_info if not isinstance(python_info, Exception) else {"error": str(python_info)},
                "filter_settings": {"show_sensitive": self.env_filter.show_sensitive, "max_sensitivity": self.env_filter.max_sensitivity.value},
            }

            return result

        except Exception as e:
            logger.error(f"Environment collection failed: {e}")
            return {"timestamp": datetime.now().isoformat(), "error": str(e)}

    def _collect_environment_variables(self) -> Dict[str, Any]:
        """收集环境变量"""
        environment_variables = {}
        stats = {
            "total_variables": 0,
            "public_variables": 0,
            "internal_variables": 0,
            "confidential_variables": 0,
            "secret_variables": 0,
            "hidden_variables": 0,
        }

        for var_name, var_value in os.environ.items():
            stats["total_variables"] += 1
            sensitivity = self.env_filter.classify_variable(var_name)

            # 更新统计
            if sensitivity == SensitivityLevel.PUBLIC:
                stats["public_variables"] += 1
            elif sensitivity == SensitivityLevel.INTERNAL:
                stats["internal_variables"] += 1
            elif sensitivity == SensitivityLevel.CONFIDENTIAL:
                stats["confidential_variables"] += 1
            else:  # SECRET
                stats["secret_variables"] += 1

            # 判断是否包含
            if self.env_filter.should_include(var_name, sensitivity):
                masked_value = self.env_filter.mask_value(var_value, sensitivity)
                environment_variables[var_name] = {
                    "value": masked_value,
                    "sensitivity": sensitivity.value,
                    "original_length": len(var_value),
                    "is_masked": masked_value != var_value,
                }
            else:
                stats["hidden_variables"] += 1

        return {"variables": environment_variables, "statistics": stats}

    def _collect_system_info(self) -> Dict[str, Any]:
        """收集系统信息"""
        return {
            "working_directory": os.getcwd(),
            "process_id": os.getpid(),
            "parent_process_id": os.getppid() if hasattr(os, "getppid") else None,
            "user_id": os.getuid() if hasattr(os, "getuid") else None,
            "group_id": os.getgid() if hasattr(os, "getgid") else None,
            "umask": oct(os.umask(os.umask(0o022))),  # 获取并恢复umask
        }

    def _collect_python_info(self) -> Dict[str, Any]:
        """收集Python相关信息"""
        import site
        import sys

        return {
            "executable": sys.executable,
            "prefix": sys.prefix,
            "exec_prefix": sys.exec_prefix,
            "base_prefix": getattr(sys, "base_prefix", sys.prefix),
            "base_exec_prefix": getattr(sys, "base_exec_prefix", sys.exec_prefix),
            "site_packages": site.getsitepackages() if hasattr(site, "getsitepackages") else [],
            "user_site": site.getusersitepackages() if hasattr(site, "getusersitepackages") else None,
            "path": sys.path[:10],  # 限制前10个路径
            "modules_count": len(sys.modules),
        }


class AsyncEnvEndpoint(BaseEndpoint):
    """异步环境信息端点

    提供高性能的异步环境信息收集,支持敏感信息保护和智能过滤.
    """

    def __init__(
        self,
        show_sensitive: bool = False,
        max_sensitivity: SensitivityLevel = SensitivityLevel.INTERNAL,
        cache_ttl: float = 600.0,  # 10分钟缓存
        max_concurrent: int = 5,
        timeout: float = 10.0,
    ):
        """初始化异步环境信息端点

        Args:
            show_sensitive: 是否显示敏感信息
            max_sensitivity: 最大敏感级别
            cache_ttl: 缓存TTL(秒)
            max_concurrent: 最大并发数
            timeout: 总超时时间(秒)
        """
        super().__init__(
            endpoint_id="env",
            enabled=True,
            sensitive=True,  # 环境信息端点标记为敏感
            cache_enabled=True,
            cache_ttl=cache_ttl,
            max_concurrent=max_concurrent,
            timeout=timeout,
        )

        # 创建环境过滤器和收集器
        self.env_filter = EnvironmentFilter(show_sensitive, max_sensitivity)
        self.collector = AsyncEnvironmentCollector(self.env_filter, timeout=timeout - 2)

        logger.info(f"AsyncEnvEndpoint initialized with sensitivity={max_sensitivity.value}, show_sensitive={show_sensitive}")

    def _create_operations(self) -> List[EndpointOperation]:
        """创建操作列表"""
        return [EndpointOperation(operation_type=OperationType.READ, method="GET", handler=self._get_environment_async)]

    async def _execute_operation_async(self, operation_type: OperationType, **kwargs) -> Any:
        """执行异步操作"""
        if operation_type == OperationType.READ:
            return await self._get_environment_async(**kwargs)
        else:
            raise ValueError(f"Unsupported operation: {operation_type}")

    @cached(ttl=600.0)  # 10分钟缓存
    async def _get_environment_async(self, **kwargs) -> Dict[str, Any]:
        """异步获取环境信息"""
        start_time = time.time()

        try:
            # 异步收集环境信息
            env_info = await self.collector.collect_environment_async()

            execution_time = time.time() - start_time

            # 添加元信息
            env_info["_meta"] = {
                "timestamp": datetime.now().isoformat(),
                "execution_time_ms": round(execution_time * 1000, 2),
                "endpoint_id": self.id,
                "sensitive_endpoint": self.sensitive,
                "cache_enabled": self.cache_enabled,
                "security_settings": {
                    "show_sensitive": self.env_filter.show_sensitive,
                    "max_sensitivity": self.env_filter.max_sensitivity.value,
                    "filter_enabled": True,
                },
            }

            return env_info

        except Exception as e:
            logger.error(f"Environment collection execution failed: {e}")
            return {
                "_meta": {
                    "timestamp": datetime.now().isoformat(),
                    "execution_time_ms": round((time.time() - start_time) * 1000, 2),
                    "endpoint_id": self.id,
                    "error": str(e),
                }
            }

    def update_filter_settings(self, show_sensitive: Optional[bool] = None, max_sensitivity: Optional[SensitivityLevel] = None) -> None:
        """更新过滤器设置"""
        if show_sensitive is not None:
            self.env_filter.show_sensitive = show_sensitive
        if max_sensitivity is not None:
            self.env_filter.max_sensitivity = max_sensitivity

        # 重新创建收集器
        self.collector = AsyncEnvironmentCollector(self.env_filter, timeout=self.timeout - 2)

        # 清空缓存以应用新设置
        if self.cache:
            self.cache.clear()

        logger.info(
            f"Updated filter settings: show_sensitive={self.env_filter.show_sensitive}, max_sensitivity={self.env_filter.max_sensitivity.value}"
        )

    def get_filter_info(self) -> Dict[str, Any]:
        """获取过滤器信息"""
        return {
            "show_sensitive": self.env_filter.show_sensitive,
            "max_sensitivity": self.env_filter.max_sensitivity.value,
            "safe_public_vars_count": len(self.env_filter.SAFE_PUBLIC_VARS),
            "sensitive_patterns_count": sum(len(patterns) for patterns in self.env_filter.SENSITIVE_PATTERNS.values()),
        }

    def __str__(self) -> str:
        return f"AsyncEnvEndpoint(sensitive={self.sensitive}, max_sensitivity={self.env_filter.max_sensitivity.value})"

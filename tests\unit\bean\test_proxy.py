#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean代理单元测试
"""

import asyncio
import unittest
from typing import Any
from unittest.mock import AsyncMock, Mock, patch

from miniboot.bean.proxy import BeanProxy


class TestSyncBean:
    """测试用同步Bean"""

    def __init__(self, name: str = "sync"):
        self.name = name
        self.call_count = 0
        self.value = "initial"

    def sync_method(self) -> str:
        """同步方法"""
        self.call_count += 1
        return f"sync_result_{self.call_count}"

    def get_value(self) -> str:
        """获取值"""
        return self.value

    def set_value(self, value: str) -> None:
        """设置值"""
        self.value = value

    def method_with_args(self, arg1: str, arg2: int = 10) -> str:
        """带参数的方法"""
        return f"{arg1}_{arg2}"


class TestAsyncBean:
    """测试用异步Bean"""

    def __init__(self, name: str = "async"):
        self.name = name
        self.call_count = 0
        self.value = "initial"

    async def async_method(self) -> str:
        """异步方法"""
        self.call_count += 1
        await asyncio.sleep(0.001)  # 短暂延迟
        return f"async_result_{self.call_count}"

    async def get_value_async(self) -> str:
        """异步获取值"""
        await asyncio.sleep(0.001)
        return self.value

    async def set_value_async(self, value: str) -> None:
        """异步设置值"""
        await asyncio.sleep(0.001)
        self.value = value

    def sync_method_in_async_bean(self) -> str:
        """异步Bean中的同步方法"""
        return "sync_in_async"


class TestBeanProxy(unittest.TestCase):
    """BeanProxy测试"""

    def setUp(self):
        """测试前置设置"""
        self.sync_bean = TestSyncBean("testSync")
        self.async_bean = TestAsyncBean("testAsync")

    def test_proxy_creation(self):
        """测试代理创建"""
        # 只有同步Bean的代理
        sync_proxy = BeanProxy(target_bean=self.sync_bean, bean_name="syncProxy")
        self.assertIsNotNone(sync_proxy)
        self.assertEqual(sync_proxy._bean_name, "syncProxy")

        # 只有异步Bean的代理
        async_proxy = BeanProxy(target_bean=self.async_bean, bean_name="asyncProxy")
        self.assertIsNotNone(async_proxy)
        self.assertEqual(async_proxy._bean_name, "asyncProxy")

    def test_sync_method_delegation(self):
        """测试同步方法委托"""
        proxy = BeanProxy(target_bean=self.sync_bean, bean_name="testProxy")

        # 调用同步方法
        result = proxy.sync_method()
        self.assertEqual(result, "sync_result_1")
        self.assertEqual(self.sync_bean.call_count, 1)

        # 再次调用
        result = proxy.sync_method()
        self.assertEqual(result, "sync_result_2")
        self.assertEqual(self.sync_bean.call_count, 2)

    def test_async_method_delegation(self):
        """测试异步方法委托"""
        proxy = BeanProxy(target_bean=self.async_bean, bean_name="testProxy")

        async def async_test():
            # 调用异步方法
            result = await proxy.async_method()
            self.assertEqual(result, "async_result_1")
            self.assertEqual(self.async_bean.call_count, 1)

            # 再次调用
            result = await proxy.async_method()
            self.assertEqual(result, "async_result_2")
            self.assertEqual(self.async_bean.call_count, 2)

        # 运行异步测试
        asyncio.run(async_test())

    def test_property_access(self):
        """测试属性访问"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="testProxy")

        # 获取属性
        value = proxy.get_value()
        self.assertEqual(value, "initial")

        # 设置属性
        proxy.set_value("updated")
        updated_value = proxy.get_value()
        self.assertEqual(updated_value, "updated")

        # 直接属性访问
        self.assertEqual(proxy.name, "sync")

    def test_method_with_arguments(self):
        """测试带参数的方法"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="testProxy")

        # 调用带参数的方法
        result = proxy.method_with_args("test", 20)
        self.assertEqual(result, "test_20")

        # 使用默认参数
        result = proxy.method_with_args("default")
        self.assertEqual(result, "default_10")

    def test_environment_detection(self):
        """测试环境检测"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="testProxy")

        # 在同步环境中
        self.assertFalse(proxy._is_async_environment())

        async def async_test():
            # 在异步环境中
            self.assertTrue(proxy._is_async_environment())

        asyncio.run(async_test())

    def test_method_not_found(self):
        """测试方法不存在的情况"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="testProxy")

        # 调用不存在的方法
        with self.assertRaises(AttributeError):
            proxy.nonexistent_method()

    def test_proxy_statistics(self):
        """测试代理统计"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="testProxy")

        # 调用一些方法
        proxy.sync_method()
        proxy.get_value()
        proxy.set_value("test")

        # 获取统计信息
        stats = proxy.get_stats()

        self.assertIn("bean_name", stats)
        self.assertIn("target_type", stats)
        self.assertIn("method_calls", stats)
        self.assertIn("total_calls", stats)

        self.assertEqual(stats["bean_name"], "testProxy")
        self.assertEqual(stats["target_type"], "TestSyncBean")
        self.assertGreater(stats["total_calls"], 0)

    def test_proxy_method_interception(self):
        """测试代理方法拦截"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="testProxy")

        # 添加方法拦截器
        intercepted_calls = []

        def interceptor(method_name: str, args: tuple, kwargs: dict, result: Any) -> Any:
            intercepted_calls.append((method_name, args, kwargs, result))
            return result

        proxy.add_method_interceptor(interceptor)

        # 调用方法
        result = proxy.sync_method()

        # 验证拦截器被调用
        self.assertEqual(len(intercepted_calls), 1)
        self.assertEqual(intercepted_calls[0][0], "sync_method")
        self.assertEqual(intercepted_calls[0][3], result)

    def test_proxy_error_handling(self):
        """测试代理错误处理"""
        # 创建会抛出异常的Bean
        class ErrorBean:
            def error_method(self):
                raise ValueError("Test error")

        error_bean = ErrorBean()
        proxy = BeanProxy(target=error_bean, bean_name="errorProxy")

        # 调用会出错的方法
        with self.assertRaises(ValueError):
            proxy.error_method()

        # 验证错误被正确传播
        try:
            proxy.error_method()
        except ValueError as e:
            self.assertEqual(str(e), "Test error")

    def test_proxy_async_error_handling(self):
        """测试代理异步错误处理"""
        class AsyncErrorBean:
            async def async_error_method(self):
                raise ValueError("Async test error")

        error_bean = AsyncErrorBean()
        proxy = BeanProxy(target=error_bean, bean_name="asyncErrorProxy")

        async def async_test():
            # 调用会出错的异步方法
            with self.assertRaises(ValueError):
                await proxy.async_error_method()

        asyncio.run(async_test())

    def test_proxy_performance_monitoring(self):
        """测试代理性能监控"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="perfProxy")

        # 启用性能监控
        proxy.enable_performance_monitoring()

        # 调用一些方法
        proxy.sync_method()
        proxy.get_value()

        # 获取性能统计
        perf_stats = proxy.get_performance_stats()

        self.assertIn("method_timings", perf_stats)
        self.assertIn("average_call_time", perf_stats)
        self.assertIn("total_execution_time", perf_stats)

        # 验证有方法调用记录
        self.assertGreater(len(perf_stats["method_timings"]), 0)

    def test_proxy_lazy_initialization(self):
        """测试代理延迟初始化"""
        # 使用工厂函数创建代理
        def bean_factory():
            return TestSyncBean("lazy")

        proxy = BeanProxy(target=None, bean_name="lazyProxy", target_factory=bean_factory)

        # 第一次调用方法时应该初始化目标Bean
        result = proxy.sync_method()
        self.assertEqual(result, "sync_result_1")

        # 验证目标Bean已被创建
        self.assertIsNotNone(proxy._target)
        self.assertEqual(proxy._target.name, "lazy")

    def test_proxy_shutdown(self):
        """测试代理关闭"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="shutdownProxy")

        # 调用一些方法
        proxy.sync_method()

        # 关闭代理
        proxy.shutdown()

        # 验证统计信息被清理
        stats = proxy.get_stats()
        self.assertEqual(stats["total_calls"], 0)

    @patch('miniboot.bean.proxy.asyncio.get_running_loop')
    def test_environment_detection_mocking(self, mock_get_loop):
        """测试环境检测（使用Mock）"""
        proxy = BeanProxy(target=self.sync_bean, bean_name="mockProxy")

        # 模拟没有运行中的事件循环
        mock_get_loop.side_effect = RuntimeError("No running loop")
        self.assertFalse(proxy._is_async_environment())

        # 模拟有运行中的事件循环
        mock_get_loop.side_effect = None
        mock_get_loop.return_value = Mock()
        self.assertTrue(proxy._is_async_environment())


if __name__ == '__main__':
    unittest.main()

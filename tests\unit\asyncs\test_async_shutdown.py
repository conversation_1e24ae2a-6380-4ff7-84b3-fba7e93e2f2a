#!/usr/bin/env python
"""
异步关闭测试

测试异步组件的正确关闭流程，确保资源得到及时清理。
"""

import asyncio
import contextlib
# 导入asyncio警告抑制器
import sys
import time
import unittest
import warnings
from pathlib import Path
from unittest.mock import MagicMock

sys.path.insert(0, str(Path(__file__).parent / ".." / ".."))
from utils.asyncio_patch import suppress_all_asyncio_warnings

# AsyncAutoConfiguration 已迁移到 processor 模块
# AsyncApplicationContextIntegration 已迁移到 processor 模块
from miniboot.asyncs.pool import ThreadPoolManager

# 立即抑制asyncio警告
suppress_all_asyncio_warnings()


class AsyncShutdownTestCase(unittest.IsolatedAsyncioTestCase):
    """异步关闭测试用例"""

    @classmethod
    def setUpClass(cls):
        """设置测试类级别的配置"""
        # 抑制asyncio慢回调警告
        warnings.filterwarnings("ignore", message=".*took .* seconds", category=RuntimeWarning)

        # 设置asyncio的慢回调阈值为更高的值（仅在测试中）
        import asyncio

        if hasattr(asyncio, "_set_running_loop"):
            # 尝试设置更高的慢回调阈值
            try:
                # 获取当前事件循环（如果存在）
                loop = asyncio.get_event_loop()
                if hasattr(loop, "slow_callback_duration"):
                    loop.slow_callback_duration = 10.0  # 设置为10秒
            except RuntimeError:
                pass  # 没有运行的事件循环

    async def asyncSetUp(self):
        """设置测试环境"""
        # 设置当前事件循环的慢回调阈值
        loop = asyncio.get_running_loop()
        if hasattr(loop, "slow_callback_duration"):
            loop.slow_callback_duration = 10.0  # 设置为10秒

        # 创建模拟的应用上下文
        self.mock_context = MagicMock()
        self.mock_context.get_property.return_value = True
        self.mock_context.register_shutdown_hook = MagicMock()

        # 创建模拟的环境对象
        mock_environment = MagicMock()
        mock_environment.get_property.return_value = True  # 启用异步功能
        self.mock_context.get_environment.return_value = mock_environment

        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        # 使用线程池管理器进行测试
        self.pool_manager = ThreadPoolManager()

    async def asyncTearDown(self):
        """清理测试环境"""
        with contextlib.suppress(Exception):
            # 确保异步组件被正确关闭
            await self._force_cleanup_async_components()

    async def _force_cleanup_async_components(self):
        """强制清理异步组件"""
        try:
            # AsyncAutoConfiguration 已迁移到 processor 模块，跳过相关清理

            # 关闭线程池管理器
            pool_mgr = ThreadPoolManager()
            if pool_mgr:
                pool_mgr.shutdown_all(wait=False, timeout=0.05)

            # AsyncConfigManager 已简化为直接使用 AsyncProperties，无需重置

        except Exception:
            pass  # 忽略强制清理时的错误

    async def test_fast_shutdown_in_test_environment(self):
        """测试在测试环境中的快速关闭"""
        start_time = time.time()

        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        # 测试线程池管理器的关闭
        self.pool_manager.shutdown_all(wait=True, timeout=30.0)

        end_time = time.time()
        shutdown_duration = end_time - start_time

        # 在测试环境中，关闭应该很快完成（小于0.5秒）
        self.assertLess(shutdown_duration, 0.5, f"Shutdown took too long: {shutdown_duration:.3f}s")

    async def test_shutdown_with_active_tasks(self):
        """测试有活跃任务时的关闭"""
        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        # 测试线程池管理器在有任务时的关闭

        # 创建一些异步任务
        async def dummy_task():
            await asyncio.sleep(0.01)
            return "completed"

        # 提交几个快速任务
        tasks = []
        for _i in range(3):
            task = asyncio.create_task(dummy_task())
            tasks.append(task)

        # 等待任务完成
        await asyncio.gather(*tasks, return_exceptions=True)

        # 执行关闭
        start_time = time.time()
        self.pool_manager.shutdown_all(wait=True, timeout=30.0)
        end_time = time.time()

        shutdown_duration = end_time - start_time
        self.assertLess(shutdown_duration, 0.3, f"Shutdown with tasks took too long: {shutdown_duration:.3f}s")

    async def test_multiple_shutdown_calls(self):
        """测试多次关闭调用的安全性"""
        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        # 测试线程池管理器的多次关闭

        # 多次调用关闭
        for i in range(3):
            start_time = time.time()
            self.pool_manager.shutdown_all(wait=True, timeout=30.0)
            end_time = time.time()

            # 后续的关闭调用应该更快
            if i > 0:
                shutdown_duration = end_time - start_time
                self.assertLess(shutdown_duration, 0.1, f"Repeated shutdown call {i} took too long: {shutdown_duration:.3f}s")

    async def test_shutdown_hook_registration(self):
        """测试关闭钩子注册"""
        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        # 简化测试，只测试线程池管理器的关闭功能

        # 测试关闭功能
        start_time = time.time()
        self.pool_manager.shutdown_all(wait=True, timeout=30.0)
        end_time = time.time()

        shutdown_duration = end_time - start_time
        self.assertLess(shutdown_duration, 0.2, f"Shutdown took too long: {shutdown_duration:.3f}s")

    def test_global_shutdown_function(self):
        """测试全局关闭函数"""
        start_time = time.time()

        # 调用全局关闭函数
        from miniboot.asyncs.base import AsyncSingletonMeta

        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        AsyncSingletonMeta.reset_instance(ThreadPoolManager)

        end_time = time.time()
        shutdown_duration = end_time - start_time

        # 全局关闭应该很快
        self.assertLess(shutdown_duration, 0.1, f"Global shutdown took too long: {shutdown_duration:.3f}s")

    async def test_concurrent_shutdown_safety(self):
        """测试并发关闭的安全性"""
        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        # 测试线程池管理器的并发关闭安全性

        # 创建多个并发关闭任务
        async def shutdown_task():
            self.pool_manager.shutdown_all(wait=True, timeout=30.0)

        # 并发执行关闭
        tasks = [asyncio.create_task(shutdown_task()) for _ in range(3)]

        start_time = time.time()
        await asyncio.gather(*tasks, return_exceptions=True)
        end_time = time.time()

        shutdown_duration = end_time - start_time
        self.assertLess(shutdown_duration, 0.3, f"Concurrent shutdown took too long: {shutdown_duration:.3f}s")


class AsyncShutdownPerformanceTestCase(unittest.TestCase):
    """异步关闭性能测试用例"""

    def test_shutdown_performance_metrics(self):
        """测试关闭性能指标"""
        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        pool_manager = ThreadPoolManager()

        # 测试多次关闭的性能
        durations = []
        for _i in range(5):
            start_time = time.time()
            pool_manager.shutdown_all(wait=True, timeout=30.0)
            end_time = time.time()
            durations.append(end_time - start_time)

        # 计算平均关闭时间
        avg_duration = sum(durations) / len(durations)
        max_duration = max(durations)

        # 验证性能指标
        self.assertLess(avg_duration, 0.1, f"Average shutdown duration too high: {avg_duration:.3f}s")
        self.assertLess(max_duration, 0.2, f"Max shutdown duration too high: {max_duration:.3f}s")

    def test_memory_cleanup_after_shutdown(self):
        """测试关闭后的内存清理"""
        import gc

        # AsyncApplicationContextIntegration 已迁移到 processor 模块
        pool_manager = ThreadPoolManager()

        # 执行关闭
        pool_manager.shutdown_all(wait=True, timeout=30.0)

        # 强制垃圾回收
        gc.collect()

        # 验证关键组件已被清理
        # 这里可以添加更多的内存清理验证逻辑
        self.assertTrue(True)  # 占位符，实际应该检查内存使用


if __name__ == "__main__":
    unittest.main()

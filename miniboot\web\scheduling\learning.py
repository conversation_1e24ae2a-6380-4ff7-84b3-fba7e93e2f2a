#!/usr/bin/env python
"""
自适应学习引擎

基于历史数据和机器学习的自适应调度优化引擎.

主要功能:
- 执行模式学习和预测
- 性能模式识别
- 调度策略自动优化
- 异常检测和处理
"""

import json
import time
import pickle
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from collections import defaultdict, deque
from statistics import mean, stdev
from loguru import logger

from ..properties import AsyncOptimizationConfig


@dataclass
class LearningModel:
    """学习模型"""

    model_type: str
    features: List[str]
    accuracy: float
    last_trained: float
    training_samples: int
    model_data: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PredictionResult:
    """预测结果"""

    predicted_strategy: str
    confidence: float
    execution_time_estimate: float
    features_used: List[str]
    model_accuracy: float


@dataclass
class TrainingData:
    """训练数据"""

    features: Dict[str, Any]
    actual_strategy: str
    execution_time: float
    success: bool
    timestamp: float


class LearningEngine:
    """自适应学习引擎

    使用机器学习技术优化任务调度策略.
    """

    def __init__(self, config: Optional[AsyncOptimizationConfig] = None):
        """初始化自适应学习引擎

        Args:
            config: 异步优化配置
        """
        self.config = config or AsyncOptimizationConfig()

        # 学习模型
        self._models: Dict[str, LearningModel] = {}

        # 训练数据
        self._training_data: deque = deque(maxlen=10000)
        self._feature_stats: Dict[str, Dict[str, float]] = defaultdict(dict)

        # 配置参数
        self._min_training_samples = 50
        self._retrain_interval = 3600  # 1小时
        self._feature_importance_threshold = 0.1

        # 性能模式
        self._performance_patterns: Dict[str, List[float]] = defaultdict(list)
        self._anomaly_threshold = 2.0  # 标准差倍数

        # 学习状态
        self._last_training_time = 0.0
        self._learning_enabled = getattr(config, "adaptive_learning", True)

        logger.info("LearningEngine initialized")

    def add_training_sample(self, features: Dict[str, Any], actual_strategy: str, execution_time: float, success: bool) -> None:
        """添加训练样本

        Args:
            features: 特征数据
            actual_strategy: 实际使用的策略
            execution_time: 执行时间
            success: 是否成功
        """
        if not self._learning_enabled:
            return

        sample = TrainingData(
            features=features.copy(), actual_strategy=actual_strategy, execution_time=execution_time, success=success, timestamp=time.time()
        )

        self._training_data.append(sample)

        # 更新特征统计
        self._update_feature_stats(features)

        # 更新性能模式
        function_name = features.get("function_name", "unknown")
        self._performance_patterns[function_name].append(execution_time)

        # 限制性能模式历史
        if len(self._performance_patterns[function_name]) > 1000:
            self._performance_patterns[function_name].pop(0)

        # 检查是否需要重新训练
        if self._should_retrain():
            self._train_models()

    def predict_optimal_strategy(self, features: Dict[str, Any]) -> PredictionResult:
        """预测最优策略

        Args:
            features: 特征数据

        Returns:
            预测结果
        """
        if not self._learning_enabled or not self._models:
            return self._get_default_prediction()

        try:
            # 使用最佳模型进行预测
            best_model = self._get_best_model()

            if not best_model:
                return self._get_default_prediction()

            # 预处理特征
            processed_features = self._preprocess_features(features, best_model.features)

            # 执行预测
            prediction = self._execute_prediction(best_model, processed_features)

            return prediction

        except Exception as e:
            logger.error(f"Prediction failed: {e}")
            return self._get_default_prediction()

    def detect_performance_anomaly(self, function_name: str, execution_time: float) -> bool:
        """检测性能异常

        Args:
            function_name: 函数名称
            execution_time: 执行时间

        Returns:
            是否为异常
        """
        if function_name not in self._performance_patterns:
            return False

        pattern = self._performance_patterns[function_name]

        if len(pattern) < 10:
            return False

        try:
            pattern_mean = mean(pattern)
            pattern_std = stdev(pattern)

            # 使用标准差检测异常
            z_score = abs(execution_time - pattern_mean) / pattern_std if pattern_std > 0 else 0

            return z_score > self._anomaly_threshold

        except Exception:
            return False

    def get_optimization_suggestions(self) -> List[str]:
        """获取优化建议

        Returns:
            优化建议列表
        """
        suggestions = []

        if not self._training_data:
            return ["Insufficient data for optimization suggestions"]

        # 分析策略效果
        strategy_performance = defaultdict(list)

        for sample in self._training_data:
            if sample.success:
                strategy_performance[sample.actual_strategy].append(sample.execution_time)

        if len(strategy_performance) < 2:
            return ["Need more diverse strategy usage for comparison"]

        # 找出最佳策略
        strategy_avg_times = {}
        for strategy, times in strategy_performance.items():
            strategy_avg_times[strategy] = mean(times)

        best_strategy = min(strategy_avg_times.keys(), key=lambda s: strategy_avg_times[s])
        worst_strategy = max(strategy_avg_times.keys(), key=lambda s: strategy_avg_times[s])

        improvement_potential = (strategy_avg_times[worst_strategy] - strategy_avg_times[best_strategy]) / strategy_avg_times[worst_strategy] * 100

        if improvement_potential > 20:
            suggestions.append(f"Consider using '{best_strategy}' strategy more often - potential {improvement_potential:.1f}% improvement")

        # 分析特征重要性
        important_features = self._analyze_feature_importance()
        if important_features:
            suggestions.append(f"Key performance factors: {', '.join(important_features[:3])}")

        # 检查数据质量
        success_rate = sum(1 for sample in self._training_data if sample.success) / len(self._training_data) * 100
        if success_rate < 90:
            suggestions.append(f"Low success rate ({success_rate:.1f}%) - investigate error patterns")

        return suggestions

    def _should_retrain(self) -> bool:
        """判断是否需要重新训练"""
        current_time = time.time()

        # 检查时间间隔
        if current_time - self._last_training_time < self._retrain_interval:
            return False

        # 检查数据量
        if len(self._training_data) < self._min_training_samples:
            return False

        # 检查新数据比例
        recent_samples = sum(1 for sample in self._training_data if current_time - sample.timestamp < self._retrain_interval)

        new_data_ratio = recent_samples / len(self._training_data)

        return new_data_ratio > 0.1  # 10%以上的新数据

    def _train_models(self) -> None:
        """训练模型"""
        try:
            logger.info("Starting model training...")

            # 准备训练数据
            features_list, strategies, execution_times = self._prepare_training_data()

            if len(features_list) < self._min_training_samples:
                logger.warning("Insufficient training data")
                return

            # 训练策略分类模型
            strategy_model = self._train_strategy_classifier(features_list, strategies)

            # 训练执行时间预测模型
            time_model = self._train_time_predictor(features_list, execution_times)

            # 保存模型
            if strategy_model:
                self._models["strategy_classifier"] = strategy_model

            if time_model:
                self._models["time_predictor"] = time_model

            self._last_training_time = time.time()

            logger.info(f"Model training completed. Models: {list(self._models.keys())}")

        except Exception as e:
            logger.error(f"Model training failed: {e}")

    def _prepare_training_data(self) -> Tuple[List[Dict], List[str], List[float]]:
        """准备训练数据"""
        features_list = []
        strategies = []
        execution_times = []

        for sample in self._training_data:
            if sample.success:  # 只使用成功的样本
                features_list.append(sample.features)
                strategies.append(sample.actual_strategy)
                execution_times.append(sample.execution_time)

        return features_list, strategies, execution_times

    def _train_strategy_classifier(self, features_list: List[Dict], strategies: List[str]) -> Optional[LearningModel]:
        """训练策略分类器"""
        try:
            # 简化的决策树实现
            feature_names = self._extract_feature_names(features_list)

            # 计算每个特征对策略的影响
            feature_strategy_correlation = {}

            for feature_name in feature_names:
                correlation = self._calculate_feature_strategy_correlation(features_list, strategies, feature_name)
                feature_strategy_correlation[feature_name] = correlation

            # 选择重要特征
            important_features = [
                feature for feature, correlation in feature_strategy_correlation.items() if abs(correlation) > self._feature_importance_threshold
            ]

            if not important_features:
                important_features = feature_names[:5]  # 取前5个特征

            # 创建简单的规则模型
            rules = self._create_strategy_rules(features_list, strategies, important_features)

            model = LearningModel(
                model_type="rule_based_classifier",
                features=important_features,
                accuracy=self._estimate_model_accuracy(features_list, strategies, rules),
                last_trained=time.time(),
                training_samples=len(features_list),
                model_data={"rules": rules, "feature_correlation": feature_strategy_correlation},
            )

            return model

        except Exception as e:
            logger.error(f"Strategy classifier training failed: {e}")
            return None

    def _train_time_predictor(self, features_list: List[Dict], execution_times: List[float]) -> Optional[LearningModel]:
        """训练执行时间预测器"""
        try:
            feature_names = self._extract_feature_names(features_list)

            # 计算特征与执行时间的相关性
            feature_time_correlation = {}

            for feature_name in feature_names:
                correlation = self._calculate_feature_time_correlation(features_list, execution_times, feature_name)
                feature_time_correlation[feature_name] = correlation

            # 选择重要特征
            important_features = [
                feature for feature, correlation in feature_time_correlation.items() if abs(correlation) > self._feature_importance_threshold
            ]

            if not important_features:
                important_features = feature_names[:3]

            # 创建简单的线性模型
            coefficients = self._calculate_linear_coefficients(features_list, execution_times, important_features)

            model = LearningModel(
                model_type="linear_regressor",
                features=important_features,
                accuracy=0.7,  # 简化的准确度估算
                last_trained=time.time(),
                training_samples=len(features_list),
                model_data={"coefficients": coefficients, "feature_correlation": feature_time_correlation},
            )

            return model

        except Exception as e:
            logger.error(f"Time predictor training failed: {e}")
            return None

    def _extract_feature_names(self, features_list: List[Dict]) -> List[str]:
        """提取特征名称"""
        all_features = set()
        for features in features_list:
            all_features.update(features.keys())

        # 过滤数值型特征
        numeric_features = []
        for feature_name in all_features:
            try:
                # 检查是否为数值型特征
                values = [features.get(feature_name, 0) for features in features_list]
                numeric_values = [v for v in values if isinstance(v, (int, float))]

                if len(numeric_values) > len(values) * 0.8:  # 80%以上为数值
                    numeric_features.append(feature_name)
            except Exception:
                continue

        return numeric_features

    def _calculate_feature_strategy_correlation(self, features_list: List[Dict], strategies: List[str], feature_name: str) -> float:
        """计算特征与策略的相关性"""
        try:
            # 简化的相关性计算
            strategy_counts = defaultdict(list)

            for features, strategy in zip(features_list, strategies):
                value = features.get(feature_name, 0)
                if isinstance(value, (int, float)):
                    strategy_counts[strategy].append(value)

            if len(strategy_counts) < 2:
                return 0.0

            # 计算策略间的特征值差异
            strategy_means = {strategy: mean(values) for strategy, values in strategy_counts.items()}

            max_mean = max(strategy_means.values())
            min_mean = min(strategy_means.values())

            return (max_mean - min_mean) / (max_mean + min_mean + 1e-6)

        except Exception:
            return 0.0

    def _calculate_feature_time_correlation(self, features_list: List[Dict], execution_times: List[float], feature_name: str) -> float:
        """计算特征与执行时间的相关性"""
        try:
            feature_values = []
            time_values = []

            for features, exec_time in zip(features_list, execution_times):
                value = features.get(feature_name, 0)
                if isinstance(value, (int, float)):
                    feature_values.append(value)
                    time_values.append(exec_time)

            if len(feature_values) < 10:
                return 0.0

            # 简化的皮尔逊相关系数
            n = len(feature_values)
            sum_x = sum(feature_values)
            sum_y = sum(time_values)
            sum_xy = sum(x * y for x, y in zip(feature_values, time_values))
            sum_x2 = sum(x * x for x in feature_values)
            sum_y2 = sum(y * y for y in time_values)

            numerator = n * sum_xy - sum_x * sum_y
            denominator = ((n * sum_x2 - sum_x * sum_x) * (n * sum_y2 - sum_y * sum_y)) ** 0.5

            return numerator / denominator if denominator != 0 else 0.0

        except Exception:
            return 0.0

    def _create_strategy_rules(self, features_list: List[Dict], strategies: List[str], important_features: List[str]) -> Dict[str, Any]:
        """创建策略规则"""
        rules = {}

        # 为每个策略创建简单规则
        strategy_feature_stats = defaultdict(lambda: defaultdict(list))

        for features, strategy in zip(features_list, strategies):
            for feature_name in important_features:
                value = features.get(feature_name, 0)
                if isinstance(value, (int, float)):
                    strategy_feature_stats[strategy][feature_name].append(value)

        # 计算每个策略的特征阈值
        for strategy, feature_stats in strategy_feature_stats.items():
            strategy_rules = {}
            for feature_name, values in feature_stats.items():
                if values:
                    strategy_rules[feature_name] = {"mean": mean(values), "min": min(values), "max": max(values)}
            rules[strategy] = strategy_rules

        return rules

    def _calculate_linear_coefficients(
        self, features_list: List[Dict], execution_times: List[float], important_features: List[str]
    ) -> Dict[str, float]:
        """计算线性回归系数"""
        coefficients = {}

        for feature_name in important_features:
            correlation = self._calculate_feature_time_correlation(features_list, execution_times, feature_name)
            coefficients[feature_name] = correlation * 0.01  # 简化的系数计算

        return coefficients

    def _estimate_model_accuracy(self, features_list: List[Dict], strategies: List[str], rules: Dict[str, Any]) -> float:
        """估算模型准确度"""
        # 简化的准确度估算
        return 0.75  # 假设75%的准确度

    def _get_best_model(self) -> Optional[LearningModel]:
        """获取最佳模型"""
        if not self._models:
            return None

        # 选择准确度最高的模型
        best_model = max(self._models.values(), key=lambda m: m.accuracy)
        return best_model

    def _preprocess_features(self, features: Dict[str, Any], model_features: List[str]) -> Dict[str, Any]:
        """预处理特征"""
        processed = {}

        for feature_name in model_features:
            value = features.get(feature_name, 0)
            if isinstance(value, (int, float)):
                processed[feature_name] = value
            else:
                processed[feature_name] = 0

        return processed

    def _execute_prediction(self, model: LearningModel, features: Dict[str, Any]) -> PredictionResult:
        """执行预测"""
        if model.model_type == "rule_based_classifier":
            return self._predict_with_rules(model, features)
        else:
            return self._get_default_prediction()

    def _predict_with_rules(self, model: LearningModel, features: Dict[str, Any]) -> PredictionResult:
        """使用规则进行预测"""
        rules = model.model_data.get("rules", {})

        best_strategy = "sync"
        best_score = 0.0

        for strategy, strategy_rules in rules.items():
            score = 0.0

            for feature_name, feature_rule in strategy_rules.items():
                feature_value = features.get(feature_name, 0)
                feature_mean = feature_rule.get("mean", 0)

                # 简单的匹配评分
                if abs(feature_value - feature_mean) < feature_mean * 0.5:
                    score += 1.0

            if score > best_score:
                best_score = score
                best_strategy = strategy

        confidence = min(1.0, best_score / len(model.features))

        # 估算执行时间
        time_model = self._models.get("time_predictor")
        if time_model:
            execution_time_estimate = self._estimate_execution_time(time_model, features)
        else:
            execution_time_estimate = 0.01

        return PredictionResult(
            predicted_strategy=best_strategy,
            confidence=confidence,
            execution_time_estimate=execution_time_estimate,
            features_used=model.features,
            model_accuracy=model.accuracy,
        )

    def _estimate_execution_time(self, model: LearningModel, features: Dict[str, Any]) -> float:
        """估算执行时间"""
        coefficients = model.model_data.get("coefficients", {})

        estimated_time = 0.01  # 基础时间

        for feature_name, coefficient in coefficients.items():
            feature_value = features.get(feature_name, 0)
            estimated_time += feature_value * coefficient

        return max(0.001, estimated_time)

    def _get_default_prediction(self) -> PredictionResult:
        """获取默认预测"""
        return PredictionResult(predicted_strategy="sync", confidence=0.1, execution_time_estimate=0.01, features_used=[], model_accuracy=0.0)

    def _update_feature_stats(self, features: Dict[str, Any]) -> None:
        """更新特征统计"""
        for feature_name, value in features.items():
            if isinstance(value, (int, float)):
                if "values" not in self._feature_stats[feature_name]:
                    self._feature_stats[feature_name]["values"] = deque(maxlen=1000)

                self._feature_stats[feature_name]["values"].append(value)

    def _analyze_feature_importance(self) -> List[str]:
        """分析特征重要性"""
        if not self._models:
            return []

        # 从模型中提取重要特征
        important_features = set()

        for model in self._models.values():
            important_features.update(model.features)

        return list(important_features)

    async def save_model(self, filepath: Optional[str] = None) -> None:
        """保存模型"""
        if not self._models:
            return

        try:
            model_data = {
                "models": {
                    name: {
                        "model_type": model.model_type,
                        "features": model.features,
                        "accuracy": model.accuracy,
                        "last_trained": model.last_trained,
                        "training_samples": model.training_samples,
                        "model_data": model.model_data,
                    }
                    for name, model in self._models.items()
                },
                "feature_stats": dict(self._feature_stats),
                "performance_patterns": dict(self._performance_patterns),
            }

            if filepath:
                with open(filepath, "w") as f:
                    json.dump(model_data, f, indent=2)

            logger.info("Models saved successfully")

        except Exception as e:
            logger.error(f"Failed to save models: {e}")

    async def load_model(self, filepath: str) -> None:
        """加载模型"""
        try:
            with open(filepath, "r") as f:
                model_data = json.load(f)

            # 重建模型
            for name, data in model_data.get("models", {}).items():
                self._models[name] = LearningModel(
                    model_type=data["model_type"],
                    features=data["features"],
                    accuracy=data["accuracy"],
                    last_trained=data["last_trained"],
                    training_samples=data["training_samples"],
                    model_data=data["model_data"],
                )

            # 重建统计数据
            self._feature_stats = defaultdict(dict, model_data.get("feature_stats", {}))
            self._performance_patterns = defaultdict(list, model_data.get("performance_patterns", {}))

            logger.info(f"Models loaded successfully: {list(self._models.keys())}")

        except Exception as e:
            logger.error(f"Failed to load models: {e}")

    def get_learning_statistics(self) -> Dict[str, Any]:
        """获取学习统计信息"""
        return {
            "learning_enabled": self._learning_enabled,
            "training_samples": len(self._training_data),
            "models_count": len(self._models),
            "models": {
                name: {
                    "type": model.model_type,
                    "accuracy": model.accuracy,
                    "features_count": len(model.features),
                    "training_samples": model.training_samples,
                    "last_trained": model.last_trained,
                }
                for name, model in self._models.items()
            },
            "feature_count": len(self._feature_stats),
            "performance_patterns_count": len(self._performance_patterns),
            "last_training_time": self._last_training_time,
        }

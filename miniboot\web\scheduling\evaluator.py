#!/usr/bin/env python
"""
异步收益评估器

评估异步执行的性能收益,为执行策略选择提供数据支持.

主要功能:
- 异步vs同步性能对比
- 并发收益分析
- 资源利用率评估
- 执行模式推荐
"""

import asyncio
import time
import concurrent.futures
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from statistics import mean
from loguru import logger

from ..properties import AsyncOptimizationConfig


class ExecutionMode(Enum):
    """执行模式"""

    SYNC = "sync"
    ASYNC = "async"
    THREAD_POOL = "thread_pool"
    PROCESS_POOL = "process_pool"


@dataclass
class BenchmarkResult:
    """基准测试结果"""

    mode: ExecutionMode
    execution_time: float
    throughput: float  # 每秒处理数量
    cpu_usage: float
    memory_usage: float
    success_rate: float
    error_count: int


@dataclass
class BenefitAnalysis:
    """收益分析结果"""

    recommended_mode: ExecutionMode
    performance_improvement: float  # 性能提升百分比
    throughput_improvement: float  # 吞吐量提升百分比
    resource_efficiency: float  # 资源效率评分
    confidence: float  # 推荐置信度
    analysis_details: Dict[str, Any]
    benchmark_results: List[BenchmarkResult]


class BenefitEvaluator:
    """异步收益评估器

    通过基准测试和性能分析,评估不同执行模式的收益.
    """

    def __init__(self, config: Optional[AsyncOptimizationConfig] = None):
        """初始化异步收益评估器

        Args:
            config: 异步优化配置
        """
        self.config = config or AsyncOptimizationConfig()

        # 配置参数
        self._benchmark_iterations = 10
        self._benchmark_timeout = 30.0
        self._min_benefit_threshold = getattr(config, "async_benefit_threshold", 0.1)

        # 执行器池
        self._thread_pool = concurrent.futures.ThreadPoolExecutor(max_workers=4)
        self._process_pool = None  # 按需创建

        # 历史评估数据
        self._evaluation_history: Dict[str, List[BenefitAnalysis]] = {}

        logger.info("BenefitEvaluator initialized")

    async def evaluate_function(self, function: Callable, args: tuple = (), kwargs: dict = None, test_data: List[Any] = None) -> BenefitAnalysis:
        """评估函数的异步收益

        Args:
            function: 要评估的函数
            args: 函数参数
            kwargs: 函数关键字参数
            test_data: 测试数据集

        Returns:
            收益分析结果
        """
        kwargs = kwargs or {}
        function_name = function.__name__

        logger.info(f"Starting benefit evaluation for function: {function_name}")

        try:
            # 1. 运行基准测试
            benchmark_results = await self._run_benchmarks(function, args, kwargs, test_data)

            # 2. 分析结果
            analysis = self._analyze_benchmark_results(benchmark_results)

            # 3. 生成推荐
            recommendation = self._generate_recommendation(analysis, benchmark_results)

            # 4. 记录历史
            self._record_evaluation(function_name, recommendation)

            logger.info(f"Benefit evaluation completed for {function_name}: {recommendation.recommended_mode.value}")

            return recommendation

        except Exception as e:
            logger.error(f"Benefit evaluation failed for {function_name}: {e}")
            return self._get_default_analysis(function)

    async def _run_benchmarks(self, function: Callable, args: tuple, kwargs: dict, test_data: List[Any] = None) -> List[BenchmarkResult]:
        """运行基准测试"""
        results = []

        # 准备测试数据
        if test_data is None:
            test_data = [None] * self._benchmark_iterations

        # 测试同步执行
        if not asyncio.iscoroutinefunction(function):
            sync_result = await self._benchmark_sync_execution(function, args, kwargs, test_data)
            results.append(sync_result)

        # 测试异步执行
        async_result = await self._benchmark_async_execution(function, args, kwargs, test_data)
        results.append(async_result)

        # 测试线程池执行
        thread_result = await self._benchmark_thread_pool_execution(function, args, kwargs, test_data)
        results.append(thread_result)

        return results

    async def _benchmark_sync_execution(self, function: Callable, args: tuple, kwargs: dict, test_data: List[Any]) -> BenchmarkResult:
        """基准测试同步执行"""
        start_time = time.time()
        success_count = 0
        error_count = 0

        try:
            for data in test_data:
                try:
                    if data is not None:
                        function(data, *args, **kwargs)
                    else:
                        function(*args, **kwargs)
                    success_count += 1
                except Exception:
                    error_count += 1

            execution_time = time.time() - start_time
            throughput = success_count / execution_time if execution_time > 0 else 0
            success_rate = success_count / len(test_data) if test_data else 0

            return BenchmarkResult(
                mode=ExecutionMode.SYNC,
                execution_time=execution_time,
                throughput=throughput,
                cpu_usage=0.0,  # 简化实现
                memory_usage=0.0,
                success_rate=success_rate,
                error_count=error_count,
            )

        except Exception as e:
            logger.error(f"Sync benchmark failed: {e}")
            return BenchmarkResult(
                mode=ExecutionMode.SYNC,
                execution_time=float("inf"),
                throughput=0.0,
                cpu_usage=0.0,
                memory_usage=0.0,
                success_rate=0.0,
                error_count=len(test_data),
            )

    async def _benchmark_async_execution(self, function: Callable, args: tuple, kwargs: dict, test_data: List[Any]) -> BenchmarkResult:
        """基准测试异步执行"""
        start_time = time.time()
        success_count = 0
        error_count = 0

        try:
            if asyncio.iscoroutinefunction(function):
                # 原生异步函数
                tasks = []
                for data in test_data:
                    if data is not None:
                        task = function(data, *args, **kwargs)
                    else:
                        task = function(*args, **kwargs)
                    tasks.append(task)

                results = await asyncio.gather(*tasks, return_exceptions=True)

                for result in results:
                    if isinstance(result, Exception):
                        error_count += 1
                    else:
                        success_count += 1
            else:
                # 同步函数异步执行
                loop = asyncio.get_event_loop()
                tasks = []

                for data in test_data:
                    if data is not None:
                        task = loop.run_in_executor(None, lambda d=data: function(d, *args, **kwargs))
                    else:
                        task = loop.run_in_executor(None, lambda: function(*args, **kwargs))
                    tasks.append(task)

                results = await asyncio.gather(*tasks, return_exceptions=True)

                for result in results:
                    if isinstance(result, Exception):
                        error_count += 1
                    else:
                        success_count += 1

            execution_time = time.time() - start_time
            throughput = success_count / execution_time if execution_time > 0 else 0
            success_rate = success_count / len(test_data) if test_data else 0

            return BenchmarkResult(
                mode=ExecutionMode.ASYNC,
                execution_time=execution_time,
                throughput=throughput,
                cpu_usage=0.0,
                memory_usage=0.0,
                success_rate=success_rate,
                error_count=error_count,
            )

        except Exception as e:
            logger.error(f"Async benchmark failed: {e}")
            return BenchmarkResult(
                mode=ExecutionMode.ASYNC,
                execution_time=float("inf"),
                throughput=0.0,
                cpu_usage=0.0,
                memory_usage=0.0,
                success_rate=0.0,
                error_count=len(test_data),
            )

    async def _benchmark_thread_pool_execution(self, function: Callable, args: tuple, kwargs: dict, test_data: List[Any]) -> BenchmarkResult:
        """基准测试线程池执行"""
        start_time = time.time()
        success_count = 0
        error_count = 0

        try:
            loop = asyncio.get_event_loop()
            futures = []

            for data in test_data:
                if data is not None:
                    future = loop.run_in_executor(self._thread_pool, lambda d=data: function(d, *args, **kwargs))
                else:
                    future = loop.run_in_executor(self._thread_pool, lambda: function(*args, **kwargs))
                futures.append(future)

            results = await asyncio.gather(*futures, return_exceptions=True)

            for result in results:
                if isinstance(result, Exception):
                    error_count += 1
                else:
                    success_count += 1

            execution_time = time.time() - start_time
            throughput = success_count / execution_time if execution_time > 0 else 0
            success_rate = success_count / len(test_data) if test_data else 0

            return BenchmarkResult(
                mode=ExecutionMode.THREAD_POOL,
                execution_time=execution_time,
                throughput=throughput,
                cpu_usage=0.0,
                memory_usage=0.0,
                success_rate=success_rate,
                error_count=error_count,
            )

        except Exception as e:
            logger.error(f"Thread pool benchmark failed: {e}")
            return BenchmarkResult(
                mode=ExecutionMode.THREAD_POOL,
                execution_time=float("inf"),
                throughput=0.0,
                cpu_usage=0.0,
                memory_usage=0.0,
                success_rate=0.0,
                error_count=len(test_data),
            )

    def _analyze_benchmark_results(self, results: List[BenchmarkResult]) -> Dict[str, Any]:
        """分析基准测试结果"""
        if not results:
            return {}

        # 找到最佳性能结果
        valid_results = [r for r in results if r.execution_time != float("inf") and r.success_rate > 0]

        if not valid_results:
            return {"error": "No valid benchmark results"}

        # 按执行时间排序
        sorted_by_time = sorted(valid_results, key=lambda r: r.execution_time)
        fastest = sorted_by_time[0]

        # 按吞吐量排序
        sorted_by_throughput = sorted(valid_results, key=lambda r: r.throughput, reverse=True)
        highest_throughput = sorted_by_throughput[0]

        # 计算性能提升
        baseline = next((r for r in results if r.mode == ExecutionMode.SYNC), fastest)

        performance_improvements = {}
        throughput_improvements = {}

        for result in valid_results:
            if result.mode != baseline.mode:
                perf_improvement = ((baseline.execution_time - result.execution_time) / baseline.execution_time) * 100
                throughput_improvement = ((result.throughput - baseline.throughput) / baseline.throughput) * 100 if baseline.throughput > 0 else 0

                performance_improvements[result.mode.value] = perf_improvement
                throughput_improvements[result.mode.value] = throughput_improvement

        return {
            "fastest_mode": fastest.mode,
            "highest_throughput_mode": highest_throughput.mode,
            "baseline_mode": baseline.mode,
            "performance_improvements": performance_improvements,
            "throughput_improvements": throughput_improvements,
            "valid_results_count": len(valid_results),
            "total_results_count": len(results),
        }

    def _generate_recommendation(self, analysis: Dict[str, Any], results: List[BenchmarkResult]) -> BenefitAnalysis:
        """生成执行模式推荐"""
        if "error" in analysis:
            return self._get_default_analysis(None)

        # 选择推荐模式
        fastest_mode = analysis.get("fastest_mode", ExecutionMode.SYNC)
        highest_throughput_mode = analysis.get("highest_throughput_mode", ExecutionMode.SYNC)

        # 综合考虑性能和吞吐量
        performance_improvements = analysis.get("performance_improvements", {})
        throughput_improvements = analysis.get("throughput_improvements", {})

        # 计算综合评分
        mode_scores = {}
        for result in results:
            if result.execution_time == float("inf") or result.success_rate == 0:
                continue

            mode = result.mode.value
            perf_score = performance_improvements.get(mode, 0)
            throughput_score = throughput_improvements.get(mode, 0)

            # 综合评分:性能权重0.6,吞吐量权重0.4
            combined_score = perf_score * 0.6 + throughput_score * 0.4
            mode_scores[result.mode] = combined_score

        # 选择最佳模式
        if mode_scores:
            recommended_mode = max(mode_scores.keys(), key=lambda m: mode_scores[m])
            max_score = mode_scores[recommended_mode]
        else:
            recommended_mode = ExecutionMode.SYNC
            max_score = 0

        # 计算置信度
        confidence = min(1.0, max(0.1, abs(max_score) / 100))

        # 只有显著改善才推荐非同步模式
        if max_score < self._min_benefit_threshold * 100 and recommended_mode != ExecutionMode.SYNC:
            recommended_mode = ExecutionMode.SYNC
            confidence = 0.5

        return BenefitAnalysis(
            recommended_mode=recommended_mode,
            performance_improvement=max_score,
            throughput_improvement=throughput_improvements.get(recommended_mode.value, 0),
            resource_efficiency=self._calculate_resource_efficiency(results),
            confidence=confidence,
            analysis_details=analysis,
            benchmark_results=results,
        )

    def _calculate_resource_efficiency(self, results: List[BenchmarkResult]) -> float:
        """计算资源效率评分"""
        if not results:
            return 0.0

        # 简化的资源效率计算
        valid_results = [r for r in results if r.execution_time != float("inf")]
        if not valid_results:
            return 0.0

        # 基于吞吐量和成功率的效率评分
        efficiency_scores = []
        for result in valid_results:
            efficiency = result.throughput * result.success_rate
            efficiency_scores.append(efficiency)

        max_efficiency = max(efficiency_scores) if efficiency_scores else 1.0
        avg_efficiency = mean(efficiency_scores) if efficiency_scores else 0.0

        return min(1.0, avg_efficiency / max_efficiency) if max_efficiency > 0 else 0.0

    def _get_default_analysis(self, function: Optional[Callable]) -> BenefitAnalysis:
        """获取默认分析结果"""
        return BenefitAnalysis(
            recommended_mode=ExecutionMode.SYNC,
            performance_improvement=0.0,
            throughput_improvement=0.0,
            resource_efficiency=0.5,
            confidence=0.1,
            analysis_details={"error": "Evaluation failed"},
            benchmark_results=[],
        )

    def _record_evaluation(self, function_name: str, analysis: BenefitAnalysis) -> None:
        """记录评估历史"""
        if function_name not in self._evaluation_history:
            self._evaluation_history[function_name] = []

        self._evaluation_history[function_name].append(analysis)

        # 限制历史记录大小
        if len(self._evaluation_history[function_name]) > 10:
            self._evaluation_history[function_name].pop(0)

    def get_evaluation_history(self, function_name: str) -> List[BenefitAnalysis]:
        """获取函数的评估历史

        Args:
            function_name: 函数名称

        Returns:
            评估历史列表
        """
        return self._evaluation_history.get(function_name, [])

    def get_summary_statistics(self) -> Dict[str, Any]:
        """获取汇总统计信息"""
        total_evaluations = sum(len(history) for history in self._evaluation_history.values())

        if total_evaluations == 0:
            return {"total_evaluations": 0, "functions_evaluated": 0}

        # 统计推荐模式分布
        mode_counts = {}
        confidence_scores = []
        performance_improvements = []

        for history in self._evaluation_history.values():
            for analysis in history:
                mode = analysis.recommended_mode.value
                mode_counts[mode] = mode_counts.get(mode, 0) + 1
                confidence_scores.append(analysis.confidence)
                performance_improvements.append(analysis.performance_improvement)

        return {
            "total_evaluations": total_evaluations,
            "functions_evaluated": len(self._evaluation_history),
            "mode_distribution": mode_counts,
            "avg_confidence": mean(confidence_scores) if confidence_scores else 0.0,
            "avg_performance_improvement": mean(performance_improvements) if performance_improvements else 0.0,
            "beneficial_evaluations": len([p for p in performance_improvements if p > 0]),
        }

    def cleanup(self) -> None:
        """清理资源"""
        if self._thread_pool:
            self._thread_pool.shutdown(wait=True)

        if self._process_pool:
            self._process_pool.shutdown(wait=True)

        logger.info("BenefitEvaluator cleaned up")

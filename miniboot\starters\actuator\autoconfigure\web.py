#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Actuator Web 自动配置类

实现 Actuator 与 Web 框架的自动集成配置，包括：
- Web 集成条件检查
- 动态路由注册器配置
- FastAPI 集成支持
- 条件化 Web 功能启用

配置条件：
- FastAPI 类可用：@ConditionalOnClass(name="fastapi.FastAPI")
- Web 模块启用：@ConditionalOnProperty(name="miniboot.starters.web.enabled", match_if_missing=False)
- Actuator Web 启用：@ConditionalOnProperty(name="miniboot.starters.actuator.web.enabled", match_if_missing=True)

使用示例：
    # application.yml
    miniboot:
        starters:
            web:
                enabled: true  # 启用 Web 模块
            actuator:
                web:
                    enabled: true  # 启用 Actuator Web 集成
                base_path: "/actuator"
                endpoints:
                    health: true
                    info: true
                    metrics: true
"""

from typing import Optional

from loguru import logger

from miniboot.annotations import (Bean, ConditionalOnClass,
                                  ConditionalOnProperty, Configuration)
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata

from ..properties import ActuatorProperties


@ConditionalOnClass(name="fastapi.FastAPI")
@ConditionalOnProperty(name="miniboot.starters.web.enabled", match_if_missing=False)
@ConditionalOnProperty(name="miniboot.starters.actuator.web.enabled", match_if_missing=True)
class WebAutoConfiguration(AutoConfiguration):
    """Actuator Web 自动配置类

    负责配置 Actuator 与 Web 框架的集成组件，包括：
    - WebIntegrationChecker：Web 集成条件检查器
    - ActuatorRouteRegistrar：动态路由注册器

    采用多重条件化配置，确保只在满足所有条件时才启用 Web 集成：
    1. FastAPI 框架可用
    2. Web 模块已启用
    3. Actuator Web 集成已启用
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="actuator-web-auto-configuration",
            description="Actuator Web 集成自动配置",
            priority=300,  # 较低优先级，在基础设施之后
            auto_configure_after=["actuator-starter-auto-configuration", "web-auto-configuration"],
        )

    @Bean
    def web_integration_checker(self) -> "WebIntegrationChecker":
        """创建 Web 集成检查器 Bean

        Returns:
            WebIntegrationChecker: Web 集成条件检查器实例
        """
        from ..web.integration import WebIntegrationChecker

        logger.debug("Creating WebIntegrationChecker bean")
        checker = WebIntegrationChecker()

        # 记录集成状态
        status = checker.get_integration_status()
        logger.info(f"Web integration status: FastAPI={status.fastapi_available}, "
                   f"Web module={status.web_module_enabled}, "
                   f"Should integrate={status.should_integrate}")

        return checker

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.web.routes.enabled", match_if_missing=True)
    def actuator_route_registrar(
        self,
        actuator_properties: ActuatorProperties,
        web_integration_checker: "WebIntegrationChecker"
    ) -> Optional["ActuatorRouteRegistrar"]:
        """创建 Actuator 路由注册器 Bean

        Args:
            actuator_properties: Actuator 配置属性
            web_integration_checker: Web 集成检查器

        Returns:
            ActuatorRouteRegistrar: 动态路由注册器实例，如果条件不满足则返回 None
        """
        from ..web.routes import ActuatorRouteRegistrar

        # 检查是否应该启用 Web 集成
        if not web_integration_checker.should_enable_web_integration():
            logger.info("Web integration conditions not met, skipping route registrar creation")
            return None

        logger.debug("Creating ActuatorRouteRegistrar bean")

        # 获取 FastAPI 应用实例（如果可用）
        app = self._get_fastapi_app()
        if not app:
            logger.warning("FastAPI app not available, route registrar will be created without app")

        # 创建 ActuatorContext 实例
        actuator_context = self._get_actuator_context(actuator_properties)

        # 获取基础路径配置
        base_path = actuator_properties.web.base_path

        registrar = ActuatorRouteRegistrar(
            app=app,
            actuator_context=actuator_context,
            base_path=base_path
        )

        logger.info(f"Created ActuatorRouteRegistrar with base_path='{base_path}'")
        return registrar

    def _get_fastapi_app(self) -> Optional["FastAPI"]:
        """获取 FastAPI 应用实例

        Returns:
            FastAPI: FastAPI 应用实例，如果不可用则返回 None
        """
        try:
            # 尝试从应用上下文获取 FastAPI 实例
            # 这里可以根据实际的应用架构调整获取方式
            from fastapi import FastAPI

            # 暂时返回 None，实际使用时需要从应用上下文获取
            # 或者在路由注册时动态设置
            return None

        except ImportError:
            logger.debug("FastAPI not available")
            return None
        except Exception as e:
            logger.warning(f"Failed to get FastAPI app: {e}")
            return None

    def _get_actuator_context(self, actuator_properties: ActuatorProperties) -> "ActuatorContext":
        """获取或创建 ActuatorContext 实例

        Args:
            actuator_properties: Actuator 配置属性

        Returns:
            ActuatorContext: Actuator 上下文实例
        """
        try:
            # 尝试从 Bean 工厂获取现有的 ActuatorContext
            # 这里简化处理，实际使用时应该从 Bean 工厂获取
            from miniboot.starters.actuator.context import ActuatorContext

            # 直接使用 Starter 配置，不需要转换
            # 创建 ActuatorContext 实例
            actuator_context = ActuatorContext(properties=actuator_properties, auto_load_config=False)

            logger.debug("Created ActuatorContext for route registrar")
            return actuator_context

        except Exception as e:
            logger.error(f"Failed to get ActuatorContext: {e}")
            raise

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.web.middleware.enabled", match_if_missing=False)
    def actuator_web_middleware(self) -> Optional["ActuatorWebMiddleware"]:
        """创建 Actuator Web 中间件 Bean（可选）

        Returns:
            ActuatorWebMiddleware: Web 中间件实例，如果未启用则返回 None
        """
        # 这是一个可选的中间件组件，可以在后续任务中实现
        logger.debug("ActuatorWebMiddleware not implemented yet")
        return None

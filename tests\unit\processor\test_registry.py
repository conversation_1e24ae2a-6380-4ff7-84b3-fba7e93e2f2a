#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean后处理器注册表测试
"""

import threading
import time
import unittest
from typing import Any

from miniboot.errors.processor import BeanProcessingError
from miniboot.processor import (BeanPostProcessor, BeanPostProcessorRegistry,
                                ProcessorRegistrationHelper)


def create_mock_processor_class(class_name: str):
    """创建不同的MockProcessor类以避免重复注册"""

    class MockProcessor(BeanPostProcessor):
        """模拟处理器"""

        def __init__(self, name: str, order: int = 0, should_fail: bool = False):
            self.name = name
            self.order = order
            self.should_fail = should_fail
            self.before_calls = []
            self.after_calls = []

        def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
            if self.should_fail:
                raise RuntimeError(f"Processor {self.name} failed")
            self.before_calls.append((bean, bean_name))
            return f"{self.name}_before_{bean}"

        def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
            if self.should_fail:
                raise RuntimeError(f"Processor {self.name} failed")
            self.after_calls.append((bean, bean_name))
            return f"{self.name}_after_{bean}"

        def get_order(self) -> int:
            return self.order

        def supports(self, bean: Any, bean_name: str) -> bool:
            # 默认支持所有bean，除非明确指定过滤条件
            # 这里实际使用参数进行简单的类型检查
            return bean is not None and isinstance(bean_name, str)

    MockProcessor.__name__ = class_name
    MockProcessor.__qualname__ = class_name
    return MockProcessor


# 为了向后兼容，保留原始的MockProcessor
MockProcessor = create_mock_processor_class("MockProcessor")


class TestBeanPostProcessorRegistry(unittest.TestCase):
    """测试BeanPostProcessorRegistry"""

    def setUp(self):
        """设置测试环境"""
        self.registry = BeanPostProcessorRegistry()

    def test_empty_registry(self):
        """测试空注册表"""
        self.assertEqual(len(self.registry), 0)
        self.assertEqual(self.registry.get_processor_count(), 0)
        self.assertEqual(self.registry.get_processors(), [])

        # 空注册表应该直接返回原始bean
        bean = "test_bean"
        result = self.registry.apply_before_initialization(bean, "test")
        self.assertEqual(result, bean)

        result = self.registry.apply_after_initialization(bean, "test")
        self.assertEqual(result, bean)

    def test_register_processor(self):
        """测试注册处理器"""
        processor = MockProcessor("test", 100)
        self.registry.register(processor)

        self.assertEqual(len(self.registry), 1)
        self.assertTrue(self.registry.has(MockProcessor))
        self.assertIn(processor, self.registry.processors())

    def test_register_duplicate_processor_type(self):
        """测试注册重复类型的处理器"""
        processor1 = MockProcessor("test1", 100)
        processor2 = MockProcessor("test2", 200)

        self.registry.register(processor1)

        # 注册相同类型的处理器应该抛出异常
        with self.assertRaises(ValueError) as cm:
            self.registry.register(processor2)
        self.assertIn("已经注册", str(cm.exception))

    def test_register_none_processor(self):
        """测试注册None处理器"""
        with self.assertRaises(ValueError) as cm:
            self.registry.register_processor(None)
        self.assertIn("不能为None", str(cm.exception))

    def test_unregister_processor(self):
        """测试注销处理器"""
        processor = MockProcessor("test", 100)
        self.registry.register_processor(processor)

        # 注销存在的处理器
        result = self.registry.unregister_processor(MockProcessor)
        self.assertTrue(result)
        self.assertEqual(len(self.registry), 0)
        self.assertFalse(self.registry.has_processor(MockProcessor))

        # 注销不存在的处理器
        result = self.registry.unregister_processor(MockProcessor)
        self.assertFalse(result)

    def test_processor_ordering(self):
        """测试处理器排序"""
        # 创建不同order的处理器
        high_processor_class = create_mock_processor_class("HighProcessor")
        low_processor_class = create_mock_processor_class("LowProcessor")
        medium_processor_class = create_mock_processor_class("MediumProcessor")

        processor1 = high_processor_class("high", 300)
        processor2 = low_processor_class("low", 100)
        processor3 = medium_processor_class("medium", 200)

        # 按非排序顺序注册
        self.registry.register_processor(processor1)
        self.registry.register_processor(processor2)
        self.registry.register_processor(processor3)

        # 获取处理器应该按order排序
        processors = self.registry.get_processors()
        orders = [p.get_order() for p in processors]
        self.assertEqual(orders, [100, 200, 300])

    def test_apply_before_initialization(self):
        """测试应用初始化前处理器"""
        first_processor_class = create_mock_processor_class("FirstProcessor")
        second_processor_class = create_mock_processor_class("SecondProcessor")

        processor1 = first_processor_class("first", 100)
        processor2 = second_processor_class("second", 200)

        self.registry.register(processor1)
        self.registry.register(processor2)

        result = self.registry.before("bean", "testBean")

        # 应该按order顺序处理
        self.assertEqual(result, "second_before_first_before_bean")
        self.assertEqual(processor1.before_calls, [("bean", "testBean")])
        self.assertEqual(processor2.before_calls, [("first_before_bean", "testBean")])

    def test_apply_after_initialization(self):
        """测试应用初始化后处理器"""
        first_processor_class = create_mock_processor_class("FirstProcessor")
        second_processor_class = create_mock_processor_class("SecondProcessor")

        processor1 = first_processor_class("first", 100)
        processor2 = second_processor_class("second", 200)

        self.registry.register_processor(processor1)
        self.registry.register_processor(processor2)

        result = self.registry.apply_after_initialization("bean", "testBean")

        # 应该按order顺序处理
        self.assertEqual(result, "second_after_first_after_bean")
        self.assertEqual(processor1.after_calls, [("bean", "testBean")])
        self.assertEqual(processor2.after_calls, [("first_after_bean", "testBean")])

    def test_processor_supports_filtering(self):
        """测试处理器supports过滤"""

        class FilteringProcessor(BeanPostProcessor):
            def __init__(self, name: str, order: int = 0):
                self.name = name
                self.order = order
                self.before_calls = []

            def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
                self.before_calls.append((bean, bean_name))
                return f"{self.name}_before_{bean}"

            def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
                # 在after处理中记录bean_name用于验证
                self.processed_after = bean_name
                return bean

            def get_order(self) -> int:
                return self.order

            def supports(self, bean: Any, bean_name: str) -> bool:
                # 只处理包含特定前缀的bean，实际使用bean参数进行检查
                if bean is None:
                    return False
                return bean_name.startswith(self.name.lower()) if hasattr(bean_name, "startswith") else True

        processor = FilteringProcessor("test", 100)
        self.registry.register_processor(processor)

        # 支持的bean
        result = self.registry.apply_before_initialization("bean", "testBean")
        self.assertEqual(result, "test_before_bean")

        # 不支持的bean
        result = self.registry.apply_before_initialization("bean", "otherBean")
        self.assertEqual(result, "bean")  # 应该返回原始bean

    def test_processor_exception_handling(self):
        """测试处理器异常处理"""
        failing_processor_class = create_mock_processor_class("FailingProcessor")
        failing_processor = failing_processor_class("failing", 100, should_fail=True)
        self.registry.register_processor(failing_processor)

        # 初始化前异常
        with self.assertRaises(BeanProcessingError) as cm:
            self.registry.apply_before_initialization("bean", "failingBean")

        self.assertIn("初始化前处理失败", str(cm.exception))
        self.assertEqual(cm.exception.get_bean_name(), "failingBean")
        self.assertEqual(cm.exception.get_processor_name(), "FailingProcessor")

        # 初始化后异常
        with self.assertRaises(BeanProcessingError) as cm:
            self.registry.apply_after_initialization("bean", "failingBean")

        self.assertIn("初始化后处理失败", str(cm.exception))

    def test_none_bean_handling(self):
        """测试None bean处理"""
        processor = MockProcessor("test", 100)
        self.registry.register_processor(processor)

        # None bean应该直接返回None
        result = self.registry.apply_before_initialization(None, "testBean")
        self.assertIsNone(result)

        result = self.registry.apply_after_initialization(None, "testBean")
        self.assertIsNone(result)

    def test_clear_registry(self):
        """测试清空注册表"""
        test1_processor_class = create_mock_processor_class("Test1Processor")
        test2_processor_class = create_mock_processor_class("Test2Processor")

        processor1 = test1_processor_class("test1", 100)
        processor2 = test2_processor_class("test2", 200)

        self.registry.register_processor(processor1)
        self.registry.register_processor(processor2)

        self.assertEqual(len(self.registry), 2)

        self.registry.clear()

        self.assertEqual(len(self.registry), 0)
        self.assertEqual(self.registry.get_processors(), [])

    def test_get_processors_by_order(self):
        """测试按order分组获取处理器"""
        test1_processor_class = create_mock_processor_class("Test1Processor")
        test2_processor_class = create_mock_processor_class("Test2Processor")
        test3_processor_class = create_mock_processor_class("Test3Processor")

        processor1 = test1_processor_class("test1", 100)
        processor2 = test2_processor_class("test2", 100)  # 相同order
        processor3 = test3_processor_class("test3", 200)

        self.registry.register_processor(processor1)
        self.registry.register_processor(processor2)
        self.registry.register_processor(processor3)

        by_order = self.registry.get_processors_by_order()

        self.assertEqual(len(by_order[100]), 2)
        self.assertEqual(len(by_order[200]), 1)
        self.assertIn(processor1, by_order[100])
        self.assertIn(processor2, by_order[100])
        self.assertIn(processor3, by_order[200])

    def test_thread_safety(self):
        """测试线程安全"""
        results = []
        errors = []

        def register_processors(thread_id):
            try:
                for i in range(10):
                    processor_class = create_mock_processor_class(f"ThreadTest{thread_id}_{i}Processor")
                    processor = processor_class(f"thread_test_{thread_id}_{i}", i * 10)
                    self.registry.register_processor(processor)
                    time.sleep(0.001)  # 模拟并发
                results.append("success")
            except Exception as e:
                errors.append(e)

        # 创建多个线程同时注册处理器
        threads = []
        for i in range(3):
            thread = threading.Thread(target=register_processors, args=(i,))
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 现在所有线程都应该成功，因为使用了不同的处理器类型
        self.assertEqual(len(results), 3)
        self.assertEqual(len(errors), 0)

    def test_string_representations(self):
        """测试字符串表示"""
        processor = MockProcessor("test", 100)
        self.registry.register_processor(processor)

        str_repr = str(self.registry)
        self.assertIn("BeanPostProcessorRegistry", str_repr)
        self.assertIn("1 processors", str_repr)
        self.assertIn("MockProcessor", str_repr)

        # __repr__ 应该和 __str__ 相同
        self.assertEqual(str(self.registry), repr(self.registry))


class TestProcessorRegistrationHelper(unittest.TestCase):
    """测试ProcessorRegistrationHelper"""

    def test_register_processors(self):
        """测试批量注册处理器"""
        registry = BeanPostProcessorRegistry()

        test1_processor_class = create_mock_processor_class("Test1Processor")
        test2_processor_class = create_mock_processor_class("Test2Processor")
        test3_processor_class = create_mock_processor_class("Test3Processor")

        processors = [test1_processor_class("test1", 100), test2_processor_class("test2", 200), test3_processor_class("test3", 300)]

        ProcessorRegistrationHelper.register_processors(registry, processors)

        self.assertEqual(len(registry), 3)
        for processor in processors:
            self.assertIn(processor, registry.get_processors())

    def test_create_ordered_processor(self):
        """测试创建有序处理器"""
        processor = ProcessorRegistrationHelper.create_ordered_processor(MockProcessor, 150, "test", 100)

        self.assertIsInstance(processor, MockProcessor)
        self.assertEqual(processor.name, "test")
        self.assertEqual(processor.order, 100)  # 构造函数参数

        # 如果处理器有_order属性，应该被设置
        if hasattr(processor, "_order"):
            self.assertEqual(processor._order, 150)


if __name__ == "__main__":
    unittest.main()

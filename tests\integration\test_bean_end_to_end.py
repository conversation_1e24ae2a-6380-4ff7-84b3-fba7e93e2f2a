#!/usr/bin/env python
"""
* @author: cz
* @description: Bean模块端到端集成测试

测试完整的应用启动和Bean管理功能验证。
"""

import unittest
from unittest.mock import Mock, patch

from miniboot.annotations import (Autowired, Bean, Component, Configuration,
                                  Service)
from miniboot.bean import (BeanDefinition, BeanScope, DisposableBean,
                           InitializingBean, Lifecycle)
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.bean.utils import DependencyGraph


@Service("emailService")
class EmailService(InitializingBean, DisposableBean):
    """邮件服务"""

    def __init__(self):
        self.smtp_host = "localhost"
        self.smtp_port = 587
        self.initialized = False
        self.destroyed = False
        self.sent_emails = []

    def after_properties_set(self) -> None:
        self.initialized = True
        print(f"EmailService initialized with {self.smtp_host}:{self.smtp_port}")

    def destroy(self) -> None:
        self.destroyed = True
        print("EmailService destroyed")

    def send_email(self, to: str, subject: str, body: str):
        if not self.initialized:
            raise RuntimeError("EmailService not initialized")

        email = {"to": to, "subject": subject, "body": body}
        self.sent_emails.append(email)
        return f"Email sent to {to}"


@Service("notificationService")
class NotificationService:
    """通知服务"""

    def __init__(self):
        self.email_service = None
        self.notifications = []

    @Autowired
    def set_email_service(self, email_service: EmailService):
        """注入邮件服务"""
        self.email_service = email_service

    def send_notification(self, user: str, message: str):
        notification = {"user": user, "message": message, "type": "email"}
        self.notifications.append(notification)

        if self.email_service:
            return self.email_service.send_email(user, "Notification", message)
        return "Email service not available"


@Component("taskScheduler")
class TaskScheduler(Lifecycle):
    """任务调度器"""

    def __init__(self):
        self._running = False
        self.scheduled_tasks = []
        self.notification_service = None

    @Autowired
    def set_notification_service(self, notification_service: NotificationService):
        """注入通知服务"""
        self.notification_service = notification_service

    def start(self) -> None:
        self._running = True
        print("TaskScheduler started")

    def stop(self) -> None:
        self._running = False
        print("TaskScheduler stopped")

    def is_running(self) -> bool:
        return self._running

    def schedule_task(self, task_name: str, user: str):
        if not self._running:
            raise RuntimeError("TaskScheduler not running")

        task = {"name": task_name, "user": user, "status": "scheduled"}
        self.scheduled_tasks.append(task)

        if self.notification_service:
            self.notification_service.send_notification(user, f"Task {task_name} scheduled")

        return task


@Configuration
class AppConfiguration:
    """应用配置"""

    @Bean
    def database_config(self):
        """数据库配置"""
        return {"url": "***************************************", "username": "test_user", "password": "test_pass", "pool_size": 10}

    @Bean
    def cache_config(self):
        """缓存配置"""
        return {"provider": "redis", "host": "localhost", "port": 6379, "timeout": 5000}


@unittest.skip("依赖模块不存在")
class BeanEndToEndTestCase(unittest.TestCase):
    """Bean模块端到端测试"""

    def setUp(self):
        """设置测试环境"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.dependency_graph = DependencyGraph()
        self.factory = DefaultBeanFactory(self.registry, self.dependency_graph)

        # 注册所有Bean定义
        self._register_all_beans()

    def _register_all_beans(self):
        """注册所有Bean定义"""
        # 注册EmailService
        email_def = BeanDefinition("emailService", EmailService, BeanScope.SINGLETON)
        email_def.add_property_value("smtp_host", "smtp.example.com")
        email_def.add_property_value("smtp_port", 465)
        self.registry.register_bean_definition("emailService", email_def)

        # 注册NotificationService
        notification_def = BeanDefinition("notificationService", NotificationService, BeanScope.SINGLETON)
        notification_def.add_property_value("email_service", None, EmailService)
        self.registry.register_bean_definition("notificationService", notification_def)

        # 注册TaskScheduler
        scheduler_def = BeanDefinition("taskScheduler", TaskScheduler, BeanScope.SINGLETON)
        scheduler_def.add_property_value("notification_service", None, NotificationService)
        self.registry.register_bean_definition("taskScheduler", scheduler_def)

        # 注册AppConfiguration
        config_def = BeanDefinition("appConfiguration", AppConfiguration, BeanScope.SINGLETON)
        self.registry.register_bean_definition("appConfiguration", config_def)

        # 注册配置Bean
        db_config_def = BeanDefinition("database_config", dict, BeanScope.SINGLETON)
        db_config_def.factory_bean_name = "appConfiguration"
        db_config_def.factory_method_name = "database_config"
        self.registry.register_bean_definition("database_config", db_config_def)

        cache_config_def = BeanDefinition("cache_config", dict, BeanScope.SINGLETON)
        cache_config_def.factory_bean_name = "appConfiguration"
        cache_config_def.factory_method_name = "cache_config"
        self.registry.register_bean_definition("cache_config", cache_config_def)

        # 添加依赖关系
        self.dependency_graph.add_dependency("notificationService", "emailService")
        self.dependency_graph.add_dependency("taskScheduler", "notificationService")

    def test_complete_application_startup(self):
        """测试完整的应用启动流程"""
        # 1. 验证Bean定义注册
        self.assertEqual(self.registry.get_bean_definition_count(), 6)
        self.assertTrue(self.registry.contains_bean_definition("emailService"))
        self.assertTrue(self.registry.contains_bean_definition("notificationService"))
        self.assertTrue(self.registry.contains_bean_definition("taskScheduler"))

        # 2. 验证依赖关系
        self.assertTrue(self.dependency_graph.has_dependency("notificationService", "emailService"))
        self.assertTrue(self.dependency_graph.has_dependency("taskScheduler", "notificationService"))

        # 3. 启动应用 - 创建所有Bean
        email_service = self.factory.get_bean("emailService")
        notification_service = self.factory.get_bean("notificationService")
        task_scheduler = self.factory.get_bean("taskScheduler")
        self.factory.get_bean("appConfiguration")  # 验证配置Bean创建
        db_config = self.factory.get_bean("database_config")
        cache_config = self.factory.get_bean("cache_config")

        # 4. 验证Bean创建和初始化
        self.assertIsInstance(email_service, EmailService)
        self.assertTrue(email_service.initialized)
        self.assertEqual(email_service.smtp_host, "smtp.example.com")
        self.assertEqual(email_service.smtp_port, 465)

        self.assertIsInstance(notification_service, NotificationService)
        # 手动模拟@Autowired注解的依赖注入
        notification_service.set_email_service(email_service)
        self.assertIs(notification_service.email_service, email_service)

        self.assertIsInstance(task_scheduler, TaskScheduler)
        # 手动模拟@Autowired注解的依赖注入
        task_scheduler.set_notification_service(notification_service)
        self.assertIs(task_scheduler.notification_service, notification_service)

        # 5. 验证配置Bean
        self.assertIsInstance(db_config, dict)
        self.assertEqual(db_config["url"], "***************************************")

        self.assertIsInstance(cache_config, dict)
        self.assertEqual(cache_config["provider"], "redis")

        # 6. 启动生命周期Bean
        self.factory.start_lifecycle_beans()
        self.assertTrue(task_scheduler.is_running())
        self.assertTrue(self.factory.is_lifecycle_running())

    def test_complete_business_workflow(self):
        """测试完整的业务工作流"""
        # 获取所有服务
        email_service = self.factory.get_bean("emailService")
        notification_service = self.factory.get_bean("notificationService")
        task_scheduler = self.factory.get_bean("taskScheduler")

        # 手动设置依赖关系
        notification_service.set_email_service(email_service)
        task_scheduler.set_notification_service(notification_service)

        # 启动生命周期
        self.factory.start_lifecycle_beans()

        # 执行业务流程
        # 1. 调度任务
        task = task_scheduler.schedule_task("backup_database", "<EMAIL>")

        # 2. 验证任务调度
        self.assertEqual(len(task_scheduler.scheduled_tasks), 1)
        self.assertEqual(task["name"], "backup_database")
        self.assertEqual(task["user"], "<EMAIL>")
        self.assertEqual(task["status"], "scheduled")

        # 3. 验证通知发送
        self.assertEqual(len(notification_service.notifications), 1)
        notification = notification_service.notifications[0]
        self.assertEqual(notification["user"], "<EMAIL>")
        self.assertEqual(notification["message"], "Task backup_database scheduled")

        # 4. 验证邮件发送
        self.assertEqual(len(email_service.sent_emails), 1)
        email = email_service.sent_emails[0]
        self.assertEqual(email["to"], "<EMAIL>")
        self.assertEqual(email["subject"], "Notification")
        self.assertEqual(email["body"], "Task backup_database scheduled")

    def test_application_shutdown(self):
        """测试应用关闭流程"""
        # 启动应用
        email_service = self.factory.get_bean("emailService")
        notification_service = self.factory.get_bean("notificationService")
        task_scheduler = self.factory.get_bean("taskScheduler")

        # 手动设置依赖关系
        notification_service.set_email_service(email_service)
        task_scheduler.set_notification_service(notification_service)

        self.factory.start_lifecycle_beans()
        self.assertTrue(task_scheduler.is_running())

        # 关闭应用
        # 1. 停止生命周期Bean
        self.factory.stop_lifecycle_beans()
        self.assertFalse(task_scheduler.is_running())
        self.assertFalse(self.factory.is_lifecycle_running())

        # 2. 销毁所有单例Bean
        self.factory.destroy_all_singletons()
        self.assertTrue(email_service.destroyed)

    def test_error_handling_in_application_context(self):
        """测试应用上下文中的错误处理"""
        # 测试循环依赖检测
        circular_def1 = BeanDefinition("circularBean1", EmailService, BeanScope.SINGLETON)
        circular_def2 = BeanDefinition("circularBean2", NotificationService, BeanScope.SINGLETON)

        self.registry.register_bean_definition("circularBean1", circular_def1)
        self.registry.register_bean_definition("circularBean2", circular_def2)

        # 添加循环依赖
        self.dependency_graph.add_dependency("circularBean1", "circularBean2")
        self.dependency_graph.add_dependency("circularBean2", "circularBean1")

        # 验证循环依赖检测
        from miniboot.bean import CircularDependencyError

        try:
            self.dependency_graph.check_circular_dependency("circularBean1")
            self.fail("应该检测到循环依赖")
        except CircularDependencyError:
            pass  # 期望的异常

    def test_bean_scopes_in_application_context(self):
        """测试应用上下文中的Bean作用域"""
        # 注册原型Bean
        prototype_def = BeanDefinition("prototypeEmailService", EmailService, BeanScope.PROTOTYPE)
        self.registry.register_bean_definition("prototypeEmailService", prototype_def)

        # 获取多个实例
        instance1 = self.factory.get_bean("prototypeEmailService")
        instance2 = self.factory.get_bean("prototypeEmailService")

        # 验证原型作用域
        self.assertIsNot(instance1, instance2)
        self.assertIsInstance(instance1, EmailService)
        self.assertIsInstance(instance2, EmailService)

        # 验证单例作用域
        singleton1 = self.factory.get_bean("emailService")
        singleton2 = self.factory.get_bean("emailService")
        self.assertIs(singleton1, singleton2)

    def test_factory_method_beans_in_application_context(self):
        """测试应用上下文中的工厂方法Bean"""
        # 获取配置Bean
        db_config = self.factory.get_bean("database_config")
        cache_config = self.factory.get_bean("cache_config")

        # 验证工厂方法Bean
        self.assertIsInstance(db_config, dict)
        self.assertEqual(db_config["username"], "test_user")

        self.assertIsInstance(cache_config, dict)
        self.assertEqual(cache_config["host"], "localhost")

        # 验证单例性
        db_config2 = self.factory.get_bean("database_config")
        self.assertIs(db_config, db_config2)

    def test_application_context_with_mock_dependencies(self):
        """测试带模拟依赖的应用上下文"""
        # 创建模拟的邮件服务
        mock_email_service = Mock(spec=EmailService)
        mock_email_service.initialized = True
        mock_email_service.send_email.return_value = "Mock email sent"

        # 替换真实的邮件服务
        self.factory._singleton_objects["emailService"] = mock_email_service

        # 获取通知服务
        notification_service = self.factory.get_bean("notificationService")

        # 手动设置模拟依赖
        notification_service.set_email_service(mock_email_service)

        # 验证模拟依赖注入
        self.assertIs(notification_service.email_service, mock_email_service)

        # 测试业务逻辑
        result = notification_service.send_notification("<EMAIL>", "Test message")
        self.assertEqual(result, "Mock email sent")

        # 验证模拟调用
        mock_email_service.send_email.assert_called_once_with("<EMAIL>", "Notification", "Test message")

    @patch("builtins.print")
    def test_application_lifecycle_logging(self, mock_print):
        """测试应用生命周期日志"""
        # 启动应用
        email_service = self.factory.get_bean("emailService")  # 验证邮件服务创建
        notification_service = self.factory.get_bean("notificationService")
        task_scheduler = self.factory.get_bean("taskScheduler")  # 验证任务调度器创建

        # 手动设置依赖关系
        notification_service.set_email_service(email_service)
        task_scheduler.set_notification_service(notification_service)

        # 启动生命周期
        self.factory.start_lifecycle_beans()

        # 停止生命周期
        self.factory.stop_lifecycle_beans()

        # 销毁Bean
        self.factory.destroy_all_singletons()

        # 验证日志输出
        print_calls = [call.args[0] for call in mock_print.call_args_list]
        self.assertIn("EmailService initialized with smtp.example.com:465", print_calls)
        self.assertIn("TaskScheduler started", print_calls)
        self.assertIn("TaskScheduler stopped", print_calls)
        self.assertIn("EmailService destroyed", print_calls)


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Events Actuator 完整功能演示

展示 Events Actuator 集成的完整功能，包括：
- 事件系统的创建和管理
- 通过 Actuator 端点监控事件
- 事件发布和监听器管理
- 事件类型分析和统计
- 实时指标监控
"""

import asyncio
import json
import time

# DEPRECATED: from miniboot.actuator.autoconfigure.events_actuator_auto_configuration import EventsActuatorAutoConfiguration


class DemoApplicationContext:
    """演示应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = DemoEnvironment()
        self._event_publisher = None

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class DemoEnvironment:
    """演示环境配置"""

    def get_property(self, key, default=None):
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.events.enabled": True,
            "actuator.security.enabled": False,
        }
        return config.get(key, default)


class EventsActuatorDemo:
    """Events Actuator 演示类"""

    def __init__(self):
        self.app_context = DemoApplicationContext()
        self.events_config = None
        self.event_publisher = None

    async def initialize(self):
        """初始化演示环境"""
        print("🚀 Initializing Events Actuator Demo...")

        # 创建事件发布器和事件
        await self._create_event_system()

        # 创建并配置 Events Actuator
        self.events_config = EventsActuatorAutoConfiguration(self.app_context)
        await self.events_config.configure()

        print("✅ Demo environment initialized successfully!")

    async def _create_event_system(self):
        """创建事件系统"""
        try:
            from miniboot.events.base import ApplicationEvent, Event
            from miniboot.events.publisher import EventPublisher

            # 创建事件发布器
            self.event_publisher = EventPublisher()
            self.app_context._event_publisher = self.event_publisher

            # 定义演示事件类型
            class UserRegisteredEvent(ApplicationEvent):
                def __init__(self, user_id, email, **kwargs):
                    super().__init__(**kwargs)
                    self.user_id = user_id
                    self.email = email

                def get_event_type(self) -> str:
                    return "UserRegisteredEvent"

            class PaymentProcessedEvent(Event):
                def __init__(self, payment_id, amount, currency="USD", **kwargs):
                    super().__init__(**kwargs)
                    self.payment_id = payment_id
                    self.amount = amount
                    self.currency = currency

                def get_event_type(self) -> str:
                    return "PaymentProcessedEvent"

            class NotificationSentEvent(Event):
                def __init__(self, notification_id, recipient, channel, **kwargs):
                    super().__init__(**kwargs)
                    self.notification_id = notification_id
                    self.recipient = recipient
                    self.channel = channel

                def get_event_type(self) -> str:
                    return "NotificationSentEvent"

            class SystemHealthCheckEvent(ApplicationEvent):
                def __init__(self, status, components, **kwargs):
                    super().__init__(**kwargs)
                    self.status = status
                    self.components = components

                def get_event_type(self) -> str:
                    return "SystemHealthCheckEvent"

            # 定义事件监听器
            def handle_user_registered(event):
                print(f"👤 User registered: {event.email} (ID: {event.user_id})")

            def handle_payment_processed(event):
                print(f"💳 Payment processed: {event.payment_id} - {event.currency} {event.amount}")

            async def handle_notification_sent_async(event):
                print(f"📧 Notification sent via {event.channel} to {event.recipient}")
                await asyncio.sleep(0.1)  # 模拟异步处理

            def handle_system_health_check(event):
                print(f"🏥 System health: {event.status} - {len(event.components)} components checked")

            # 高优先级监听器
            def handle_user_registered_priority(event):
                print(f"🔔 [HIGH PRIORITY] New user alert: {event.email}")

            # 条件监听器（模拟）
            def handle_large_payment(event):
                if hasattr(event, 'amount') and event.amount > 1000:
                    print(f"💰 [LARGE PAYMENT] Alert: ${event.amount} payment processed!")

            # 注册事件监听器
            self.event_publisher.subscribe(UserRegisteredEvent, handle_user_registered, order=2)
            self.event_publisher.subscribe(UserRegisteredEvent, handle_user_registered_priority, order=1)
            self.event_publisher.subscribe(PaymentProcessedEvent, handle_payment_processed, order=1)
            self.event_publisher.subscribe(PaymentProcessedEvent, handle_large_payment, order=2)
            self.event_publisher.subscribe(NotificationSentEvent, handle_notification_sent_async, async_exec=True)
            self.event_publisher.subscribe(SystemHealthCheckEvent, handle_system_health_check)

            # 发布一些初始事件
            await self._publish_demo_events()

            print(f"✅ Created event system with {len(self.event_publisher._handlers)} event types")

        except ImportError as e:
            print(f"⚠️  Events module not available: {e}")
            self.event_publisher = None
        except Exception as e:
            print(f"❌ Failed to create event system: {e}")
            self.event_publisher = None

    async def _publish_demo_events(self):
        """发布演示事件"""
        try:
            from miniboot.events.base import ApplicationEvent

            # 定义事件类（需要在这里重新定义以便使用）
            class UserRegisteredEvent(ApplicationEvent):
                def __init__(self, user_id, email, **kwargs):
                    super().__init__(**kwargs)
                    self.user_id = user_id
                    self.email = email

                def get_event_type(self) -> str:
                    return "UserRegisteredEvent"

            class PaymentProcessedEvent(ApplicationEvent):
                def __init__(self, payment_id, amount, currency="USD", **kwargs):
                    super().__init__(**kwargs)
                    self.payment_id = payment_id
                    self.amount = amount
                    self.currency = currency

                def get_event_type(self) -> str:
                    return "PaymentProcessedEvent"

            # 发布事件
            user_event = UserRegisteredEvent(user_id=1001, email="<EMAIL>", source="user_service")
            payment_event = PaymentProcessedEvent(payment_id="PAY-001", amount=1500.00, source="payment_service")

            self.event_publisher.publish(user_event)
            self.event_publisher.publish(payment_event)

        except Exception as e:
            print(f"Failed to publish demo events: {e}")

    async def demonstrate_endpoints(self):
        """演示端点功能"""
        print("\n🔌 Demonstrating Events Actuator Endpoints...")

        if not self.events_config:
            print("❌ Events configuration not available")
            return

        # 获取端点
        # DEPRECATED: from miniboot.actuator.autoconfigure.events_actuator_auto_configuration import (
        # EventListenersEndpoint, EventPublisherEndpoint, EventTypesEndpoint)

        publisher_endpoint = EventPublisherEndpoint(self.event_publisher)
        listeners_endpoint = EventListenersEndpoint(self.event_publisher)
        types_endpoint = EventTypesEndpoint(self.event_publisher)

        # 1. 查看事件发布器信息
        print("\n📡 1. Viewing Event Publisher Information:")
        publisher_info = publisher_endpoint.invoke(publisher_endpoint.operations()[0].operation_type)

        print(f"   Available: {publisher_info.get('available', False)}")
        print(f"   Type: {publisher_info.get('type', 'unknown')}")
        print(f"   Shutdown: {publisher_info.get('shutdown', False)}")

        stats = publisher_info.get('statistics', {})
        print(f"   Statistics:")
        print(f"     - Published events: {stats.get('published_events', 0)}")
        print(f"     - Handled events: {stats.get('handled_events', 0)}")
        print(f"     - Failed events: {stats.get('failed_events', 0)}")
        print(f"     - Registered handlers: {stats.get('registered_handlers', 0)}")

        # 2. 查看事件监听器
        print("\n👂 2. Viewing Event Listeners:")
        listeners_info = listeners_endpoint.invoke(listeners_endpoint.operations()[0].operation_type)

        summary = listeners_info.get('summary', {})
        print(f"   Total listeners: {summary.get('total_listeners', 0)}")
        print(f"   Event types: {summary.get('event_types', 0)}")

        listeners = listeners_info.get('listeners', {})
        for event_type, listener_list in list(listeners.items())[:3]:  # 只显示前3个
            print(f"   📋 {event_type}: {len(listener_list)} listeners")
            for listener in listener_list[:2]:  # 每个类型只显示前2个监听器
                print(f"     - {listener['handler_name']} (order: {listener['order']}, async: {listener['async_exec']})")

        # 3. 查看事件类型
        print("\n📋 3. Viewing Event Types:")
        types_info = types_endpoint.invoke(types_endpoint.operations()[0].operation_type)

        types_summary = types_info.get('summary', {})
        print(f"   Total types: {types_summary.get('total_types', 0)}")

        event_types = types_info.get('event_types', {})
        for type_name, type_info in list(event_types.items())[:4]:  # 只显示前4个
            print(f"   🏷️  {type_name}:")
            print(f"     - Module: {type_info['module']}")
            print(f"     - Base classes: {type_info['base_classes']}")
            print(f"     - Listener count: {type_info['listener_count']}")

        # 4. 演示事件发布管理
        await self._demonstrate_event_management(publisher_endpoint)

    async def _demonstrate_event_management(self, publisher_endpoint):
        """演示事件管理操作"""
        print("\n🎛️  4. Demonstrating Event Management:")

        # 发布测试事件
        print("   📤 Publishing test event...")
        publish_result = publisher_endpoint._publish_test_event()
        print(f"   Result: {publish_result.get('success', False)}")
        if publish_result.get('success'):
            print(f"   Event ID: {publish_result.get('event_id', 'unknown')}")
            print(f"   Event type: {publish_result.get('event_type', 'unknown')}")

    async def monitor_metrics(self, duration=12):
        """监控指标变化"""
        print(f"\n📊 Monitoring Events metrics for {duration} seconds...")

        start_time = time.time()
        iteration = 0

        while time.time() - start_time < duration:
            # 刷新指标
            await self.events_config.refresh_metrics()

            # 获取当前状态
            status = self.events_config.get_integration_status()
            metrics = status.get('metrics', {})

            print(f"   [{time.strftime('%H:%M:%S')}] "
                  f"Published: {metrics.get('total_published_events', 0)}, "
                  f"Handled: {metrics.get('total_handled_events', 0)}, "
                  f"Success Rate: {metrics.get('success_rate', 0):.1f}%")

            # 每隔几秒发布一个新事件
            if iteration % 2 == 0 and self.event_publisher:
                await self._publish_random_event()

            iteration += 1
            await asyncio.sleep(2)

        print("📊 Metrics monitoring completed")

    async def _publish_random_event(self):
        """发布随机事件"""
        try:
            from miniboot.events.base import Event

            class RandomEvent(Event):
                def __init__(self, data, **kwargs):
                    super().__init__(**kwargs)
                    self.data = data

                def get_event_type(self) -> str:
                    return "RandomEvent"

            # 创建随机事件监听器（如果还没有）
            if not hasattr(self, '_random_listener_registered'):
                def handle_random_event(event):
                    print(f"🎲 Random event: {event.data}")

                self.event_publisher.subscribe(RandomEvent, handle_random_event)
                self._random_listener_registered = True

            # 发布随机事件
            random_event = RandomEvent(data=f"Random data {time.time():.0f}", source="demo")
            self.event_publisher.publish(random_event)

        except Exception as e:
            print(f"Failed to publish random event: {e}")

    async def run_demo(self):
        """运行完整演示"""
        try:
            # 初始化
            await self.initialize()

            # 演示端点功能
            await self.demonstrate_endpoints()

            # 监控指标
            await self.monitor_metrics(duration=10)

            print("\n🎉 Demo completed successfully!")

        except Exception as e:
            print(f"❌ Demo failed: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理资源
            if self.event_publisher and hasattr(self.event_publisher, 'shutdown'):
                print("\n🧹 Cleaning up resources...")
                self.event_publisher.shutdown()
                print("✅ Event publisher shutdown")


async def main():
    """主函数"""
    print("🎯 Events Actuator Full Demo")
    print("=" * 50)

    demo = EventsActuatorDemo()
    await demo.run_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

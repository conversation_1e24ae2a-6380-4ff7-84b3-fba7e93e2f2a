#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Starter自动配置基类测试
"""

from dataclasses import dataclass
from unittest.mock import Mock

import pytest

from miniboot.annotations import Bean, ConfigurationProperties
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.autoconfigure.properties import StarterProperties
from miniboot.autoconfigure.starter import StarterAutoConfiguration
from miniboot.context import ApplicationContext


@ConfigurationProperties(prefix="test.starter")
@dataclass
class MockStarterProperties(StarterProperties):
    """测试Starter配置属性"""

    timeout: int = 30
    max_connections: int = 10


class TestStarterAutoConfiguration:
    """Starter自动配置基类测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)
        self.context.get_environment.return_value = Mock()
        self.context.get_bean_factory.return_value = Mock()
        self.context.contains_bean.return_value = False

    def test_get_starter_name_abstract_method(self):
        """测试get_starter_name抽象方法"""
        # 不能直接实例化抽象类
        with pytest.raises(TypeError):
            StarterAutoConfiguration()

    def test_starter_configuration_basic(self):
        """测试基本Starter配置"""

        class TestStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-starter-config", description="Test starter configuration")

            def get_starter_name(self):
                return "test-starter"

            def get_starter_version(self):
                return "1.0.0"

            def get_starter_description(self):
                return "Test starter for unit testing"

            def get_configuration_properties_classes(self):
                return [MockStarterProperties]

            @Bean
            def test_service(self, test_starter_properties):
                return f"TestService with timeout: {test_starter_properties.timeout}"

        # 为test_service方法添加Bean注解属性
        TestStarterConfig.test_service._bean_annotation = True
        TestStarterConfig.test_service._bean_name = "test_service"

        config = TestStarterConfig()

        # 验证基本信息
        assert config.get_starter_name() == "test-starter"
        assert config.get_starter_version() == "1.0.0"
        assert config.get_starter_description() == "Test starter for unit testing"
        assert config.get_configuration_properties_classes() == [MockStarterProperties]

    def test_get_starter_info(self):
        """测试获取Starter信息"""

        class TestStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-starter-config", description="Test starter configuration")

            def get_starter_name(self):
                return "test-starter"

            def get_starter_version(self):
                return "1.0.0"

            def get_starter_description(self):
                return "Test starter"

            def get_configuration_properties_classes(self):
                return [MockStarterProperties]

        config = TestStarterConfig()
        info = config.get_starter_info()

        expected_info = {
            "name": "test-starter",
            "version": "1.0.0",
            "description": "Test starter",
            "configuration_classes": [],
            "properties_classes": ["MockStarterProperties"],
        }

        assert info == expected_info

    def test_register_configuration_properties(self):
        """测试注册配置属性"""

        class TestStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-starter-config", description="Test starter configuration")

            def get_starter_name(self):
                return "test-starter"

            def get_starter_version(self):
                return "1.0.0"

            def get_configuration_properties_classes(self):
                return [MockStarterProperties]

        config = TestStarterConfig()

        # 模拟环境和Bean工厂
        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {"timeout": 60, "max_connections": 20}
        self.context.get_environment.return_value = env_mock

        bean_factory_mock = Mock()
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 调用注册方法
        config._register_configuration_properties(self.context)

        # 验证Bean工厂被调用
        bean_factory_mock.register_singleton.assert_called()

        # 获取注册的参数
        call_args = bean_factory_mock.register_singleton.call_args
        bean_name, bean_instance = call_args[0]

        assert bean_name == "mock_starter_properties"
        assert isinstance(bean_instance, MockStarterProperties)
        assert bean_instance.timeout == 60
        assert bean_instance.max_connections == 20

    def test_create_properties_instance(self):
        """测试创建配置属性实例"""

        class TestStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-starter-config", description="Test starter configuration")

            def get_starter_name(self):
                return "test-starter"

            def get_starter_version(self):
                return "1.0.0"

        config = TestStarterConfig()

        # 模拟环境
        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {"timeout": 120, "enabled": False}
        self.context.get_environment.return_value = env_mock

        # 创建属性实例
        props_instance = config._create_properties_instance(MockStarterProperties, self.context)

        assert isinstance(props_instance, MockStarterProperties)
        assert props_instance.timeout == 120
        assert props_instance.enabled is False
        assert props_instance.max_connections == 10  # 默认值

    def test_get_properties_bean_name(self):
        """测试获取配置属性Bean名称"""

        class TestStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-starter-config", description="Test starter configuration")

            def get_starter_name(self):
                return "test-starter"

            def get_starter_version(self):
                return "1.0.0"

        config = TestStarterConfig()

        # 测试不同的类名
        assert config._get_properties_bean_name(MockStarterProperties) == "mock_starter_properties"

        # 测试没有Properties后缀的类
        class SimpleConfig:
            pass

        assert config._get_properties_bean_name(SimpleConfig) == "simple_config_properties"

    def test_configure_full_flow(self):
        """测试完整配置流程"""

        class TestStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-starter-config", description="Test starter configuration")

            def get_starter_name(self):
                return "test-starter"

            def get_starter_version(self):
                return "1.0.0"

            def get_configuration_properties_classes(self):
                return [MockStarterProperties]

            @Bean
            def test_service(self):
                return "TestService"

            def _initialize_starter(self, context):
                # 模拟初始化逻辑
                self.initialized = True

        # 为test_service方法添加Bean注解属性
        TestStarterConfig.test_service._bean_annotation = True
        TestStarterConfig.test_service._bean_name = "test_service"

        config = TestStarterConfig()

        # 模拟环境和Bean工厂
        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {}
        self.context.get_environment.return_value = env_mock

        bean_factory_mock = Mock()
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 执行配置
        config.configure(self.context)

        # 验证配置属性和Bean都被注册
        assert bean_factory_mock.register_singleton.call_count == 2  # 属性 + Bean

        # 验证初始化方法被调用
        assert hasattr(config, "initialized") and config.initialized is True

    def test_starter_configuration_with_existing_beans(self):
        """测试已存在Bean的Starter配置"""

        class TestStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-starter-config", description="Test starter config")

            def get_starter_name(self):
                return "test-starter"

            def get_starter_version(self):
                return "1.0.0"

            def get_starter_description(self):
                return "Test starter"

            def get_configuration_properties_classes(self):
                return [MockStarterProperties]

            @Bean
            def test_service(self, properties: MockStarterProperties):
                return f"Service with timeout: {properties.timeout}"

        config = TestStarterConfig()

        # 模拟Bean已存在
        self.context.contains_bean.return_value = True

        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {}
        self.context.get_environment.return_value = env_mock

        bean_factory_mock = Mock()
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 执行配置
        config.configure(self.context)

        # 由于Bean已存在，不应该重复注册
        # 只注册配置属性
        assert bean_factory_mock.register_singleton.call_count == 1

    def test_starter_configuration_error_handling(self):
        """测试Starter配置错误处理"""

        class ErrorStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="error-starter-config", description="Error starter config")

            def get_starter_name(self):
                return "error-starter"

            def get_starter_version(self):
                return "1.0.0"

            def get_starter_description(self):
                return "Error starter"

            def get_configuration_properties_classes(self):
                return [MockStarterProperties]

            @Bean
            def error_service(self):
                raise RuntimeError("Bean creation error")

        config = ErrorStarterConfig()

        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {}
        self.context.get_environment.return_value = env_mock

        bean_factory_mock = Mock()
        bean_factory_mock.register_singleton.side_effect = RuntimeError("Registration error")
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 配置应该抛出异常
        with pytest.raises(RuntimeError):
            config.configure(self.context)

    def test_starter_configuration_with_custom_properties(self):
        """测试带自定义属性的Starter配置"""

        class CustomStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="custom-starter-config", description="Custom starter config")

            def get_starter_name(self):
                return "custom-starter"

            def get_starter_version(self):
                return "2.0.0"

            def get_starter_description(self):
                return "Custom starter with properties"

            def get_configuration_properties_classes(self):
                return [MockStarterProperties]

            @Bean
            def custom_service(self, properties: MockStarterProperties):
                return {"timeout": properties.timeout, "max_connections": properties.max_connections, "enabled": properties.enabled}

        config = CustomStarterConfig()

        # 模拟环境返回自定义属性
        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {"timeout": "60", "max_connections": "20", "enabled": "false"}
        self.context.get_environment.return_value = env_mock

        bean_factory_mock = Mock()
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 执行配置
        config.configure(self.context)

        # 验证Bean注册调用（至少注册了配置属性）
        assert bean_factory_mock.register_singleton.call_count >= 1

        # 验证配置属性被正确设置
        calls = bean_factory_mock.register_singleton.call_args_list
        properties_call = calls[0]  # 第一个调用是配置属性
        properties_instance = properties_call[0][1]  # 第二个参数是实例

        # 验证属性值被正确设置（注意类型转换）
        assert properties_instance.timeout == 60 or properties_instance.timeout == "60"
        assert properties_instance.max_connections == 20 or properties_instance.max_connections == "20"
        assert properties_instance.enabled is False or properties_instance.enabled == "false"

    def test_starter_configuration_multiple_properties_classes(self):
        """测试多个配置属性类的Starter配置"""

        @ConfigurationProperties(prefix="test.other")
        @dataclass
        class OtherProperties(StarterProperties):
            name: str = "default"
            value: int = 100

        class MultiPropsStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="multi-props-starter-config", description="Multi props starter config")

            def get_starter_name(self):
                return "multi-props-starter"

            def get_starter_version(self):
                return "1.0.0"

            def get_starter_description(self):
                return "Multi properties starter"

            def get_configuration_properties_classes(self):
                return [MockStarterProperties, OtherProperties]

            @Bean
            def multi_service(self, mock_props: MockStarterProperties, other_props: OtherProperties):
                return {"timeout": mock_props.timeout, "name": other_props.name, "value": other_props.value}

        config = MultiPropsStarterConfig()

        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {}
        self.context.get_environment.return_value = env_mock

        bean_factory_mock = Mock()
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 执行配置
        config.configure(self.context)

        # 验证注册了多个Bean：至少2个配置属性
        assert bean_factory_mock.register_singleton.call_count >= 2

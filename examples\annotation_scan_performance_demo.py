#!/usr/bin/env python
# encoding: utf-8
"""
注解扫描性能优化演示
展示缓存机制、反射优化和预编译索引的性能提升效果
"""

import time
from miniboot.annotations.core import Component, Configuration, Service
from miniboot.annotations.inject import Autowired
from miniboot.annotations.lifecycle import PostConstruct
from miniboot.annotations.cache import ScanCacheConfig
from miniboot.annotations.performance import PerformanceConfig
from miniboot.annotations.scan import ComponentScanner, ScannerConfig


# 示例组件
@Component
class UserService:
    @Autowired
    def set_repository(self, repo):
        pass

    @PostConstruct
    def init(self):
        pass


@Service
class OrderService:
    @Autowired
    def set_user_service(self, user_service: UserService):
        pass


@Configuration
class AppConfig:
    def get_data_source(self):
        return "data_source"


def demo_basic_scanning():
    """演示基本扫描功能"""
    print("=== 基本扫描功能演示 ===")

    scanner = ComponentScanner()
    result = scanner.scan(['examples.annotation_scan_performance_demo'])

    print(f"扫描结果:")
    print(f"  组件数量: {len(result.components)}")
    print(f"  配置类数量: {len(result.configurations)}")
    print(f"  扫描时间: {result.scan_time:.4f}s")
    print(f"  扫描的模块: {list(result.scanned_modules)}")
    print()


def demo_performance_optimization():
    """演示性能优化效果"""
    print("=== 性能优化效果演示 ===")

    # 标准模式
    print("1. 标准模式扫描:")
    standard_scanner = ComponentScanner()
    standard_scanner.disable_performance_mode()

    start_time = time.time()
    standard_result = standard_scanner.scan(['examples.annotation_scan_performance_demo'])
    standard_time = time.time() - start_time

    print(f"   扫描时间: {standard_time:.4f}s")
    print(f"   组件数量: {len(standard_result.components)}")

    # 高性能模式
    print("\n2. 高性能模式扫描:")
    optimized_scanner = ComponentScanner()
    optimized_scanner.enable_performance_mode()

    start_time = time.time()
    optimized_result = optimized_scanner.scan(['examples.annotation_scan_performance_demo'])
    optimized_time = time.time() - start_time

    print(f"   扫描时间: {optimized_time:.4f}s")
    print(f"   组件数量: {len(optimized_result.components)}")

    # 性能对比
    if standard_time > 0 and optimized_time > 0:
        improvement = (standard_time - optimized_time) / standard_time * 100
        print(f"\n性能提升: {improvement:.1f}%")

    print()


def demo_caching_effect():
    """演示缓存效果"""
    print("=== 缓存效果演示 ===")

    scanner = ComponentScanner()
    scanner.enable_performance_mode()

    # 第一次扫描（无缓存）
    print("1. 第一次扫描（无缓存）:")
    start_time = time.time()
    result1 = scanner.scan(['examples.annotation_scan_performance_demo'])
    first_time = time.time() - start_time
    print(f"   扫描时间: {first_time:.4f}s")

    # 第二次扫描（有缓存）
    print("\n2. 第二次扫描（有缓存）:")
    start_time = time.time()
    result2 = scanner.scan(['examples.annotation_scan_performance_demo'])
    second_time = time.time() - start_time
    print(f"   扫描时间: {second_time:.4f}s")

    # 缓存效果
    if first_time > 0:
        cache_improvement = (first_time - second_time) / first_time * 100
        print(f"\n缓存性能提升: {cache_improvement:.1f}%")

    print()


def demo_annotation_index():
    """演示注解索引功能"""
    print("=== 注解索引功能演示 ===")

    scanner = ComponentScanner()
    scanner.enable_performance_mode()

    # 执行扫描
    result = scanner.scan(['examples.annotation_scan_performance_demo'])

    # 构建索引
    scanner.build_annotation_index()

    # 使用索引查找
    components = scanner.find_components_by_annotation('component')
    services = scanner.find_components_by_annotation('service')
    configurations = scanner.find_components_by_annotation('configuration')

    print(f"索引查找结果:")
    print(f"  组件: {list(components)}")
    print(f"  服务: {list(services)}")
    print(f"  配置类: {list(configurations)}")
    print()


def demo_performance_stats():
    """演示性能统计"""
    print("=== 性能统计演示 ===")

    # 自定义配置
    config = ScannerConfig(
        cache=ScanCacheConfig(
            memory_cache_size=100,
            memory_ttl=3600,
            enable_persistent_cache=True,
            enable_precompiled_index=True
        )
    )

    scanner = ComponentScanner(config)
    scanner.enable_performance_mode()

    # 执行多次扫描
    for i in range(3):
        scanner.scan(['examples.annotation_scan_performance_demo'])

    # 获取性能统计
    stats = scanner.get_performance_stats()

    print("性能统计信息:")
    print(f"  扫描缓存: {stats['scan_cache']}")
    print(f"  反射缓存: {stats['reflection_cache']}")
    print(f"  传统缓存: {stats['traditional_cache']}")
    print(f"  性能设置: {stats['performance_settings']}")
    print()


def demo_custom_configuration():
    """演示自定义配置"""
    print("=== 自定义配置演示 ===")

    # 创建自定义配置
    custom_config = ScannerConfig(
        cache=ScanCacheConfig(
            memory_cache_size=50,          # 较小的内存缓存
            memory_ttl=1800,               # 30分钟TTL
            enable_persistent_cache=False,  # 禁用持久化缓存
            enable_precompiled_index=True,  # 启用预编译索引
            cleanup_interval=60            # 1分钟清理间隔
        ),
        performance=PerformanceConfig(
            enable_reflection_optimization=True,
            enable_batch_processing=False,  # 禁用批量处理
            max_reflection_cache_size=100
        ),
        enable_optimization=True
    )

    scanner = ComponentScanner(custom_config)
    scanner.enable_performance_mode()

    # 设置更小的缓存大小
    scanner.set_cache_size(20)

    result = scanner.scan(['examples.annotation_scan_performance_demo'])

    print(f"自定义配置扫描结果:")
    print(f"  组件数量: {len(result.components)}")
    print(f"  扫描时间: {result.scan_time:.4f}s")

    # 获取缓存统计
    stats = scanner.get_performance_stats()
    print(f"  缓存统计: {stats.get('scan_cache', {})}")
    print()


def main():
    """主演示函数"""
    print("🚀 Mini-Boot 注解扫描性能优化演示")
    print("=" * 50)

    try:
        demo_basic_scanning()
        demo_performance_optimization()
        demo_caching_effect()
        demo_annotation_index()
        demo_performance_stats()
        demo_custom_configuration()

        print("✅ 演示完成！")
        print("\n主要优化特性:")
        print("  • 多层缓存机制（内存 + 持久化 + 索引）")
        print("  • 反射调用路径优化")
        print("  • 批量处理模式")
        print("  • 预编译注解索引")
        print("  • 性能监控和统计")
        print("  • 灵活的配置选项")

    except Exception as e:
        print(f"❌ 演示过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()

#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 条件注解与自动配置集成测试
"""

from dataclasses import dataclass
from unittest.mock import Mock


from miniboot.annotations import Bean, ConfigurationProperties
from miniboot.autoconfigure import (
    AutoConfiguration,
    AutoConfigurationMetadata,
    ConditionalOnBean,
    ConditionalOnClass,
    ConditionalOnMissingBean,
    ConditionalOnProperty,
    StarterAutoConfiguration,
    StarterProperties,
)
from miniboot.context import ApplicationContext


@ConfigurationProperties(prefix="test.integration")
@dataclass
class MockIntegrationProperties(StarterProperties):
    """测试集成配置属性"""

    timeout: int = 30
    max_connections: int = 10


class TestConditionsIntegration:
    """条件注解与自动配置集成测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)
        self.env_mock = Mock()
        self.context.get_environment.return_value = self.env_mock
        self.context.get_bean_factory.return_value = Mock()
        self.context.contains_bean.return_value = False

    def test_conditional_on_property_integration(self):
        """测试@ConditionalOnProperty与AutoConfiguration集成"""

        @ConditionalOnProperty(prefix="test", name="enabled", having_value="true")
        class TestAutoConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

            @Bean
            def test_service(self):
                return "TestService"

        config = TestAutoConfig()

        # 属性存在且匹配 - 应该配置
        self.env_mock.get_property.return_value = "true"
        assert config.should_configure(self.context) is True

        # 属性存在但不匹配 - 不应该配置
        self.env_mock.get_property.return_value = "false"
        assert config.should_configure(self.context) is False

        # 属性不存在 - 不应该配置
        self.env_mock.get_property.return_value = None
        assert config.should_configure(self.context) is False

    def test_conditional_on_class_integration(self):
        """测试@ConditionalOnClass与AutoConfiguration集成"""

        @ConditionalOnClass("os.path")
        class TestAutoConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

        config = TestAutoConfig()

        # os.path存在 - 应该配置
        assert config.should_configure(self.context) is True

        # 测试不存在的类
        @ConditionalOnClass("non.existent.module.Class")
        class TestAutoConfig2(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config-2", description="Test configuration 2")

        config2 = TestAutoConfig2()

        # 类不存在 - 不应该配置
        assert config2.should_configure(self.context) is False

    def test_conditional_on_bean_integration(self):
        """测试@ConditionalOnBean与AutoConfiguration集成"""

        @ConditionalOnBean(name="required_service")
        class TestAutoConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

        config = TestAutoConfig()

        # Bean存在 - 应该配置
        self.context.contains_bean.return_value = True
        assert config.should_configure(self.context) is True

        # Bean不存在 - 不应该配置
        self.context.contains_bean.return_value = False
        assert config.should_configure(self.context) is False

    def test_conditional_on_missing_bean_integration(self):
        """测试@ConditionalOnMissingBean与AutoConfiguration集成"""

        @ConditionalOnMissingBean(name="conflicting_service")
        class TestAutoConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

        config = TestAutoConfig()

        # Bean不存在 - 应该配置
        self.context.contains_bean.return_value = False
        assert config.should_configure(self.context) is True

        # Bean存在 - 不应该配置
        self.context.contains_bean.return_value = True
        assert config.should_configure(self.context) is False

    def test_multiple_conditions_all_pass(self):
        """测试多个条件都通过"""

        @ConditionalOnProperty(name="feature.enabled", having_value="true")
        @ConditionalOnClass("os.path")
        @ConditionalOnMissingBean(name="conflicting_service")
        class TestAutoConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

        config = TestAutoConfig()

        # 设置所有条件都满足
        self.env_mock.get_property.return_value = "true"  # 属性条件
        # os.path存在（类条件）
        self.context.contains_bean.return_value = False  # Bean不存在条件

        assert config.should_configure(self.context) is True

    def test_multiple_conditions_one_fails(self):
        """测试多个条件中有一个失败"""

        @ConditionalOnProperty(name="feature.enabled", having_value="true")
        @ConditionalOnClass("os.path")
        @ConditionalOnBean(name="required_service")
        class TestAutoConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

        config = TestAutoConfig()

        # 设置属性和类条件满足，但Bean条件不满足
        self.env_mock.get_property.return_value = "true"  # 属性条件满足
        # os.path存在（类条件满足）
        self.context.contains_bean.return_value = False  # Bean条件不满足

        assert config.should_configure(self.context) is False

    def test_starter_auto_configuration_with_conditions(self):
        """测试StarterAutoConfiguration与条件注解集成"""

        @ConditionalOnProperty(prefix="test.starter", name="enabled", having_value="true", match_if_missing=True)
        class TestStarterConfig(StarterAutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-starter-config", description="Test starter configuration")

            def get_starter_name(self):
                return "test-starter"

            def get_starter_version(self):
                return "1.0.0"

            def get_configuration_properties_classes(self):
                return [MockIntegrationProperties]

            @Bean
            def test_service(self):
                return "TestService"

        config = TestStarterConfig()

        # 属性不存在但match_if_missing=True - 应该配置
        self.env_mock.get_property.return_value = None
        assert config.should_configure(self.context) is True

        # 属性存在且匹配 - 应该配置
        self.env_mock.get_property.return_value = "true"
        assert config.should_configure(self.context) is True

        # 属性存在但不匹配 - 不应该配置
        self.env_mock.get_property.return_value = "false"
        assert config.should_configure(self.context) is False

    def test_conditions_with_metadata_conditions(self):
        """测试装饰器条件与元数据条件的组合"""

        @ConditionalOnProperty(name="decorator.enabled", having_value="true")
        class TestAutoConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(
                    name="test-config",
                    description="Test configuration",
                    conditions=["property:metadata.enabled"],  # 元数据中的字符串条件
                )

        config = TestAutoConfig()

        # 模拟两个属性都存在且为true
        def mock_get_property(key):
            if key == "decorator.enabled":
                return "true"
            elif key == "metadata.enabled":
                return "value"  # 非None值
            return None

        self.env_mock.get_property.side_effect = mock_get_property

        # 两个条件都满足 - 应该配置
        assert config.should_configure(self.context) is True

        # 装饰器条件不满足
        def mock_get_property_fail_decorator(key):
            if key == "decorator.enabled":
                return "false"
            elif key == "metadata.enabled":
                return "value"
            return None

        self.env_mock.get_property.side_effect = mock_get_property_fail_decorator

        # 装饰器条件不满足 - 不应该配置
        assert config.should_configure(self.context) is False

    def test_condition_evaluation_error_handling(self):
        """测试条件评估错误处理"""

        @ConditionalOnProperty(name="test.prop")
        class TestAutoConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

        config = TestAutoConfig()

        # 模拟环境获取时抛出异常
        self.context.get_environment.side_effect = Exception("Environment error")

        # 应该返回False而不是抛出异常
        assert config.should_configure(self.context) is False

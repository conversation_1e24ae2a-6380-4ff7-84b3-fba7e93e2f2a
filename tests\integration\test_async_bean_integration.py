#!/usr/bin/env python
"""
* @author: cz
* @description: 异步Bean集成测试

测试同步/异步Bean的混合调用和集成功能。
"""

import asyncio
import unittest

from miniboot.bean import BeanDefinition, BeanScope, DisposableBean, InitializingBean, Lifecycle
from miniboot.bean.advanced import DefaultBeanDefinitionRegistry, DependencyGraph


class SyncUserService:
    """同步用户服务"""

    def __init__(self):
        self.name = "SyncUserService"
        self.call_count = 0

    def get_user(self, user_id: str) -> dict:
        """同步获取用户"""
        self.call_count += 1
        return {"id": user_id, "name": f"User {user_id}", "type": "sync"}

    def process_users(self, user_ids: list) -> list:
        """同步处理多个用户"""
        return [self.get_user(uid) for uid in user_ids]


class AsyncUserService(InitializingBean, DisposableBean, Lifecycle):
    """异步用户服务"""

    def __init__(self):
        self.name = "AsyncUserService"
        self.call_count = 0
        self.initialized = False
        self.destroyed = False
        self.running = False

    async def after_properties_set(self):
        """异步初始化"""
        await asyncio.sleep(0.01)
        self.initialized = True

    async def destroy(self):
        """异步销毁"""
        await asyncio.sleep(0.01)
        self.destroyed = True

    async def start(self):
        """异步启动"""
        await asyncio.sleep(0.01)
        self.running = True

    async def stop(self):
        """异步停止"""
        await asyncio.sleep(0.01)
        self.running = False

    def is_running(self) -> bool:
        return self.running

    async def get_user_async(self, user_id: str) -> dict:
        """异步获取用户"""
        await asyncio.sleep(0.01)  # 模拟异步操作
        self.call_count += 1
        return {"id": user_id, "name": f"User {user_id}", "type": "async"}

    async def process_users_async(self, user_ids: list) -> list:
        """异步处理多个用户"""
        tasks = [self.get_user_async(uid) for uid in user_ids]
        return await asyncio.gather(*tasks)


class HybridOrderService:
    """混合订单服务，同时使用同步和异步用户服务"""

    def __init__(self):
        self.name = "HybridOrderService"
        self.sync_user_service = None
        self.async_user_service = None
        self.orders = []

    def set_sync_user_service(self, service):
        """设置同步用户服务"""
        self.sync_user_service = service

    def set_async_user_service(self, service):
        """设置异步用户服务"""
        self.async_user_service = service

    def create_sync_order(self, user_id: str, product: str) -> dict:
        """创建同步订单"""
        user = self.sync_user_service.get_user(user_id)
        order = {"id": f"order_{len(self.orders) + 1}", "user": user, "product": product, "type": "sync_order"}
        self.orders.append(order)
        return order

    async def create_async_order(self, user_id: str, product: str) -> dict:
        """创建异步订单"""
        user = await self.async_user_service.get_user_async(user_id)
        order = {"id": f"order_{len(self.orders) + 1}", "user": user, "product": product, "type": "async_order"}
        self.orders.append(order)
        return order

    async def create_hybrid_order(self, user_id: str, product: str) -> dict:
        """创建混合订单，同时使用同步和异步服务"""
        # 异步获取用户信息
        async_user = await self.async_user_service.get_user_async(user_id)

        # 在线程池中执行同步服务
        loop = asyncio.get_event_loop()
        sync_user = await loop.run_in_executor(None, self.sync_user_service.get_user, user_id)

        order = {"id": f"order_{len(self.orders) + 1}", "async_user": async_user, "sync_user": sync_user, "product": product, "type": "hybrid_order"}
        self.orders.append(order)
        return order


@unittest.skip("依赖模块不存在")
class AsyncBeanIntegrationTestCase(unittest.TestCase):
    """异步Bean集成测试"""

    def setUp(self):
        """设置测试环境"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.dependency_graph = DependencyGraph()
        self.factory = SmartBeanFactory(self.registry, self.dependency_graph)

        # 注册Bean定义
        self._register_beans()

    def _register_beans(self):
        """注册Bean定义"""
        # 注册同步用户服务
        sync_user_def = BeanDefinition("syncUserService", SyncUserService, BeanScope.SINGLETON)
        self.registry.register_bean_definition("syncUserService", sync_user_def)

        # 注册异步用户服务
        async_user_def = BeanDefinition("asyncUserService", AsyncUserService, BeanScope.SINGLETON)
        self.registry.register_bean_definition("asyncUserService", async_user_def)

        # 注册混合订单服务
        hybrid_order_def = BeanDefinition("hybridOrderService", HybridOrderService, BeanScope.SINGLETON)
        self.registry.register_bean_definition("hybridOrderService", hybrid_order_def)

    def test_sync_async_bean_coexistence(self):
        """测试同步和异步Bean的共存"""
        # 获取同步服务
        sync_service = self.factory.get_bean("syncUserService")
        self.assertIsNotNone(sync_service)

        # 验证同步服务工作正常
        sync_result = sync_service.get_user("123")
        self.assertEqual(sync_result["type"], "sync")

        # 在异步环境中测试异步Bean
        async def test_async():
            # 直接使用异步工厂获取异步Bean
            async_service = await self.factory.async_factory.get_bean_async("asyncUserService")
            self.assertIsNotNone(async_service)
            self.assertEqual(async_service.name, "AsyncUserService")
            self.assertTrue(async_service.initialized)

            # 测试异步方法
            async_result = await async_service.get_user_async("123")
            self.assertEqual(async_result["type"], "async")

            return async_service

        asyncio.run(test_async())

    def test_smart_proxy_automatic_adaptation(self):
        """测试智能代理的自动适配"""
        # 获取同步服务代理
        sync_proxy = self.factory.get_bean("syncUserService")

        # 在同步环境中调用
        sync_result = sync_proxy.get_user("sync_test")
        self.assertEqual(sync_result["id"], "sync_test")

        # 验证代理类型
        from miniboot.bean import SmartBeanProxy

        self.assertIsInstance(sync_proxy, SmartBeanProxy)

    def test_hybrid_service_integration(self):
        """测试混合服务集成"""

        async def test():
            # 直接创建服务实例，避免代理问题
            sync_service = SyncUserService()
            async_service = AsyncUserService()
            hybrid_service = HybridOrderService()

            # 手动设置依赖（在实际应用中由依赖注入完成）
            hybrid_service.set_sync_user_service(sync_service)
            hybrid_service.set_async_user_service(async_service)

            # 创建同步订单
            sync_order = hybrid_service.create_sync_order("user1", "laptop")
            self.assertEqual(sync_order["type"], "sync_order")
            self.assertEqual(sync_order["user"]["type"], "sync")

            # 创建异步订单
            async_order = await hybrid_service.create_async_order("user2", "phone")
            self.assertEqual(async_order["type"], "async_order")
            self.assertEqual(async_order["user"]["type"], "async")

            # 验证订单数量
            self.assertEqual(len(hybrid_service.orders), 2)

            return hybrid_service

        asyncio.run(test())

    def test_lifecycle_management_integration(self):
        """测试生命周期管理集成"""

        async def test():
            # 获取异步服务
            async_service = await self.factory.async_factory.get_bean_async("asyncUserService")
            self.assertTrue(async_service.initialized)
            self.assertFalse(async_service.running)

            # 启动异步生命周期
            await self.factory.async_factory.start_lifecycle_beans_async()
            self.assertTrue(async_service.running)
            self.assertTrue(self.factory.async_factory.is_async_lifecycle_running())

            # 停止异步生命周期
            await self.factory.async_factory.stop_lifecycle_beans_async()
            self.assertFalse(async_service.running)
            self.assertFalse(self.factory.async_factory.is_async_lifecycle_running())

            # 销毁所有Bean
            await self.factory.async_factory.destroy_all_singletons_async()
            self.assertTrue(async_service.destroyed)

        asyncio.run(test())

    def test_concurrent_bean_access(self):
        """测试并发Bean访问"""

        async def test():
            # 并发获取Bean
            tasks = []
            for i in range(10):
                task = asyncio.create_task(self._concurrent_bean_operation(i))
                tasks.append(task)

            results = await asyncio.gather(*tasks)

            # 验证所有操作都成功
            self.assertEqual(len(results), 10)
            for result in results:
                self.assertIsNotNone(result)
                self.assertIn("user", result)

        asyncio.run(test())

    async def _concurrent_bean_operation(self, index: int) -> dict:
        """并发Bean操作"""
        # 获取异步服务
        async_service = await self.factory.async_factory.get_bean_async("asyncUserService")

        # 执行异步操作
        user = await async_service.get_user_async(f"user_{index}")

        return {"index": index, "user": user}

    def test_error_handling_in_async_context(self):
        """测试异步上下文中的错误处理"""

        async def test():
            # 尝试获取不存在的Bean
            try:
                self.factory.get_bean("nonExistentBean")
                self.fail("应该抛出异常")
            except Exception as e:
                self.assertIn("nonExistentBean", str(e))

            # 正常获取Bean
            service = self.factory.get_bean("asyncUserService")
            self.assertIsNotNone(service)

        asyncio.run(test())

    def test_performance_comparison(self):
        """测试性能对比"""

        async def test():
            sync_service = self.factory.get_bean("syncUserService")
            async_service = await self.factory.async_factory.get_bean_async("asyncUserService")

            # 测试同步批量处理
            import time

            start_time = time.time()
            sync_results = await sync_service.process_users(["u1", "u2", "u3", "u4", "u5"])
            sync_duration = time.time() - start_time

            # 测试异步批量处理
            start_time = time.time()
            async_results = await async_service.process_users_async(["u1", "u2", "u3", "u4", "u5"])
            async_duration = time.time() - start_time

            # 验证结果
            self.assertEqual(len(sync_results), 5)
            self.assertEqual(len(async_results), 5)

            # 异步处理应该更快（由于并发）
            print(f"同步处理时间: {sync_duration:.4f}s")
            print(f"异步处理时间: {async_duration:.4f}s")

            # 验证结果类型
            for result in sync_results:
                self.assertEqual(result["type"], "sync")

            for result in async_results:
                self.assertEqual(result["type"], "async")

        asyncio.run(test())


if __name__ == "__main__":
    unittest.main()

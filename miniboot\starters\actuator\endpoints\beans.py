#!/usr/bin/env python
"""
* @author: cz
* @description: Bean 信息端点实现

提供 Bean 定义、实例、依赖关系等信息的监控端点.
类似于 Spring Boot Actuator 的 /beans 端点.
"""

from datetime import datetime
from typing import TYPE_CHECKING, Any, Optional

from loguru import logger

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.registry import DefaultBeanDefinitionRegistry

from .base import BaseEndpoint, EndpointOperation, OperationType

if TYPE_CHECKING:
    from miniboot.context.application import ApplicationContext


class BeansEndpoint(BaseEndpoint):
    """Bean 信息端点

    提供应用中所有 Bean 的详细信息,包括:
    - Bean 定义信息
    - Bean 实例状态
    - Bean 依赖关系
    - Bean 作用域信息
    """

    def __init__(self, application_context: Optional["ApplicationContext"] = None):
        """初始化 Bean 信息端点

        Args:
            application_context: 应用上下文,用于获取 Bean 信息
        """
        super().__init__("beans", enabled=True, sensitive=False)
        self.application_context = application_context

    def _create_operations(self) -> list[EndpointOperation]:
        """创建操作列表"""
        return [EndpointOperation(operation_type=OperationType.READ, method="GET", handler=self.get)]

    def invoke(self, operation_type: OperationType, **_kwargs) -> Any:
        """执行端点操作"""
        if operation_type == OperationType.READ:
            return self.get()
        raise ValueError(f"Unsupported operation: {operation_type}")

    def get(self) -> dict[str, Any]:
        """获取所有 Bean 信息"""
        try:
            if not self.application_context:
                return {"error": "Application context not available", "timestamp": datetime.now().isoformat()}

            # 获取 Bean 工厂和注册表
            bean_factory = getattr(self.application_context, "_bean_factory", None)
            bean_registry = getattr(bean_factory, "_registry", None) if bean_factory else None

            if not bean_registry:
                return {"error": "Bean registry not available", "timestamp": datetime.now().isoformat()}

            # 收集 Bean 信息
            beans_info = self._collect(bean_registry, bean_factory)

            # 收集统计信息
            stats = self._stats(bean_registry, bean_factory)

            return {
                "timestamp": datetime.now().isoformat(),
                "contexts": {"application": {"beans": beans_info, "parentId": None}},
                "statistics": stats,
            }

        except Exception as e:
            logger.error(f"Failed to get beans info: {e}")
            return {"error": str(e), "timestamp": datetime.now().isoformat()}

    def _collect(self, bean_registry: DefaultBeanDefinitionRegistry, bean_factory: DefaultBeanFactory) -> dict[str, Any]:
        """收集 Bean 信息

        Args:
            bean_registry: Bean 注册表
            bean_factory: Bean 工厂

        Returns:
            Bean 信息字典
        """
        beans_info = {}

        # 获取所有 Bean 定义名称
        bean_names = bean_registry.get_bean_names()

        for bean_name in bean_names:
            try:
                bean_definition = bean_registry.get_bean_definition(bean_name)
                bean_info = self._build(bean_name, bean_definition, bean_factory)
                beans_info[bean_name] = bean_info
            except Exception as e:
                logger.warning(f"Failed to get info for bean '{bean_name}': {e}")
                beans_info[bean_name] = {"error": str(e), "aliases": [], "scope": "unknown", "type": "unknown", "resource": None, "dependencies": []}

        return beans_info

    def _build(self, bean_name: str, bean_definition: BeanDefinition, bean_factory: DefaultBeanFactory) -> dict[str, Any]:
        """创建单个 Bean 的信息

        Args:
            bean_name: Bean 名称
            bean_definition: Bean 定义
            bean_factory: Bean 工厂

        Returns:
            Bean 信息字典
        """
        # 基本信息
        bean_info = {
            "aliases": self._aliases(bean_name, bean_factory),
            "scope": bean_definition.scope.value if bean_definition.scope else "singleton",
            "type": bean_definition.bean_class.__name__ if bean_definition.bean_class else "unknown",
            "resource": self._resource(bean_definition),
            "dependencies": self._deps(bean_definition),
        }

        # 添加作用域特定信息
        if bean_definition.scope:
            bean_info["scopeDetails"] = self._scope(bean_definition.scope)

        # 添加生命周期信息
        lifecycle_info = self._lifecycle(bean_definition)
        if lifecycle_info:
            bean_info["lifecycle"] = lifecycle_info

        # 添加实例信息(如果是单例且已创建)
        if bean_definition.is_singleton():
            instance_info = self._instance(bean_name, bean_factory)
            if instance_info:
                bean_info["instance"] = instance_info

        return bean_info

    def _aliases(self, _bean_name: str, _bean_factory: DefaultBeanFactory) -> list[str]:
        """获取 Bean 别名"""
        # 目前 Mini-Boot 还没有实现别名功能,返回空列表
        return []

    def _resource(self, bean_definition: BeanDefinition) -> Optional[str]:
        """获取 Bean 资源信息"""
        if bean_definition.bean_class:
            module = getattr(bean_definition.bean_class, "__module__", None)
            if module:
                return f"class path resource [{module.replace('.', '/')}.py]"
        return None

    def _deps(self, bean_definition: BeanDefinition) -> list[str]:
        """获取 Bean 依赖"""
        dependencies = []

        # 添加显式依赖
        if bean_definition.depends_on:
            dependencies.extend(bean_definition.depends_on)

        # TODO: 添加自动装配依赖分析
        # 这需要分析 Bean 类的构造函数和字段注解

        return dependencies

    def _scope(self, scope: BeanScope) -> dict[str, Any]:
        """获取作用域详细信息"""
        scope_details = {"name": scope.value, "singleton": scope.is_singleton(), "prototype": scope.is_prototype()}

        if scope.is_web_scope():
            scope_details["webScope"] = True
            scope_details["webScopeType"] = scope.value

        return scope_details

    def _lifecycle(self, bean_definition: BeanDefinition) -> Optional[dict[str, Any]]:
        """获取生命周期信息"""
        lifecycle_info = {}

        if bean_definition.init_method:
            lifecycle_info["initMethod"] = bean_definition.init_method

        if bean_definition.destroy_method:
            lifecycle_info["destroyMethod"] = bean_definition.destroy_method

        return lifecycle_info if lifecycle_info else None

    def _instance(self, bean_name: str, bean_factory: DefaultBeanFactory) -> Optional[dict[str, Any]]:
        """获取实例信息"""
        try:
            # 检查单例缓存
            singleton_objects = getattr(bean_factory, "_singleton_objects", {})
            if bean_name in singleton_objects:
                instance = singleton_objects[bean_name]
                return {
                    "created": True,
                    "type": type(instance).__name__,
                    "module": type(instance).__module__,
                    "id": id(instance),
                    "hasLifecycleMethods": self._methods(instance),
                }
        except Exception as e:
            logger.debug(f"Failed to get instance info for bean '{bean_name}': {e}")

        return None

    def _methods(self, instance: Any) -> dict[str, bool]:
        """检查实例是否有生命周期方法"""
        # 检查是否正在运行,但避免阻塞调用
        is_running = False
        if hasattr(instance, "is_running") and callable(instance.is_running):
            try:
                # 使用线程池和超时机制避免阻塞
                import concurrent.futures

                def check_running():
                    return instance.is_running()

                # 使用线程池执行,设置 1 秒超时
                with concurrent.futures.ThreadPoolExecutor(max_workers=1) as executor:
                    future = executor.submit(check_running)
                    try:
                        is_running = future.result(timeout=1.0)
                    except concurrent.futures.TimeoutError:
                        logger.debug("is_running() call timed out after 1 second")
                        is_running = False
                    except Exception as e:
                        logger.debug(f"Error calling is_running(): {e}")
                        is_running = False

            except Exception as e:
                logger.debug(f"Failed to check is_running status: {e}")
                is_running = False

        return {
            "hasStart": hasattr(instance, "start") and callable(instance.start),
            "hasStop": hasattr(instance, "stop") and callable(instance.stop),
            "hasDestroy": hasattr(instance, "destroy") and callable(instance.destroy),
            "isRunning": is_running,
        }

    def _stats(self, bean_registry: DefaultBeanDefinitionRegistry, bean_factory: DefaultBeanFactory) -> dict[str, Any]:
        """收集统计信息

        Args:
            bean_registry: Bean 注册表
            bean_factory: Bean 工厂

        Returns:
            统计信息字典
        """
        try:
            bean_names = bean_registry.get_bean_definition_names()
            total_beans = len(bean_names)

            # 按作用域统计
            scope_stats = {}
            singleton_count = 0
            prototype_count = 0
            web_scope_count = 0

            # 按类型统计
            type_stats = {}

            # 实例统计
            created_instances = 0
            singleton_objects = getattr(bean_factory, "_singleton_objects", {})

            for bean_name in bean_names:
                try:
                    bean_definition = bean_registry.get_bean_definition(bean_name)

                    # 作用域统计
                    scope = bean_definition.scope
                    if scope:
                        scope_name = scope.value
                        scope_stats[scope_name] = scope_stats.get(scope_name, 0) + 1

                        if scope.is_singleton():
                            singleton_count += 1
                        elif scope.is_prototype():
                            prototype_count += 1
                        elif scope.is_web_scope():
                            web_scope_count += 1

                    # 类型统计
                    if bean_definition.bean_class:
                        class_name = bean_definition.bean_class.__name__
                        type_stats[class_name] = type_stats.get(class_name, 0) + 1

                    # 实例统计
                    if bean_name in singleton_objects:
                        created_instances += 1

                except Exception as e:
                    logger.debug(f"Failed to collect stats for bean '{bean_name}': {e}")

            return {
                "totalBeans": total_beans,
                "createdInstances": created_instances,
                "scopeStatistics": {"singleton": singleton_count, "prototype": prototype_count, "webScope": web_scope_count, "details": scope_stats},
                "typeStatistics": type_stats,
                "memoryUsage": {
                    "singletonCacheSize": len(singleton_objects),
                    "earlySingletonCacheSize": len(getattr(bean_factory, "_early_singleton_objects", {})),
                    "singletonFactoryCacheSize": len(getattr(bean_factory, "_singleton_factories", {})),
                },
            }

        except Exception as e:
            logger.error(f"Failed to collect statistics: {e}")
            return {"error": str(e)}

    def set_context(self, application_context: "ApplicationContext") -> None:
        """设置应用上下文

        Args:
            application_context: 应用上下文
        """
        self.application_context = application_context


# 便利函数
def create_beans_endpoint(application_context: Optional["ApplicationContext"] = None) -> BeansEndpoint:
    """创建 Bean 信息端点

    Args:
        application_context: 应用上下文

    Returns:
        Bean 信息端点实例
    """
    return BeansEndpoint(application_context)

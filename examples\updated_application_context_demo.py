#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
更新后的应用上下文演示

展示更新后的默认应用上下文的新功能，包括：
- 重构后的模块初始化器集成
- 增强的模块管理功能
- 详细的状态监控
- 生命周期管理
- 健康检查功能
"""

import asyncio
import time
from pathlib import Path

from miniboot.context.application import DefaultApplicationContext


async def demo_basic_functionality():
    """演示基本功能"""
    print("🧪 Testing Basic Application Context Functionality...")

    # 创建应用上下文
    context = DefaultApplicationContext(
        config_path="examples/config/demo.properties",
        packages_to_scan=["examples.demo"],
        auto_detect=True
    )

    try:
        # 启动应用上下文
        print("🚀 Starting application context...")
        start_time = time.time()
        await context.start()
        startup_time = time.time() - start_time

        print(f"✅ Application context started in {startup_time:.3f}s")

        # 检查应用状态
        print(f"📊 Application state: {context.get_state().value}")
        print(f"🏃 Is running: {context.is_running()}")

        # 获取详细状态信息
        status = context.get_application_status()
        print(f"\n📋 Application Status Summary:")
        print(f"  - State: {status['application']['state']}")
        print(f"  - Uptime: {status['application']['uptime']:.3f}s")
        print(f"  - Module initializer available: {status['components']['module_initializer_available']}")
        print(f"  - Total modules: {status['modules'].get('total_modules', 0)}")
        print(f"  - Running modules: {len(status['modules'].get('running_modules', set()))}")

        # 等待一段时间让模块完全启动
        await asyncio.sleep(1)

        return context, True

    except Exception as e:
        print(f"❌ Failed to start application context: {e}")
        return context, False


async def demo_module_management(context):
    """演示模块管理功能"""
    print("\n🧪 Testing Module Management...")

    try:
        # 获取模块状态
        module_status = context.get_module_status()
        print(f"📊 Module Status:")
        print(f"  - Total modules: {module_status.get('total_modules', 0)}")
        print(f"  - Initialized modules: {len(module_status.get('initialized_modules', set()))}")
        print(f"  - Running modules: {len(module_status.get('running_modules', set()))}")
        print(f"  - Failed modules: {len(module_status.get('failed_modules', set()))}")

        # 显示具体的模块状态
        module_states = module_status.get('module_states', {})
        if module_states:
            print(f"\n📋 Individual Module States:")
            for name, state in module_states.items():
                print(f"  - {name}: {state}")

        # 测试模块健康检查
        print(f"\n🏥 Testing Module Health Checks...")
        for module_name in module_status.get('initialized_modules', set()):
            health = await context.check_module_health(module_name)
            status_icon = "✅" if health.get('status') == 'healthy' else "❌"
            print(f"  {status_icon} {module_name}: {health.get('status', 'unknown')}")

        return True

    except Exception as e:
        print(f"❌ Module management test failed: {e}")
        return False


async def demo_module_lifecycle(context):
    """演示模块生命周期管理"""
    print("\n🧪 Testing Module Lifecycle Management...")

    try:
        module_status = context.get_module_status()
        running_modules = list(module_status.get('running_modules', set()))

        if not running_modules:
            print("ℹ️ No running modules to test lifecycle management")
            return True

        # 选择第一个运行中的模块进行测试
        test_module = running_modules[0]
        print(f"🎯 Testing lifecycle with module: {test_module}")

        # 测试模块重启
        print(f"🔄 Restarting module: {test_module}")
        restart_success = await context.restart_module(test_module)
        print(f"  Restart result: {'✅ Success' if restart_success else '❌ Failed'}")

        # 检查重启后的健康状态
        if restart_success:
            await asyncio.sleep(0.5)  # 等待重启完成
            health = await context.check_module_health(test_module)
            print(f"  Health after restart: {health.get('status', 'unknown')}")

        return True

    except Exception as e:
        print(f"❌ Module lifecycle test failed: {e}")
        return False


async def demo_application_monitoring(context):
    """演示应用监控功能"""
    print("\n🧪 Testing Application Monitoring...")

    try:
        # 获取详细的应用状态
        status = context.get_application_status()

        print(f"📊 Detailed Application Status:")

        # 应用信息
        app_info = status['application']
        print(f"  🏃 Application:")
        print(f"    - State: {app_info['state']}")
        print(f"    - Running: {app_info['is_running']}")
        print(f"    - Uptime: {app_info.get('uptime', 0):.3f}s")

        # 环境信息
        env_info = status['environment']
        print(f"  🌍 Environment:")
        print(f"    - Active profiles: {env_info['active_profiles']}")
        print(f"    - Config path: {env_info['config_path']}")
        print(f"    - Scan packages: {env_info['packages_to_scan']}")

        # 组件信息
        comp_info = status['components']
        print(f"  🔧 Components:")
        print(f"    - Bean factory: {'✅' if comp_info['bean_factory_available'] else '❌'}")
        print(f"    - Event publisher: {'✅' if comp_info['event_publisher_available'] else '❌'}")
        print(f"    - Module initializer: {'✅' if comp_info['module_initializer_available'] else '❌'}")
        print(f"    - Actuator integration: {'✅' if comp_info['actuator_integration_available'] else '❌'}")

        # Bean信息
        bean_info = status['beans']
        print(f"  📦 Beans:")
        print(f"    - Total registered: {bean_info['total_registered']}")
        print(f"    - Singleton count: {bean_info['singleton_count']}")

        # 运行时信息
        if 'runtime' in status:
            runtime_info = status['runtime']
            print(f"  ⚡ Runtime:")
            print(f"    - Async mode: {runtime_info['async_mode']}")
            print(f"    - Auto detect: {runtime_info['auto_detect_enabled']}")

        return True

    except Exception as e:
        print(f"❌ Application monitoring test failed: {e}")
        return False


async def demo_graceful_shutdown(context):
    """演示优雅关闭"""
    print("\n🧪 Testing Graceful Shutdown...")

    try:
        print("🛑 Stopping application context...")
        stop_start_time = time.time()

        await context.stop()

        stop_time = time.time() - stop_start_time
        print(f"✅ Application context stopped in {stop_time:.3f}s")

        # 检查最终状态
        print(f"📊 Final state: {context.get_state().value}")
        print(f"🏃 Is running: {context.is_running()}")

        return True

    except Exception as e:
        print(f"❌ Graceful shutdown test failed: {e}")
        return False


async def demo_error_handling():
    """演示错误处理"""
    print("\n🧪 Testing Error Handling...")

    try:
        # 测试应用的容错能力 - 使用不存在的配置文件
        context = DefaultApplicationContext(
            config_path="non_existent_config.properties",
            packages_to_scan=["non.existent.package"],
            auto_detect=True
        )

        try:
            await context.start()
            # 应用应该能够容错启动，这是好事！
            print("✅ Application successfully handled missing config (good fault tolerance)")

            # 测试应用状态
            if context.is_running():
                print("✅ Application is running despite missing config")
                await context.stop()
                print("✅ Application stopped gracefully")
                return True
            else:
                print("❌ Application not running after start")
                return False

        except Exception as e:
            print(f"✅ Application correctly failed with error: {type(e).__name__}")
            return True

    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Updated Application Context Demo")
    print("=" * 50)

    context = None
    tests = [
        ("Basic Functionality", demo_basic_functionality),
        ("Error Handling", demo_error_handling),
    ]

    # 运行基础测试
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} Test...")
            if test_name == "Basic Functionality":
                context, success = await test_func()
                results.append((test_name, success))
            else:
                success = await test_func()
                results.append((test_name, success))

            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{status} {test_name} Test")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ FAILED {test_name} Test: {e}")

    # 如果基础功能测试成功，运行其他测试
    if context and context.is_running():
        advanced_tests = [
            ("Module Management", demo_module_management),
            ("Module Lifecycle", demo_module_lifecycle),
            ("Application Monitoring", demo_application_monitoring),
            ("Graceful Shutdown", demo_graceful_shutdown),
        ]

        for test_name, test_func in advanced_tests:
            try:
                print(f"\n🧪 Running {test_name} Test...")
                success = await test_func(context)
                results.append((test_name, success))

                status = "✅ PASSED" if success else "❌ FAILED"
                print(f"{status} {test_name} Test")
            except Exception as e:
                results.append((test_name, False))
                print(f"❌ FAILED {test_name} Test: {e}")

    # 总结
    print(f"\n📋 Test Summary:")
    passed = sum(1 for _, success in results if success)
    total = len(results)

    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  - {test_name}: {status}")

    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")

    if passed == total:
        print("🎉 All tests passed! Updated application context is working correctly!")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

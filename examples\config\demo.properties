# Demo Configuration for Updated Application Context
# 演示配置文件

# Application Information
miniboot.application.name=Updated Application Context Demo
miniboot.application.version=2.0.0

# Banner Configuration
miniboot.banner.enabled=true
miniboot.banner.text=Updated Application Context Demo

# Module Configuration
miniboot.modules.max_concurrent=3

# Async Module
miniboot.async.enabled=true

# Events Module
miniboot.events.enabled=true

# Web Module
miniboot.web.enabled=true
miniboot.web.port=8080

# Scheduler Module
miniboot.scheduler.enabled=true

# Actuator Module
miniboot.actuators.endpoints.web.enabled=true
miniboot.actuators.endpoints.health.enabled=true
miniboot.actuators.endpoints.info.enabled=true

# Logging Configuration
miniboot.logging.level.root=INFO
miniboot.logging.level.miniboot=DEBUG

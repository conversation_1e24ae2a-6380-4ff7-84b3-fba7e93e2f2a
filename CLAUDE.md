# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Mini-Boot 是一个轻量级的 Python Web 框架，模仿 Spring Boot 的设计理念，提供 IoC 容器、依赖注入、自动配置等企业级功能。当前版本为 0.1.0，项目已完成核心功能开发，正在进行扩展模块开发。

## Common Development Commands

### Testing

```bash
# Run all tests
python tests/test_runner.py

# Run specific test types
python tests/test_runner.py --unit            # Unit tests only
python tests/test_runner.py --integration     # Integration tests only
python tests/test_runner.py --quality         # Code quality checks only
python tests/test_runner.py --coverage        # Tests with coverage report

# Run single test file
python -m unittest tests.unit.env.test_environment
```

### Code Quality

```bash
# Format code
ruff format .

# Lint code
ruff check .

# Type checking
mypy miniboot/
```

### Package Management

```bash
# Install dependencies
uv install

# Add new dependency
uv add package_name

# Add dev dependency
uv add --dev package_name
```

### Build and Release

```bash
# Build package
uv build

# Run release script
python scripts/release.py
```

## Architecture Overview

### Core Modules Structure

-   `miniboot/env/` - Environment configuration and property management
-   `miniboot/log/` - Logging system based on loguru
-   `miniboot/banner/` - Application startup banner system
-   `miniboot/annotations/` - Annotation system (34 annotations implemented)
-   `miniboot/bean/` - Bean management and dependency injection
-   `miniboot/asyncs/` - Async processing and thread pool management
-   `miniboot/events/` - Event system for publish-subscribe pattern
-   `miniboot/schedule/` - Task scheduling system
-   `miniboot/processor/` - Bean post-processors and annotation processing
    -   `base.py` - Base processor interfaces and exception definitions
    -   `registry.py` - Processor registration and management system
-   `miniboot/web/` - Web framework integration
-   `miniboot/actuator/` - Health checks and monitoring
-   `miniboot/starters/` - Auto-configuration mechanism

### Key Design Patterns

-   **IoC Container**: Bean definition, registration, and dependency injection
-   **Annotation-driven**: Extensive use of decorators for configuration
-   **Async Support**: Smart proxy system for sync/async method adaptation
-   **Event-driven**: Publisher-subscriber pattern for loose coupling
-   **Auto-configuration**: Starter mechanism for modular functionality

## Important Files and Locations

### Configuration

-   `pyproject.toml` - Project configuration, dependencies, and tool settings
-   `ruff.toml` - Code style and linting configuration
-   `resources/application.yml` - Application configuration template

### Test Framework

-   `tests/test_runner.py` - Unified test runner with progress bars
-   `tests/testutils/code_quality.py` - Code quality validation
-   Test structure: `tests/unit/`, `tests/integration/`, `tests/benchmark/`
    -   `tests/unit/processor/` - Processor module unit tests

### Documentation

-   `docs/` - Comprehensive documentation covering all modules
-   `docs/tasks.md` - Detailed development task list and progress tracking
-   `docs/04.测试规范.md` - Testing standards and conventions

## Development Workflow

### Module Development Status

-   ✅ **Completed**: env, logger, banner, annotations, bean, asyncs, events, schedule (100%)
-   🔄 **In Progress**: processor (30% - 基础接口和注册表已完成)
-   ⏳ **Planned**: web, actuator, starters

### Testing Requirements

-   Each module must have >90% test coverage
-   Use unittest framework with descriptive test names
-   Tests must pass before committing
-   Integration tests required for multi-module features

### Code Quality Standards

-   Follow PEP 8 style guidelines
-   100% type annotation coverage
-   All code must pass mypy type checking
-   Line length: 150 characters (configured in ruff.toml)

## Module-Specific Guidelines

### Annotations Module

-   34 annotations implemented (@Component, @Service, @Autowired, etc.)
-   Comprehensive component scanning system
-   Metadata management for annotation processing
-   Performance optimizations for scanning

### Processor Module

-   Base processor interfaces (BeanPostProcessor, OrderedBeanPostProcessor)
-   Thread-safe processor registry (BeanPostProcessorRegistry)
-   Processor execution order management (ProcessorOrder constants)
-   Exception handling mechanism (BeanProcessingError hierarchy)
-   Annotation-based processor discovery and registration

### Bean Module

-   Three-tier caching system for singleton beans
-   Circular dependency detection
-   Smart proxy system for async/sync method adaptation
-   Comprehensive lifecycle management

### Environment Module

-   Multi-source property resolution (YAML, JSON, env vars)
-   Type conversion system with custom converters
-   Configuration binding with dataclass support
-   Profile-specific configuration loading

## Testing Patterns

### Unit Testing

```python
import unittest
from miniboot.annotations import Component, get_component_metadata

class ComponentTestCase(unittest.TestCase):
    def test_component_annotation(self):
        @Component("test_service")
        class TestService:
            pass

        self.assertTrue(hasattr(TestService, '__component__'))
        metadata = get_component_metadata(TestService)
        self.assertEqual(metadata.name, "test_service")
```

### Integration Testing

-   Test module interactions
-   Verify dependency injection works correctly
-   Test configuration loading and binding
-   Validate async/sync method adaptation

## Performance Considerations

### Bean Creation

-   Target: >1000 beans/second
-   Use caching for reflection operations
-   Minimize object creation overhead

### Memory Usage

-   Target: <50MB for basic configuration
-   Monitor memory usage in tests
-   Use weak references where appropriate

### Application Startup

-   Target: <2 seconds startup time
-   Optimize component scanning
-   Lazy initialization where possible

## Troubleshooting

### Common Issues

1. **Import Errors**: Check module structure and **init**.py files
2. **Circular Dependencies**: Use lazy initialization or proxy patterns
3. **Type Annotation Issues**: Ensure proper forward references
4. **Test Failures**: Run with verbose output to identify issues

### Debugging Tips

-   Use `tests/test_runner.py --verbose` for detailed test output
-   Check logs in development environment
-   Verify configuration loading with environment module
-   Test annotation processing with metadata inspection

## Contributing Guidelines

### Before Making Changes

1. Run full test suite to ensure baseline
2. Check code quality with ruff and mypy
3. Verify no regressions in existing functionality
4. Update documentation for public APIs

### Code Review Checklist

-   [ ] Tests added for new functionality
-   [ ] Type annotations complete
-   [ ] Documentation updated
-   [ ] Code style compliant
-   [ ] Performance impact considered

---

_This project is actively developed with comprehensive testing and documentation. Refer to docs/tasks.md for detailed progress tracking and development roadmap._

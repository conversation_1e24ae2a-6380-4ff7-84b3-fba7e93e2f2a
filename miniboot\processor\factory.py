#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Bean后置处理器工厂 - 解决循环依赖问题,提供处理器创建的统一接口
"""

from abc import ABC, abstractmethod
from typing import TYPE_CHECKING, Any

if TYPE_CHECKING:
    from ..bean.bean_factory import BeanFactory
    from .base import BeanPostProcessor


class BeanPostProcessorFactory(ABC):
    """Bean后置处理器工厂接口

    用于创建Bean后置处理器,解决Bean工厂和处理器之间的循环依赖问题.
    """

    @abstractmethod
    def core(self, bean_factory: "BeanFactory") -> list["BeanPostProcessor"]:
        """创建核心Bean后置处理器

        Args:
            bean_factory: Bean工厂实例

        Returns:
            List[BeanPostProcessor]: 核心处理器列表
        """
        pass

    @abstractmethod
    def names(self) -> list[str]:
        """获取处理器名称列表

        Returns:
            List[str]: 处理器名称列表
        """
        pass


class DefaultBeanPostProcessorFactory(BeanPostProcessorFactory):
    """默认Bean后置处理器工厂实现

    创建框架核心的Bean后置处理器,包括:
    - AutowiredAnnotationProcessor: 自动装配处理器
    - ValueAnnotationProcessor: 值注入处理器
    """

    def core(self, bean_factory: "BeanFactory") -> list["BeanPostProcessor"]:
        """创建核心Bean后置处理器

        Args:
            bean_factory: Bean工厂实例

        Returns:
            List[BeanPostProcessor]: 核心处理器列表
        """
        processors = []

        try:
            # 创建自动装配处理器
            autowired_processor = self._create_autowired_processor(bean_factory)
            if autowired_processor:
                processors.append(autowired_processor)

            # 创建值注入处理器
            value_processor = self._create_value_processor()
            if value_processor:
                processors.append(value_processor)

        except Exception as e:
            # 记录错误但不抛出异常,允许部分处理器创建失败
            from loguru import logger

            logger.warning(f"Failed to create some core processors: {e}")

        return processors

    def names(self) -> list[str]:
        """获取处理器名称列表

        Returns:
            List[str]: 处理器名称列表
        """
        return ["AutowiredAnnotationProcessor", "ValueAnnotationProcessor"]

    def _create_autowired_processor(self, bean_factory: "BeanFactory") -> Any:
        """创建自动装配处理器

        Args:
            bean_factory: Bean工厂实例

        Returns:
            AutowiredAnnotationProcessor: 自动装配处理器实例
        """
        try:
            from .autowired import AutowiredAnnotationProcessor

            return AutowiredAnnotationProcessor(bean_factory)
        except ImportError as e:
            from loguru import logger

            logger.warning(f"Failed to import AutowiredAnnotationProcessor: {e}")
            return None

    def _create_value_processor(self) -> Any:
        """创建值注入处理器

        Returns:
            ValueAnnotationProcessor: 值注入处理器实例
        """
        try:
            from .value import ValueAnnotationProcessor

            return ValueAnnotationProcessor()
        except ImportError as e:
            from loguru import logger

            logger.warning(f"Failed to import ValueAnnotationProcessor: {e}")
            return None


class ProcessorFactoryRegistry:
    """处理器工厂注册表

    管理不同类型的处理器工厂,支持扩展和自定义.
    """

    def __init__(self):
        """初始化处理器工厂注册表"""
        self._factories: dict[str, BeanPostProcessorFactory] = {}
        # 注册默认工厂
        self.register("default", DefaultBeanPostProcessorFactory())

    def register(self, name: str, factory: BeanPostProcessorFactory) -> None:
        """注册处理器工厂

        Args:
            name: 工厂名称
            factory: 处理器工厂实例
        """
        self._factories[name] = factory

    def factory(self, name: str = "default") -> BeanPostProcessorFactory:
        """获取处理器工厂

        Args:
            name: 工厂名称,默认为"default"

        Returns:
            BeanPostProcessorFactory: 处理器工厂实例

        Raises:
            KeyError: 如果工厂不存在
        """
        if name not in self._factories:
            raise KeyError(f"Processor factory '{name}' not found")
        return self._factories[name]

    def available(self) -> list[str]:
        """获取可用的工厂名称列表

        Returns:
            List[str]: 工厂名称列表
        """
        return list(self._factories.keys())


# 全局处理器工厂注册表实例
_processor_factory_registry = ProcessorFactoryRegistry()


def factory(name: str = "default") -> BeanPostProcessorFactory:
    """获取处理器工厂的便捷函数

    Args:
        name: 工厂名称,默认为"default"

    Returns:
        BeanPostProcessorFactory: 处理器工厂实例
    """
    return _processor_factory_registry.factory(name)


def register_factory(name: str, factory: BeanPostProcessorFactory) -> None:
    """注册处理器工厂的便捷函数

    Args:
        name: 工厂名称
        factory: 处理器工厂实例
    """
    _processor_factory_registry.register(name, factory)

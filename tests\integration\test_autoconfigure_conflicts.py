#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置冲突处理集成测试
"""

from dataclasses import dataclass
from unittest.mock import Mock

import pytest

from miniboot.annotations import Bean, ConfigurationProperties
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.autoconfigure.properties import StarterProperties
from miniboot.autoconfigure.registry import AutoConfigurationRegistry
from miniboot.autoconfigure.starter import StarterAutoConfiguration
from miniboot.context import ApplicationContext


@ConfigurationProperties(prefix="test.conflict.a")
@dataclass
class ConflictPropertiesA(StarterProperties):
    """冲突测试配置属性A"""

    service_name: str = "service-a"
    port: int = 8080


@ConfigurationProperties(prefix="test.conflict.b")
@dataclass
class ConflictPropertiesB(StarterProperties):
    """冲突测试配置属性B"""

    service_name: str = "service-b"
    port: int = 8081


class ConflictServiceA:
    """冲突服务A"""

    def __init__(self, properties: ConflictPropertiesA):
        self.properties = properties

    def get_name(self):
        return f"ServiceA-{self.properties.service_name}"


class ConflictServiceB:
    """冲突服务B"""

    def __init__(self, properties: ConflictPropertiesB):
        self.properties = properties

    def get_name(self):
        return f"ServiceB-{self.properties.service_name}"


class ConflictAutoConfigurationA(StarterAutoConfiguration):
    """冲突自动配置A"""

    def get_metadata(self) -> AutoConfigurationMetadata:
        return AutoConfigurationMetadata(
            name="conflict-auto-configuration-a",
            description="Conflict auto configuration A",
            priority=100,
            conflicts_with=["conflict-auto-configuration-b"],
        )

    def get_starter_name(self) -> str:
        return "conflict-starter-a"

    def get_starter_version(self) -> str:
        return "1.0.0"

    def get_starter_description(self) -> str:
        return "Conflict starter A"

    def get_configuration_properties_classes(self):
        return [ConflictPropertiesA]

    @Bean
    def conflict_service(self, properties: ConflictPropertiesA) -> ConflictServiceA:
        return ConflictServiceA(properties)

    @Bean
    def shared_service(self, properties: ConflictPropertiesA) -> str:
        """共享Bean名称，会产生冲突"""
        return f"SharedServiceA-{properties.service_name}"


class ConflictAutoConfigurationB(StarterAutoConfiguration):
    """冲突自动配置B"""

    def get_metadata(self) -> AutoConfigurationMetadata:
        return AutoConfigurationMetadata(
            name="conflict-auto-configuration-b",
            description="Conflict auto configuration B",
            priority=100,
            conflicts_with=["conflict-auto-configuration-a"],
        )

    def get_starter_name(self) -> str:
        return "conflict-starter-b"

    def get_starter_version(self) -> str:
        return "1.0.0"

    def get_starter_description(self) -> str:
        return "Conflict starter B"

    def get_configuration_properties_classes(self):
        return [ConflictPropertiesB]

    @Bean
    def conflict_service(self, properties: ConflictPropertiesB) -> ConflictServiceB:
        return ConflictServiceB(properties)

    @Bean
    def shared_service(self, properties: ConflictPropertiesB) -> str:
        """共享Bean名称，会产生冲突"""
        return f"SharedServiceB-{properties.service_name}"


class CircularDependencyConfigA(AutoConfiguration):
    """循环依赖配置A"""

    def get_metadata(self) -> AutoConfigurationMetadata:
        return AutoConfigurationMetadata(
            name="circular-dependency-config-a", description="Circular dependency config A", depends_on=["circular-dependency-config-b"]
        )

    def configure(self, context: ApplicationContext) -> None:
        pass


class CircularDependencyConfigB(AutoConfiguration):
    """循环依赖配置B"""

    def get_metadata(self) -> AutoConfigurationMetadata:
        return AutoConfigurationMetadata(
            name="circular-dependency-config-b", description="Circular dependency config B", depends_on=["circular-dependency-config-a"]
        )

    def configure(self, context: ApplicationContext) -> None:
        pass


class TestAutoConfigurationConflicts:
    """自动配置冲突处理测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)
        self.context.get_environment.return_value = Mock()
        self.context.get_bean_factory.return_value = Mock()
        self.context.contains_bean.return_value = False
        self.context.is_running.return_value = True

    def test_configuration_conflict_detection(self):
        """测试配置冲突检测"""
        registry = AutoConfigurationRegistry()

        # 注册冲突的配置
        config_a = ConflictAutoConfigurationA()
        config_b = ConflictAutoConfigurationB()

        registry.register(config_a)
        registry.register(config_b)

        # 验证冲突检测
        conflicts = registry.detect_conflicts()
        assert len(conflicts) > 0

        # 验证冲突信息
        conflict_found = False
        for conflict in conflicts:
            if "conflict-auto-configuration-a" in conflict and "conflict-auto-configuration-b" in conflict:
                conflict_found = True
                break

        assert conflict_found, "Expected conflict not detected"

    def test_circular_dependency_detection(self):
        """测试循环依赖检测"""
        registry = AutoConfigurationRegistry()

        # 注册循环依赖的配置
        config_a = CircularDependencyConfigA()
        config_b = CircularDependencyConfigB()

        registry.register(config_a)
        registry.register(config_b)

        # 验证配置被注册
        configs = registry.get_all_configurations()
        assert len(configs) == 2
        assert "circular-dependency-config-a" in configs
        assert "circular-dependency-config-b" in configs

    def test_bean_name_conflict_handling(self):
        """测试Bean名称冲突处理"""
        # 模拟环境
        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {}
        self.context.get_environment.return_value = env_mock

        # 模拟Bean工厂，检测重复注册
        bean_factory_mock = Mock()
        registered_beans = {}

        def register_singleton(name, instance):
            if name in registered_beans:
                raise RuntimeError(f"Bean with name '{name}' already exists")
            registered_beans[name] = instance

        bean_factory_mock.register_singleton.side_effect = register_singleton
        self.context.get_bean_factory.return_value = bean_factory_mock
        self.context.contains_bean.side_effect = lambda name: name in registered_beans

        # 配置A
        config_a = ConflictAutoConfigurationA()
        config_a.configure(self.context)

        # 配置B（应该产生Bean名称冲突）
        config_b = ConflictAutoConfigurationB()

        # 验证Bean名称冲突被检测到（可能在Bean方法执行时）
        try:
            config_b.configure(self.context)
            # 如果没有抛出异常，验证至少有Bean被注册
            assert len(registered_beans) > 0
        except RuntimeError as e:
            assert "already exists" in str(e)

    def test_configuration_exclusion(self):
        """测试配置排除功能"""
        from miniboot.autoconfigure.integration import AutoConfigurationIntegrator

        integrator = AutoConfigurationIntegrator(self.context)

        # 排除冲突配置
        integrator.exclude_configurations("conflict-auto-configuration-b")

        # 验证排除功能
        assert "conflict-auto-configuration-b" in integrator._excluded_configurations
        assert "conflict-auto-configuration-a" not in integrator._excluded_configurations

    def test_configuration_priority_handling(self):
        """测试配置优先级处理"""
        registry = AutoConfigurationRegistry()

        # 创建不同优先级的配置
        high_config = ConflictAutoConfigurationA()  # 使用现有的配置类
        low_config = ConflictAutoConfigurationB()  # 使用现有的配置类

        # 注册配置
        registry.register(low_config)
        registry.register(high_config)

        # 验证配置被注册
        configs = registry.get_all_configurations()
        assert len(configs) == 2
        assert "conflict-auto-configuration-a" in configs
        assert "conflict-auto-configuration-b" in configs

    def test_error_recovery_mechanisms(self):
        """测试错误恢复机制"""
        registry = AutoConfigurationRegistry()

        # 使用现有的配置类
        config_a = ConflictAutoConfigurationA()
        config_b = ConflictAutoConfigurationB()

        registry.register(config_a)
        registry.register(config_b)

        # 执行配置（应该处理错误并继续）
        results = registry.configure_all(self.context)

        # 验证配置执行
        assert len(results) == 2
        assert "conflict-auto-configuration-a" in results
        assert "conflict-auto-configuration-b" in results


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

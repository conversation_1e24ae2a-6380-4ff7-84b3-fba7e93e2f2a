#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 处理器管理器单元测试
"""

import time
import unittest

from miniboot.errors.processor import BeanProcessingError
from miniboot.processor.base import BeanPostProcessor
from miniboot.processor.manager import BeanPostProcessorManager, ProcessorConfig, ProcessorState


class MockProcessor(BeanPostProcessor):
    """模拟处理器"""

    def __init__(self, name="MockProcessor", order=100, should_fail=False):
        self.name = name
        self.order = order
        self.should_fail = should_fail
        self.before_calls = []
        self.after_calls = []

    def post_process_before_initialization(self, bean, bean_name):
        self.before_calls.append((bean, bean_name))
        if self.should_fail:
            raise RuntimeError(f"Mock error in {self.name}")
        return bean

    def post_process_after_initialization(self, bean, bean_name):
        self.after_calls.append((bean, bean_name))
        if self.should_fail:
            raise RuntimeError(f"Mock error in {self.name}")
        return bean

    def get_order(self):
        return self.order

    def supports(self, _bean, _bean_name):
        return True


class MockProcessor1(MockProcessor):
    """模拟处理器1"""

    pass


class MockProcessor2(MockProcessor):
    """模拟处理器2"""

    pass


class SlowProcessor(BeanPostProcessor):
    """慢处理器，用于测试性能监控"""

    def __init__(self, delay=0.1):
        self.delay = delay

    def post_process_before_initialization(self, bean, _bean_name):
        time.sleep(self.delay)
        return bean

    def post_process_after_initialization(self, bean, _bean_name):
        time.sleep(self.delay)
        return bean

    def get_order(self):
        return 200


class TestBeanPostProcessorManager(unittest.TestCase):
    """处理器管理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.manager = BeanPostProcessorManager()
        self.test_bean = {"name": "test"}
        self.bean_name = "testBean"

    def test_register_processor(self):
        """测试注册处理器"""
        processor = MockProcessor()
        config = ProcessorConfig(timeout_seconds=5.0)

        self.manager.register_processor(processor, config)

        # 验证处理器已注册
        self.assertEqual(self.manager.get_processor_count(), 1)

        # 验证配置已设置
        stored_config = self.manager.get_processor_config("MockProcessor")
        self.assertIsNotNone(stored_config)
        self.assertEqual(stored_config.timeout_seconds, 5.0)

        # 验证状态已设置
        state = self.manager.get_processor_state("MockProcessor")
        self.assertEqual(state, ProcessorState.ACTIVE)

    def test_register_none_processor(self):
        """测试注册None处理器"""
        with self.assertRaises(ValueError):
            self.manager.register_processor(None)

    def test_unregister_processor(self):
        """测试注销处理器"""
        processor = MockProcessor()
        self.manager.register_processor(processor)

        # 验证注册成功
        self.assertEqual(self.manager.get_processor_count(), 1)

        # 注销处理器
        success = self.manager.unregister_processor(MockProcessor)
        self.assertTrue(success)

        # 验证注销成功
        self.assertEqual(self.manager.get_processor_count(), 0)
        self.assertIsNone(self.manager.get_processor_config("MockProcessor"))
        self.assertIsNone(self.manager.get_processor_state("MockProcessor"))

    def test_enable_disable_processor(self):
        """测试启用/禁用处理器"""
        processor = MockProcessor()
        self.manager.register_processor(processor)

        # 初始状态应该是启用的
        self.assertEqual(self.manager.get_processor_state("MockProcessor"), ProcessorState.ACTIVE)

        # 禁用处理器
        success = self.manager.disable_processor("MockProcessor")
        self.assertTrue(success)
        self.assertEqual(self.manager.get_processor_state("MockProcessor"), ProcessorState.DISABLED)

        # 启用处理器
        success = self.manager.enable_processor("MockProcessor")
        self.assertTrue(success)
        self.assertEqual(self.manager.get_processor_state("MockProcessor"), ProcessorState.ACTIVE)

    def test_apply_before_initialization(self):
        """测试应用初始化前处理器"""
        processor1 = MockProcessor1("Processor1", order=100)
        processor2 = MockProcessor2("Processor2", order=50)

        self.manager.register_processor(processor1)
        self.manager.register_processor(processor2)

        # 执行处理
        result = self.manager.apply_before_initialization(self.test_bean, self.bean_name)

        # 验证结果
        self.assertEqual(result, self.test_bean)

        # 验证处理器被调用（按order顺序）
        self.assertEqual(len(processor2.before_calls), 1)
        self.assertEqual(len(processor1.before_calls), 1)
        self.assertEqual(processor2.before_calls[0], (self.test_bean, self.bean_name))
        self.assertEqual(processor1.before_calls[0], (self.test_bean, self.bean_name))

    def test_apply_after_initialization(self):
        """测试应用初始化后处理器"""
        processor1 = MockProcessor1("Processor1", order=100)
        processor2 = MockProcessor2("Processor2", order=50)

        self.manager.register_processor(processor1)
        self.manager.register_processor(processor2)

        # 执行处理
        result = self.manager.apply_after_initialization(self.test_bean, self.bean_name)

        # 验证结果
        self.assertEqual(result, self.test_bean)

        # 验证处理器被调用
        self.assertEqual(len(processor2.after_calls), 1)
        self.assertEqual(len(processor1.after_calls), 1)

    def test_disabled_processor_not_executed(self):
        """测试禁用的处理器不被执行"""
        processor = MockProcessor()
        self.manager.register_processor(processor)

        # 禁用处理器
        self.manager.disable_processor("MockProcessor")

        # 执行处理
        self.manager.apply_before_initialization(self.test_bean, self.bean_name)

        # 验证处理器没有被调用
        self.assertEqual(len(processor.before_calls), 0)

    def test_processor_metrics(self):
        """测试处理器性能指标"""
        processor = SlowProcessor(delay=0.01)  # 10ms延迟
        self.manager.register_processor(processor)

        # 执行几次处理
        for _ in range(3):
            self.manager.apply_before_initialization(self.test_bean, self.bean_name)

        # 获取指标
        metrics = self.manager.get_processor_metrics("SlowProcessor")
        self.assertIsNotNone(metrics)

        # 验证指标
        self.assertEqual(metrics.total_executions, 3)
        self.assertGreater(metrics.total_execution_time, 0)
        self.assertGreater(metrics.average_execution_time, 0)
        self.assertGreater(metrics.max_execution_time, 0)
        self.assertLess(metrics.min_execution_time, float("inf"))
        self.assertEqual(metrics.error_count, 0)
        self.assertIsNotNone(metrics.last_execution_time)

    def test_processor_error_handling(self):
        """测试处理器错误处理"""
        processor = MockProcessor(should_fail=True)
        self.manager.register_processor(processor)

        # 执行处理，应该抛出异常
        with self.assertRaises(BeanProcessingError):
            self.manager.apply_before_initialization(self.test_bean, self.bean_name)

        # 获取指标
        metrics = self.manager.get_processor_metrics("MockProcessor")
        self.assertIsNotNone(metrics)

        # 验证错误指标
        self.assertEqual(metrics.total_executions, 1)
        self.assertEqual(metrics.error_count, 1)
        self.assertIsNotNone(metrics.last_error_time)
        self.assertIsNotNone(metrics.last_error_message)

    def test_circuit_breaker(self):
        """测试熔断器功能"""
        processor = MockProcessor(should_fail=True)
        config = ProcessorConfig(circuit_breaker_enabled=True, error_threshold=2, circuit_breaker_timeout=1.0)
        self.manager.register_processor(processor, config)

        # 触发足够的错误来打开熔断器
        import contextlib

        for _ in range(3):
            with contextlib.suppress(Exception):
                self.manager.apply_before_initialization(self.test_bean, self.bean_name)

        # 验证熔断器已打开
        state = self.manager.get_processor_state("MockProcessor")
        self.assertEqual(state, ProcessorState.CIRCUIT_OPEN)

    def test_get_all_processor_metrics(self):
        """测试获取所有处理器指标"""
        processor1 = MockProcessor1("Processor1")
        processor2 = MockProcessor2("Processor2")

        self.manager.register_processor(processor1)
        self.manager.register_processor(processor2)

        # 执行处理
        self.manager.apply_before_initialization(self.test_bean, self.bean_name)

        # 获取所有指标
        all_metrics = self.manager.get_all_processor_metrics()

        # 验证指标
        self.assertEqual(len(all_metrics), 2)
        self.assertIn("MockProcessor1", all_metrics)
        self.assertIn("MockProcessor2", all_metrics)

    def test_update_processor_config(self):
        """测试更新处理器配置"""
        processor = MockProcessor()
        self.manager.register_processor(processor)

        # 更新配置
        new_config = ProcessorConfig(timeout_seconds=10.0, enabled=False)
        success = self.manager.update_processor_config("MockProcessor", new_config)
        self.assertTrue(success)

        # 验证配置已更新
        stored_config = self.manager.get_processor_config("MockProcessor")
        self.assertEqual(stored_config.timeout_seconds, 10.0)
        self.assertFalse(stored_config.enabled)

    def test_get_processors_by_state(self):
        """测试按状态获取处理器"""
        processor1 = MockProcessor1("Processor1")
        processor2 = MockProcessor2("Processor2")

        self.manager.register_processor(processor1)
        self.manager.register_processor(processor2)

        # 禁用一个处理器
        self.manager.disable_processor("MockProcessor1")

        # 获取不同状态的处理器
        active_processors = self.manager.get_processors_by_state(ProcessorState.ACTIVE)
        disabled_processors = self.manager.get_processors_by_state(ProcessorState.DISABLED)

        # 验证结果
        self.assertEqual(len(active_processors), 1)
        self.assertEqual(len(disabled_processors), 1)
        self.assertIn("MockProcessor2", active_processors)
        self.assertIn("MockProcessor1", disabled_processors)

    def test_reset_processor_metrics(self):
        """测试重置处理器指标"""
        processor = MockProcessor()
        self.manager.register_processor(processor)

        # 执行处理以生成指标
        self.manager.apply_before_initialization(self.test_bean, self.bean_name)

        # 验证有指标
        metrics = self.manager.get_processor_metrics("MockProcessor")
        self.assertEqual(metrics.total_executions, 1)

        # 重置指标
        success = self.manager.reset_processor_metrics("MockProcessor")
        self.assertTrue(success)

        # 验证指标已重置
        metrics = self.manager.get_processor_metrics("MockProcessor")
        self.assertEqual(metrics.total_executions, 0)

    def test_monitoring_enabled(self):
        """测试监控开关"""
        # 默认应该启用监控
        self.assertTrue(self.manager.is_monitoring_enabled())

        # 禁用监控
        self.manager.set_monitoring_enabled(False)
        self.assertFalse(self.manager.is_monitoring_enabled())

        # 启用监控
        self.manager.set_monitoring_enabled(True)
        self.assertTrue(self.manager.is_monitoring_enabled())

    def test_none_bean_handling(self):
        """测试None Bean的处理"""
        processor = MockProcessor()
        self.manager.register_processor(processor)

        # 处理None Bean
        result_before = self.manager.apply_before_initialization(None, self.bean_name)
        result_after = self.manager.apply_after_initialization(None, self.bean_name)

        # 验证返回None
        self.assertIsNone(result_before)
        self.assertIsNone(result_after)

        # 验证处理器没有被调用
        self.assertEqual(len(processor.before_calls), 0)
        self.assertEqual(len(processor.after_calls), 0)


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean后处理器基础接口和统一生命周期接口定义

这个模块定义了Mini-Boot框架中所有的生命周期接口,
消除重复定义,建立统一的生命周期管理体系.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Optional, Set, Type

from loguru import logger

from ..errors import ProcessorExecutionError as BeanProcessingError


class BeanPostProcessor(ABC):
    """Bean后置处理器接口 - 统一定义

    这是Mini-Boot框架的核心扩展点,允许在Bean的生命周期中
    对Bean进行自定义处理和增强.处理器在Bean初始化前后被调用,
    可以修改Bean实例、添加代理、注入依赖等.

    处理器的执行顺序由get_order()方法决定,数字越小优先级越高.

    Example:
        class MyProcessor(BeanPostProcessor):
            def post_process_before_initialization(self, bean, bean_name):
                # 在Bean初始化前的处理逻辑
                return bean

            def post_process_after_initialization(self, bean, bean_name):
                # 在Bean初始化后的处理逻辑
                return bean

            def get_order(self):
                return 100  # 执行优先级
    """

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化之前调用

        此方法在Bean的构造函数调用之后、初始化方法(如@PostConstruct)
        调用之前执行.可以用于:
        - 依赖注入
        - 属性设置
        - 代理创建
        - 验证检查

        Args:
            bean: Bean实例,已经通过构造函数创建
            bean_name: Bean的名称,通常是类名的小写形式

        Returns:
            处理后的Bean实例.可以返回原实例或新的代理实例.
            如果返回None,则Bean将被忽略.

        Raises:
            BeanProcessingException: 当处理过程中发生错误时
        """
        try:
            return self._do_post_process_before_initialization(bean, bean_name)
        except Exception as e:
            # 创建处理异常
            processing_error = BeanProcessingError(f"Failed to process bean '{bean_name}' before initialization in {self.__class__.__name__}")
            raise processing_error from e

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """在Bean初始化之后调用

        此方法在Bean的所有初始化方法(如@PostConstruct)执行完成后调用.
        可以用于:
        - AOP代理创建
        - 最终验证
        - 注册到其他系统
        - 启动后台任务

        Args:
            bean: 已完全初始化的Bean实例
            bean_name: Bean的名称

        Returns:
            处理后的Bean实例.通常用于返回代理对象.

        Raises:
            BeanProcessingException: 当处理过程中发生错误时
        """
        try:
            return self._do_post_process_after_initialization(bean, bean_name)
        except Exception as e:
            # 创建处理异常
            processing_error = BeanProcessingError(f"Failed to process bean '{bean_name}' after initialization in {self.__class__.__name__}")
            raise processing_error from e

    def _do_post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """实际的Bean初始化前处理逻辑

        子类应该重写此方法而不是post_process_before_initialization,
        以便自动获得异常处理和性能监控功能.

        Args:
            bean: 要处理的Bean实例
            bean_name: Bean的名称

        Returns:
            处理后的Bean实例,可以是原实例或新的代理实例
        """
        return bean

    def _do_post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """实际的Bean初始化后处理逻辑

        子类应该重写此方法而不是post_process_after_initialization,
        以便自动获得异常处理和性能监控功能.

        Args:
            bean: 要处理的Bean实例
            bean_name: Bean的名称

        Returns:
            处理后的Bean实例,可以是原实例或新的代理实例
        """
        return bean

    @abstractmethod
    def get_order(self) -> int:
        """获取处理器的执行顺序

        处理器按照order值从小到大的顺序执行.相同order值的处理器
        执行顺序不确定.

        常用的order值约定:
        - 0-99: 框架核心处理器(依赖注入、配置绑定等)
        - 100-199: 业务处理器
        - 200-299: AOP和代理处理器
        - 300+: 后置处理器(监控、日志等)

        Returns:
            执行顺序,数字越小优先级越高
        """
        pass

    def supports(self, bean: Any, bean_name: str) -> bool:
        """检查此处理器是否支持处理指定的Bean

        默认实现返回True,表示处理所有Bean.子类可以重写此方法
        来实现选择性处理.

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True表示支持处理此Bean,False表示跳过
        """
        # 默认实现:检查bean不为None且bean_name是有效字符串
        return bean is not None and isinstance(bean_name, str) and len(bean_name) > 0


class InitializingBean(ABC):
    """初始化Bean接口

    实现此接口的Bean在属性设置完成后会调用after_properties_set方法.
    这是Spring Boot风格的初始化回调接口.
    """

    @abstractmethod
    def after_properties_set(self) -> None:
        """在属性设置完成后调用

        此方法在Bean的所有属性设置完成后调用,
        可以在此方法中进行初始化逻辑.
        """
        pass


class DisposableBean(ABC):
    """可销毁Bean接口

    实现此接口的Bean在销毁时会调用destroy方法.
    这是Spring Boot风格的销毁回调接口.
    """

    @abstractmethod
    def destroy(self) -> None:
        """销毁Bean时调用

        此方法在Bean销毁时调用,
        可以在此方法中进行清理逻辑.
        """
        pass


class BeanNameAware(ABC):
    """Bean名称感知接口

    实现此接口的Bean会在初始化时获得自己的Bean名称.
    """

    @abstractmethod
    def set_bean_name(self, bean_name: str) -> None:
        """设置Bean名称

        Args:
            bean_name: Bean名称
        """
        pass


class BeanFactoryAware(ABC):
    """Bean工厂感知接口

    实现此接口的Bean会在初始化时获得Bean工厂的引用.
    """

    @abstractmethod
    def set_bean_factory(self, bean_factory: Any) -> None:
        """设置Bean工厂

        Args:
            bean_factory: Bean工厂实例
        """
        pass


class ApplicationContextAware(ABC):
    """应用上下文感知接口

    实现此接口的Bean会在初始化时获得应用上下文的引用.
    """

    @abstractmethod
    def set_application_context(self, application_context: Any) -> None:
        """设置应用上下文

        Args:
            application_context: 应用上下文实例
        """
        pass


class Lifecycle(ABC):
    """生命周期接口

    实现此接口的Bean可以接收启动和停止通知.
    """

    @abstractmethod
    def start(self) -> None:
        """启动Bean"""
        pass

    @abstractmethod
    def stop(self) -> None:
        """停止Bean"""
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查Bean是否正在运行

        Returns:
            bool: 如果Bean正在运行返回True
        """
        pass


class SmartLifecycle(Lifecycle):
    """智能生命周期接口

    扩展Lifecycle接口,提供更精细的生命周期控制.
    """

    def get_phase(self) -> int:
        """获取生命周期阶段

        Returns:
            int: 阶段值,数字越小越早启动,越晚停止
        """
        return 0

    def is_auto_startup(self) -> bool:
        """是否自动启动

        Returns:
            bool: 如果应该自动启动返回True
        """
        return True

    def stop(self, callback: Optional[Any] = None) -> None:
        """异步停止Bean

        Args:
            callback: 停止完成后的回调函数
        """
        self.stop()
        if callback:
            callback()


class OrderedBeanPostProcessor(BeanPostProcessor):
    """带有固定执行顺序的Bean后处理器基类

    提供了一个便捷的基类,允许在构造时指定执行顺序.
    子类仍需要实现抽象方法.
    """

    def __init__(self, order: int = 0):
        """初始化有序处理器

        Args:
            order: 执行顺序,数字越小优先级越高
        """
        self._order = order

    def get_order(self) -> int:
        """返回构造时指定的执行顺序"""
        return self._order


# 处理器执行顺序常量
class ProcessorOrder:
    """处理器执行顺序常量定义"""

    # 核心框架处理器
    DEPENDENCY_INJECTION_PROCESSOR = 10  # 依赖注入处理器
    VALUE_INJECTION_PROCESSOR = 20  # 值注入处理器
    AWARE_PROCESSOR = 30  # 感知接口处理器

    # 业务处理器
    VALIDATION_PROCESSOR = 100  # 验证处理器
    CONFIGURATION_PROCESSOR = 110  # 配置处理器

    # AOP和代理处理器
    AOP_PROCESSOR = 200  # AOP处理器
    PROXY_PROCESSOR = 210  # 代理处理器

    # 生命周期处理器
    LIFECYCLE_PROCESSOR = 300  # 生命周期处理器

    # 后置处理器
    MONITORING_PROCESSOR = 400  # 监控处理器
    LOGGING_PROCESSOR = 410  # 日志处理器

    # 最低优先级
    LOWEST_PRECEDENCE = 1000  # 最低优先级


class LifecycleProcessor:
    """生命周期处理器

    管理所有实现Lifecycle接口的Bean的生命周期.
    负责在应用启动和关闭时协调所有生命周期Bean的启动和停止.
    """

    def __init__(self):
        """初始化生命周期处理器"""
        self._lifecycle_beans: dict[str, Lifecycle] = {}
        self._running = False

    def add_lifecycle_bean(self, bean_name: str, bean: Lifecycle) -> None:
        """添加生命周期Bean

        Args:
            bean_name: Bean名称
            bean: 生命周期Bean实例
        """
        self._lifecycle_beans[bean_name] = bean

    def remove_lifecycle_bean(self, bean_name: str) -> None:
        """移除生命周期Bean

        Args:
            bean_name: Bean名称
        """
        self._lifecycle_beans.pop(bean_name, None)

    def start(self) -> None:
        """启动所有生命周期Bean"""
        if self._running:
            return

        # 按阶段排序启动
        sorted_beans = self._get_sorted_lifecycle_beans()

        for _, bean in sorted_beans:
            try:
                if isinstance(bean, SmartLifecycle):
                    if bean.is_auto_startup() and not bean.is_running():
                        bean.start()
                elif not bean.is_running():
                    bean.start()
            except Exception:
                # 忽略启动异常,继续启动其他Bean
                continue

        self._running = True

    def stop(self) -> None:
        """停止所有生命周期Bean"""
        if not self._running:
            return

        # 按阶段倒序停止
        sorted_beans = self._get_sorted_lifecycle_beans(reverse=True)

        for _, bean in sorted_beans:
            try:
                if bean.is_running():
                    bean.stop()
            except Exception:
                # 忽略停止异常,继续停止其他Bean
                continue

        self._running = False

    def is_running(self) -> bool:
        """检查生命周期处理器是否正在运行

        Returns:
            bool: 如果正在运行返回True
        """
        return self._running

    def _get_sorted_lifecycle_beans(self, reverse: bool = False) -> list[tuple[str, Lifecycle]]:
        """获取按阶段排序的生命周期Bean

        Args:
            reverse: 是否倒序排列

        Returns:
            list[tuple[str, Lifecycle]]: 排序后的Bean列表
        """

        def get_phase(item):
            bean_name, bean = item
            if isinstance(bean, SmartLifecycle):
                return bean.get_phase()
            return 0

        items = list(self._lifecycle_beans.items())
        return sorted(items, key=get_phase, reverse=reverse)

    def get_lifecycle_beans(self) -> dict[str, Lifecycle]:
        """获取所有生命周期Bean

        Returns:
            dict[str, Lifecycle]: 生命周期Bean字典
        """
        return self._lifecycle_beans.copy()

    def __str__(self) -> str:
        """字符串表示"""
        bean_count = len(self._lifecycle_beans)
        return f"LifecycleProcessor(beans={bean_count}, running={self._running})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        bean_names = list(self._lifecycle_beans.keys())
        return f"LifecycleProcessor(bean_names={bean_names}, running={self._running})"


# 导出所有接口和类
# ========== 抽象注解处理器基类 ==========


class AbstractAnnotationProcessor(BeanPostProcessor, ABC):
    """抽象注解处理器基类

    封装所有注解处理器的通用逻辑,消除重复代码.
    使用模板方法模式定义处理流程,子类只需实现具体的注解处理逻辑.

    通用功能:
    - 处理状态管理:避免重复处理同一个Bean
    - 注解缓存:提高注解检查性能
    - 统一异常处理:标准化错误处理和日志记录
    - 模板方法:定义标准的处理流程

    子类只需实现:
    - _process_before(): 前置处理逻辑
    - _process_after(): 后置处理逻辑
    - _get_annotation_type(): 目标注解类型
    """

    def __init__(self):
        """初始化抽象注解处理器"""
        self._processed: Set[str] = set()  # 已处理的Bean名称
        self._annotation_cache: Dict[Type, bool] = {}  # 注解检查缓存

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """Bean初始化前处理 - 模板方法

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        if not self._should_run(bean, bean_name):
            return bean

        try:
            return self._process_before(bean, bean_name)
        except Exception as e:
            self._handle_error(e, bean_name, "before")

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """Bean初始化后处理 - 模板方法

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        if not self._should_run(bean, bean_name):
            return bean

        try:
            return self._process_after(bean, bean_name)
        except Exception as e:
            self._handle_error(e, bean_name, "after")

    # ========== 抽象方法 - 子类必须实现 ==========

    @abstractmethod
    def _process_before(self, bean: Any, bean_name: str) -> Any:
        """前置处理逻辑 - 子类实现

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        pass

    @abstractmethod
    def _process_after(self, bean: Any, bean_name: str) -> Any:
        """后置处理逻辑 - 子类实现

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例
        """
        pass

    @abstractmethod
    def _get_annotation_type(self) -> str:
        """获取目标注解类型 - 子类实现

        Returns:
            注解类型名称,如 "Autowired", "Value", "EventListener"
        """
        pass

    # ========== 通用方法 - 统一实现 ==========

    def _should_run(self, bean: Any, bean_name: str) -> bool:
        """检查是否应该处理此Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True表示应该处理,False表示跳过
        """
        if bean is None:
            return False

        if bean_name in self._processed:
            return False

        # 检查Bean类是否有目标注解
        return self._has_annotations(bean.__class__)

    def _has_annotations(self, cls: Type) -> bool:
        """检查类是否有目标注解 - 带缓存

        Args:
            cls: 要检查的类

        Returns:
            True表示有目标注解
        """
        # 检查缓存
        if cls in self._annotation_cache:
            return self._annotation_cache[cls]

        # 执行检查
        result = self._check_annotations(cls)

        # 缓存结果
        self._annotation_cache[cls] = result

        return result

    def _check_annotations(self, cls: Type) -> bool:
        """检查类是否有目标注解 - 实际检查逻辑

        Args:
            cls: 要检查的类

        Returns:
            True表示有目标注解
        """
        anno_type = self._get_annotation_type()

        # 根据注解类型检查对应的属性
        if anno_type == "Autowired":
            return self._has_autowired_annotations(cls)
        elif anno_type == "Value":
            return self._has_value_annotations(cls)
        elif anno_type == "EventListener":
            return self._has_event_listener_annotations(cls)
        elif anno_type == "Scheduled":
            return self._has_scheduled_annotations(cls)
        else:
            # 通用检查
            return self._has_generic_annotations(cls, anno_type)

    def _has_autowired_annotations(self, cls: Type) -> bool:
        """检查是否有@Autowired注解"""
        # 检查字段
        for attr_name in dir(cls):
            if attr_name.startswith("_"):
                continue
            try:
                attr = getattr(cls, attr_name)
                if hasattr(attr, "__is_autowired__"):
                    return True
            except (AttributeError, TypeError):
                continue

        # 检查方法
        for method_name in dir(cls):
            if method_name.startswith("_"):
                continue
            try:
                method = getattr(cls, method_name)
                if callable(method) and hasattr(method, "__is_autowired__"):
                    return True
            except (AttributeError, TypeError):
                continue

        return False

    def _has_value_annotations(self, cls: Type) -> bool:
        """检查是否有@Value注解"""
        # 检查字段和方法的@Value注解
        for attr_name in dir(cls):
            if attr_name.startswith("_"):
                continue
            try:
                attr = getattr(cls, attr_name)
                if hasattr(attr, "__is_value__"):
                    return True
            except (AttributeError, TypeError):
                continue
        return False

    def _has_event_listener_annotations(self, cls: Type) -> bool:
        """检查是否有@EventListener注解"""
        for method_name in dir(cls):
            if method_name.startswith("_"):
                continue
            try:
                method = getattr(cls, method_name)
                if callable(method) and hasattr(method, "__is_event_listener__"):
                    return True
            except (AttributeError, TypeError):
                continue
        return False

    def _has_scheduled_annotations(self, cls: Type) -> bool:
        """检查是否有@Scheduled注解"""
        for method_name in dir(cls):
            if method_name.startswith("_"):
                continue
            try:
                method = getattr(cls, method_name)
                if callable(method) and hasattr(method, "__is_scheduled__"):
                    return True
            except (AttributeError, TypeError):
                continue
        return False

    def _has_generic_annotations(self, cls: Type, anno_type: str) -> bool:
        """通用注解检查"""
        attr_name = f"__is_{anno_type.lower()}__"
        return hasattr(cls, attr_name)

    def _handle_error(self, error: Exception, bean_name: str, phase: str) -> None:
        """统一异常处理

        Args:
            error: 原始异常
            bean_name: Bean名称
            phase: 处理阶段 ("before" 或 "after")

        Raises:
            BeanProcessingError: 包装后的异常
        """
        anno_type = self._get_annotation_type()
        processor_name = self.__class__.__name__

        error_msg = f"Failed to process @{anno_type} annotations for bean '{bean_name}' in {phase} phase"

        logger.error(f"{error_msg}: {error}")

        raise BeanProcessingError(
            error_msg,
            bean_name=bean_name,
            processor_name=processor_name,
            cause=error,
        ) from error

    def _mark_processed(self, bean_name: str) -> None:
        """标记Bean为已处理

        Args:
            bean_name: Bean名称
        """
        self._processed.add(bean_name)

    def _is_processed(self, bean_name: str) -> bool:
        """检查Bean是否已处理

        Args:
            bean_name: Bean名称

        Returns:
            True表示已处理
        """
        return bean_name in self._processed

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存统计信息

        Returns:
            缓存统计信息
        """
        return {
            "processed_beans": len(self._processed),
            "annotation_cache_size": len(self._annotation_cache),
            "annotation_type": self._get_annotation_type(),
        }

    def clear_cache(self) -> None:
        """清理缓存"""
        self._annotation_cache.clear()
        logger.debug(f"Cleared annotation cache for {self.__class__.__name__}")

    def supports(self, bean: Any, bean_name: str) -> bool:
        """检查是否支持处理指定的Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True表示支持处理此Bean
        """
        return self._should_run(bean, bean_name)

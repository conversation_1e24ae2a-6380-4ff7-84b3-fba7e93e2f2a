#!/usr/bin/env python
"""
* @author: cz
* @description: 组件扫描器配置化测试

测试组件扫描器的配置化功能，包括@ComponentScan注解和自动扫描。
"""

import unittest

from miniboot.annotations import (Component, ComponentScan, ComponentScanner,
                                  Configuration, MiniBootApplication, Service)


# 测试用的组件类
@Component
class TestComponent:
    pass


@Service
class TestService:
    pass


# 测试用的配置类
@Configuration
@ComponentScan(base_packages=["tests.unit.annotations"])
class TestConfig:
    pass


@Configuration
@ComponentScan(base_packages=["tests.unit.annotations"], include_filters=["*Component*"], exclude_filters=["*Test*"])
class FilteredConfig:
    pass


@Configuration
@ComponentScan(base_packages=["miniboot.annotations.core"])
class CoreConfig:
    pass


@MiniBootApplication
class TestApplication:
    pass


@MiniBootApplication(base_packages=["tests.unit.annotations"])
class ConfiguredApplication:
    pass


class TestScannerConfig(unittest.TestCase):
    """组件扫描器配置化测试类"""

    def test_scan_from_config(self):
        """测试从配置类扫描"""
        scanner = ComponentScanner()

        # 从配置类扫描
        result = scanner.scan_from_config(TestConfig)

        # 验证扫描结果
        self.assertIsNotNone(result)
        self.assertEqual(result.config_class, "TestConfig")
        self.assertIsNotNone(result.scan_metadata)

        # 验证扫描的包
        self.assertIn("tests.unit.annotations", result.scan_metadata.base_packages)

        print(f"从配置类扫描到 {len(result.scanned_modules)} 个模块")
        print(f"组件总数: {result.get_total_components()}")

    def test_scan_from_config_with_filters(self):
        """测试带过滤器的配置类扫描"""
        scanner = ComponentScanner()

        # 从带过滤器的配置类扫描
        result = scanner.scan_from_config(FilteredConfig)

        # 验证扫描结果
        self.assertIsNotNone(result)
        self.assertEqual(result.config_class, "FilteredConfig")
        self.assertIsNotNone(result.scan_metadata)

        # 验证过滤器配置
        self.assertIn("*Component*", result.scan_metadata.include_filters)
        self.assertIn("*Test*", result.scan_metadata.exclude_filters)

        print(f"带过滤器扫描到 {len(result.scanned_modules)} 个模块")

    def test_scan_from_config_error(self):
        """测试从无@ComponentScan注解的类扫描"""
        scanner = ComponentScanner()

        # 尝试从没有@ComponentScan注解的类扫描
        with self.assertRaises(ValueError) as context:
            scanner.scan_from_config(TestComponent)

        self.assertIn("没有@ComponentScan注解", str(context.exception))

    def test_auto_scan_with_component_scan(self):
        """测试自动扫描带@ComponentScan的类"""
        scanner = ComponentScanner()

        # 自动扫描配置类
        result = scanner.auto_scan(TestConfig)

        # 验证扫描结果
        self.assertIsNotNone(result)
        self.assertEqual(result.config_class, "TestConfig")

        print(f"自动扫描配置类到 {len(result.scanned_modules)} 个模块")

    def test_auto_scan_without_component_scan(self):
        """测试自动扫描没有@ComponentScan的类"""
        scanner = ComponentScanner()

        # 自动扫描普通类
        result = scanner.auto_scan(TestComponent)

        # 验证扫描结果
        self.assertIsNotNone(result)
        self.assertIsNone(result.config_class)  # 没有配置类

        print(f"自动扫描普通类到 {len(result.scanned_modules)} 个模块")

    def test_auto_scan_miniboot_application(self):
        """测试自动扫描@MiniBootApplication类"""
        scanner = ComponentScanner()

        # 自动扫描应用类
        result = scanner.auto_scan(TestApplication)

        # 验证扫描结果
        self.assertIsNotNone(result)

        print(f"自动扫描应用类到 {len(result.scanned_modules)} 个模块")

    def test_auto_scan_configured_application(self):
        """测试自动扫描配置化的@MiniBootApplication类"""
        scanner = ComponentScanner()

        # 自动扫描配置化应用类
        result = scanner.auto_scan(ConfiguredApplication)

        # 验证扫描结果
        self.assertIsNotNone(result)
        self.assertIsNotNone(result.config_class)

        print(f"自动扫描配置化应用类到 {len(result.scanned_modules)} 个模块")

    def test_scan_from_config_function(self):
        """测试scan_from_config方法"""
        # 使用ComponentScanner扫描
        scanner = ComponentScanner()
        result = scanner.scan_from_config(TestConfig)

        # 验证扫描结果
        self.assertIsNotNone(result)
        self.assertEqual(result.config_class, "TestConfig")

        print(f"ComponentScanner扫描到 {len(result.scanned_modules)} 个模块")

    def test_auto_scan_function(self):
        """测试auto_scan方法"""
        # 使用ComponentScanner自动扫描
        scanner = ComponentScanner()
        result = scanner.auto_scan(TestConfig)

        # 验证扫描结果
        self.assertIsNotNone(result)
        self.assertEqual(result.config_class, "TestConfig")

        print(f"ComponentScanner自动扫描到 {len(result.scanned_modules)} 个模块")

    def test_scan_metadata_preservation(self):
        """测试扫描元数据保存"""
        scanner = ComponentScanner()

        # 从配置类扫描
        result = scanner.scan_from_config(FilteredConfig)

        # 验证元数据
        self.assertIsNotNone(result.scan_metadata)
        self.assertEqual(result.scan_metadata.base_packages, ["tests.unit.annotations"])
        self.assertEqual(result.scan_metadata.include_filters, ["*Component*"])
        self.assertEqual(result.scan_metadata.exclude_filters, ["*Test*"])
        self.assertFalse(result.scan_metadata.lazy_init)

    def test_filter_restoration(self):
        """测试过滤器恢复"""
        scanner = ComponentScanner()

        # 设置初始过滤器
        original_filter = scanner.scan_filter
        original_filter.add_include_pattern("original")

        # 从配置类扫描（会临时改变过滤器）
        scanner.scan_from_config(TestConfig)

        # 验证过滤器已恢复
        self.assertEqual(scanner.scan_filter, original_filter)
        self.assertIn("original", scanner.scan_filter.include_patterns)

    def test_multiple_config_scans(self):
        """测试多个配置类扫描"""
        scanner = ComponentScanner()

        # 扫描第一个配置类
        result1 = scanner.scan_from_config(TestConfig)

        # 扫描第二个配置类
        result2 = scanner.scan_from_config(CoreConfig)

        # 验证两次扫描结果不同
        self.assertNotEqual(result1.config_class, result2.config_class)
        self.assertNotEqual(result1.scan_metadata.base_packages, result2.scan_metadata.base_packages)

        print(f"第一次扫描: {result1.config_class}, 包: {result1.scan_metadata.base_packages}")
        print(f"第二次扫描: {result2.config_class}, 包: {result2.scan_metadata.base_packages}")


if __name__ == "__main__":
    unittest.main(verbosity=2)

#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 聊天室示例
"""

from datetime import datetime

from miniboot.annotations import Component
from miniboot.starters.websocket.annotations import (
    WebSocketController,
    WebSocketOnConnect,
    WebSocketOnDisconnect,
    WebSocketOnMessage,
    WebSocketOnError,
)
from miniboot.starters.websocket.session import WebSocketSession


@Component
@WebSocketController("/chat")
class ChatController:
    """WebSocket 聊天室控制器示例

    演示如何使用 WebSocket Starter 创建一个简单的聊天室应用.
    支持用户加入/离开、发送消息、私聊等功能.
    """

    def __init__(self):
        """初始化聊天室控制器"""
        # 在线用户集合
        self.online_users: set[str] = set()

        # 用户会话映射
        self.user_sessions: dict[str, WebSocketSession] = {}

        # 聊天室列表
        self.rooms: dict[str, set[str]] = {"general": set()}

    @WebSocketOnConnect()
    async def on_connect(self, session: WebSocketSession) -> None:
        """处理用户连接事件

        Args:
            session: WebSocket 会话
        """
        user_id = session.get_user_id() or f"anonymous_{session.get_id()[:8]}"
        session.set_user_id(user_id)

        # 添加到在线用户列表
        self.online_users.add(user_id)
        self.user_sessions[user_id] = session

        # 加入默认聊天室
        self.rooms["general"].add(user_id)
        session.set_attribute("current_room", "general")

        # 发送欢迎消息
        await session.send_json(
            {
                "type": "system",
                "message": f"欢迎 {user_id} 加入聊天室!",
                "timestamp": datetime.now().isoformat(),
                "online_users": list(self.online_users),
                "current_room": "general",
            }
        )

        # 通知其他用户
        await self._broadcast_to_room(
            "general",
            {
                "type": "user_join",
                "user_id": user_id,
                "message": f"{user_id} 加入了聊天室",
                "timestamp": datetime.now().isoformat(),
                "online_count": len(self.online_users),
            },
            exclude_user=user_id,
        )

    @WebSocketOnDisconnect()
    async def on_disconnect(self, session: WebSocketSession) -> None:
        """处理用户断开连接事件

        Args:
            session: WebSocket 会话
        """
        user_id = session.get_user_id()
        if not user_id:
            return

        # 从在线用户列表移除
        self.online_users.discard(user_id)
        self.user_sessions.pop(user_id, None)

        # 从所有聊天室移除
        current_room = session.get_attribute("current_room", "general")
        for _room_name, room_users in self.rooms.items():
            room_users.discard(user_id)

        # 通知其他用户
        await self._broadcast_to_room(
            current_room,
            {
                "type": "user_leave",
                "user_id": user_id,
                "message": f"{user_id} 离开了聊天室",
                "timestamp": datetime.now().isoformat(),
                "online_count": len(self.online_users),
            },
        )

    @WebSocketOnMessage("json")
    async def on_json_message(self, session: WebSocketSession, message: dict) -> None:
        """处理 JSON 消息

        Args:
            session: WebSocket 会话
            message: JSON 消息
        """
        message_type = message.get("type")
        session.get_user_id()

        if message_type == "chat":
            await self._handle_chat_message(session, message)
        elif message_type == "private":
            await self._handle_private_message(session, message)
        elif message_type == "join_room":
            await self._handle_join_room(session, message)
        elif message_type == "leave_room":
            await self._handle_leave_room(session, message)
        elif message_type == "ping":
            await session.send_json({"type": "pong", "timestamp": datetime.now().isoformat()})
        elif message_type == "get_users":
            await session.send_json({"type": "users_list", "users": list(self.online_users), "timestamp": datetime.now().isoformat()})
        elif message_type == "get_rooms":
            await session.send_json(
                {"type": "rooms_list", "rooms": {name: len(users) for name, users in self.rooms.items()}, "timestamp": datetime.now().isoformat()}
            )
        else:
            await session.send_json({"type": "error", "message": f"未知的消息类型: {message_type}", "timestamp": datetime.now().isoformat()})

    @WebSocketOnMessage("text")
    async def on_text_message(self, session: WebSocketSession, message: str) -> None:
        """处理文本消息

        Args:
            session: WebSocket 会话
            message: 文本消息
        """
        user_id = session.get_user_id()
        current_room = session.get_attribute("current_room", "general")

        if message.startswith("/"):
            # 处理命令
            await self._handle_command(session, message)
        else:
            # 普通聊天消息
            await self._broadcast_to_room(
                current_room, {"type": "chat", "user_id": user_id, "message": message, "room": current_room, "timestamp": datetime.now().isoformat()}
            )

    @WebSocketOnError()
    async def on_error(self, session: WebSocketSession, error: Exception) -> None:
        """处理错误事件

        Args:
            session: WebSocket 会话
            error: 发生的错误
        """
        user_id = session.get_user_id()
        print(f"WebSocket 错误 - 用户: {user_id}, 错误: {error}")

        try:
            await session.send_json({"type": "error", "message": "服务器发生错误,请稍后重试", "timestamp": datetime.now().isoformat()})
        except Exception:
            # 如果发送错误消息也失败,则忽略
            pass

    async def _handle_chat_message(self, session: WebSocketSession, message: dict) -> None:
        """处理聊天消息

        Args:
            session: WebSocket 会话
            message: 消息内容
        """
        user_id = session.get_user_id()
        content = message.get("content", "")
        room = message.get("room") or session.get_attribute("current_room", "general")

        if not content.strip():
            await session.send_json({"type": "error", "message": "消息内容不能为空", "timestamp": datetime.now().isoformat()})
            return

        # 广播消息到指定房间
        await self._broadcast_to_room(
            room, {"type": "chat", "user_id": user_id, "message": content, "room": room, "timestamp": datetime.now().isoformat()}
        )

    async def _handle_private_message(self, session: WebSocketSession, message: dict) -> None:
        """处理私聊消息

        Args:
            session: WebSocket 会话
            message: 消息内容
        """
        user_id = session.get_user_id()
        target_user = message.get("target_user")
        content = message.get("content", "")

        if not target_user or not content.strip():
            await session.send_json({"type": "error", "message": "私聊消息需要指定目标用户和消息内容", "timestamp": datetime.now().isoformat()})
            return

        if target_user not in self.online_users:
            await session.send_json({"type": "error", "message": f"用户 {target_user} 不在线", "timestamp": datetime.now().isoformat()})
            return

        # 发送给目标用户
        target_session = self.user_sessions.get(target_user)
        if target_session:
            await target_session.send_json({"type": "private", "from_user": user_id, "message": content, "timestamp": datetime.now().isoformat()})

        # 确认消息已发送
        await session.send_json({"type": "private_sent", "to_user": target_user, "message": content, "timestamp": datetime.now().isoformat()})

    async def _handle_join_room(self, session: WebSocketSession, message: dict) -> None:
        """处理加入房间

        Args:
            session: WebSocket 会话
            message: 消息内容
        """
        user_id = session.get_user_id()
        room_name = message.get("room_name", "").strip()

        if not room_name:
            await session.send_json({"type": "error", "message": "房间名称不能为空", "timestamp": datetime.now().isoformat()})
            return

        # 离开当前房间
        current_room = session.get_attribute("current_room", "general")
        if current_room in self.rooms:
            self.rooms[current_room].discard(user_id)

        # 加入新房间
        if room_name not in self.rooms:
            self.rooms[room_name] = set()

        self.rooms[room_name].add(user_id)
        session.set_attribute("current_room", room_name)

        # 通知用户
        await session.send_json(
            {"type": "room_joined", "room_name": room_name, "message": f"已加入房间: {room_name}", "timestamp": datetime.now().isoformat()}
        )

        # 通知房间内其他用户
        await self._broadcast_to_room(
            room_name,
            {
                "type": "user_join_room",
                "user_id": user_id,
                "room_name": room_name,
                "message": f"{user_id} 加入了房间 {room_name}",
                "timestamp": datetime.now().isoformat(),
            },
            exclude_user=user_id,
        )

    async def _handle_leave_room(self, session: WebSocketSession, message: dict) -> None:
        """处理离开房间

        Args:
            session: WebSocket 会话
            message: 消息内容
        """
        user_id = session.get_user_id()
        current_room = session.get_attribute("current_room", "general")

        # 从当前房间移除
        if current_room in self.rooms:
            self.rooms[current_room].discard(user_id)

        # 加入默认房间
        self.rooms["general"].add(user_id)
        session.set_attribute("current_room", "general")

        # 通知用户
        await session.send_json(
            {
                "type": "room_left",
                "room_name": current_room,
                "message": f"已离开房间: {current_room},回到默认房间",
                "timestamp": datetime.now().isoformat(),
            }
        )

    async def _handle_command(self, session: WebSocketSession, command: str) -> None:
        """处理命令

        Args:
            session: WebSocket 会话
            command: 命令字符串
        """
        session.get_user_id()
        parts = command.strip().split()
        cmd = parts[0].lower()

        if cmd == "/help":
            await session.send_json(
                {
                    "type": "system",
                    "message": """
可用命令:
/help - 显示帮助信息
/users - 显示在线用户列表
/rooms - 显示房间列表
/join <房间名> - 加入指定房间
/leave - 离开当前房间
/private <用户名> <消息> - 发送私聊消息
                """.strip(),
                    "timestamp": datetime.now().isoformat(),
                }
            )

        elif cmd == "/users":
            await session.send_json(
                {
                    "type": "system",
                    "message": f"在线用户 ({len(self.online_users)}): {', '.join(self.online_users)}",
                    "timestamp": datetime.now().isoformat(),
                }
            )

        elif cmd == "/rooms":
            room_info = []
            for room_name, room_users in self.rooms.items():
                room_info.append(f"{room_name} ({len(room_users)} 人)")

            await session.send_json({"type": "system", "message": f"房间列表: {', '.join(room_info)}", "timestamp": datetime.now().isoformat()})

        elif cmd == "/join" and len(parts) > 1:
            room_name = parts[1]
            await self._handle_join_room(session, {"room_name": room_name})

        elif cmd == "/leave":
            await self._handle_leave_room(session, {})

        elif cmd == "/private" and len(parts) > 2:
            target_user = parts[1]
            content = " ".join(parts[2:])
            await self._handle_private_message(session, {"target_user": target_user, "content": content})

        else:
            await session.send_json({"type": "error", "message": f"未知命令: {cmd},输入 /help 查看帮助", "timestamp": datetime.now().isoformat()})

    async def _broadcast_to_room(self, room_name: str, message: dict, exclude_user: str = None) -> None:
        """向房间内的所有用户广播消息

        Args:
            room_name: 房间名称
            message: 要广播的消息
            exclude_user: 要排除的用户 ID
        """
        if room_name not in self.rooms:
            return

        room_users = self.rooms[room_name]
        for user_id in room_users:
            if exclude_user and user_id == exclude_user:
                continue

            session = self.user_sessions.get(user_id)
            if session and session.is_active():
                try:
                    await session.send_json(message)
                except Exception as e:
                    print(f"发送消息给用户 {user_id} 失败: {e}")


# 使用示例
if __name__ == "__main__":
    print("WebSocket 聊天室示例")
    print("请在 Mini-Boot 应用中使用此控制器")
    print("WebSocket 连接地址: ws://localhost:8080/chat")

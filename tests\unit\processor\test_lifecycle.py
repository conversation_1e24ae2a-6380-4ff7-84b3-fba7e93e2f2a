#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 生命周期处理器单元测试 - 简化版本
"""

import unittest

from miniboot.processor.lifecycle import LifecycleAnnotationProcessor
from miniboot.processor.base import ProcessorOrder

# 删除不再需要的Mock类


class TestBeanWithLifecycle:
    """测试Bean - 有生命周期方法"""

    def __init__(self):
        self.init_called = False
        self.cleanup_called = False

    def init(self):
        """@PostConstruct方法"""
        self.init_called = True

    def cleanup(self):
        """@PreDestroy方法"""
        self.cleanup_called = True


# 为测试Bean添加生命周期注解
TestBeanWithLifecycle.init.__is_post_construct__ = True
TestBeanWithLifecycle.init.__post_construct_metadata__ = type("PostConstructMetadata", (), {"method": "init"})()

TestBeanWithLifecycle.cleanup.__is_pre_destroy__ = True
TestBeanWithLifecycle.cleanup.__pre_destroy_metadata__ = type("PreDestroyMetadata", (), {"method": "cleanup"})()


class TestBeanWithMultipleLifecycle:
    """测试Bean - 多个生命周期方法"""

    def __init__(self):
        self.init1_called = False
        self.init2_called = False
        self.cleanup1_called = False
        self.cleanup2_called = False

    def init1(self):
        """第一个@PostConstruct方法"""
        self.init1_called = True

    def init2(self):
        """第二个@PostConstruct方法"""
        self.init2_called = True

    def cleanup1(self):
        """第一个@PreDestroy方法"""
        self.cleanup1_called = True

    def cleanup2(self):
        """第二个@PreDestroy方法"""
        self.cleanup2_called = True


# 为多个生命周期方法添加注解
TestBeanWithMultipleLifecycle.init1.__is_post_construct__ = True
TestBeanWithMultipleLifecycle.init1.__post_construct_metadata__ = type("PostConstructMetadata", (), {"method": "init1"})()

TestBeanWithMultipleLifecycle.init2.__is_post_construct__ = True
TestBeanWithMultipleLifecycle.init2.__post_construct_metadata__ = type("PostConstructMetadata", (), {"method": "init2"})()

TestBeanWithMultipleLifecycle.cleanup1.__is_pre_destroy__ = True
TestBeanWithMultipleLifecycle.cleanup1.__pre_destroy_metadata__ = type("PreDestroyMetadata", (), {"method": "cleanup1"})()

TestBeanWithMultipleLifecycle.cleanup2.__is_pre_destroy__ = True
TestBeanWithMultipleLifecycle.cleanup2.__pre_destroy_metadata__ = type("PreDestroyMetadata", (), {"method": "cleanup2"})()


class TestBeanWithoutLifecycle:
    """测试Bean - 无生命周期注解"""

    def __init__(self):
        self.value = "test"


class TestLifecycleProcessor(unittest.TestCase):
    """生命周期处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.processor = LifecycleAnnotationProcessor()

    def test_processor_order(self):
        """测试处理器执行顺序"""
        self.assertEqual(self.processor.get_order(), ProcessorOrder.LIFECYCLE_PROCESSOR)

    def test_supports_bean_with_lifecycle(self):
        """测试支持有生命周期注解的Bean"""
        bean = TestBeanWithLifecycle()
        self.assertTrue(self.processor.supports(bean, "testBean"))

    def test_supports_bean_without_lifecycle(self):
        """测试不支持无生命周期注解的Bean"""
        bean = TestBeanWithoutLifecycle()
        self.assertFalse(self.processor.supports(bean, "testBean"))

    def test_supports_none_bean(self):
        """测试不支持None Bean"""
        self.assertFalse(self.processor.supports(None, "testBean"))

    def test_post_construct_invocation(self):
        """测试@PostConstruct方法调用"""
        bean = TestBeanWithLifecycle()

        # 执行初始化后处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)
        self.assertTrue(bean.init_called)

    def test_pre_destroy_invocation(self):
        """测试@PreDestroy方法调用"""
        bean = TestBeanWithLifecycle()

        # 执行销毁处理
        self.processor.destroy_bean(bean, "testBean")

        # 验证结果
        self.assertTrue(bean.cleanup_called)

    def test_multiple_lifecycle_methods(self):
        """测试多个生命周期方法"""
        bean = TestBeanWithMultipleLifecycle()

        # 执行初始化后处理
        self.processor.post_process_after_initialization(bean, "testBean")

        # 验证@PostConstruct方法都被调用
        self.assertTrue(bean.init1_called)
        self.assertTrue(bean.init2_called)

        # 执行销毁处理
        self.processor.destroy_bean(bean, "testBean")

        # 验证@PreDestroy方法都被调用
        self.assertTrue(bean.cleanup1_called)
        self.assertTrue(bean.cleanup2_called)

    def test_post_process_before_initialization(self):
        """测试初始化前处理（应该直接返回原Bean）"""
        bean = TestBeanWithLifecycle()

        result = self.processor.post_process_before_initialization(bean, "testBean")

        self.assertIs(result, bean)
        # @PostConstruct方法不应该在初始化前被调用
        self.assertFalse(bean.init_called)

    def test_already_processed_bean(self):
        """测试已处理的Bean不会重复处理"""
        bean = TestBeanWithLifecycle()

        # 第一次处理
        self.processor.post_process_after_initialization(bean, "testBean")
        self.assertTrue(bean.init_called)

        # 重置状态
        bean.init_called = False

        # 第二次处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证没有重复调用
        self.assertFalse(bean.init_called)
        self.assertIs(result, bean)

    def test_already_destroyed_bean(self):
        """测试已销毁的Bean不会重复销毁"""
        bean = TestBeanWithLifecycle()

        # 第一次销毁
        self.processor.destroy_bean(bean, "testBean")
        self.assertTrue(bean.cleanup_called)

        # 重置状态
        bean.cleanup_called = False

        # 第二次销毁
        self.processor.destroy_bean(bean, "testBean")

        # 验证没有重复调用
        self.assertFalse(bean.cleanup_called)

    def test_none_bean_handling(self):
        """测试None Bean的处理"""
        result = self.processor.post_process_after_initialization(None, "testBean")
        self.assertIsNone(result)

        # 销毁None Bean不应该抛出异常
        self.processor.destroy_bean(None, "testBean")

    def test_lifecycle_method_exception_handling(self):
        """测试生命周期方法异常处理"""

        class FailingLifecycleMethod:
            def __init__(self):
                self.__is_post_construct__ = True
                self.__post_construct_metadata__ = type("PostConstructMetadata", (), {"method": "failing_init"})()

            def __call__(self, _instance):
                raise Exception("Lifecycle method failed")

        class TestBeanWithFailingLifecycle:
            def __init__(self):
                pass

            failing_init = FailingLifecycleMethod()

        bean = TestBeanWithFailingLifecycle()

        # 生命周期方法异常不应该阻止Bean创建
        result = self.processor.post_process_after_initialization(bean, "testBean")
        self.assertIs(result, bean)

    def test_managed_beans_count(self):
        """测试管理的Bean数量"""
        initial_count = self.processor.get_managed_beans_count()

        bean1 = TestBeanWithLifecycle()
        bean2 = TestBeanWithLifecycle()

        self.processor.post_process_after_initialization(bean1, "testBean1")
        self.processor.post_process_after_initialization(bean2, "testBean2")

        self.assertEqual(self.processor.get_managed_beans_count(), initial_count + 2)

    def test_destroyed_beans_count(self):
        """测试已销毁的Bean数量"""
        initial_count = self.processor.get_destroyed_beans_count()

        bean1 = TestBeanWithLifecycle()
        bean2 = TestBeanWithLifecycle()

        self.processor.destroy_bean(bean1, "testBean1")
        self.processor.destroy_bean(bean2, "testBean2")

        self.assertEqual(self.processor.get_destroyed_beans_count(), initial_count + 2)


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Bean Actuator 集成自动配置演示

展示如何使用 BeanActuatorAutoConfiguration 自动配置 Actuator 与 Bean 容器系统的集成，
包括 Bean 管理、依赖注入监控、Bean 生命周期管理等功能。

功能特性:
- 自动检测 Bean 容器环境
- 自动创建 Bean 相关端点
- Bean 管理和监控
- 依赖注入状态监控
- Bean 生命周期管理
- Bean 配置信息展示
"""

import asyncio
import time

# DEPRECATED: from miniboot.actuator.autoconfigure.bean_actuator_auto_configuration import (
#     BeanActuatorAutoConfiguration,
#     BeanIntegrationStatus,
#     BeanContainerEndpoint,
#     BeanDependencyEndpoint
# )
# 注意：原 Bean Actuator 自动配置已迁移，请使用新的配置类


class MockApplicationContext:
    """模拟应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = MockEnvironment()
        self._bean_factory = MockBeanFactory()

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class MockEnvironment:
    """模拟环境配置"""

    def get_property(self, key, default=None):
        # 演示配置
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.bean.enabled": True,
            "actuator.security.enabled": False,
        }
        return config.get(key, default)


class MockBeanDefinition:
    """模拟 Bean 定义"""

    def __init__(self, name, bean_class, scope="singleton"):
        self.bean_name = name
        self.bean_class = bean_class
        self.scope = MockScope(scope)
        self.constructor_arguments = []
        self.property_values = []


class MockScope:
    """模拟作用域"""

    def __init__(self, value):
        self.value = value

    def is_singleton(self):
        return self.value == "singleton"

    def is_prototype(self):
        return self.value == "prototype"


class MockBeanRegistry:
    """模拟 Bean 注册表"""

    def __init__(self):
        self._definitions = {}
        self._create_demo_beans()

    def _create_demo_beans(self):
        """创建演示 Bean 定义"""
        # 创建一些演示 Bean
        class DemoService:
            pass

        class DemoRepository:
            pass

        class DemoController:
            pass

        class DemoConfig:
            pass

        # 注册 Bean 定义
        self._definitions["demoService"] = MockBeanDefinition("demoService", DemoService, "singleton")
        self._definitions["demoRepository"] = MockBeanDefinition("demoRepository", DemoRepository, "singleton")
        self._definitions["demoController"] = MockBeanDefinition("demoController", DemoController, "prototype")
        self._definitions["demoConfig"] = MockBeanDefinition("demoConfig", DemoConfig, "singleton")

    def count(self):
        return len(self._definitions)

    def names(self):
        return list(self._definitions.keys())

    def singleton_names(self):
        return [name for name, definition in self._definitions.items()
                if definition.scope.is_singleton()]

    def has_definition(self, name):
        return name in self._definitions

    def get_definition(self, name):
        if name not in self._definitions:
            raise KeyError(f"No bean definition found for name: {name}")
        return self._definitions[name]


class MockCache:
    """模拟三级缓存"""

    def __init__(self):
        self._singleton_objects = {
            "demoService": object(),
            "demoRepository": object(),
            "demoConfig": object()
        }
        self._early_singleton_objects = {}
        self._singleton_factories = {}


class MockBeanFactory:
    """模拟 Bean 工厂"""

    def __init__(self):
        self._registry = MockBeanRegistry()
        self._cache = MockCache()
        self._enable_thread_safety = True


async def test_bean_actuator_auto_configuration():
    """测试 Bean Actuator 自动配置"""
    print("🧪 Testing Bean Actuator Auto Configuration...")

    try:
        # 1. 创建模拟应用上下文
        app_context = MockApplicationContext()

        # 2. 创建 Bean 自动配置实例
        bean_config = BeanActuatorAutoConfiguration(app_context)
        print(f"📊 Initial status: {bean_config.status.value}")

        # 3. 执行自动配置
        print("🔧 Executing Bean Actuator auto configuration...")
        await bean_config.configure()

        # 4. 检查配置结果
        print(f"📊 Final status: {bean_config.status.value}")

        # 5. 获取集成状态
        status_info = bean_config.get_integration_status()
        print("\n📈 Integration Status:")
        for key, value in status_info.items():
            if key != "metrics":
                print(f"  - {key}: {value}")

        print("📊 Metrics:")
        for key, value in status_info["metrics"].items():
            print(f"  - {key}: {value}")

        # 6. 测试端点功能
        await test_bean_endpoints(bean_config)

        # 7. 刷新指标
        print("\n🔄 Refreshing metrics...")
        await bean_config.refresh_metrics()
        updated_status = bean_config.get_integration_status()
        print(f"📊 Updated metrics:")
        for key, value in updated_status["metrics"].items():
            print(f"  - {key}: {value}")

        print("\n✅ Bean Actuator Auto Configuration test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_bean_endpoints(bean_config):
    """测试 Bean 端点功能"""
    print("\n🔌 Testing Bean Endpoints...")

    try:
        app_context = bean_config.get_application_context()

        # 测试 Bean 容器端点
        container_endpoint = BeanContainerEndpoint(app_context)

        print("📦 Testing BeanContainer endpoint:")
        container_info = container_endpoint.invoke(container_endpoint.operations()[0].operation_type)
        print(f"  - Factory available: {container_info.get('factory', {}).get('available', False)}")
        print(f"  - Registry available: {container_info.get('registry', {}).get('available', False)}")
        print(f"  - Bean count: {container_info.get('registry', {}).get('bean_count', 0)}")
        print(f"  - Cache levels: {container_info.get('cache', {}).get('levels', {})}")

        # 测试缓存清理
        print("\n🧹 Testing cache clear:")
        clear_result = container_endpoint._clear_cache()
        print(f"  - Clear success: {clear_result.get('success', False)}")
        print(f"  - Cleared items: {clear_result.get('cleared', {})}")

        # 测试 Bean 依赖关系端点
        dependency_endpoint = BeanDependencyEndpoint(app_context)

        print("\n🔗 Testing BeanDependency endpoint:")
        dependencies_info = dependency_endpoint.invoke(dependency_endpoint.operations()[0].operation_type)
        dependencies = dependencies_info.get('dependencies', {})
        print(f"  - Total beans analyzed: {len(dependencies)}")

        stats = dependencies_info.get('statistics', {})
        print(f"  - Beans with dependencies: {stats.get('beans_with_dependencies', 0)}")
        print(f"  - Total dependencies: {stats.get('total_dependencies', 0)}")

        # 测试单个 Bean 依赖
        print("\n🔍 Testing single bean dependency:")
        single_bean_info = dependency_endpoint._get_bean_dependencies("demoService")
        bean_deps = single_bean_info.get('dependencies', {})
        print(f"  - Bean: {single_bean_info.get('bean_name', 'unknown')}")
        print(f"  - Total dependencies: {bean_deps.get('total_dependencies', 0)}")
        print(f"  - Bean class: {bean_deps.get('bean_class', 'unknown')}")

        print("✅ Bean endpoints tested successfully")

    except Exception as e:
        print(f"❌ Endpoint testing failed: {e}")


async def test_integration_components():
    """测试集成组件"""
    print("\n🧪 Testing Integration Components...")

    try:
        # 测试状态枚举
        print("📊 Integration Status Values:")
        for status in BeanIntegrationStatus:
            print(f"  - {status.name}: {status.value}")

        # 测试导入的组件
        # DEPRECATED: from miniboot.actuator.autoconfigure.bean_actuator_auto_configuration import BeanMetrics

        print("✅ All components imported successfully!")
        print("  - BeanActuatorAutoConfiguration: ✓")
        print("  - BeanContainerEndpoint: ✓")
        print("  - BeanDependencyEndpoint: ✓")
        print("  - BeanIntegrationStatus: ✓")
        print("  - BeanMetrics: ✓")

        return True

    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Starting Bean Actuator Integration Tests...\n")

    # 测试组件导入
    component_test = await test_integration_components()

    # 测试自动配置
    config_test = await test_bean_actuator_auto_configuration()

    # 总结
    print(f"\n📋 Test Summary:")
    print(f"  - Component Import Test: {'✅ PASSED' if component_test else '❌ FAILED'}")
    print(f"  - Auto Configuration Test: {'✅ PASSED' if config_test else '❌ FAILED'}")

    if component_test and config_test:
        print("\n🎉 All tests passed! Bean Actuator integration is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

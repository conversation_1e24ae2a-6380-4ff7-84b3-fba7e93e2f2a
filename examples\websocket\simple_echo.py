#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 简单回显示例
"""

from miniboot.annotations import Component
from miniboot.starters.websocket.annotations import (
    WebSocketController,
    WebSocketOnConnect,
    WebSocketOnDisconnect,
    WebSocketOnMessage,
    WebSocketOnError,
)
from miniboot.starters.websocket.session import WebSocketSession


@Component
@WebSocketController("/echo")
class EchoController:
    """WebSocket 回显控制器示例

    演示最简单的 WebSocket 使用方式,接收消息并原样返回.
    """

    @WebSocketOnConnect()
    async def on_connect(self, session: WebSocketSession) -> None:
        """处理连接建立事件

        Args:
            session: WebSocket 会话
        """
        print(f"客户端连接: {session.get_id()}")
        await session.send_text("欢迎使用 WebSocket 回显服务!发送任何消息,我会原样返回给你.")

    @WebSocketOnDisconnect()
    async def on_disconnect(self, session: WebSocketSession) -> None:
        """处理连接断开事件

        Args:
            session: WebSocket 会话
        """
        print(f"客户端断开连接: {session.get_id()}")

    @WebSocketOnMessage("text")
    async def on_text_message(self, session: WebSocketSession, message: str) -> None:
        """处理文本消息

        Args:
            session: WebSocket 会话
            message: 文本消息
        """
        print(f"收到文本消息: {message}")
        await session.send_text(f"回显: {message}")

    @WebSocketOnMessage("json")
    async def on_json_message(self, session: WebSocketSession, message: dict) -> None:
        """处理 JSON 消息

        Args:
            session: WebSocket 会话
            message: JSON 消息
        """
        print(f"收到 JSON 消息: {message}")
        await session.send_json({"type": "echo", "original": message, "timestamp": "2024-01-01T00:00:00Z"})

    @WebSocketOnMessage("binary")
    async def on_binary_message(self, session: WebSocketSession, data: bytes) -> None:
        """处理二进制消息

        Args:
            session: WebSocket 会话
            data: 二进制数据
        """
        print(f"收到二进制消息,长度: {len(data)} 字节")
        await session.send_bytes(data)  # 原样返回

    @WebSocketOnError()
    async def on_error(self, session: WebSocketSession, error: Exception) -> None:
        """处理错误事件

        Args:
            session: WebSocket 会话
            error: 发生的错误
        """
        print(f"WebSocket 错误: {error}")
        try:
            await session.send_text(f"发生错误: {str(error)}")
        except Exception:
            # 如果发送错误消息也失败,则忽略
            pass


# 使用示例
if __name__ == "__main__":
    print("WebSocket 回显服务示例")
    print("请在 Mini-Boot 应用中使用此控制器")
    print("WebSocket 连接地址: ws://localhost:8080/echo")
    print()
    print("测试方法:")
    print("1. 发送文本消息: 'Hello World'")
    print("2. 发送 JSON 消息: {'message': 'Hello', 'type': 'greeting'}")
    print("3. 发送二进制数据")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web注解系统测试案例

测试@Controller、@RestController、@RequestMapping等Web注解的完整功能,
包括注解处理、元数据提取、路由映射等核心功能测试.

主要测试内容:
- Web注解的基本功能验证
- 注解元数据的正确提取
- 路径参数解析和验证
- 注解处理器的集成测试
- 边界条件和异常场景测试
"""

import unittest
from typing import Any, Dict, List, Optional
from unittest.mock import Mock, patch

from miniboot.annotations.web import (
    Controller, RestController, RequestMapping, GetMapping, PostMapping,
    PutMapping, DeleteMapping, PatchMapping, RequestParam, PathVariable,
    RequestBody, RequestHeader, ResponseBody, ResponseStatus,
    is_controller, is_rest_controller, controller_path, has_route, route_info,
    _extract_path_params
)
from miniboot.processor.web import WebAnnotationProcessor


class WebAnnotationsBasicTestCase(unittest.TestCase):
    """Web注解基本功能测试类"""

    def test_controller_annotation_basic_functionality(self):
        """测试@Controller注解基本功能"""
        # Arrange & Act
        @Controller("/api/users")
        class UserController:
            def get_users(self):
                return {"users": []}

        # Assert
        self.assertTrue(hasattr(UserController, "__controller__"))
        self.assertTrue(UserController.__controller__)
        self.assertEqual(UserController.__request_mapping__, "/api/users")
        self.assertTrue(is_controller(UserController))
        self.assertFalse(is_rest_controller(UserController))
        self.assertEqual(controller_path(UserController), "/api/users")

    def test_rest_controller_annotation_basic_functionality(self):
        """测试@RestController注解基本功能"""
        # Arrange & Act
        @RestController("/api/products")
        class ProductController:
            def get_products(self):
                return {"products": []}

        # Assert
        self.assertTrue(hasattr(ProductController, "__rest_controller__"))
        self.assertTrue(ProductController.__rest_controller__)
        self.assertEqual(ProductController.__request_mapping__, "/api/products")
        self.assertTrue(is_controller(ProductController))
        self.assertTrue(is_rest_controller(ProductController))
        self.assertEqual(controller_path(ProductController), "/api/products")

    def test_controller_annotation_without_path(self):
        """测试@Controller注解不指定路径的情况"""
        # Arrange & Act
        @Controller()
        class DefaultController:
            pass

        # Assert
        self.assertTrue(is_controller(DefaultController))
        self.assertEqual(controller_path(DefaultController), "")

    def test_request_mapping_annotation_basic_functionality(self):
        """测试@RequestMapping注解基本功能"""
        # Arrange & Act
        class TestController:
            @RequestMapping("/users/{user_id}", method="GET", produces="application/json")
            def get_user(self, user_id: int):
                return {"user_id": user_id}

        # Assert
        method = TestController.get_user
        self.assertTrue(has_route(method))

        route_info_data = route_info(method)
        self.assertIsNotNone(route_info_data)
        self.assertEqual(route_info_data["path"], "/users/{user_id}")
        self.assertEqual(route_info_data["method"], "GET")
        self.assertEqual(route_info_data["produces"], "application/json")
        self.assertEqual(route_info_data["path_params"], ["user_id"])

    def test_http_method_mapping_annotations(self):
        """测试HTTP方法映射注解"""
        # Arrange & Act
        class HttpMethodController:
            @GetMapping("/users")
            def get_users(self):
                pass

            @PostMapping("/users", consumes="application/json")
            def create_user(self, user_data: dict):
                pass

            @PutMapping("/users/{user_id}")
            def update_user(self, user_id: int, user_data: dict):
                pass

            @DeleteMapping("/users/{user_id}")
            def delete_user(self, user_id: int):
                pass

            @PatchMapping("/users/{user_id}")
            def patch_user(self, user_id: int, patch_data: dict):
                pass

        # Assert
        # 测试GET映射
        get_route = route_info(HttpMethodController.get_users)
        self.assertEqual(get_route["method"], "GET")
        self.assertEqual(get_route["path"], "/users")

        # 测试POST映射
        post_route = route_info(HttpMethodController.create_user)
        self.assertEqual(post_route["method"], "POST")
        self.assertEqual(post_route["consumes"], "application/json")

        # 测试PUT映射
        put_route = route_info(HttpMethodController.update_user)
        self.assertEqual(put_route["method"], "PUT")
        self.assertEqual(put_route["path_params"], ["user_id"])

        # 测试DELETE映射
        delete_route = route_info(HttpMethodController.delete_user)
        self.assertEqual(delete_route["method"], "DELETE")

        # 测试PATCH映射
        patch_route = route_info(HttpMethodController.patch_user)
        self.assertEqual(patch_route["method"], "PATCH")


class WebAnnotationsPathParamsTestCase(unittest.TestCase):
    """Web注解路径参数测试类"""

    def test_extract_path_params_single_param(self):
        """测试提取单个路径参数"""
        # Arrange
        path = "/users/{user_id}"

        # Act
        params = _extract_path_params(path)

        # Assert
        self.assertEqual(params, ["user_id"])

    def test_extract_path_params_multiple_params(self):
        """测试提取多个路径参数"""
        # Arrange
        path = "/users/{user_id}/posts/{post_id}/comments/{comment_id}"

        # Act
        params = _extract_path_params(path)

        # Assert
        self.assertEqual(params, ["user_id", "post_id", "comment_id"])

    def test_extract_path_params_no_params(self):
        """测试无路径参数的情况"""
        # Arrange
        path = "/users/list"

        # Act
        params = _extract_path_params(path)

        # Assert
        self.assertEqual(params, [])

    def test_extract_path_params_empty_path(self):
        """测试空路径的情况"""
        # Arrange
        path = ""

        # Act
        params = _extract_path_params(path)

        # Assert
        self.assertEqual(params, [])

    def test_extract_path_params_complex_patterns(self):
        """测试复杂路径参数模式"""
        # Arrange
        test_cases = [
            ("/api/v1/users/{user_id}", ["user_id"]),
            ("/api/{version}/users/{user_id}", ["version", "user_id"]),
            ("/{category}/{subcategory}/{item_id}", ["category", "subcategory", "item_id"]),
            ("/static/files", []),
            ("/users/{user_id}/profile", ["user_id"]),
        ]

        for path, expected_params in test_cases:
            with self.subTest(path=path):
                # Act
                params = _extract_path_params(path)

                # Assert
                self.assertEqual(params, expected_params)


class WebAnnotationsParameterBindingTestCase(unittest.TestCase):
    """Web注解参数绑定测试类"""

    def test_request_param_annotation(self):
        """测试@RequestParam注解"""
        # Arrange & Act
        class ParamController:
            @RequestParam(name="page", required=True, default_value=1, description="页码")
            @GetMapping("/users")
            def get_users(self, page: int = 1):
                pass

        # Assert
        method = ParamController.get_users
        self.assertTrue(hasattr(method, "__request_params__"))
        params = method.__request_params__
        self.assertEqual(len(params), 1)

        param = params[0]
        self.assertEqual(param["name"], "page")
        self.assertTrue(param["required"])
        self.assertEqual(param["default_value"], 1)
        self.assertEqual(param["description"], "页码")

    def test_multiple_request_params(self):
        """测试多个@RequestParam注解"""
        # Arrange & Act
        class MultiParamController:
            @RequestParam(name="page", required=True, default_value=1)
            @RequestParam(name="size", required=False, default_value=10)
            @RequestParam(name="sort", required=False, default_value="id")
            @GetMapping("/users")
            def get_users(self, page: int = 1, size: int = 10, sort: str = "id"):
                pass

        # Assert
        method = MultiParamController.get_users
        params = method.__request_params__
        self.assertEqual(len(params), 3)

        # 验证参数数量和内容（顺序可能不确定）
        param_names = [p["name"] for p in params]
        self.assertEqual(len(param_names), 3)
        self.assertIn("page", param_names)
        self.assertIn("size", param_names)
        self.assertIn("sort", param_names)


class WebAnnotationsProcessorTestCase(unittest.TestCase):
    """Web注解处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.mock_web_app = Mock()

        # 创建一个具体的WebAnnotationProcessor子类来测试
        class TestWebAnnotationProcessor(WebAnnotationProcessor):
            def get_order(self) -> int:
                return 100

        self.processor = TestWebAnnotationProcessor(self.mock_web_app)

    def test_processor_supports_controller(self):
        """测试处理器支持控制器检测"""
        # Arrange
        @Controller("/api")
        class TestController:
            pass

        controller_instance = TestController()

        # Act
        supports = self.processor.supports(controller_instance, "TestController")

        # Assert
        self.assertTrue(supports)

    def test_processor_supports_rest_controller(self):
        """测试处理器支持REST控制器检测"""
        # Arrange
        @RestController("/api")
        class TestRestController:
            pass

        controller_instance = TestRestController()

        # Act
        supports = self.processor.supports(controller_instance, "TestRestController")

        # Assert
        self.assertTrue(supports)

    def test_processor_does_not_support_regular_class(self):
        """测试处理器不支持普通类"""
        # Arrange
        class RegularClass:
            pass

        instance = RegularClass()

        # Act
        supports = self.processor.supports(instance, "RegularClass")

        # Assert
        self.assertFalse(supports)

    def test_processor_post_process_before_initialization(self):
        """测试处理器初始化前处理"""
        # Arrange
        @Controller("/api")
        class TestController:
            pass

        controller_instance = TestController()

        # Act
        result = self.processor.post_process_before_initialization(
            controller_instance, "TestController"
        )

        # Assert
        self.assertIs(result, controller_instance)

    def test_processor_post_process_after_initialization(self):
        """测试处理器初始化后处理"""
        # Arrange
        @Controller("/api")
        class TestController:
            @GetMapping("/users")
            def get_users(self):
                pass

        controller_instance = TestController()

        # Act
        result = self.processor.post_process_after_initialization(
            controller_instance, "TestController"
        )

        # Assert
        self.assertIs(result, controller_instance)
        # 验证路由映射被提取
        self.assertGreater(len(self.processor.route_mappings), 0)


class WebAnnotationsEdgeCasesTestCase(unittest.TestCase):
    """Web注解边界条件测试类"""

    def test_controller_with_none_path(self):
        """测试控制器路径为None的情况"""
        # Arrange & Act
        @Controller(None)
        class NonePathController:
            pass

        # Assert
        self.assertTrue(is_controller(NonePathController))
        self.assertEqual(controller_path(NonePathController), None)

    def test_request_mapping_with_empty_path(self):
        """测试请求映射路径为空的情况"""
        # Arrange & Act
        class EmptyPathController:
            @RequestMapping("", method="GET")
            def root_handler(self):
                pass

        # Assert
        route_data = route_info(EmptyPathController.root_handler)
        self.assertEqual(route_data["path"], "")
        self.assertEqual(route_data["path_params"], [])

    def test_request_mapping_with_invalid_method(self):
        """测试请求映射使用无效HTTP方法"""
        # Arrange & Act
        class InvalidMethodController:
            @RequestMapping("/test", method="INVALID")
            def invalid_method_handler(self):
                pass

        # Assert
        route_data = route_info(InvalidMethodController.invalid_method_handler)
        self.assertEqual(route_data["method"], "INVALID")  # 注解不验证方法有效性

    def test_method_without_route_annotation(self):
        """测试没有路由注解的方法"""
        # Arrange & Act
        @Controller("/api")
        class ControllerWithoutRoute:
            def regular_method(self):
                pass

        # Assert
        self.assertFalse(has_route(ControllerWithoutRoute.regular_method))
        self.assertIsNone(route_info(ControllerWithoutRoute.regular_method))

    def test_class_without_controller_annotation(self):
        """测试没有控制器注解的类"""
        # Arrange & Act
        class RegularClass:
            @GetMapping("/test")
            def test_method(self):
                pass

        # Assert
        self.assertFalse(is_controller(RegularClass))
        self.assertFalse(is_rest_controller(RegularClass))
        self.assertEqual(controller_path(RegularClass), "")


class WebAnnotationsIntegrationTestCase(unittest.TestCase):
    """Web注解集成测试类"""

    def test_complete_controller_with_all_annotations(self):
        """测试包含所有注解的完整控制器"""
        # Arrange & Act
        @RestController("/api/v1/users")
        class CompleteUserController:
            @GetMapping("")
            def list_users(self):
                return {"users": []}

            @GetMapping("/{user_id}")
            def get_user(self, user_id: int):
                return {"user_id": user_id}

            @PostMapping("", consumes="application/json")
            def create_user(self, user_data: dict):
                return {"created": True}

            @PutMapping("/{user_id}", consumes="application/json")
            def update_user(self, user_id: int, user_data: dict):
                return {"updated": True}

            @DeleteMapping("/{user_id}")
            def delete_user(self, user_id: int):
                return {"deleted": True}

        # Assert
        # 验证控制器注解
        self.assertTrue(is_rest_controller(CompleteUserController))
        self.assertEqual(controller_path(CompleteUserController), "/api/v1/users")

        # 验证所有方法都有路由注解
        methods = [
            CompleteUserController.list_users,
            CompleteUserController.get_user,
            CompleteUserController.create_user,
            CompleteUserController.update_user,
            CompleteUserController.delete_user,
        ]

        for method in methods:
            self.assertTrue(has_route(method))
            route_data = route_info(method)
            self.assertIsNotNone(route_data)
            self.assertIn("method", route_data)
            self.assertIn("path", route_data)

    def test_nested_controller_path_resolution(self):
        """测试嵌套控制器路径解析"""
        # Arrange & Act
        @Controller("/api/v1")
        class ApiV1Controller:
            @GetMapping("/users/{user_id}/posts/{post_id}")
            def get_user_post(self, user_id: int, post_id: int):
                pass

        # Assert
        route_data = route_info(ApiV1Controller.get_user_post)
        self.assertEqual(route_data["path"], "/users/{user_id}/posts/{post_id}")
        self.assertEqual(route_data["path_params"], ["user_id", "post_id"])

    def test_annotation_metadata_consistency(self):
        """测试注解元数据一致性"""
        # Arrange & Act
        @RestController("/api/test")
        class MetadataTestController:
            @GetMapping("/items/{item_id}", produces="application/json")
            @RequestParam(name="include_details", required=False, default_value=False)
            def get_item(self, item_id: str, include_details: bool = False):
                pass

        # Assert
        # 验证控制器元数据
        self.assertTrue(is_rest_controller(MetadataTestController))
        self.assertEqual(controller_path(MetadataTestController), "/api/test")

        # 验证方法路由元数据
        route_data = route_info(MetadataTestController.get_item)
        self.assertEqual(route_data["method"], "GET")
        self.assertEqual(route_data["path"], "/items/{item_id}")
        self.assertEqual(route_data["produces"], "application/json")
        self.assertEqual(route_data["path_params"], ["item_id"])

        # 验证参数元数据
        self.assertTrue(hasattr(MetadataTestController.get_item, "__request_params__"))
        params = MetadataTestController.get_item.__request_params__
        self.assertEqual(len(params), 1)
        self.assertEqual(params[0]["name"], "include_details")
        self.assertFalse(params[0]["required"])


if __name__ == "__main__":
    unittest.main(verbosity=2)

#!/usr/bin/env python
"""
降级管理器

提供服务降级策略管理和自动降级控制功能.

主要功能:
- 多级降级策略管理 (功能降级、性能降级、容量降级)
- 自动降级触发和恢复机制
- 手动降级控制和管理
- 降级状态监控和指标收集
- 降级策略配置和动态调整
"""

import asyncio
import time
from enum import Enum
from typing import Dict, List, Optional, Any, Callable, Union
from dataclasses import dataclass, field
from threading import Lock
from loguru import logger

from ..properties import BackpressureConfig


class DegradationLevel(Enum):
    """降级级别"""

    NONE = "none"  # 无降级
    LIGHT = "light"  # 轻度降级
    MODERATE = "moderate"  # 中度降级
    HEAVY = "heavy"  # 重度降级
    CRITICAL = "critical"  # 严重降级


class DegradationType(Enum):
    """降级类型"""

    FEATURE = "feature"  # 功能降级
    PERFORMANCE = "performance"  # 性能降级
    CAPACITY = "capacity"  # 容量降级
    DATA = "data"  # 数据降级


@dataclass
class DegradationRule:
    """降级规则"""

    name: str
    degradation_type: DegradationType
    level: DegradationLevel
    condition: str  # 触发条件描述
    action: Callable[[], Any]  # 降级动作
    recovery_action: Optional[Callable[[], Any]] = None  # 恢复动作
    priority: int = 0  # 优先级,数字越大优先级越高
    enabled: bool = True
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class DegradationStatus:
    """降级状态"""

    rule_name: str
    degradation_type: DegradationType
    level: DegradationLevel
    is_active: bool
    activated_at: Optional[float] = None
    deactivated_at: Optional[float] = None
    activation_count: int = 0
    total_duration: float = 0.0
    last_error: Optional[str] = None


class DegradationManager:
    """降级管理器

    管理服务降级策略,支持自动和手动降级控制.
    提供多级降级策略、状态监控和指标收集功能.
    """

    def __init__(self, config: Optional[BackpressureConfig] = None):
        """初始化降级管理器

        Args:
            config: 背压控制配置
        """
        self.config = config

        # 降级规则管理
        self._rules: Dict[str, DegradationRule] = {}
        self._active_degradations: Dict[str, DegradationStatus] = {}

        # 状态管理
        self._current_level = DegradationLevel.NONE
        self._is_enabled = True

        # 统计信息
        self._total_activations = 0
        self._total_recoveries = 0
        self._degradation_history: List[Dict[str, Any]] = []

        # 回调函数
        self._degradation_callbacks: List[Callable[[str, bool], None]] = []

        # 线程安全锁
        self._lock = Lock()

        logger.info("DegradationManager initialized")

    def add_rule(self, rule: DegradationRule) -> None:
        """添加降级规则

        Args:
            rule: 降级规则
        """
        with self._lock:
            self._rules[rule.name] = rule
            logger.info(f"Added degradation rule: {rule.name} ({rule.degradation_type.value}, {rule.level.value})")

    def remove_rule(self, rule_name: str) -> bool:
        """移除降级规则

        Args:
            rule_name: 规则名称

        Returns:
            是否成功移除
        """
        with self._lock:
            if rule_name in self._rules:
                # 如果规则正在激活,先停用
                if rule_name in self._active_degradations:
                    self._deactivate_rule(rule_name)

                del self._rules[rule_name]
                logger.info(f"Removed degradation rule: {rule_name}")
                return True
            return False

    def activate_degradation(self, rule_name: str, reason: str = "Manual activation") -> bool:
        """激活降级规则

        Args:
            rule_name: 规则名称
            reason: 激活原因

        Returns:
            是否成功激活
        """
        if not self._is_enabled:
            logger.warning("DegradationManager is disabled")
            return False

        with self._lock:
            if rule_name not in self._rules:
                logger.error(f"Degradation rule not found: {rule_name}")
                return False

            rule = self._rules[rule_name]
            if not rule.enabled:
                logger.warning(f"Degradation rule is disabled: {rule_name}")
                return False

            if rule_name in self._active_degradations:
                logger.warning(f"Degradation rule already active: {rule_name}")
                return False

            try:
                # 执行降级动作
                if rule.action:
                    result = rule.action()
                    if asyncio.iscoroutine(result):
                        # 如果是协程,需要在事件循环中执行
                        logger.warning(f"Async degradation action not supported in sync context: {rule_name}")

                # 记录激活状态
                status = DegradationStatus(
                    rule_name=rule_name,
                    degradation_type=rule.degradation_type,
                    level=rule.level,
                    is_active=True,
                    activated_at=time.time(),
                    activation_count=1,
                )

                self._active_degradations[rule_name] = status
                self._total_activations += 1

                # 更新当前降级级别
                self._update_current_level()

                # 记录历史
                self._record_degradation_event(rule_name, "activated", reason)

                # 通知回调
                self._notify_degradation_change(rule_name, True)

                logger.info(f"Activated degradation rule: {rule_name}, reason: {reason}")
                return True

            except Exception as e:
                logger.error(f"Failed to activate degradation rule {rule_name}: {e}")
                return False

    def deactivate_degradation(self, rule_name: str, reason: str = "Manual deactivation") -> bool:
        """停用降级规则

        Args:
            rule_name: 规则名称
            reason: 停用原因

        Returns:
            是否成功停用
        """
        with self._lock:
            return self._deactivate_rule(rule_name, reason)

    def _deactivate_rule(self, rule_name: str, reason: str = "System deactivation") -> bool:
        """内部停用规则方法"""
        if rule_name not in self._active_degradations:
            logger.warning(f"Degradation rule not active: {rule_name}")
            return False

        if rule_name not in self._rules:
            logger.error(f"Degradation rule not found: {rule_name}")
            return False

        try:
            rule = self._rules[rule_name]
            status = self._active_degradations[rule_name]

            # 执行恢复动作
            if rule.recovery_action:
                result = rule.recovery_action()
                if asyncio.iscoroutine(result):
                    logger.warning(f"Async recovery action not supported in sync context: {rule_name}")

            # 更新状态
            status.is_active = False
            status.deactivated_at = time.time()
            if status.activated_at:
                status.total_duration += status.deactivated_at - status.activated_at

            # 移除活跃状态
            del self._active_degradations[rule_name]
            self._total_recoveries += 1

            # 更新当前降级级别
            self._update_current_level()

            # 记录历史
            self._record_degradation_event(rule_name, "deactivated", reason)

            # 通知回调
            self._notify_degradation_change(rule_name, False)

            logger.info(f"Deactivated degradation rule: {rule_name}, reason: {reason}")
            return True

        except Exception as e:
            logger.error(f"Failed to deactivate degradation rule {rule_name}: {e}")
            return False

    def is_degradation_active(self, rule_name: str) -> bool:
        """检查降级规则是否激活"""
        with self._lock:
            return rule_name in self._active_degradations

    def get_current_level(self) -> DegradationLevel:
        """获取当前降级级别"""
        return self._current_level

    def get_active_degradations(self) -> Dict[str, DegradationStatus]:
        """获取所有激活的降级状态"""
        with self._lock:
            return self._active_degradations.copy()

    def get_all_rules(self) -> Dict[str, DegradationRule]:
        """获取所有降级规则"""
        with self._lock:
            return self._rules.copy()

    def enable_rule(self, rule_name: str) -> bool:
        """启用降级规则"""
        with self._lock:
            if rule_name in self._rules:
                self._rules[rule_name].enabled = True
                logger.info(f"Enabled degradation rule: {rule_name}")
                return True
            return False

    def disable_rule(self, rule_name: str) -> bool:
        """禁用降级规则"""
        with self._lock:
            if rule_name in self._rules:
                # 如果规则正在激活,先停用
                if rule_name in self._active_degradations:
                    self._deactivate_rule(rule_name, "Rule disabled")

                self._rules[rule_name].enabled = False
                logger.info(f"Disabled degradation rule: {rule_name}")
                return True
            return False

    def enable_manager(self) -> None:
        """启用降级管理器"""
        self._is_enabled = True
        logger.info("DegradationManager enabled")

    def disable_manager(self) -> None:
        """禁用降级管理器"""
        with self._lock:
            # 停用所有激活的降级
            active_rules = list(self._active_degradations.keys())
            for rule_name in active_rules:
                self._deactivate_rule(rule_name, "Manager disabled")

        self._is_enabled = False
        logger.info("DegradationManager disabled")

    def is_enabled(self) -> bool:
        """检查管理器是否启用"""
        return self._is_enabled

    def _update_current_level(self) -> None:
        """更新当前降级级别"""
        if not self._active_degradations:
            self._current_level = DegradationLevel.NONE
            return

        # 找到最高的降级级别
        max_level = DegradationLevel.NONE
        level_values = {
            DegradationLevel.NONE: 0,
            DegradationLevel.LIGHT: 1,
            DegradationLevel.MODERATE: 2,
            DegradationLevel.HEAVY: 3,
            DegradationLevel.CRITICAL: 4,
        }

        for status in self._active_degradations.values():
            if level_values[status.level] > level_values[max_level]:
                max_level = status.level

        self._current_level = max_level

    def _record_degradation_event(self, rule_name: str, action: str, reason: str) -> None:
        """记录降级事件"""
        event = {"timestamp": time.time(), "rule_name": rule_name, "action": action, "reason": reason, "current_level": self._current_level.value}

        self._degradation_history.append(event)

        # 保留最近1000个事件
        if len(self._degradation_history) > 1000:
            self._degradation_history.pop(0)

    def _notify_degradation_change(self, rule_name: str, is_activated: bool) -> None:
        """通知降级状态变化"""
        for callback in self._degradation_callbacks:
            try:
                callback(rule_name, is_activated)
            except Exception as e:
                logger.error(f"Error in degradation callback: {e}")

    def add_degradation_callback(self, callback: Callable[[str, bool], None]) -> None:
        """添加降级状态变化回调"""
        self._degradation_callbacks.append(callback)

    def remove_degradation_callback(self, callback: Callable[[str, bool], None]) -> None:
        """移除降级状态变化回调"""
        if callback in self._degradation_callbacks:
            self._degradation_callbacks.remove(callback)

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        with self._lock:
            active_by_type = {}
            active_by_level = {}

            for status in self._active_degradations.values():
                # 按类型统计
                type_name = status.degradation_type.value
                active_by_type[type_name] = active_by_type.get(type_name, 0) + 1

                # 按级别统计
                level_name = status.level.value
                active_by_level[level_name] = active_by_level.get(level_name, 0) + 1

            return {
                "enabled": self._is_enabled,
                "current_level": self._current_level.value,
                "total_rules": len(self._rules),
                "active_degradations": len(self._active_degradations),
                "total_activations": self._total_activations,
                "total_recoveries": self._total_recoveries,
                "active_by_type": active_by_type,
                "active_by_level": active_by_level,
                "recent_events": len(self._degradation_history),
            }

    def get_recent_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的降级事件"""
        with self._lock:
            return self._degradation_history[-limit:] if self._degradation_history else []

    def reset_statistics(self) -> None:
        """重置统计信息"""
        with self._lock:
            self._total_activations = 0
            self._total_recoveries = 0
            self._degradation_history.clear()

        logger.info("DegradationManager statistics reset")

#!/usr/bin/env python3
"""
安装 Git 钩子脚本

自动安装 pre-commit 钩子来确保代码质量.
"""

import stat
import sys
from pathlib import Path


def install_pre_commit_hook():
    """安装 pre-commit 钩子"""
    project_root = Path(__file__).parent.parent
    hooks_dir = project_root / ".git" / "hooks"
    pre_commit_hook = hooks_dir / "pre-commit"

    # 确保 hooks 目录存在
    hooks_dir.mkdir(exist_ok=True)

    # pre-commit 钩子内容
    hook_content = """#!/bin/sh
# Mini-Boot Pre-commit Hook
# 自动运行代码质量检查

echo "🔍 运行代码质量检查..."

# 运行代码格式化
echo "📝 格式化代码..."
uv run ruff format .

# 修复可自动修复的问题
echo "🔧 修复代码问题..."
uv run ruff check --fix .

# 运行质量检查
echo "✅ 检查代码质量..."
uv run tests/test_runner.py --quality

if [ $? -ne 0 ]; then
    echo "❌ 代码质量检查失败,请修复问题后再提交"
    exit 1
fi

echo "🎉 代码质量检查通过!"
exit 0
"""

    # 写入钩子文件
    with pre_commit_hook.open("w", encoding="utf-8") as f:
        f.write(hook_content)

    # 设置执行权限
    current_permissions = pre_commit_hook.stat().st_mode
    pre_commit_hook.chmod(current_permissions | stat.S_IEXEC)

    print(f"✅ 已安装 pre-commit 钩子: {pre_commit_hook}")


def install_commit_msg_hook():
    """安装 commit-msg 钩子"""
    project_root = Path(__file__).parent.parent
    hooks_dir = project_root / ".git" / "hooks"
    commit_msg_hook = hooks_dir / "commit-msg"

    # commit-msg 钩子内容
    hook_content = """#!/bin/sh
# Mini-Boot Commit Message Hook
# 检查提交消息格式

commit_regex='^(feat|fix|docs|style|refactor|test|chore)(\\(.+\\))?: .{1,50}'

if ! grep -qE "$commit_regex" "$1"; then
    echo "❌ 提交消息格式不正确!"
    echo "格式应为: type(scope): description"
    echo "类型: feat, fix, docs, style, refactor, test, chore"
    echo "示例: feat(core): 添加IoC容器功能"
    exit 1
fi

echo "✅ 提交消息格式正确"
exit 0
"""

    # 写入钩子文件
    with commit_msg_hook.open("w", encoding="utf-8") as f:
        f.write(hook_content)

    # 设置执行权限
    current_permissions = commit_msg_hook.stat().st_mode
    commit_msg_hook.chmod(current_permissions | stat.S_IEXEC)

    print(f"✅ 已安装 commit-msg 钩子: {commit_msg_hook}")


def main():
    """主函数"""
    project_root = Path(__file__).parent.parent

    # 检查是否在 Git 仓库中
    if not (project_root / ".git").exists():
        print("❌ 当前目录不是 Git 仓库")
        sys.exit(1)

    print("🚀 安装 Git 钩子...")

    try:
        install_pre_commit_hook()
        install_commit_msg_hook()

        print("\n🎉 Git 钩子安装完成!")
        print("\n📋 已安装的钩子:")
        print("  - pre-commit: 自动运行代码质量检查")
        print("  - commit-msg: 检查提交消息格式")
        print("\n💡 提示:")
        print("  - 每次提交前会自动格式化代码并检查质量")
        print("  - 提交消息格式: type(scope): description")
        print("  - 如需跳过钩子: git commit --no-verify")

    except (OSError, FileNotFoundError) as e:
        print(f"❌ 安装失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()

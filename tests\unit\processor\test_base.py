#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean后处理器基础接口测试
"""

import unittest
from typing import Any

from miniboot.errors.processor import BeanProcessingError
from miniboot.processor import BeanPostProcessor, OrderedBeanPostProcessor, ProcessorOrder


class TestBeanPostProcessor(unittest.TestCase):
    """测试BeanPostProcessor基础接口"""

    def test_abstract_interface(self):
        """测试抽象接口不能直接实例化"""
        with self.assertRaises(TypeError):
            BeanPostProcessor()

    def test_concrete_implementation(self):
        """测试具体实现"""

        class TestProcessor(BeanPostProcessor):
            def __init__(self):
                self.before_calls = []
                self.after_calls = []

            def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
                self.before_calls.append((bean, bean_name))
                return f"before_{bean}"

            def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
                self.after_calls.append((bean, bean_name))
                return f"after_{bean}"

            def get_order(self) -> int:
                return 100

        processor = TestProcessor()

        # 测试before处理
        result = processor.post_process_before_initialization("test_bean", "testBean")
        self.assertEqual(result, "before_test_bean")
        self.assertEqual(processor.before_calls, [("test_bean", "testBean")])

        # 测试after处理
        result = processor.post_process_after_initialization("test_bean", "testBean")
        self.assertEqual(result, "after_test_bean")
        self.assertEqual(processor.after_calls, [("test_bean", "testBean")])

        # 测试order
        self.assertEqual(processor.get_order(), 100)

        # 测试supports
        self.assertTrue(processor.supports("any_bean", "anyBean"))

    def test_default_implementations(self):
        """测试默认实现"""

        class MinimalProcessor(BeanPostProcessor):
            def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
                # 实际使用bean_name进行简单验证
                if not isinstance(bean_name, str):
                    raise ValueError("bean_name must be string")
                return bean

            def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
                # 实际使用bean_name进行简单验证
                if not isinstance(bean_name, str):
                    raise ValueError("bean_name must be string")
                return bean

            def get_order(self) -> int:
                return 0

        processor = MinimalProcessor()

        # 默认实现应该返回原始bean
        bean = "test_bean"
        self.assertEqual(processor.post_process_before_initialization(bean, "test"), bean)
        self.assertEqual(processor.post_process_after_initialization(bean, "test"), bean)

        # 默认order为0
        self.assertEqual(processor.get_order(), 0)

        # 默认支持所有bean
        self.assertTrue(processor.supports(bean, "test"))


class TestOrderedBeanPostProcessor(unittest.TestCase):
    """测试OrderedBeanPostProcessor"""

    def test_ordered_processor_creation(self):
        """测试有序处理器创建"""

        class TestOrderedProcessor(OrderedBeanPostProcessor):
            def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
                # 实际使用bean_name进行日志记录
                self.last_processed = bean_name
                return bean

            def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
                # 实际使用bean_name进行日志记录
                self.last_processed = bean_name
                return bean

        processor = TestOrderedProcessor(order=200)
        self.assertEqual(processor.get_order(), 200)

    def test_default_order(self):
        """测试默认order"""

        class TestOrderedProcessor(OrderedBeanPostProcessor):
            def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
                # 实际使用bean_name进行验证
                assert isinstance(bean_name, str), "bean_name should be string"
                return bean

            def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
                # 实际使用bean_name进行验证
                assert isinstance(bean_name, str), "bean_name should be string"
                return bean

        processor = TestOrderedProcessor()
        self.assertEqual(processor.get_order(), 0)

    def test_custom_implementation(self):
        """测试自定义实现"""

        class CustomOrderedProcessor(OrderedBeanPostProcessor):
            def __init__(self, order: int = 0):
                super().__init__(order)
                self.processed_beans = []

            def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
                self.processed_beans.append(f"before_{bean_name}")
                return bean

            def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
                self.processed_beans.append(f"after_{bean_name}")
                return bean

        processor = CustomOrderedProcessor(150)
        self.assertEqual(processor.get_order(), 150)

        # 测试处理功能
        processor.post_process_before_initialization("bean", "testBean")
        processor.post_process_after_initialization("bean", "testBean")

        self.assertEqual(processor.processed_beans, ["before_testBean", "after_testBean"])


class TestBeanProcessingError(unittest.TestCase):
    """测试BeanProcessingError"""

    def test_basic_exception(self):
        """测试基础异常"""
        exc = BeanProcessingError("Test error")
        self.assertEqual(str(exc), "Test error")
        self.assertIsNone(exc.get_bean_name())
        self.assertIsNone(exc.get_processor_name())
        self.assertIsNone(exc.cause)

    def test_detailed_exception(self):
        """测试详细异常信息"""
        cause = ValueError("Original error")
        exc = BeanProcessingError("Processing failed", bean_name="testBean", processor_name="TestProcessor", cause=cause)

        self.assertEqual(exc.get_bean_name(), "testBean")
        self.assertEqual(exc.get_processor_name(), "TestProcessor")
        self.assertEqual(exc.cause, cause)

        # 检查消息包含所有必要信息
        message = str(exc)
        self.assertIn("Processing failed", message)
        self.assertIn("testBean", message)
        self.assertIn("TestProcessor", message)

    def test_partial_details(self):
        """测试部分详细信息"""
        exc = BeanProcessingError("Error", bean_name="testBean")
        message = str(exc)
        self.assertIn("testBean", message)
        self.assertNotIn("Processor:", message)

    def test_exception_chaining(self):
        """测试异常链"""
        original = ValueError("Original")
        exc = BeanProcessingError("Wrapper", cause=original)

        self.assertEqual(exc.__cause__, original)


class TestProcessorOrder(unittest.TestCase):
    """测试ProcessorOrder常量"""

    def test_order_constants(self):
        """测试order常量定义"""
        # 验证常量存在且为整数
        self.assertIsInstance(ProcessorOrder.DEPENDENCY_INJECTION_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.VALUE_INJECTION_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.AWARE_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.VALIDATION_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.AOP_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.PROXY_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.LOWEST_PRECEDENCE, int)

    def test_order_hierarchy(self):
        """测试order层次结构"""
        # 验证优先级顺序（数字越小优先级越高）
        self.assertLess(ProcessorOrder.DEPENDENCY_INJECTION_PROCESSOR, ProcessorOrder.VALUE_INJECTION_PROCESSOR)
        self.assertLess(ProcessorOrder.VALUE_INJECTION_PROCESSOR, ProcessorOrder.AWARE_PROCESSOR)
        self.assertLess(ProcessorOrder.AWARE_PROCESSOR, ProcessorOrder.VALIDATION_PROCESSOR)
        self.assertLess(ProcessorOrder.VALIDATION_PROCESSOR, ProcessorOrder.AOP_PROCESSOR)
        self.assertLess(ProcessorOrder.AOP_PROCESSOR, ProcessorOrder.LOWEST_PRECEDENCE)

    def test_specific_values(self):
        """测试特定的order值"""
        self.assertEqual(ProcessorOrder.DEPENDENCY_INJECTION_PROCESSOR, 10)
        self.assertEqual(ProcessorOrder.VALUE_INJECTION_PROCESSOR, 20)
        self.assertEqual(ProcessorOrder.AWARE_PROCESSOR, 30)
        self.assertEqual(ProcessorOrder.VALIDATION_PROCESSOR, 100)
        self.assertEqual(ProcessorOrder.AOP_PROCESSOR, 200)
        self.assertEqual(ProcessorOrder.PROXY_PROCESSOR, 210)
        self.assertEqual(ProcessorOrder.LOWEST_PRECEDENCE, 1000)


if __name__ == "__main__":
    unittest.main()

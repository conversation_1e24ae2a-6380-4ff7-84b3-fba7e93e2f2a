"""
性能分析报告生成器

生成性能分析报告和优化建议:
- 性能趋势分析
- 瓶颈识别
- 优化建议
- 对比分析
- 预测分析
"""

import time
import statistics
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass, field
from datetime import datetime, timedelta
import json

from loguru import logger


@dataclass
class PerformanceIssue:
    """性能问题"""

    category: str
    severity: str  # 'critical', 'high', 'medium', 'low'
    title: str
    description: str
    impact: str
    recommendation: str
    metrics: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PerformanceReport:
    """性能报告"""

    timestamp: float
    period_start: float
    period_end: float
    overall_score: float  # 0-100
    summary: Dict[str, Any]
    issues: List[PerformanceIssue]
    recommendations: List[str]
    trends: Dict[str, Any]
    comparisons: Dict[str, Any]


class PerformanceAnalyzer:
    """性能分析器"""

    def __init__(self, web_metrics=None, backpressure_metrics=None, scheduling_metrics=None):
        """
        初始化性能分析器

        Args:
            web_metrics: Web 指标收集器
            backpressure_metrics: 背压指标收集器
            scheduling_metrics: 调度指标收集器
        """
        self.web_metrics = web_metrics
        self.backpressure_metrics = backpressure_metrics
        self.scheduling_metrics = scheduling_metrics

        # 性能基线和阈值
        self.performance_thresholds = {
            "response_time": {
                "excellent": 100,  # ms
                "good": 300,
                "acceptable": 1000,
                "poor": 3000,
            },
            "error_rate": {
                "excellent": 0.1,  # %
                "good": 1.0,
                "acceptable": 5.0,
                "poor": 10.0,
            },
            "cpu_usage": {
                "excellent": 30,  # %
                "good": 60,
                "acceptable": 80,
                "poor": 95,
            },
            "memory_usage": {
                "excellent": 40,  # %
                "good": 70,
                "acceptable": 85,
                "poor": 95,
            },
        }

        logger.info("PerformanceAnalyzer initialized")

    def analyze_web_performance(self, period_seconds: int = 3600) -> Dict[str, Any]:
        """分析 Web 性能"""
        if not self.web_metrics:
            return {"error": "Web metrics not available"}

        try:
            summary = self.web_metrics.get_metrics_summary(period_seconds)
            path_stats = self.web_metrics.get_path_statistics()

            # 性能评分
            response_time_score = self._score_response_time(summary.avg_response_time)
            error_rate_score = self._score_error_rate(summary.error_rate)
            throughput_score = self._score_throughput(summary.requests_per_second)

            overall_score = (response_time_score + error_rate_score + throughput_score) / 3

            # 识别问题
            issues = []

            if summary.avg_response_time > self.performance_thresholds["response_time"]["acceptable"]:
                issues.append(
                    PerformanceIssue(
                        category="web",
                        severity="high" if summary.avg_response_time > self.performance_thresholds["response_time"]["poor"] else "medium",
                        title="High Response Time",
                        description=f"Average response time is {summary.avg_response_time:.2f}ms",
                        impact="Poor user experience and reduced throughput",
                        recommendation="Optimize slow endpoints, add caching, or scale resources",
                        metrics={"avg_response_time": summary.avg_response_time},
                    )
                )

            if summary.error_rate > self.performance_thresholds["error_rate"]["acceptable"]:
                issues.append(
                    PerformanceIssue(
                        category="web",
                        severity="critical" if summary.error_rate > self.performance_thresholds["error_rate"]["poor"] else "high",
                        title="High Error Rate",
                        description=f"Error rate is {summary.error_rate:.2f}%",
                        impact="Service reliability issues and user frustration",
                        recommendation="Investigate error causes, improve error handling, and add monitoring",
                        metrics={"error_rate": summary.error_rate},
                    )
                )

            # 慢端点分析
            slow_paths = []
            for path, stats in path_stats.items():
                if stats["avg_response_time"] > self.performance_thresholds["response_time"]["acceptable"]:
                    slow_paths.append({"path": path, "avg_response_time": stats["avg_response_time"], "count": stats["count"]})

            if slow_paths:
                issues.append(
                    PerformanceIssue(
                        category="web",
                        severity="medium",
                        title="Slow Endpoints Detected",
                        description=f"{len(slow_paths)} endpoints have high response times",
                        impact="Specific endpoints causing performance degradation",
                        recommendation="Optimize identified slow endpoints",
                        metrics={"slow_paths": slow_paths},
                    )
                )

            return {
                "score": overall_score,
                "summary": {
                    "avg_response_time": summary.avg_response_time,
                    "error_rate": summary.error_rate,
                    "requests_per_second": summary.requests_per_second,
                    "total_requests": summary.total_requests,
                },
                "issues": issues,
                "path_analysis": {
                    "total_paths": len(path_stats),
                    "slow_paths_count": len(slow_paths),
                    "top_slow_paths": sorted(slow_paths, key=lambda x: x["avg_response_time"], reverse=True)[:5],
                },
            }

        except Exception as e:
            logger.error(f"Web performance analysis failed: {e}")
            return {"error": str(e)}

    def analyze_backpressure_performance(self, period_seconds: int = 3600) -> Dict[str, Any]:
        """分析背压控制性能"""
        if not self.backpressure_metrics:
            return {"error": "Backpressure metrics not available"}

        try:
            summary = self.backpressure_metrics.get_metrics_summary(period_seconds)

            # 性能评分
            stability_score = summary.system_stability_score
            effectiveness_score = summary.protection_effectiveness

            overall_score = (stability_score + effectiveness_score) / 2

            # 识别问题
            issues = []

            if summary.system_stability_score < 70:
                issues.append(
                    PerformanceIssue(
                        category="backpressure",
                        severity="high",
                        title="Low System Stability",
                        description=f"System stability score is {summary.system_stability_score:.1f}",
                        impact="System may be unstable under load",
                        recommendation="Review load patterns and adjust backpressure thresholds",
                        metrics={"stability_score": summary.system_stability_score},
                    )
                )

            if summary.circuit_breaker_triggers > 10:
                issues.append(
                    PerformanceIssue(
                        category="backpressure",
                        severity="medium",
                        title="Frequent Circuit Breaker Triggers",
                        description=f"{summary.circuit_breaker_triggers} circuit breaker triggers in period",
                        impact="Service degradation due to circuit breaker activations",
                        recommendation="Investigate root causes and adjust circuit breaker settings",
                        metrics={"triggers": summary.circuit_breaker_triggers},
                    )
                )

            return {
                "score": overall_score,
                "summary": {
                    "stability_score": summary.system_stability_score,
                    "protection_effectiveness": summary.protection_effectiveness,
                    "circuit_breaker_triggers": summary.circuit_breaker_triggers,
                    "load_shedding_events": summary.load_shedding_events,
                },
                "issues": issues,
            }

        except Exception as e:
            logger.error(f"Backpressure performance analysis failed: {e}")
            return {"error": str(e)}

    def analyze_scheduling_performance(self, period_seconds: int = 3600) -> Dict[str, Any]:
        """分析调度性能"""
        if not self.scheduling_metrics:
            return {"error": "Scheduling metrics not available"}

        try:
            summary = self.scheduling_metrics.get_metrics_summary(period_seconds)

            # 性能评分
            classification_score = summary.classification_accuracy
            effectiveness_score = summary.strategy_effectiveness
            improvement_score = min(100, summary.performance_improvement)

            overall_score = (classification_score + effectiveness_score + improvement_score) / 3

            # 识别问题
            issues = []

            if summary.classification_accuracy < 70:
                issues.append(
                    PerformanceIssue(
                        category="scheduling",
                        severity="medium",
                        title="Low Classification Accuracy",
                        description=f"Task classification accuracy is {summary.classification_accuracy:.1f}%",
                        impact="Suboptimal task scheduling decisions",
                        recommendation="Improve task classification rules or training data",
                        metrics={"accuracy": summary.classification_accuracy},
                    )
                )

            if summary.strategy_effectiveness < 50:
                issues.append(
                    PerformanceIssue(
                        category="scheduling",
                        severity="medium",
                        title="Low Strategy Effectiveness",
                        description=f"Strategy effectiveness is {summary.strategy_effectiveness:.1f}%",
                        impact="Scheduling strategies not providing expected benefits",
                        recommendation="Review and tune scheduling strategies",
                        metrics={"effectiveness": summary.strategy_effectiveness},
                    )
                )

            return {
                "score": overall_score,
                "summary": {
                    "classification_accuracy": summary.classification_accuracy,
                    "strategy_effectiveness": summary.strategy_effectiveness,
                    "performance_improvement": summary.performance_improvement,
                    "async_adoption_rate": summary.async_adoption_rate,
                },
                "issues": issues,
            }

        except Exception as e:
            logger.error(f"Scheduling performance analysis failed: {e}")
            return {"error": str(e)}

    def _score_response_time(self, response_time: float) -> float:
        """响应时间评分"""
        thresholds = self.performance_thresholds["response_time"]

        if response_time <= thresholds["excellent"]:
            return 100
        elif response_time <= thresholds["good"]:
            return 80
        elif response_time <= thresholds["acceptable"]:
            return 60
        elif response_time <= thresholds["poor"]:
            return 40
        else:
            return 20

    def _score_error_rate(self, error_rate: float) -> float:
        """错误率评分"""
        thresholds = self.performance_thresholds["error_rate"]

        if error_rate <= thresholds["excellent"]:
            return 100
        elif error_rate <= thresholds["good"]:
            return 80
        elif error_rate <= thresholds["acceptable"]:
            return 60
        elif error_rate <= thresholds["poor"]:
            return 40
        else:
            return 20

    def _score_throughput(self, throughput: float) -> float:
        """吞吐量评分(简化实现)"""
        if throughput >= 100:
            return 100
        elif throughput >= 50:
            return 80
        elif throughput >= 20:
            return 60
        elif throughput >= 5:
            return 40
        else:
            return 20

    def generate_performance_report(self, period_seconds: int = 3600) -> PerformanceReport:
        """生成性能报告"""
        end_time = time.time()
        start_time = end_time - period_seconds

        # 分析各个组件
        web_analysis = self.analyze_web_performance(period_seconds)
        backpressure_analysis = self.analyze_backpressure_performance(period_seconds)
        scheduling_analysis = self.analyze_scheduling_performance(period_seconds)

        # 计算总体评分
        scores = []
        if "score" in web_analysis:
            scores.append(web_analysis["score"])
        if "score" in backpressure_analysis:
            scores.append(backpressure_analysis["score"])
        if "score" in scheduling_analysis:
            scores.append(scheduling_analysis["score"])

        overall_score = statistics.mean(scores) if scores else 0

        # 收集所有问题
        all_issues = []
        for analysis in [web_analysis, backpressure_analysis, scheduling_analysis]:
            if "issues" in analysis:
                all_issues.extend(analysis["issues"])

        # 生成建议
        recommendations = self._generate_recommendations(all_issues)

        # 创建报告
        report = PerformanceReport(
            timestamp=time.time(),
            period_start=start_time,
            period_end=end_time,
            overall_score=overall_score,
            summary={
                "web": web_analysis.get("summary", {}),
                "backpressure": backpressure_analysis.get("summary", {}),
                "scheduling": scheduling_analysis.get("summary", {}),
            },
            issues=all_issues,
            recommendations=recommendations,
            trends={},  # 可以添加趋势分析
            comparisons={},  # 可以添加对比分析
        )

        return report

    def _generate_recommendations(self, issues: List[PerformanceIssue]) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 按严重程度分组
        critical_issues = [issue for issue in issues if issue.severity == "critical"]
        high_issues = [issue for issue in issues if issue.severity == "high"]

        if critical_issues:
            recommendations.append("🚨 Critical issues detected - immediate action required")
            for issue in critical_issues:
                recommendations.append(f"• {issue.recommendation}")

        if high_issues:
            recommendations.append("⚠️ High priority issues need attention")
            for issue in high_issues:
                recommendations.append(f"• {issue.recommendation}")

        # 通用建议
        if not issues:
            recommendations.append("✅ System performance is healthy")
        else:
            recommendations.append("📊 Regular monitoring and optimization recommended")

        return recommendations

    def to_dict(self, period_seconds: int = 3600) -> Dict[str, Any]:
        """转换为字典格式"""
        report = self.generate_performance_report(period_seconds)

        return {
            "timestamp": report.timestamp,
            "period": {"start": report.period_start, "end": report.period_end, "duration": period_seconds},
            "overall_score": report.overall_score,
            "summary": report.summary,
            "issues": [
                {
                    "category": issue.category,
                    "severity": issue.severity,
                    "title": issue.title,
                    "description": issue.description,
                    "impact": issue.impact,
                    "recommendation": issue.recommendation,
                    "metrics": issue.metrics,
                }
                for issue in report.issues
            ],
            "recommendations": report.recommendations,
        }

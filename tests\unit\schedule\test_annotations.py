#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务注解系统单元测试
"""

import unittest

from miniboot.schedule import EnableScheduling, Scheduled, ScheduledConfig, get_scheduled_config, is_scheduled_method, is_scheduling_enabled


class TestScheduledConfig(unittest.TestCase):
    """测试ScheduledConfig配置类"""

    def test_cron_config(self):
        """测试cron配置"""
        config = ScheduledConfig(cron="0 */5 * * * *")
        self.assertEqual(config.cron, "0 */5 * * * *")
        self.assertIsNone(config.fixed_rate)
        self.assertIsNone(config.fixed_delay)

    def test_fixed_rate_config(self):
        """测试固定频率配置"""
        config = ScheduledConfig(fixed_rate="30s")
        self.assertEqual(config.get_parsed_fixed_rate(), 30.0)
        self.assertIsNone(config.cron)
        self.assertIsNone(config.fixed_delay)

    def test_fixed_delay_config(self):
        """测试固定延迟配置"""
        config = ScheduledConfig(fixed_delay="1m", initial_delay="10s")
        self.assertEqual(config.get_parsed_fixed_delay(), 60.0)
        self.assertEqual(config.get_parsed_initial_delay(), 10.0)
        self.assertIsNone(config.cron)
        self.assertIsNone(config.fixed_rate)

    def test_numeric_duration(self):
        """测试数字持续时间"""
        config = ScheduledConfig(fixed_rate=30)
        self.assertEqual(config.get_parsed_fixed_rate(), 30.0)

    def test_duration_parsing(self):
        """测试持续时间解析"""
        config = ScheduledConfig(fixed_rate="2h")
        self.assertEqual(config.get_parsed_fixed_rate(), 7200.0)  # 2小时 = 7200秒

        config = ScheduledConfig(fixed_rate="1d")
        self.assertEqual(config.get_parsed_fixed_rate(), 86400.0)  # 1天 = 86400秒

    def test_invalid_cron_expression(self):
        """测试无效的cron表达式"""
        with self.assertRaises(ValueError):
            ScheduledConfig(cron="invalid cron")

    def test_multiple_schedule_types_error(self):
        """测试同时指定多种调度方式的错误"""
        with self.assertRaises(ValueError):
            ScheduledConfig(cron="0 */5 * * * *", fixed_rate="30s")

    def test_no_schedule_type_error(self):
        """测试未指定调度方式的错误"""
        with self.assertRaises(ValueError):
            ScheduledConfig()

    def test_invalid_duration_format(self):
        """测试无效的持续时间格式"""
        with self.assertRaises(ValueError):
            ScheduledConfig(fixed_rate="invalid")

    def test_negative_duration(self):
        """测试负数持续时间"""
        with self.assertRaises(ValueError):
            ScheduledConfig(fixed_rate=-30)


class TestScheduledDecorator(unittest.TestCase):
    """测试@Scheduled装饰器"""

    def test_cron_decorator(self):
        """测试cron装饰器"""

        @Scheduled(cron="0 */5 * * * *")
        def test_method():
            pass

        self.assertTrue(is_scheduled_method(test_method))
        config = get_scheduled_config(test_method)
        self.assertIsNotNone(config)
        self.assertEqual(config.cron, "0 */5 * * * *")

    def test_fixed_rate_decorator(self):
        """测试固定频率装饰器"""

        @Scheduled(fixed_rate="30s")
        def test_method():
            pass

        config = get_scheduled_config(test_method)
        self.assertEqual(config.fixed_rate, "30s")

    def test_fixed_delay_decorator(self):
        """测试固定延迟装饰器"""

        @Scheduled(fixed_delay="1m", initial_delay="10s")
        def test_method():
            pass

        config = get_scheduled_config(test_method)
        self.assertEqual(config.fixed_delay, "1m")
        self.assertEqual(config.initial_delay, "10s")

    def test_method_execution(self):
        """测试装饰后的方法仍可正常执行"""
        result = []

        @Scheduled(fixed_rate="30s")
        def test_method():
            result.append("executed")

        test_method()
        self.assertEqual(result, ["executed"])

    def test_method_with_parameters(self):
        """测试带参数的方法"""

        @Scheduled(fixed_rate="30s")
        def test_method(x, y):
            return x + y

        result = test_method(1, 2)
        self.assertEqual(result, 3)


class TestEnableSchedulingDecorator(unittest.TestCase):
    """测试@EnableScheduling装饰器"""

    def test_enable_scheduling_decorator(self):
        """测试启用定时任务装饰器"""

        @EnableScheduling
        class TestClass:
            pass

        self.assertTrue(is_scheduling_enabled(TestClass))

    def test_class_without_decorator(self):
        """测试未装饰的类"""

        class TestClass:
            pass

        self.assertFalse(is_scheduling_enabled(TestClass))


class TestUtilityFunctions(unittest.TestCase):
    """测试工具函数"""

    def test_is_scheduled_method_with_regular_method(self):
        """测试普通方法的检查"""

        def regular_method():
            pass

        self.assertFalse(is_scheduled_method(regular_method))

    def test_get_scheduled_config_with_regular_method(self):
        """测试获取普通方法的配置"""

        def regular_method():
            pass

        config = get_scheduled_config(regular_method)
        self.assertIsNone(config)


class TestComplexScenarios(unittest.TestCase):
    """测试复杂场景"""

    def test_class_with_multiple_scheduled_methods(self):
        """测试包含多个定时任务方法的类"""

        @EnableScheduling
        class TestService:
            @Scheduled(cron="0 */5 * * * *")
            def cron_task(self):
                pass

            @Scheduled(fixed_rate="30s")
            def rate_task(self):
                pass

            def regular_method(self):
                pass

        service = TestService()

        # 检查类是否启用定时任务
        self.assertTrue(is_scheduling_enabled(TestService))

        # 检查各个方法
        self.assertTrue(is_scheduled_method(service.cron_task))
        self.assertTrue(is_scheduled_method(service.rate_task))
        self.assertFalse(is_scheduled_method(service.regular_method))

        # 检查配置
        cron_config = get_scheduled_config(service.cron_task)
        self.assertEqual(cron_config.cron, "0 */5 * * * *")

        rate_config = get_scheduled_config(service.rate_task)
        self.assertEqual(rate_config.fixed_rate, "30s")


if __name__ == "__main__":
    unittest.main()

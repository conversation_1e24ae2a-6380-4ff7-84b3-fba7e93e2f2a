#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 应用上下文Actuator集成扩展 - 一键启动功能

为DefaultApplicationContext提供Actuator集成的便捷方法,实现零配置、零阻塞的一键启动.

核心特性:
- 一键启动应用上下文并自动集成Actuator
- 智能检测Web模块状态
- 零配置自动集成
- 完整的错误处理和降级机制
"""

import asyncio
from typing import TYPE_CHECKING, Optional

from fastapi import FastAPI
from loguru import logger

if TYPE_CHECKING:
    pass


class ActuatorContextIntegration:
    """应用上下文Actuator集成器

    为DefaultApplicationContext提供Actuator集成功能的扩展类.
    """

    def __init__(self, application_context):
        """初始化集成器

        Args:
            application_context: DefaultApplicationContext实例
        """
        self.application_context = application_context
        self._actuator_integrated = False
        self._integration_result = None

    async def start_with_actuator(self, main_app: Optional[FastAPI] = None) -> None:
        """启动应用上下文并自动集成Actuator - 一键启动

        Args:
            main_app: 可选的外部FastAPI应用,如果不提供则使用内置Web模块
        """
        logger.info("🚀 Starting application context with Actuator integration...")

        try:
            # 1. 启动核心应用上下文
            await self.application_context.start()
            logger.info("✅ Core application context started")

            # 2. 检查Actuator启用状态
            if not self._is_actuator_enabled():
                logger.info("Actuator is disabled, skipping integration")
                return

            # 3. 执行Actuator集成
            await self._integrate_actuator(main_app)

            logger.info("🎉 Application context started with Actuator integration successfully")

        except Exception as e:
            logger.error(f"Failed to start application context with Actuator: {e}")
            raise

    async def _integrate_actuator(self, main_app: Optional[FastAPI] = None) -> None:
        """集成Actuator到应用"""
        try:
            # 1. 确定要使用的FastAPI应用
            target_app = await self._resolve_target_app(main_app)
            if not target_app:
                logger.warning("No FastAPI application available, Actuator integration skipped")
                return

            # 2. 使用ActuatorInitializer进行一键初始化
            from miniboot.starters.actuator.integration import \
                ActuatorInitializer

            actuator_initializer = ActuatorInitializer(self.application_context)
            actuator_context = await actuator_initializer.init(target_app)

            # 3. 保存集成结果
            self._integration_result = {
                "actuator_context": actuator_context,
                "integration": actuator_initializer.integration,
                "target_app": target_app,
                "base_path": actuator_context.properties.base_path,
            }
            self._actuator_integrated = True

            logger.info(f"✅ Actuator integrated successfully at: {actuator_context.properties.base_path}")

        except Exception as e:
            logger.error(f"Failed to integrate Actuator: {e}")
            # 不抛出异常,允许应用继续运行
            logger.info("Application will continue without Actuator integration")

    async def _resolve_target_app(self, main_app: Optional[FastAPI] = None) -> Optional[FastAPI]:
        """解析目标FastAPI应用

        Args:
            main_app: 外部提供的FastAPI应用

        Returns:
            Optional[FastAPI]: 要集成的FastAPI应用
        """
        # 1. 优先使用外部提供的应用
        if main_app:
            logger.debug("Using provided FastAPI application")
            return main_app

        # 2. 尝试从应用上下文获取Web应用
        try:
            web_app = self.application_context.get_bean("webApp")
            if web_app:
                logger.debug("Using Web module FastAPI application")
                return web_app
        except Exception:
            pass

        # 3. 尝试从Web应用包装器获取
        try:
            web_application = self.application_context.get_bean("webApplication")
            if web_application and hasattr(web_application, "get_app"):
                web_app = web_application.get_app()
                if web_app:
                    logger.debug("Using Web application wrapper FastAPI app")
                    return web_app
        except Exception:
            pass

        # 4. 创建新的FastAPI应用(最后的选择)
        logger.debug("Creating new FastAPI application for Actuator")
        return FastAPI(title="Mini-Boot Application", description="Auto-created for Actuator integration")

    def _is_actuator_enabled(self) -> bool:
        """检查Actuator是否启用"""
        try:
            # 检查多个可能的配置键
            enabled_keys = ["miniboot.actuators.endpoints.web.enabled", "actuator.enabled", "management.endpoints.enabled"]

            for key in enabled_keys:
                if self.application_context.get_environment().get_property(key, None) is not None:
                    return self.application_context.get_environment().get_property(key, True)

            # 默认启用
            return True

        except Exception as e:
            logger.debug(f"Error checking Actuator enabled status: {e}")
            return True  # 默认启用

    def get_integration_info(self) -> dict:
        """获取集成信息"""
        if not self._actuator_integrated:
            return {"integrated": False, "reason": "Not integrated"}

        if not self._integration_result:
            return {"integrated": False, "reason": "Integration failed"}

        return {
            "integrated": True,
            "base_path": self._integration_result["base_path"],
            "endpoints": list(self._integration_result["actuator_context"].registry.get_enabled_endpoint_ids()),
            "performance_monitoring": self._integration_result["integration"].performance_monitor.is_monitoring(),
            "target_app_type": type(self._integration_result["target_app"]).__name__,
        }

    def is_integrated(self) -> bool:
        """检查是否已集成"""
        return self._actuator_integrated

    async def shutdown_actuator(self) -> None:
        """关闭Actuator集成"""
        if not self._actuator_integrated or not self._integration_result:
            return

        try:
            integration = self._integration_result["integration"]
            await integration.shutdown()

            self._actuator_integrated = False
            self._integration_result = None

            logger.info("🛑 Actuator integration shutdown completed")

        except Exception as e:
            logger.error(f"Error shutting down Actuator integration: {e}")


def add_actuator_integration_to_context(context_class):
    """为应用上下文类添加Actuator集成功能的装饰器

    Args:
        context_class: 应用上下文类

    Returns:
        装饰后的类,包含Actuator集成方法
    """

    def __init_with_actuator__(self, *args, **kwargs):
        """增强的初始化方法"""
        # 调用原始初始化
        original_init = getattr(context_class, "__init__")
        original_init(self, *args, **kwargs)

        # 添加Actuator集成器
        self._actuator_integration = ActuatorContextIntegration(self)

    async def start_with_actuator(self, main_app: Optional[FastAPI] = None) -> None:
        """启动应用上下文并自动集成Actuator"""
        return await self._actuator_integration.start_with_actuator(main_app)

    def get_actuator_integration_info(self) -> dict:
        """获取Actuator集成信息"""
        return self._actuator_integration.get_integration_info()

    def is_actuator_integrated(self) -> bool:
        """检查Actuator是否已集成"""
        return self._actuator_integration.is_integrated()

    async def shutdown_actuator(self) -> None:
        """关闭Actuator集成"""
        return await self._actuator_integration.shutdown_actuator()

    # 替换原始方法
    context_class.__init__ = __init_with_actuator__
    context_class.start_with_actuator = start_with_actuator
    context_class.get_actuator_integration_info = get_actuator_integration_info
    context_class.is_actuator_integrated = is_actuator_integrated
    context_class.shutdown_actuator = shutdown_actuator

    return context_class


# 便捷函数
async def create_application_with_actuator(
    config_path: Optional[str] = None, packages_to_scan: Optional[list[str]] = None, main_app: Optional[FastAPI] = None
):
    """创建并启动带Actuator集成的应用上下文 - 一键启动函数

    Args:
        config_path: 配置文件路径
        packages_to_scan: 要扫描的包列表
        main_app: 可选的外部FastAPI应用

    Returns:
        启动后的应用上下文
    """
    from .application import DefaultApplicationContext

    # 创建应用上下文(避免递归调用)
    context = DefaultApplicationContext(config_path=config_path, packages_to_scan=packages_to_scan)

    # 启动并集成Actuator(直接调用方法,避免装饰器递归)
    await context.start_with_actuator(main_app)

    return context

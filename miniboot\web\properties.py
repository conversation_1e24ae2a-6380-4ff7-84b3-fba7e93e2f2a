"""
Web 配置属性模块

提供 Web 应用的统一配置管理功能,支持配置驱动的背压控制和智能调度.

主要功能:
- WebProperties - Web应用统一配置类
- BackpressureConfig - 背压控制配置
- AsyncOptimizationConfig - 异步优化配置
- TaskClassificationRule - 任务分类规则
- CorsConfig - CORS跨域配置
- CompressionConfig - 压缩配置
- LoggingConfig - 日志配置
- DocsConfig - API文档配置
- StaticConfig - 静态文件配置

枚举类型:
- BackpressureStrategy - 背压策略
- DegradationLevel - 降级级别
- TaskType - 任务类型
- ExecutionStrategy - 执行策略
"""

from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Dict, List, Optional


class BackpressureStrategy(Enum):
    """背压策略枚举"""

    NONE = "none"  # 不启用背压控制
    SIMPLE = "simple"  # 简单背压控制
    ADAPTIVE = "adaptive"  # 自适应背压控制
    INTELLIGENT = "intelligent"  # 智能背压控制


class DegradationLevel(Enum):
    """降级级别枚举"""

    NONE = 0  # 不降级
    LOW = 1  # 低级降级
    MEDIUM = 2  # 中级降级
    HIGH = 3  # 高级降级
    CRITICAL = 4  # 关键降级


class TaskType(Enum):
    """任务类型枚举"""

    CPU_BOUND = "cpu_bound"  # CPU密集型
    IO_BOUND = "io_bound"  # I/O密集型
    LIGHTWEIGHT = "lightweight"  # 轻量级任务
    MIXED = "mixed"  # 混合型任务
    UNKNOWN = "unknown"  # 未知类型


class ExecutionStrategy(Enum):
    """执行策略枚举"""

    SYNC = "sync"  # 同步执行
    ASYNC = "async"  # 异步执行
    THREAD_POOL = "thread_pool"  # 线程池执行
    PROCESS_POOL = "process_pool"  # 进程池执行
    AUTO = "auto"  # 自动选择


@dataclass
class TaskClassificationRule:
    """任务分类规则"""

    name: str
    patterns: List[str]  # 匹配模式
    task_type: TaskType
    execution_strategy: ExecutionStrategy
    priority: int = 0  # 规则优先级
    enabled: bool = True


@dataclass
class CorsConfig:
    """CORS跨域配置

    配置跨域资源共享(CORS)相关参数.
    """

    enabled: bool = True
    allowed_origins: list[str] = field(default_factory=lambda: ["*"])
    allowed_methods: list[str] = field(default_factory=lambda: ["GET", "POST", "PUT", "DELETE", "OPTIONS"])
    allowed_headers: list[str] = field(default_factory=lambda: ["*"])
    allow_credentials: bool = True

    def __post_init__(self):
        """初始化后处理,确保默认值正确设置"""
        if self.allowed_origins is None:
            self.allowed_origins = ["*"]
        if self.allowed_methods is None:
            self.allowed_methods = ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        if self.allowed_headers is None:
            self.allowed_headers = ["*"]


@dataclass
class CompressionConfig:
    """压缩配置

    配置HTTP响应压缩相关参数.
    """

    enabled: bool = True
    min_size: int = 1024  # 最小压缩大小(字节)


@dataclass
class LoggingConfig:
    """日志配置

    配置Web请求日志相关参数.
    """

    enabled: bool = True
    include_headers: bool = False  # 是否包含请求头
    include_body: bool = False  # 是否包含请求体


@dataclass
class DocsConfig:
    """API文档配置

    配置Swagger/OpenAPI文档相关参数.
    """

    enabled: bool = True
    title: str = "Mini-Boot API"
    description: str = "Mini-Boot Web API Documentation"


@dataclass
class StaticConfig:
    """静态文件配置

    配置静态文件服务相关参数.
    """

    enabled: bool = False  # 是否启用静态文件服务,默认禁用以减少资源消耗
    directory: str = "static"  # 静态文件目录
    mount_path: str = "/static"  # 挂载路径
    html: bool = True  # 是否启用HTML文件服务
    check_dir: bool = True  # 是否检查目录存在


@dataclass
class BackpressureConfig:
    """背压控制配置"""

    # 基础配置
    enabled: bool = True
    strategy: BackpressureStrategy = BackpressureStrategy.ADAPTIVE

    # 并发控制配置
    max_concurrent_requests: int = 1000
    max_concurrent_controllers: int = 50
    max_queue_size: int = 5000

    # 负载监控配置
    load_monitoring_enabled: bool = True  # 是否启用负载监控
    load_check_interval: float = 1.0  # 负载检查间隔(秒)
    monitor_interval: float = 1.0  # 监控间隔(秒)
    cpu_threshold: float = 0.8  # CPU使用率阈值
    cpu_threshold_warning: float = 0.7  # CPU警告阈值
    cpu_threshold_critical: float = 0.9  # CPU临界阈值
    memory_threshold: float = 0.85  # 内存使用率阈值
    memory_threshold_warning: float = 0.75  # 内存警告阈值
    memory_threshold_critical: float = 0.95  # 内存临界阈值
    response_time_threshold: float = 2.0  # 响应时间阈值(秒)
    response_time_threshold_warning: float = 1.5  # 响应时间警告阈值(秒)
    response_time_threshold_critical: float = 3.0  # 响应时间临界阈值(秒)

    # 自适应调整配置
    adaptive_enabled: bool = True
    adjustment_factor: float = 0.1  # 调整因子
    min_concurrent: int = 10  # 最小并发数
    max_adjustment_step: int = 100  # 最大调整步长

    # 熔断器配置
    circuit_breaker_enabled: bool = True
    failure_threshold: int = 50  # 失败阈值
    failure_rate_threshold: float = 0.5  # 失败率阈值
    error_rate_threshold_warning: float = 0.3  # 错误率警告阈值
    error_rate_threshold_critical: float = 0.7  # 错误率临界阈值
    recovery_timeout: float = 30.0  # 恢复超时(秒)
    half_open_max_calls: int = 10  # 半开状态最大调用数

    # 优雅降级配置
    degradation_enabled: bool = True
    degradation_levels: Dict[DegradationLevel, Dict[str, Any]] = field(
        default_factory=lambda: {
            DegradationLevel.LOW: {"disable_features": ["metrics", "tracing"], "reduce_logging": True, "cache_ttl_reduction": 0.5},
            DegradationLevel.MEDIUM: {
                "disable_features": ["metrics", "tracing", "validation"],
                "reduce_logging": True,
                "cache_ttl_reduction": 0.3,
                "skip_non_critical_middleware": True,
            },
            DegradationLevel.HIGH: {
                "disable_features": ["metrics", "tracing", "validation", "compression"],
                "reduce_logging": True,
                "cache_ttl_reduction": 0.1,
                "skip_non_critical_middleware": True,
                "simple_response_mode": True,
            },
            DegradationLevel.CRITICAL: {
                "disable_features": ["metrics", "tracing", "validation", "compression", "cors"],
                "reduce_logging": False,
                "cache_disabled": True,
                "skip_non_critical_middleware": True,
                "simple_response_mode": True,
                "emergency_mode": True,
            },
        }
    )

    # 监控和告警配置
    monitoring_enabled: bool = True
    alert_thresholds: Dict[str, float] = field(
        default_factory=lambda: {"high_load": 0.8, "high_latency": 1.0, "high_error_rate": 0.1, "queue_full": 0.9}
    )


@dataclass
class AsyncOptimizationConfig:
    """异步优化配置"""

    # 基础配置
    enabled: bool = True
    intelligent_scheduling: bool = True
    performance_profiling: bool = True

    # 任务分类配置
    classification_enabled: bool = True
    classification_rules: List[TaskClassificationRule] = field(
        default_factory=lambda: [
            TaskClassificationRule(
                name="database_operations",
                patterns=["*database*", "*db*", "*sql*", "*query*"],
                task_type=TaskType.IO_BOUND,
                execution_strategy=ExecutionStrategy.ASYNC,
                priority=1,
            ),
            TaskClassificationRule(
                name="file_operations",
                patterns=["*file*", "*read*", "*write*", "*upload*", "*download*"],
                task_type=TaskType.IO_BOUND,
                execution_strategy=ExecutionStrategy.ASYNC,
                priority=2,
            ),
            TaskClassificationRule(
                name="network_requests",
                patterns=["*http*", "*api*", "*request*", "*fetch*"],
                task_type=TaskType.IO_BOUND,
                execution_strategy=ExecutionStrategy.ASYNC,
                priority=3,
            ),
            TaskClassificationRule(
                name="computation_heavy",
                patterns=["*compute*", "*calculate*", "*process*", "*analyze*"],
                task_type=TaskType.CPU_BOUND,
                execution_strategy=ExecutionStrategy.THREAD_POOL,
                priority=4,
            ),
            TaskClassificationRule(
                name="simple_operations",
                patterns=["*get*", "*list*", "*info*", "*status*"],
                task_type=TaskType.LIGHTWEIGHT,
                execution_strategy=ExecutionStrategy.SYNC,
                priority=5,
            ),
        ]
    )

    # 性能阈值配置
    performance_thresholds: Dict[str, float] = field(
        default_factory=lambda: {
            "async_benefit_threshold": 0.1,  # 异步收益阈值
            "cpu_bound_threshold": 0.05,  # CPU密集型任务阈值
            "io_bound_threshold": 0.01,  # I/O密集型任务阈值
            "lightweight_threshold": 0.001,  # 轻量级任务阈值
            "thread_pool_threshold": 0.1,  # 线程池执行阈值
            "process_pool_threshold": 1.0,  # 进程池执行阈值
        }
    )

    # 执行策略配置
    execution_strategies: Dict[TaskType, ExecutionStrategy] = field(
        default_factory=lambda: {
            TaskType.CPU_BOUND: ExecutionStrategy.THREAD_POOL,
            TaskType.IO_BOUND: ExecutionStrategy.ASYNC,
            TaskType.LIGHTWEIGHT: ExecutionStrategy.SYNC,
            TaskType.MIXED: ExecutionStrategy.AUTO,
            TaskType.UNKNOWN: ExecutionStrategy.AUTO,
        }
    )

    # 线程池配置
    thread_pool_config: Dict[str, int] = field(
        default_factory=lambda: {
            "max_workers": 20,  # 最大工作线程数
            "queue_size": 1000,  # 队列大小
            "keep_alive_time": 60,  # 线程保活时间(秒)
        }
    )

    # 自适应学习配置
    adaptive_learning: bool = True
    learning_window_size: int = 1000  # 学习窗口大小
    learning_rate: float = 0.01  # 学习率
    model_update_interval: int = 100  # 模型更新间隔

    # 监控配置
    monitoring_enabled: bool = True
    metrics_collection_interval: float = 5.0  # 指标收集间隔(秒)
    performance_history_size: int = 10000  # 性能历史记录大小


@dataclass
class WebProperties:
    """Web配置属性

    Web应用的主配置类,包含所有Web相关的配置参数.
    支持配置驱动的背压控制和智能调度功能.
    """

    # 基础配置
    enabled: bool = True
    host: str = "0.0.0.0"
    port: int = 8080
    title: str = "Mini-Boot Application"
    description: str = "Mini-Boot Web Application"
    version: str = "1.0.0"
    log_level: str = "INFO"

    # 传统Web配置
    cors: Optional[CorsConfig] = None
    compression: Optional[CompressionConfig] = None
    logging: Optional[LoggingConfig] = None
    docs: Optional[DocsConfig] = None
    static: Optional[StaticConfig] = None

    # 新增:背压控制配置
    backpressure: BackpressureConfig = field(default_factory=BackpressureConfig)

    # 新增:异步优化配置
    async_optimization: AsyncOptimizationConfig = field(default_factory=AsyncOptimizationConfig)

    def __post_init__(self):
        """初始化后处理,确保子配置对象正确创建"""
        # 传统Web配置初始化
        if self.cors is None:
            self.cors = CorsConfig()
        if self.compression is None:
            self.compression = CompressionConfig()
        if self.logging is None:
            self.logging = LoggingConfig()
        if self.docs is None:
            self.docs = DocsConfig()
        if self.static is None:
            self.static = StaticConfig()

        # 新增配置初始化(使用 field(default_factory) 自动处理)
        # backpressure 和 async_optimization 会自动初始化

    @classmethod
    def from_environment(cls, environment) -> "WebProperties":
        """从环境配置创建Web配置

        Args:
            environment: 环境配置对象

        Returns:
            WebProperties实例
        """
        return cls(
            # 基础配置
            enabled=environment.get_property("miniboot.web.enabled", True),
            host=environment.get_property("miniboot.web.host", "0.0.0.0"),
            port=environment.get_property("miniboot.web.port", 8080),
            title=environment.get_property("miniboot.web.title", "Mini-Boot Application"),
            description=environment.get_property("miniboot.web.description", "Mini-Boot Web Application"),
            version=environment.get_property("miniboot.web.version", "1.0.0"),
            log_level=environment.get_property("miniboot.web.log-level", "INFO"),
            # 传统Web配置
            cors=CorsConfig(
                enabled=environment.get_property("miniboot.web.cors.enabled", True),
                allowed_origins=environment.get_property("miniboot.web.cors.allowed-origins", ["*"]),
                allowed_methods=environment.get_property("miniboot.web.cors.allowed-methods", ["GET", "POST", "PUT", "DELETE", "OPTIONS"]),
                allowed_headers=environment.get_property("miniboot.web.cors.allowed-headers", ["*"]),
                allow_credentials=environment.get_property("miniboot.web.cors.allow-credentials", True),
            ),
            compression=CompressionConfig(
                enabled=environment.get_property("miniboot.web.compression.enabled", True),
                min_size=environment.get_property("miniboot.web.compression.min-size", 1024),
            ),
            logging=LoggingConfig(
                enabled=environment.get_property("miniboot.web.logging.enabled", True),
                include_headers=environment.get_property("miniboot.web.logging.include-headers", False),
                include_body=environment.get_property("miniboot.web.logging.include-body", False),
            ),
            docs=DocsConfig(
                enabled=environment.get_property("miniboot.web.docs.enabled", True),
                title=environment.get_property("miniboot.web.docs.title", "Mini-Boot API"),
                description=environment.get_property("miniboot.web.docs.description", "Mini-Boot Web API Documentation"),
            ),
            static=StaticConfig(
                enabled=environment.get_property("miniboot.web.static.enabled", False),
                directory=environment.get_property("miniboot.web.static.directory", "static"),
                mount_path=environment.get_property("miniboot.web.static.mount-path", "/static"),
                html=environment.get_property("miniboot.web.static.html", True),
                check_dir=environment.get_property("miniboot.web.static.check-dir", True),
            ),
            # 新增:背压控制配置
            backpressure=BackpressureConfig(
                enabled=environment.get_property("miniboot.web.backpressure.enabled", True),
                strategy=BackpressureStrategy(environment.get_property("miniboot.web.backpressure.strategy", "adaptive")),
                max_concurrent_requests=environment.get_property("miniboot.web.backpressure.max-concurrent-requests", 1000),
                max_concurrent_controllers=environment.get_property("miniboot.web.backpressure.max-concurrent-controllers", 50),
                max_queue_size=environment.get_property("miniboot.web.backpressure.max-queue-size", 5000),
                load_check_interval=environment.get_property("miniboot.web.backpressure.load-check-interval", 1.0),
                cpu_threshold=environment.get_property("miniboot.web.backpressure.cpu-threshold", 0.8),
                memory_threshold=environment.get_property("miniboot.web.backpressure.memory-threshold", 0.85),
                response_time_threshold=environment.get_property("miniboot.web.backpressure.response-time-threshold", 2.0),
                adaptive_enabled=environment.get_property("miniboot.web.backpressure.adaptive-enabled", True),
                adjustment_factor=environment.get_property("miniboot.web.backpressure.adjustment-factor", 0.1),
                min_concurrent=environment.get_property("miniboot.web.backpressure.min-concurrent", 10),
                max_adjustment_step=environment.get_property("miniboot.web.backpressure.max-adjustment-step", 100),
                circuit_breaker_enabled=environment.get_property("miniboot.web.backpressure.circuit-breaker-enabled", True),
                failure_threshold=environment.get_property("miniboot.web.backpressure.failure-threshold", 50),
                failure_rate_threshold=environment.get_property("miniboot.web.backpressure.failure-rate-threshold", 0.5),
                recovery_timeout=environment.get_property("miniboot.web.backpressure.recovery-timeout", 30.0),
                half_open_max_calls=environment.get_property("miniboot.web.backpressure.half-open-max-calls", 10),
                degradation_enabled=environment.get_property("miniboot.web.backpressure.degradation-enabled", True),
                monitoring_enabled=environment.get_property("miniboot.web.backpressure.monitoring-enabled", True),
            ),
            # 新增:异步优化配置
            async_optimization=AsyncOptimizationConfig(
                enabled=environment.get_property("miniboot.web.async-optimization.enabled", True),
                intelligent_scheduling=environment.get_property("miniboot.web.async-optimization.intelligent-scheduling", True),
                performance_profiling=environment.get_property("miniboot.web.async-optimization.performance-profiling", True),
                classification_enabled=environment.get_property("miniboot.web.async-optimization.classification-enabled", True),
                adaptive_learning=environment.get_property("miniboot.web.async-optimization.adaptive-learning", True),
                learning_window_size=environment.get_property("miniboot.web.async-optimization.learning-window-size", 1000),
                learning_rate=environment.get_property("miniboot.web.async-optimization.learning-rate", 0.01),
                model_update_interval=environment.get_property("miniboot.web.async-optimization.model-update-interval", 100),
                monitoring_enabled=environment.get_property("miniboot.web.async-optimization.monitoring-enabled", True),
                metrics_collection_interval=environment.get_property("miniboot.web.async-optimization.metrics-collection-interval", 5.0),
                performance_history_size=environment.get_property("miniboot.web.async-optimization.performance-history-size", 10000),
            ),
        )

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Schedule Actuator 集成自动配置演示

展示如何使用 ScheduleActuatorAutoConfiguration 自动配置 Actuator 与调度系统的集成，
包括调度任务监控、调度器管理、任务执行指标等功能。

功能特性:
- 自动检测调度系统环境
- 自动创建调度相关端点
- 调度任务监控和管理
- 调度器状态监控
- 任务执行指标收集
"""

import asyncio
import time

# DEPRECATED: from miniboot.actuator.autoconfigure.schedule_actuator_auto_configuration import (
#     ScheduleActuatorAutoConfiguration,
#     ScheduleIntegrationStatus,
#     ScheduledTasksEndpoint,
#     SchedulerEndpoint
# )


class MockApplicationContext:
    """模拟应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = MockEnvironment()

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class MockEnvironment:
    """模拟环境配置"""

    def get_property(self, key, default=None):
        # 演示配置
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.schedule.enabled": True,
            "actuator.security.enabled": False,
        }
        return config.get(key, default)


async def create_demo_scheduler():
    """创建演示调度器"""
    try:
        from miniboot.annotations.schedule import ScheduledConfig
        from miniboot.schedule.scheduler import MiniBootScheduler
        from miniboot.schedule.tasks import TaskFactory

        # 创建调度器
        scheduler = MiniBootScheduler()

        # 创建一些演示任务
        def demo_task_1():
            print(f"🔄 Demo Task 1 executed at {time.strftime('%H:%M:%S')}")

        def demo_task_2():
            print(f"⚡ Demo Task 2 executed at {time.strftime('%H:%M:%S')}")

        async def async_demo_task():
            print(f"🚀 Async Demo Task executed at {time.strftime('%H:%M:%S')}")
            await asyncio.sleep(0.1)  # 模拟异步操作

        # 创建任务配置
        config1 = ScheduledConfig(fixed_rate="5s")
        config2 = ScheduledConfig(fixed_delay="3s")
        config3 = ScheduledConfig(cron="0 */1 * * * *")  # 每分钟执行

        # 创建任务
        task1 = TaskFactory.create_lambda_task(demo_task_1, config1, "demo-task-1")
        task2 = TaskFactory.create_lambda_task(demo_task_2, config2, "demo-task-2")
        task3 = TaskFactory.create_lambda_task(async_demo_task, config3, "async-demo-task")

        # 启动调度器
        scheduler.start()

        # 调度任务
        scheduler.schedule_task(task1)
        scheduler.schedule_task(task2)
        scheduler.schedule_task(task3)

        print("✅ Demo scheduler created with 3 tasks")
        return scheduler

    except ImportError as e:
        print(f"⚠️  Schedule module not available: {e}")
        return None
    except Exception as e:
        print(f"❌ Failed to create demo scheduler: {e}")
        return None


async def test_schedule_actuator_auto_configuration():
    """测试 Schedule Actuator 自动配置"""
    print("🧪 Testing Schedule Actuator Auto Configuration...")

    try:
        # 1. 创建模拟应用上下文
        app_context = MockApplicationContext()

        # 2. 创建演示调度器
        scheduler = await create_demo_scheduler()
        if scheduler:
            app_context.register_singleton("scheduler", scheduler)

        # 3. 创建 Schedule 自动配置实例
        schedule_config = ScheduleActuatorAutoConfiguration(app_context)
        print(f"📊 Initial status: {schedule_config.status.value}")

        # 4. 执行自动配置
        print("🔧 Executing Schedule Actuator auto configuration...")
        await schedule_config.configure()

        # 5. 检查配置结果
        print(f"📊 Final status: {schedule_config.status.value}")

        # 6. 获取集成状态
        status_info = schedule_config.get_integration_status()
        print("\n📈 Integration Status:")
        for key, value in status_info.items():
            if key != "metrics":
                print(f"  - {key}: {value}")

        print("📊 Metrics:")
        for key, value in status_info["metrics"].items():
            print(f"  - {key}: {value}")

        # 7. 测试端点功能
        await test_schedule_endpoints(schedule_config)

        # 8. 等待一段时间观察任务执行
        if scheduler:
            print("\n⏰ Observing task execution for 10 seconds...")
            await asyncio.sleep(10)

            # 刷新指标
            await schedule_config.refresh_metrics()
            updated_status = schedule_config.get_integration_status()
            print(f"\n📊 Updated metrics after 10 seconds:")
            for key, value in updated_status["metrics"].items():
                print(f"  - {key}: {value}")

        print("\n✅ Schedule Actuator Auto Configuration test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_schedule_endpoints(schedule_config):
    """测试调度端点功能"""
    print("\n🔌 Testing Schedule Endpoints...")

    try:
        scheduler_manager = schedule_config.get_scheduler_manager()
        if not scheduler_manager:
            print("⚠️  No scheduler available for endpoint testing")
            return

        # 测试调度任务端点
        tasks_endpoint = ScheduledTasksEndpoint(scheduler_manager)

        print("📋 Testing ScheduledTasks endpoint:")
        tasks_info = tasks_endpoint.invoke(tasks_endpoint.operations()[0].operation_type)
        print(f"  - Total tasks: {tasks_info.get('summary', {}).get('total', 0)}")
        print(f"  - Running tasks: {tasks_info.get('summary', {}).get('running', 0)}")
        print(f"  - Scheduler status: {tasks_info.get('scheduler_status', 'unknown')}")

        # 测试调度器端点
        scheduler_endpoint = SchedulerEndpoint(scheduler_manager)

        print("\n⚙️  Testing Scheduler endpoint:")
        scheduler_info = scheduler_endpoint.invoke(scheduler_endpoint.operations()[0].operation_type)
        print(f"  - Status: {scheduler_info.get('status', 'unknown')}")
        print(f"  - State: {scheduler_info.get('state', 'unknown')}")
        print(f"  - Total tasks: {scheduler_info.get('total_tasks', 0)}")
        print(f"  - Running tasks: {scheduler_info.get('running_tasks', 0)}")

        print("✅ Schedule endpoints tested successfully")

    except Exception as e:
        print(f"❌ Endpoint testing failed: {e}")


async def test_integration_components():
    """测试集成组件"""
    print("\n🧪 Testing Integration Components...")

    try:
        # 测试状态枚举
        print("📊 Integration Status Values:")
        for status in ScheduleIntegrationStatus:
            print(f"  - {status.name}: {status.value}")

        # 测试导入的组件
        # DEPRECATED: from miniboot.actuator.autoconfigure.schedule_actuator_auto_configuration import \
            ScheduleMetrics

        print("✅ All components imported successfully!")
        print("  - ScheduleActuatorAutoConfiguration: ✓")
        print("  - ScheduledTasksEndpoint: ✓")
        print("  - SchedulerEndpoint: ✓")
        print("  - ScheduleIntegrationStatus: ✓")
        print("  - ScheduleMetrics: ✓")

        return True

    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Starting Schedule Actuator Integration Tests...\n")

    # 测试组件导入
    component_test = await test_integration_components()

    # 测试自动配置
    config_test = await test_schedule_actuator_auto_configuration()

    # 总结
    print(f"\n📋 Test Summary:")
    print(f"  - Component Import Test: {'✅ PASSED' if component_test else '❌ FAILED'}")
    print(f"  - Auto Configuration Test: {'✅ PASSED' if config_test else '❌ FAILED'}")

    if component_test and config_test:
        print("\n🎉 All tests passed! Schedule Actuator integration is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python
"""
* @author: cz
* @description: 应用启动流程测试

测试ApplicationContext的完整启动流程，包括配置加载、日志初始化、Banner显示、组件启动、Bean管理等。
"""

import unittest
from unittest.mock import patch

from miniboot.context import DefaultApplicationContext


class StartupFlowTestCase(unittest.IsolatedAsyncioTestCase):
    """应用启动流程测试"""

    def setUp(self):
        """测试前置设置"""
        # 使用测试配置，禁用服务器模块
        self.context = DefaultApplicationContext(config_path="tests/resources/test_config_no_web.yml")

    async def asyncTearDown(self):
        """异步测试后置清理"""
        if self.context.is_running():
            await self.context.stop()

    async def test_complete_startup_flow(self):
        """测试完整的启动流程"""
        # 测试启动前状态
        self.assertFalse(self.context.is_running())

        # 执行启动
        await self.context.start()

        # 验证启动后状态
        self.assertTrue(self.context.is_running())

        # 验证核心组件已初始化
        self.assertIsNotNone(self.context.get_environment())
        self.assertIsNotNone(self.context.get_bean_factory())

    async def test_startup_with_configuration(self):
        """测试带配置的启动流程"""
        context = DefaultApplicationContext(config_path="tests/resources/test_config_no_web.yml")

        try:
            await context.start()
            self.assertTrue(context.is_running())

            # 验证配置路径设置
            self.assertEqual(context._config_path, "tests/resources/test_config_no_web.yml")
        finally:
            if context.is_running():
                await context.stop()

    async def test_startup_with_package_scanning(self):
        """测试带包扫描的启动流程"""
        context = DefaultApplicationContext(config_path="tests/resources/test_config_no_web.yml", packages_to_scan=["miniboot.test"])

        try:
            await context.start()
            self.assertTrue(context.is_running())

            # 验证包扫描配置
            self.assertEqual(context._packages_to_scan, ["miniboot.test"])
        finally:
            if context.is_running():
                await context.stop()

    async def test_startup_flow_steps(self):
        """测试启动流程各个步骤"""
        with (
            patch.object(self.context, "_initialize_logging") as mock_logging,
            patch.object(self.context, "_display_banner") as mock_banner,
            patch.object(self.context, "_initialize_environment") as mock_env,
            patch.object(self.context, "_initialize_event_publisher") as mock_event_pub,
            patch.object(self.context, "_scan_and_register_components") as mock_scan,
            patch.object(self.context, "_register_post_processors") as mock_processors,
            patch.object(self.context, "_create_singleton_beans") as mock_beans,
            patch.object(self.context, "_initialize_modules") as mock_modules,
            patch.object(self.context, "_start_lifecycle_components") as mock_lifecycle,
            patch.object(self.context, "_publish_startup_event") as mock_event,
            patch.object(self.context, "_log_startup_info") as mock_info,
        ):
            await self.context.start()

            # 验证所有步骤都被调用
            mock_logging.assert_called_once()
            mock_banner.assert_called_once()
            mock_env.assert_called_once()
            mock_event_pub.assert_called_once()
            mock_scan.assert_called_once()
            mock_processors.assert_called_once()
            mock_beans.assert_called_once()
            mock_modules.assert_called_once()
            mock_lifecycle.assert_called_once()
            mock_event.assert_called_once()
            mock_info.assert_called_once()

    async def test_startup_error_handling(self):
        """测试启动错误处理"""
        with patch.object(self.context, "_initialize_logging", side_effect=Exception("Test error")):
            with self.assertRaises((RuntimeError, ValueError, Exception)):
                await self.context.start()

            # 验证启动失败后状态
            self.assertFalse(self.context.is_running())

    async def test_duplicate_startup_prevention(self):
        """测试防止重复启动"""
        # 第一次启动
        await self.context.start()
        self.assertTrue(self.context.is_running())

        # 第二次启动应该无效果
        await self.context.start()
        self.assertTrue(self.context.is_running())

    async def test_startup_logging_output(self):
        """测试启动日志输出"""
        with patch("miniboot.context.application.logger") as mock_logger:
            await self.context.start()

            # 验证关键日志被记录
            mock_logger.info.assert_any_call("Starting application context...")
            mock_logger.info.assert_any_call("Application context started successfully")

    async def test_banner_display(self):
        """测试Banner显示"""
        # 测试Banner显示方法
        await self.context._display_banner()

        # 测试禁用Banner
        with patch.object(self.context, "get_property", return_value=False):
            await self.context._display_banner()

    async def test_configuration_loading(self):
        """测试配置加载"""
        # 测试无配置路径
        await self.context._load_configuration()

        # 测试有配置路径
        self.context._config_path = "test.yml"
        await self.context._load_configuration()

    async def test_component_scanning(self):
        """测试组件扫描"""
        # 测试无包扫描
        await self.context._scan_and_register_components()

        # 测试有包扫描
        self.context._packages_to_scan = ["test.package"]
        await self.context._scan_and_register_components()

    async def test_post_processor_registration(self):
        """测试后置处理器注册"""
        await self.context._register_post_processors()

    async def test_singleton_bean_creation(self):
        """测试单例Bean创建"""
        await self.context._create_singleton_beans()

    async def test_lifecycle_component_startup(self):
        """测试生命周期组件启动"""
        await self.context._start_lifecycle_components()

    async def test_startup_event_publishing(self):
        """测试启动事件发布"""
        await self.context._publish_startup_event()

    async def test_startup_info_logging(self):
        """测试启动信息记录"""
        await self.context._log_startup_info()

    async def test_environment_integration(self):
        """测试环境集成"""
        await self.context.start()

        # 验证环境配置可用
        env = self.context.get_environment()
        self.assertIsNotNone(env)

        # 测试属性获取
        value = self.context.get_property("test.key", "default")
        self.assertEqual(value, "default")

    async def test_bean_factory_integration(self):
        """测试Bean工厂集成"""
        await self.context.start()

        # 验证Bean工厂可用
        factory = self.context.get_bean_factory()
        self.assertIsNotNone(factory)

    async def test_event_publisher_integration(self):
        """测试事件发布器集成"""
        await self.context.start()

        # 测试事件发布
        try:
            self.context.publish_event("test_event")
            await self.context.publish_event_async("test_async_event")
        except Exception:
            # 如果事件发布失败，这是可以接受的
            pass


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean工具类单元测试
"""

import unittest
from typing import Any, Dict, List
from unittest.mock import Mock, patch

from miniboot.bean.utils import (
    BeanCreationError, 
    NoSuchBeanDefinitionError, 
    CircularDependencyError,
    DependencyGraph
)


class TestBeanExceptions(unittest.TestCase):
    """Bean异常类测试"""
    
    def test_bean_creation_error(self):
        """测试Bean创建错误"""
        # 基本异常
        error = BeanCreationError("Test error")
        self.assertEqual(str(error), "Test error")
        self.assertIsInstance(error, Exception)
        
        # 带Bean名称的异常
        error_with_name = BeanCreationError("Creation failed", bean_name="testBean")
        self.assertIn("testBean", str(error_with_name))
        self.assertEqual(error_with_name.bean_name, "testBean")
        
        # 带原因的异常
        cause = ValueError("Original error")
        error_with_cause = BeanCreationError("Wrapper error", cause=cause)
        self.assertEqual(error_with_cause.cause, cause)
    
    def test_no_such_bean_definition_error(self):
        """测试Bean定义不存在错误"""
        # 基本异常
        error = NoSuchBeanDefinitionError("Bean not found")
        self.assertEqual(str(error), "Bean not found")
        self.assertIsInstance(error, Exception)
        
        # 带Bean名称的异常
        error_with_name = NoSuchBeanDefinitionError("Not found", bean_name="missingBean")
        self.assertIn("missingBean", str(error_with_name))
        self.assertEqual(error_with_name.bean_name, "missingBean")
    
    def test_circular_dependency_error(self):
        """测试循环依赖错误"""
        # 基本异常
        error = CircularDependencyError("Circular dependency detected")
        self.assertEqual(str(error), "Circular dependency detected")
        self.assertIsInstance(error, Exception)
        
        # 带依赖路径的异常
        dependency_path = ["beanA", "beanB", "beanC", "beanA"]
        error_with_path = CircularDependencyError(
            "Circular dependency", 
            dependency_path=dependency_path
        )
        self.assertEqual(error_with_path.dependency_path, dependency_path)
        self.assertIn("beanA", str(error_with_path))


class TestDependencyGraph(unittest.TestCase):
    """依赖图测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.graph = DependencyGraph()
    
    def test_graph_creation(self):
        """测试依赖图创建"""
        self.assertIsNotNone(self.graph)
        self.assertIsInstance(self.graph, DependencyGraph)
    
    def test_add_dependency(self):
        """测试添加依赖关系"""
        # 添加简单依赖
        self.graph.add_dependency("beanA", "beanB")
        
        # 验证依赖关系
        dependencies = self.graph.get_dependencies("beanA")
        self.assertIn("beanB", dependencies)
        
        # 添加多个依赖
        self.graph.add_dependency("beanA", "beanC")
        self.graph.add_dependency("beanA", "beanD")
        
        dependencies = self.graph.get_dependencies("beanA")
        self.assertIn("beanB", dependencies)
        self.assertIn("beanC", dependencies)
        self.assertIn("beanD", dependencies)
        self.assertEqual(len(dependencies), 3)
    
    def test_remove_dependency(self):
        """测试移除依赖关系"""
        # 添加依赖
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanA", "beanC")
        
        # 移除一个依赖
        self.graph.remove_dependency("beanA", "beanB")
        
        dependencies = self.graph.get_dependencies("beanA")
        self.assertNotIn("beanB", dependencies)
        self.assertIn("beanC", dependencies)
        
        # 移除不存在的依赖（应该不报错）
        self.graph.remove_dependency("beanA", "nonexistent")
    
    def test_has_dependency(self):
        """测试检查依赖关系"""
        # 初始状态
        self.assertFalse(self.graph.has_dependency("beanA", "beanB"))
        
        # 添加依赖后
        self.graph.add_dependency("beanA", "beanB")
        self.assertTrue(self.graph.has_dependency("beanA", "beanB"))
        self.assertFalse(self.graph.has_dependency("beanB", "beanA"))
    
    def test_circular_dependency_detection(self):
        """测试循环依赖检测"""
        # 简单循环：A -> B -> A
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanB", "beanA")
        
        self.assertTrue(self.graph.has_circular_dependency("beanA"))
        self.assertTrue(self.graph.has_circular_dependency("beanB"))
        
        # 复杂循环：A -> B -> C -> A
        self.graph.clear()
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanB", "beanC")
        self.graph.add_dependency("beanC", "beanA")
        
        self.assertTrue(self.graph.has_circular_dependency("beanA"))
        self.assertTrue(self.graph.has_circular_dependency("beanB"))
        self.assertTrue(self.graph.has_circular_dependency("beanC"))
    
    def test_no_circular_dependency(self):
        """测试无循环依赖情况"""
        # 线性依赖：A -> B -> C
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanB", "beanC")
        
        self.assertFalse(self.graph.has_circular_dependency("beanA"))
        self.assertFalse(self.graph.has_circular_dependency("beanB"))
        self.assertFalse(self.graph.has_circular_dependency("beanC"))
        
        # 树形依赖：A -> B, A -> C, B -> D
        self.graph.add_dependency("beanA", "beanC")
        self.graph.add_dependency("beanB", "beanD")
        
        self.assertFalse(self.graph.has_circular_dependency("beanA"))
    
    def test_get_circular_dependency_path(self):
        """测试获取循环依赖路径"""
        # 创建循环依赖
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanB", "beanC")
        self.graph.add_dependency("beanC", "beanA")
        
        # 获取循环路径
        path = self.graph.get_circular_dependency_path("beanA")
        self.assertIsNotNone(path)
        self.assertIn("beanA", path)
        self.assertIn("beanB", path)
        self.assertIn("beanC", path)
        
        # 路径应该以相同Bean开始和结束
        self.assertEqual(path[0], path[-1])
    
    def test_topological_sort(self):
        """测试拓扑排序"""
        # 创建有向无环图
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanA", "beanC")
        self.graph.add_dependency("beanB", "beanD")
        self.graph.add_dependency("beanC", "beanD")
        
        # 获取拓扑排序
        sorted_beans = self.graph.topological_sort()
        
        # 验证排序结果
        self.assertIn("beanA", sorted_beans)
        self.assertIn("beanB", sorted_beans)
        self.assertIn("beanC", sorted_beans)
        self.assertIn("beanD", sorted_beans)
        
        # 验证依赖顺序
        a_index = sorted_beans.index("beanA")
        b_index = sorted_beans.index("beanB")
        c_index = sorted_beans.index("beanC")
        d_index = sorted_beans.index("beanD")
        
        self.assertLess(b_index, a_index)  # B应该在A之前
        self.assertLess(c_index, a_index)  # C应该在A之前
        self.assertLess(d_index, b_index)  # D应该在B之前
        self.assertLess(d_index, c_index)  # D应该在C之前
    
    def test_topological_sort_with_cycle(self):
        """测试有循环的拓扑排序"""
        # 创建循环依赖
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanB", "beanA")
        
        # 有循环时应该抛出异常
        with self.assertRaises(CircularDependencyError):
            self.graph.topological_sort()
    
    def test_get_all_dependencies(self):
        """测试获取所有依赖"""
        # 创建依赖关系
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanB", "beanC")
        self.graph.add_dependency("beanA", "beanD")
        
        # 获取直接依赖
        direct_deps = self.graph.get_dependencies("beanA")
        self.assertEqual(set(direct_deps), {"beanB", "beanD"})
        
        # 获取所有传递依赖
        all_deps = self.graph.get_all_dependencies("beanA")
        self.assertEqual(set(all_deps), {"beanB", "beanC", "beanD"})
    
    def test_get_dependents(self):
        """测试获取依赖者"""
        # 创建依赖关系
        self.graph.add_dependency("beanA", "beanC")
        self.graph.add_dependency("beanB", "beanC")
        self.graph.add_dependency("beanC", "beanD")
        
        # 获取依赖beanC的Bean
        dependents = self.graph.get_dependents("beanC")
        self.assertEqual(set(dependents), {"beanA", "beanB"})
        
        # 获取依赖beanD的Bean
        dependents_d = self.graph.get_dependents("beanD")
        self.assertEqual(set(dependents_d), {"beanC"})
    
    def test_graph_clear(self):
        """测试清空依赖图"""
        # 添加一些依赖
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanB", "beanC")
        
        # 验证依赖存在
        self.assertTrue(self.graph.has_dependency("beanA", "beanB"))
        
        # 清空图
        self.graph.clear()
        
        # 验证依赖已清空
        self.assertFalse(self.graph.has_dependency("beanA", "beanB"))
        self.assertEqual(len(self.graph.get_dependencies("beanA")), 0)
    
    def test_graph_statistics(self):
        """测试依赖图统计"""
        # 添加依赖
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanA", "beanC")
        self.graph.add_dependency("beanB", "beanD")
        
        # 获取统计信息
        stats = self.graph.get_statistics()
        
        self.assertIn("node_count", stats)
        self.assertIn("edge_count", stats)
        self.assertIn("has_cycles", stats)
        
        self.assertEqual(stats["node_count"], 4)  # A, B, C, D
        self.assertEqual(stats["edge_count"], 3)  # A->B, A->C, B->D
        self.assertFalse(stats["has_cycles"])
    
    def test_graph_export_import(self):
        """测试依赖图导出导入"""
        # 创建依赖关系
        self.graph.add_dependency("beanA", "beanB")
        self.graph.add_dependency("beanB", "beanC")
        
        # 导出图
        exported_data = self.graph.export()
        self.assertIsInstance(exported_data, dict)
        
        # 创建新图并导入
        new_graph = DependencyGraph()
        new_graph.import_data(exported_data)
        
        # 验证导入结果
        self.assertTrue(new_graph.has_dependency("beanA", "beanB"))
        self.assertTrue(new_graph.has_dependency("beanB", "beanC"))
        self.assertEqual(
            set(new_graph.get_dependencies("beanA")),
            set(self.graph.get_dependencies("beanA"))
        )


if __name__ == '__main__':
    unittest.main()

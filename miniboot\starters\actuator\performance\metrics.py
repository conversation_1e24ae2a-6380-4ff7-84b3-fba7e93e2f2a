#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: PerformanceMetrics - 性能指标收集系统

提供全面的性能指标收集和监控功能,包括:
- 响应时间监控
- 吞吐量统计
- 并发数监控
- 资源使用率监控
- 错误率统计
- 性能趋势分析
"""

import asyncio
import threading
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Union

from loguru import logger


class MetricType(Enum):
    """指标类型枚举"""

    COUNTER = "counter"  # 计数器
    GAUGE = "gauge"  # 仪表盘
    HISTOGRAM = "histogram"  # 直方图
    TIMER = "timer"  # 计时器
    RATE = "rate"  # 速率


@dataclass
class MetricValue:
    """指标值"""

    value: Union[int, float]
    timestamp: float = field(default_factory=time.time)
    tags: Dict[str, str] = field(default_factory=dict)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {"value": self.value, "timestamp": self.timestamp, "tags": self.tags, "datetime": datetime.fromtimestamp(self.timestamp).isoformat()}


@dataclass
class ResponseTimeMetrics:
    """响应时间指标"""

    total_requests: int = 0
    total_time: float = 0.0
    min_time: float = float("inf")
    max_time: float = 0.0
    p50: float = 0.0
    p95: float = 0.0
    p99: float = 0.0
    recent_times: deque = field(default_factory=lambda: deque(maxlen=1000))

    def add_time(self, response_time: float) -> None:
        """添加响应时间"""
        self.total_requests += 1
        self.total_time += response_time
        self.min_time = min(self.min_time, response_time)
        self.max_time = max(self.max_time, response_time)
        self.recent_times.append(response_time)

        # 计算百分位数
        if len(self.recent_times) >= 10:
            sorted_times = sorted(self.recent_times)
            self.p50 = sorted_times[len(sorted_times) // 2]
            self.p95 = sorted_times[int(len(sorted_times) * 0.95)]
            self.p99 = sorted_times[int(len(sorted_times) * 0.99)]

    @property
    def average_time(self) -> float:
        """平均响应时间"""
        return self.total_time / max(self.total_requests, 1)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_requests": self.total_requests,
            "average_time": self.average_time,
            "min_time": self.min_time if self.min_time != float("inf") else 0.0,
            "max_time": self.max_time,
            "p50": self.p50,
            "p95": self.p95,
            "p99": self.p99,
        }


@dataclass
class ThroughputMetrics:
    """吞吐量指标"""

    total_requests: int = 0
    start_time: float = field(default_factory=time.time)
    last_minute_requests: deque = field(default_factory=lambda: deque(maxlen=60))
    last_hour_requests: deque = field(default_factory=lambda: deque(maxlen=3600))

    def add_request(self) -> None:
        """添加请求"""
        current_time = time.time()
        self.total_requests += 1
        self.last_minute_requests.append(current_time)
        self.last_hour_requests.append(current_time)

    @property
    def requests_per_second(self) -> float:
        """每秒请求数"""
        current_time = time.time()
        minute_ago = current_time - 60
        recent_requests = [t for t in self.last_minute_requests if t > minute_ago]
        return len(recent_requests) / 60.0

    @property
    def requests_per_minute(self) -> float:
        """每分钟请求数"""
        return len(self.last_minute_requests)

    @property
    def requests_per_hour(self) -> float:
        """每小时请求数"""
        return len(self.last_hour_requests)

    @property
    def average_throughput(self) -> float:
        """平均吞吐量(总体)"""
        elapsed = time.time() - self.start_time
        return self.total_requests / max(elapsed, 1)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_requests": self.total_requests,
            "requests_per_second": self.requests_per_second,
            "requests_per_minute": self.requests_per_minute,
            "requests_per_hour": self.requests_per_hour,
            "average_throughput": self.average_throughput,
        }


@dataclass
class ConcurrencyMetrics:
    """并发指标"""

    current_concurrent: int = 0
    max_concurrent: int = 0
    total_concurrent_time: float = 0.0
    concurrent_history: deque = field(default_factory=lambda: deque(maxlen=1000))

    def start_request(self) -> None:
        """开始请求"""
        self.current_concurrent += 1
        self.max_concurrent = max(self.max_concurrent, self.current_concurrent)
        self.concurrent_history.append((time.time(), self.current_concurrent))

    def end_request(self) -> None:
        """结束请求"""
        self.current_concurrent = max(0, self.current_concurrent - 1)
        self.concurrent_history.append((time.time(), self.current_concurrent))

    @property
    def average_concurrent(self) -> float:
        """平均并发数"""
        if not self.concurrent_history:
            return 0.0
        return sum(count for _, count in self.concurrent_history) / len(self.concurrent_history)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {"current_concurrent": self.current_concurrent, "max_concurrent": self.max_concurrent, "average_concurrent": self.average_concurrent}


@dataclass
class ErrorMetrics:
    """错误指标"""

    total_errors: int = 0
    error_types: Dict[str, int] = field(default_factory=dict)
    recent_errors: deque = field(default_factory=lambda: deque(maxlen=100))

    def add_error(self, error_type: str = "unknown", error_message: str = "") -> None:
        """添加错误"""
        self.total_errors += 1
        self.error_types[error_type] = self.error_types.get(error_type, 0) + 1
        self.recent_errors.append({"type": error_type, "message": error_message, "timestamp": time.time()})

    def get_error_rate(self, total_requests: int) -> float:
        """获取错误率"""
        return self.total_errors / max(total_requests, 1)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "total_errors": self.total_errors,
            "error_types": self.error_types,
            "recent_errors": list(self.recent_errors)[-10:],  # 最近10个错误
        }


class PerformanceMetrics:
    """性能指标收集器"""

    def __init__(self, name: str):
        """初始化性能指标收集器

        Args:
            name: 指标名称
        """
        self.name = name
        self.start_time = time.time()

        # 各类指标
        self.response_time = ResponseTimeMetrics()
        self.throughput = ThroughputMetrics()
        self.concurrency = ConcurrencyMetrics()
        self.errors = ErrorMetrics()

        # 自定义指标
        self.custom_metrics: Dict[str, List[MetricValue]] = defaultdict(list)

        # 线程安全
        self._lock = threading.RLock()

        logger.debug(f"PerformanceMetrics initialized for {name}")

    def record_request_start(self) -> str:
        """记录请求开始

        Returns:
            str: 请求ID
        """
        with self._lock:
            request_id = f"{self.name}_{time.time()}_{id(self)}"
            self.throughput.add_request()
            self.concurrency.start_request()
            return request_id

    def record_request_end(self, request_id: str, response_time: float, error: Optional[Exception] = None) -> None:
        """记录请求结束

        Args:
            request_id: 请求ID
            response_time: 响应时间(秒)
            error: 错误信息(如果有)
        """
        with self._lock:
            self.response_time.add_time(response_time)
            self.concurrency.end_request()

            if error:
                error_type = type(error).__name__
                self.errors.add_error(error_type, str(error))

    def record_custom_metric(self, metric_name: str, value: Union[int, float], tags: Optional[Dict[str, str]] = None) -> None:
        """记录自定义指标

        Args:
            metric_name: 指标名称
            value: 指标值
            tags: 标签
        """
        with self._lock:
            metric_value = MetricValue(value, tags=tags or {})
            self.custom_metrics[metric_name].append(metric_value)

            # 保持最近1000个值
            if len(self.custom_metrics[metric_name]) > 1000:
                self.custom_metrics[metric_name] = self.custom_metrics[metric_name][-1000:]

    def get_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self._lock:
            uptime = time.time() - self.start_time

            return {
                "name": self.name,
                "uptime": uptime,
                "response_time": self.response_time.to_dict(),
                "throughput": self.throughput.to_dict(),
                "concurrency": self.concurrency.to_dict(),
                "errors": self.errors.to_dict(),
                "error_rate": self.errors.get_error_rate(self.throughput.total_requests),
                "custom_metrics_count": len(self.custom_metrics),
                "timestamp": time.time(),
            }

    def get_custom_metrics(self, metric_name: Optional[str] = None) -> Dict[str, Any]:
        """获取自定义指标

        Args:
            metric_name: 指标名称,None表示获取所有

        Returns:
            Dict[str, Any]: 自定义指标数据
        """
        with self._lock:
            if metric_name:
                values = self.custom_metrics.get(metric_name, [])
                return {
                    metric_name: [v.to_dict() for v in values[-100:]]  # 最近100个值
                }
            else:
                result = {}
                for name, values in self.custom_metrics.items():
                    result[name] = [v.to_dict() for v in values[-100:]]
                return result

    def reset(self) -> None:
        """重置所有指标"""
        with self._lock:
            self.start_time = time.time()
            self.response_time = ResponseTimeMetrics()
            self.throughput = ThroughputMetrics()
            self.concurrency = ConcurrencyMetrics()
            self.errors = ErrorMetrics()
            self.custom_metrics.clear()

            logger.debug(f"PerformanceMetrics reset for {self.name}")


class PerformanceMetricsRegistry:
    """性能指标注册表"""

    def __init__(self):
        """初始化性能指标注册表"""
        self._metrics: Dict[str, PerformanceMetrics] = {}
        self._lock = threading.RLock()

        logger.info("PerformanceMetricsRegistry initialized")

    def get_or_create_metrics(self, name: str) -> PerformanceMetrics:
        """获取或创建性能指标

        Args:
            name: 指标名称

        Returns:
            PerformanceMetrics: 性能指标实例
        """
        with self._lock:
            if name not in self._metrics:
                self._metrics[name] = PerformanceMetrics(name)
                logger.debug(f"Created new PerformanceMetrics: {name}")
            return self._metrics[name]

    def get_metrics(self, name: str) -> Optional[PerformanceMetrics]:
        """获取性能指标

        Args:
            name: 指标名称

        Returns:
            Optional[PerformanceMetrics]: 性能指标实例
        """
        with self._lock:
            return self._metrics.get(name)

    def get_all_metrics(self) -> Dict[str, PerformanceMetrics]:
        """获取所有性能指标"""
        with self._lock:
            return self._metrics.copy()

    def remove_metrics(self, name: str) -> bool:
        """移除性能指标

        Args:
            name: 指标名称

        Returns:
            bool: 是否成功移除
        """
        with self._lock:
            if name in self._metrics:
                del self._metrics[name]
                logger.debug(f"Removed PerformanceMetrics: {name}")
                return True
            return False

    def get_summary(self) -> Dict[str, Any]:
        """获取所有指标摘要"""
        with self._lock:
            summary = {"total_metrics": len(self._metrics), "metrics": {}, "timestamp": time.time()}

            for name, metrics in self._metrics.items():
                summary["metrics"][name] = metrics.get_summary()

            return summary

    def clear(self) -> None:
        """清空所有指标"""
        with self._lock:
            self._metrics.clear()
            logger.info("All performance metrics cleared")


# 全局性能指标注册表
_global_registry = PerformanceMetricsRegistry()


def get_metrics(name: str) -> PerformanceMetrics:
    """获取性能指标实例

    Args:
        name: 指标名称

    Returns:
        PerformanceMetrics: 性能指标实例
    """
    return _global_registry.get_or_create_metrics(name)


def get_metrics_registry() -> PerformanceMetricsRegistry:
    """获取全局性能指标注册表

    Returns:
        PerformanceMetricsRegistry: 性能指标注册表
    """
    return _global_registry


class PerformanceAnalyzer:
    """性能分析器 - 提供智能的性能分析和优化建议"""

    def __init__(self, registry: Optional[PerformanceMetricsRegistry] = None):
        """初始化性能分析器

        Args:
            registry: 性能指标注册表
        """
        self.registry = registry or get_metrics_registry()

        # 性能阈值配置
        self.thresholds = {
            "response_time_warning": 1.0,  # 响应时间警告阈值(秒)
            "response_time_critical": 5.0,  # 响应时间严重阈值(秒)
            "error_rate_warning": 0.05,  # 错误率警告阈值(5%)
            "error_rate_critical": 0.10,  # 错误率严重阈值(10%)
            "throughput_min": 1.0,  # 最小吞吐量(请求/秒)
            "concurrency_max": 100,  # 最大并发数
        }

        logger.debug("PerformanceAnalyzer initialized")

    def analyze_all_metrics(self) -> Dict[str, Any]:
        """分析所有性能指标

        Returns:
            Dict[str, Any]: 分析结果
        """
        all_metrics = self.registry.get_all_metrics()
        analysis_results = {}

        for name, metrics in all_metrics.items():
            analysis_results[name] = self.analyze_metrics(metrics)

        # 生成总体分析
        overall_analysis = self._generate_overall_analysis(analysis_results)

        return {"individual_analysis": analysis_results, "overall_analysis": overall_analysis, "timestamp": time.time()}

    def analyze_metrics(self, metrics: PerformanceMetrics) -> Dict[str, Any]:
        """分析单个性能指标

        Args:
            metrics: 性能指标实例

        Returns:
            Dict[str, Any]: 分析结果
        """
        summary = metrics.get_summary()

        # 响应时间分析
        response_time_analysis = self._analyze_response_time(summary["response_time"])

        # 吞吐量分析
        throughput_analysis = self._analyze_throughput(summary["throughput"])

        # 错误率分析
        error_analysis = self._analyze_errors(summary["errors"], summary["throughput"]["total_requests"])

        # 并发分析
        concurrency_analysis = self._analyze_concurrency(summary["concurrency"])

        # 生成优化建议
        recommendations = self._generate_recommendations(response_time_analysis, throughput_analysis, error_analysis, concurrency_analysis)

        # 计算健康分数
        health_score = self._calculate_health_score(response_time_analysis, throughput_analysis, error_analysis, concurrency_analysis)

        return {
            "health_score": health_score,
            "response_time_analysis": response_time_analysis,
            "throughput_analysis": throughput_analysis,
            "error_analysis": error_analysis,
            "concurrency_analysis": concurrency_analysis,
            "recommendations": recommendations,
            "timestamp": time.time(),
        }

    def _analyze_response_time(self, rt_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析响应时间"""
        avg_time = rt_data["average_time"]
        max_time = rt_data["max_time"]
        p95 = rt_data["p95"]
        p99 = rt_data["p99"]

        status = "good"
        issues = []

        if avg_time > self.thresholds["response_time_critical"]:
            status = "critical"
            issues.append(f"Average response time ({avg_time:.3f}s) exceeds critical threshold")
        elif avg_time > self.thresholds["response_time_warning"]:
            status = "warning"
            issues.append(f"Average response time ({avg_time:.3f}s) exceeds warning threshold")

        if p99 > self.thresholds["response_time_critical"] * 2:
            issues.append(f"P99 response time ({p99:.3f}s) is very high")

        return {"status": status, "issues": issues, "metrics": rt_data}

    def _analyze_throughput(self, tp_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析吞吐量"""
        rps = tp_data["requests_per_second"]
        total_requests = tp_data["total_requests"]

        status = "good"
        issues = []

        if rps < self.thresholds["throughput_min"] and total_requests > 10:
            status = "warning"
            issues.append(f"Low throughput ({rps:.2f} req/s)")

        return {"status": status, "issues": issues, "metrics": tp_data}

    def _analyze_errors(self, error_data: Dict[str, Any], total_requests: int) -> Dict[str, Any]:
        """分析错误率"""
        total_errors = error_data["total_errors"]
        error_rate = total_errors / max(total_requests, 1)

        status = "good"
        issues = []

        if error_rate > self.thresholds["error_rate_critical"]:
            status = "critical"
            issues.append(f"High error rate ({error_rate:.1%})")
        elif error_rate > self.thresholds["error_rate_warning"]:
            status = "warning"
            issues.append(f"Elevated error rate ({error_rate:.1%})")

        return {"status": status, "issues": issues, "error_rate": error_rate, "metrics": error_data}

    def _analyze_concurrency(self, conc_data: Dict[str, Any]) -> Dict[str, Any]:
        """分析并发"""
        current = conc_data["current_concurrent"]
        max_concurrent = conc_data["max_concurrent"]

        status = "good"
        issues = []

        if max_concurrent > self.thresholds["concurrency_max"]:
            status = "warning"
            issues.append(f"High concurrency detected (max: {max_concurrent})")

        return {"status": status, "issues": issues, "metrics": conc_data}

    def _generate_recommendations(self, rt_analysis: Dict, tp_analysis: Dict, error_analysis: Dict, conc_analysis: Dict) -> List[str]:
        """生成优化建议"""
        recommendations = []

        # 响应时间建议
        if rt_analysis["status"] in ["warning", "critical"]:
            recommendations.append("Consider optimizing slow operations or adding caching")
            if rt_analysis["metrics"]["p99"] > rt_analysis["metrics"]["average_time"] * 3:
                recommendations.append("High P99 suggests occasional very slow requests - investigate outliers")

        # 吞吐量建议
        if tp_analysis["status"] == "warning":
            recommendations.append("Low throughput detected - consider scaling or optimization")

        # 错误率建议
        if error_analysis["status"] in ["warning", "critical"]:
            recommendations.append("High error rate - review error logs and implement error handling")

        # 并发建议
        if conc_analysis["status"] == "warning":
            recommendations.append("High concurrency - consider implementing rate limiting or load balancing")

        return recommendations

    def _calculate_health_score(self, rt_analysis: Dict, tp_analysis: Dict, error_analysis: Dict, conc_analysis: Dict) -> float:
        """计算健康分数(0-100)"""
        score = 100.0

        # 响应时间扣分
        if rt_analysis["status"] == "warning":
            score -= 20
        elif rt_analysis["status"] == "critical":
            score -= 40

        # 错误率扣分
        if error_analysis["status"] == "warning":
            score -= 15
        elif error_analysis["status"] == "critical":
            score -= 30

        # 吞吐量扣分
        if tp_analysis["status"] == "warning":
            score -= 10

        # 并发扣分
        if conc_analysis["status"] == "warning":
            score -= 10

        return max(0.0, score)

    def _generate_overall_analysis(self, individual_results: Dict[str, Any]) -> Dict[str, Any]:
        """生成总体分析"""
        if not individual_results:
            return {"status": "no_data", "health_score": 0.0}

        # 计算平均健康分数
        health_scores = [result["health_score"] for result in individual_results.values()]
        avg_health_score = sum(health_scores) / len(health_scores)

        # 统计状态
        critical_count = sum(
            1
            for result in individual_results.values()
            if any(analysis["status"] == "critical" for analysis in [result["response_time_analysis"], result["error_analysis"]])
        )

        warning_count = sum(
            1
            for result in individual_results.values()
            if any(
                analysis["status"] == "warning"
                for analysis in [
                    result["response_time_analysis"],
                    result["throughput_analysis"],
                    result["error_analysis"],
                    result["concurrency_analysis"],
                ]
            )
        )

        # 确定总体状态
        if critical_count > 0:
            overall_status = "critical"
        elif warning_count > 0:
            overall_status = "warning"
        else:
            overall_status = "good"

        # 收集所有建议
        all_recommendations = []
        for result in individual_results.values():
            all_recommendations.extend(result["recommendations"])

        # 去重建议
        unique_recommendations = list(set(all_recommendations))

        return {
            "status": overall_status,
            "health_score": avg_health_score,
            "total_metrics": len(individual_results),
            "critical_count": critical_count,
            "warning_count": warning_count,
            "recommendations": unique_recommendations[:10],  # 最多10条建议
        }

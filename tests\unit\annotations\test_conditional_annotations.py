#!/usr/bin/env python
"""
* @author: cz
* @description: 条件装配注解测试

测试条件装配注解的功能，包括@Conditional、@ConditionalOnProperty等。
"""

import unittest

from miniboot.annotations import (
    Conditional,
    ConditionalOnBean,
    ConditionalOnClass,
    ConditionalOnExpression,
    ConditionalOnMissingBean,
    ConditionalOnMissingClass,
    ConditionalOnProperty,
    ConditionalOnResource,
    ConditionalOnWeb,
    get_condition_type,
    get_conditional_metadata,
    is_conditional,
)


# 测试用的条件类
class TestCondition:
    """测试条件类"""

    def matches(self, _context):
        return True


class AlwaysFalseCondition:
    """总是返回False的条件类"""

    def matches(self, _context):
        return False


# 测试用的Bean类
class DataSource:
    """测试用的数据源类"""

    pass


class TestConditionalAnnotations(unittest.TestCase):
    """条件装配注解测试类"""

    def test_conditional_annotation(self):
        """测试@Conditional注解"""

        @Conditional(TestCondition)
        class ConditionalBean:
            pass

        # 验证注解属性
        self.assertTrue(hasattr(ConditionalBean, "__is_conditional__"))
        self.assertTrue(ConditionalBean.__is_conditional__)
        self.assertTrue(hasattr(ConditionalBean, "__condition_class__"))
        self.assertEqual(ConditionalBean.__condition_class__, TestCondition)

        # 验证元数据
        metadata = ConditionalBean.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "Custom")
        self.assertEqual(metadata.class_names, ["TestCondition"])

    def test_conditional_on_property_annotation(self):
        """测试@ConditionalOnProperty注解"""

        @ConditionalOnProperty(name="app.feature.enabled", having_value="true")
        class FeatureBean:
            pass

        # 验证注解属性
        self.assertTrue(FeatureBean.__is_conditional__)
        self.assertEqual(FeatureBean.__condition_property__, "app.feature.enabled")
        self.assertEqual(FeatureBean.__condition_having_value__, "true")
        self.assertFalse(FeatureBean.__condition_match_if_missing__)

        # 验证元数据
        metadata = FeatureBean.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnProperty")
        self.assertEqual(metadata.properties, ["app.feature.enabled"])
        self.assertEqual(metadata.having_value, "true")
        self.assertFalse(metadata.match_if_missing)

    def test_conditional_on_property_with_match_if_missing(self):
        """测试@ConditionalOnProperty注解带match_if_missing参数"""

        @ConditionalOnProperty(name="app.debug", match_if_missing=True)
        class DebugBean:
            pass

        # 验证注解属性
        self.assertTrue(DebugBean.__is_conditional__)
        self.assertEqual(DebugBean.__condition_property__, "app.debug")
        self.assertIsNone(DebugBean.__condition_having_value__)
        self.assertTrue(DebugBean.__condition_match_if_missing__)

        # 验证元数据
        metadata = DebugBean.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnProperty")
        self.assertEqual(metadata.properties, ["app.debug"])
        self.assertIsNone(metadata.having_value)
        self.assertTrue(metadata.match_if_missing)

    def test_conditional_on_bean_annotation_with_type(self):
        """测试@ConditionalOnBean注解带类型参数"""

        @ConditionalOnBean(DataSource)
        class DatabaseService:
            pass

        # 验证注解属性
        self.assertTrue(DatabaseService.__is_conditional__)
        self.assertEqual(DatabaseService.__condition_bean_type__, DataSource)
        self.assertIsNone(DatabaseService.__condition_bean_name__)

        # 验证元数据
        metadata = DatabaseService.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnBean")
        self.assertEqual(metadata.class_names, ["DataSource"])
        self.assertEqual(metadata.bean_names, [])

    def test_conditional_on_bean_annotation_with_name(self):
        """测试@ConditionalOnBean注解带名称参数"""

        @ConditionalOnBean(name="primaryDataSource")
        class PrimaryDatabaseService:
            pass

        # 验证注解属性
        self.assertTrue(PrimaryDatabaseService.__is_conditional__)
        self.assertIsNone(PrimaryDatabaseService.__condition_bean_type__)
        self.assertEqual(PrimaryDatabaseService.__condition_bean_name__, "primaryDataSource")

        # 验证元数据
        metadata = PrimaryDatabaseService.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnBean")
        self.assertEqual(metadata.bean_names, ["primaryDataSource"])
        self.assertEqual(metadata.class_names, [])

    def test_conditional_on_missing_bean_annotation_with_type(self):
        """测试@ConditionalOnMissingBean注解带类型参数"""

        @ConditionalOnMissingBean(DataSource)
        class DefaultDataSource:
            pass

        # 验证注解属性
        self.assertTrue(DefaultDataSource.__is_conditional__)
        self.assertEqual(DefaultDataSource.__condition_bean_type__, DataSource)
        self.assertIsNone(DefaultDataSource.__condition_bean_name__)

        # 验证元数据
        metadata = DefaultDataSource.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnMissingBean")
        self.assertEqual(metadata.class_names, ["DataSource"])
        self.assertEqual(metadata.bean_names, [])

    def test_conditional_on_missing_bean_annotation_with_name(self):
        """测试@ConditionalOnMissingBean注解带名称参数"""

        @ConditionalOnMissingBean(name="customService")
        class DefaultService:
            pass

        # 验证注解属性
        self.assertTrue(DefaultService.__is_conditional__)
        self.assertIsNone(DefaultService.__condition_bean_type__)
        self.assertEqual(DefaultService.__condition_bean_name__, "customService")

        # 验证元数据
        metadata = DefaultService.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnMissingBean")
        self.assertEqual(metadata.bean_names, ["customService"])
        self.assertEqual(metadata.class_names, [])

    def test_conditional_on_class_annotation(self):
        """测试@ConditionalOnClass注解"""

        @ConditionalOnClass("redis.Redis")
        class RedisService:
            pass

        # 验证注解属性
        self.assertTrue(RedisService.__is_conditional__)
        self.assertEqual(RedisService.__condition_class_name__, "redis.Redis")

        # 验证元数据
        metadata = RedisService.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnClass")
        self.assertEqual(metadata.class_names, ["redis.Redis"])

    def test_conditional_on_web_annotation_with_parameter(self):
        """测试@ConditionalOnWeb注解带参数"""

        @ConditionalOnWeb
        class WebController:
            pass

        # 验证注解属性
        self.assertTrue(WebController.__is_conditional__)
        self.assertTrue(WebController.__condition_web__)

        # 验证元数据
        metadata = WebController.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnWeb")

    def test_conditional_on_web_annotation_without_parameter(self):
        """测试@ConditionalOnWeb注解不带参数"""

        @ConditionalOnWeb()
        class AnotherWebController:
            pass

        # 验证注解属性
        self.assertTrue(AnotherWebController.__is_conditional__)
        self.assertTrue(AnotherWebController.__condition_web__)

        # 验证元数据
        metadata = AnotherWebController.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnWeb")

    def test_conditional_on_method(self):
        """测试条件注解在方法上的使用"""

        class ConfigClass:
            @ConditionalOnProperty(name="feature.enabled", having_value="true")
            def feature_bean(self):
                return "feature"

        method = ConfigClass.feature_bean

        # 验证方法注解属性
        self.assertTrue(method.__is_conditional__)
        self.assertEqual(method.__condition_property__, "feature.enabled")
        self.assertEqual(method.__condition_having_value__, "true")

        # 验证元数据
        metadata = method.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnProperty")
        self.assertEqual(metadata.properties, ["feature.enabled"])
        self.assertEqual(metadata.having_value, "true")

    def test_multiple_conditional_annotations(self):
        """测试多个条件注解的组合使用"""

        @ConditionalOnProperty(name="app.enabled", having_value="true")
        @ConditionalOnBean(DataSource)
        class MultiConditionalBean:
            pass

        # 验证第一个注解（最后应用的）
        self.assertTrue(MultiConditionalBean.__is_conditional__)

        # 注意：由于装饰器的执行顺序，最上面的注解最后执行，会覆盖前面的
        # 这里应该是ConditionalOnProperty的元数据（最上面的装饰器）
        metadata = MultiConditionalBean.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnProperty")

    def test_utility_functions(self):
        """测试工具函数"""

        @ConditionalOnProperty(name="test.property")
        class TestBean:
            pass

        class PlainBean:
            pass

        # 测试is_conditional函数
        self.assertTrue(is_conditional(TestBean))
        self.assertFalse(is_conditional(PlainBean))

        # 测试get_conditional_metadata函数
        metadata = get_conditional_metadata(TestBean)
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.condition_type, "OnProperty")

        plain_metadata = get_conditional_metadata(PlainBean)
        self.assertIsNone(plain_metadata)

        # 测试get_condition_type函数
        condition_type = get_condition_type(TestBean)
        self.assertEqual(condition_type, "OnProperty")

        plain_condition_type = get_condition_type(PlainBean)
        self.assertIsNone(plain_condition_type)

    def test_conditional_annotation_edge_cases(self):
        """测试条件注解的边界情况"""

        # 测试ConditionalOnBean不带任何参数
        @ConditionalOnBean()
        class EmptyConditionalBean:
            pass

        metadata = EmptyConditionalBean.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnBean")
        self.assertEqual(metadata.bean_names, [])
        self.assertEqual(metadata.class_names, [])

        # 测试ConditionalOnProperty只有name参数
        @ConditionalOnProperty(name="simple.property")
        class SimplePropertyBean:
            pass

        simple_metadata = SimplePropertyBean.__conditional_metadata__
        self.assertEqual(simple_metadata.condition_type, "OnProperty")
        self.assertEqual(simple_metadata.properties, ["simple.property"])
        self.assertIsNone(simple_metadata.having_value)
        self.assertFalse(simple_metadata.match_if_missing)

    def test_enhanced_conditional_on_property(self):
        """测试增强的@ConditionalOnProperty注解"""

        # 测试多属性检查
        @ConditionalOnProperty(names=["app.feature1.enabled", "app.feature2.enabled"])
        class MultiPropertyBean:
            pass

        metadata = MultiPropertyBean.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnProperty")
        self.assertEqual(metadata.properties, ["app.feature1.enabled", "app.feature2.enabled"])

        # 测试前缀检查
        @ConditionalOnProperty(prefix="app.database")
        class DatabaseBean:
            pass

        db_metadata = DatabaseBean.__conditional_metadata__
        self.assertEqual(db_metadata.condition_type, "OnProperty")
        self.assertEqual(db_metadata.prefix, "app.database")

        # 测试value参数（Spring Boot风格）
        @ConditionalOnProperty(name="app.debug", value="true")
        class DebugBean:
            pass

        debug_metadata = DebugBean.__conditional_metadata__
        self.assertEqual(debug_metadata.condition_type, "OnProperty")
        self.assertEqual(debug_metadata.having_value, "true")

    def test_enhanced_conditional_on_class(self):
        """测试增强的@ConditionalOnClass注解"""

        # 测试多类检查
        @ConditionalOnClass(names=["redis.Redis", "redis.ConnectionPool"])
        class RedisService:
            pass

        metadata = RedisService.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnClass")
        self.assertEqual(metadata.class_names, ["redis.Redis", "redis.ConnectionPool"])

        # 测试name参数
        @ConditionalOnClass(name="redis.Redis")
        class SingleRedisService:
            pass

        single_metadata = SingleRedisService.__conditional_metadata__
        self.assertEqual(single_metadata.condition_type, "OnClass")
        self.assertEqual(single_metadata.class_names, ["redis.Redis"])

    def test_conditional_on_missing_class(self):
        """测试@ConditionalOnMissingClass注解"""

        @ConditionalOnMissingClass("redis.Redis")
        class MockRedisService:
            pass

        # 验证注解属性
        self.assertTrue(MockRedisService.__is_conditional__)
        self.assertEqual(MockRedisService.__condition_class_name__, "redis.Redis")

        # 验证元数据
        metadata = MockRedisService.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnMissingClass")
        self.assertEqual(metadata.class_names, ["redis.Redis"])

        # 测试多类检查
        @ConditionalOnMissingClass(names=["redis.Redis", "redis.ConnectionPool"])
        class MultiMockRedisService:
            pass

        multi_metadata = MultiMockRedisService.__conditional_metadata__
        self.assertEqual(multi_metadata.condition_type, "OnMissingClass")
        self.assertEqual(multi_metadata.class_names, ["redis.Redis", "redis.ConnectionPool"])

    def test_conditional_on_expression(self):
        """测试@ConditionalOnExpression注解"""

        @ConditionalOnExpression("'${app.env}' == 'development'")
        class DevBean:
            pass

        # 验证注解属性
        self.assertTrue(DevBean.__is_conditional__)
        self.assertEqual(DevBean.__condition_expression__, "'${app.env}' == 'development'")

        # 验证元数据
        metadata = DevBean.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnExpression")
        self.assertEqual(metadata.expression, "'${app.env}' == 'development'")

        # 测试复杂表达式
        @ConditionalOnExpression("'${app.debug}' == 'true' and '${app.env}' != 'production'")
        class DebugBean:
            pass

        debug_metadata = DebugBean.__conditional_metadata__
        self.assertEqual(debug_metadata.condition_type, "OnExpression")
        self.assertEqual(debug_metadata.expression, "'${app.debug}' == 'true' and '${app.env}' != 'production'")

    def test_conditional_on_resource(self):
        """测试@ConditionalOnResource注解"""

        @ConditionalOnResource("config/database.yml")
        class DatabaseBean:
            pass

        # 验证注解属性
        self.assertTrue(DatabaseBean.__is_conditional__)
        self.assertEqual(DatabaseBean.__condition_resources__, ["config/database.yml"])

        # 验证元数据
        metadata = DatabaseBean.__conditional_metadata__
        self.assertEqual(metadata.condition_type, "OnResource")
        self.assertEqual(metadata.resources, ["config/database.yml"])

        # 测试多资源检查
        @ConditionalOnResource(["config/redis.yml", "config/cache.yml"])
        class CacheBean:
            pass

        cache_metadata = CacheBean.__conditional_metadata__
        self.assertEqual(cache_metadata.condition_type, "OnResource")
        self.assertEqual(cache_metadata.resources, ["config/redis.yml", "config/cache.yml"])


if __name__ == "__main__":
    unittest.main()

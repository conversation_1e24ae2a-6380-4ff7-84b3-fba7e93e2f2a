#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 条件注解系统测试
"""

import pytest
from unittest.mock import Mock
import tempfile
import os

from miniboot.autoconfigure.conditions import (
    ConditionalOnProperty,
    ConditionalOnClass,
    ConditionalOnBean,
    ConditionalOnMissingBean,
    ConditionalOnResource,
    ConditionEvaluator,
)
from miniboot.context import ApplicationContext


class TestConditionalOnProperty:
    """属性条件测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)
        self.env_mock = Mock()
        self.context.get_environment.return_value = self.env_mock

    def test_property_exists_and_matches(self):
        """测试属性存在且匹配"""
        condition = ConditionalOnProperty(prefix="app", name="enabled", having_value="true")
        self.env_mock.get_property.return_value = "true"

        assert condition.matches(self.context) is True
        self.env_mock.get_property.assert_called_with("app.enabled")

    def test_property_exists_but_not_matches(self):
        """测试属性存在但不匹配"""
        condition = ConditionalOnProperty(prefix="app", name="enabled", having_value="true")
        self.env_mock.get_property.return_value = "false"

        assert condition.matches(self.context) is False

    def test_property_missing_match_if_missing_true(self):
        """测试属性不存在且match_if_missing为True"""
        condition = ConditionalOnProperty(name="missing.prop", match_if_missing=True)
        self.env_mock.get_property.return_value = None

        assert condition.matches(self.context) is True

    def test_property_missing_match_if_missing_false(self):
        """测试属性不存在且match_if_missing为False"""
        condition = ConditionalOnProperty(name="missing.prop", match_if_missing=False)
        self.env_mock.get_property.return_value = None

        assert condition.matches(self.context) is False

    def test_boolean_value_comparison(self):
        """测试布尔值比较"""
        condition = ConditionalOnProperty(name="debug", having_value=True)

        # 字符串"true"应该匹配布尔True
        self.env_mock.get_property.return_value = "true"
        assert condition.matches(self.context) is True

        # 字符串"false"应该不匹配布尔True
        self.env_mock.get_property.return_value = "false"
        assert condition.matches(self.context) is False

    def test_case_insensitive_string_comparison(self):
        """测试大小写不敏感的字符串比较"""
        condition = ConditionalOnProperty(name="mode", having_value="DEBUG")

        self.env_mock.get_property.return_value = "debug"
        assert condition.matches(self.context) is True

        self.env_mock.get_property.return_value = "Debug"
        assert condition.matches(self.context) is True

    def test_invalid_property_name(self):
        """测试无效的属性名称"""
        with pytest.raises(ValueError, match="Either 'name' or both 'prefix' and 'name' must be provided"):
            ConditionalOnProperty()

    def test_condition_message(self):
        """测试条件消息"""
        condition = ConditionalOnProperty(prefix="app", name="enabled", having_value="true")
        message = condition.get_condition_message()
        assert "@ConditionalOnProperty" in message
        assert "app.enabled" in message
        assert "true" in message


class TestConditionalOnClass:
    """类条件测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)

    def test_existing_class(self):
        """测试存在的类"""
        condition = ConditionalOnClass("os.path")
        assert condition.matches(self.context) is True

    def test_non_existing_class(self):
        """测试不存在的类"""
        condition = ConditionalOnClass("non.existent.module.Class")
        assert condition.matches(self.context) is False

    def test_existing_module_non_existing_class(self):
        """测试存在的模块但不存在的类"""
        condition = ConditionalOnClass("os.NonExistentClass")
        assert condition.matches(self.context) is False

    def test_condition_message(self):
        """测试条件消息"""
        condition = ConditionalOnClass("some.module.Class")
        message = condition.get_condition_message()
        assert "@ConditionalOnClass" in message
        assert "some.module.Class" in message


class TestConditionalOnBean:
    """Bean条件测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)

    def test_bean_exists_by_name(self):
        """测试按名称查找Bean存在"""
        condition = ConditionalOnBean(name="test_bean")
        self.context.contains_bean.return_value = True

        assert condition.matches(self.context) is True
        self.context.contains_bean.assert_called_with("test_bean")

    def test_bean_not_exists_by_name(self):
        """测试按名称查找Bean不存在"""
        condition = ConditionalOnBean(name="test_bean")
        self.context.contains_bean.return_value = False

        assert condition.matches(self.context) is False

    def test_bean_exists_by_type_string(self):
        """测试按字符串类型查找Bean存在"""
        condition = ConditionalOnBean(bean_type="TestService")
        self.context.contains_bean.return_value = True

        assert condition.matches(self.context) is True
        self.context.contains_bean.assert_called_with("TestService")

    def test_bean_exists_by_type_class(self):
        """测试按类类型查找Bean存在"""

        class TestService:
            pass

        condition = ConditionalOnBean(bean_type=TestService)
        self.context.get_bean.return_value = TestService()

        assert condition.matches(self.context) is True
        self.context.get_bean.assert_called_with(TestService)

    def test_bean_not_exists_by_type_class(self):
        """测试按类类型查找Bean不存在"""

        class TestService:
            pass

        condition = ConditionalOnBean(bean_type=TestService)
        self.context.get_bean.side_effect = Exception("Bean not found")

        assert condition.matches(self.context) is False

    def test_invalid_parameters(self):
        """测试无效参数"""
        with pytest.raises(ValueError, match="Either 'bean_type' or 'name' must be provided"):
            ConditionalOnBean()

    def test_condition_message_with_name(self):
        """测试带名称的条件消息"""
        condition = ConditionalOnBean(name="test_bean")
        message = condition.get_condition_message()
        assert "@ConditionalOnBean" in message
        assert "name=test_bean" in message

    def test_condition_message_with_type(self):
        """测试带类型的条件消息"""

        class TestService:
            pass

        condition = ConditionalOnBean(bean_type=TestService)
        message = condition.get_condition_message()
        assert "@ConditionalOnBean" in message
        assert "type=TestService" in message


class TestConditionalOnMissingBean:
    """Bean缺失条件测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)

    def test_bean_missing(self):
        """测试Bean不存在"""
        condition = ConditionalOnMissingBean(name="test_bean")
        self.context.contains_bean.return_value = False

        assert condition.matches(self.context) is True

    def test_bean_exists(self):
        """测试Bean存在"""
        condition = ConditionalOnMissingBean(name="test_bean")
        self.context.contains_bean.return_value = True

        assert condition.matches(self.context) is False

    def test_condition_message(self):
        """测试条件消息"""
        condition = ConditionalOnMissingBean(name="test_bean")
        message = condition.get_condition_message()
        assert "@ConditionalOnMissingBean" in message
        assert "name=test_bean" in message


class TestConditionalOnResource:
    """资源条件测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)

    def test_existing_resource(self):
        """测试存在的资源"""
        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name

        try:
            condition = ConditionalOnResource(temp_path)
            assert condition.matches(self.context) is True
        finally:
            os.unlink(temp_path)

    def test_non_existing_resource(self):
        """测试不存在的资源"""
        condition = ConditionalOnResource("/non/existent/file.txt")
        assert condition.matches(self.context) is False

    def test_condition_message(self):
        """测试条件消息"""
        condition = ConditionalOnResource("/path/to/resource.txt")
        message = condition.get_condition_message()
        assert "@ConditionalOnResource" in message
        assert "/path/to/resource.txt" in message


class TestConditionEvaluator:
    """条件评估器测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)
        self.evaluator = ConditionEvaluator()

    def test_empty_conditions(self):
        """测试空条件列表"""
        assert self.evaluator.evaluate_conditions([], self.context) is True

    def test_all_conditions_pass(self):
        """测试所有条件都通过"""
        condition1 = Mock()
        condition1.matches.return_value = True
        condition1.get_condition_message.return_value = "condition1"

        condition2 = Mock()
        condition2.matches.return_value = True
        condition2.get_condition_message.return_value = "condition2"

        conditions = [condition1, condition2]
        assert self.evaluator.evaluate_conditions(conditions, self.context) is True

    def test_one_condition_fails(self):
        """测试一个条件失败"""
        condition1 = Mock()
        condition1.matches.return_value = True
        condition1.get_condition_message.return_value = "condition1"

        condition2 = Mock()
        condition2.matches.return_value = False
        condition2.get_condition_message.return_value = "condition2"

        conditions = [condition1, condition2]
        assert self.evaluator.evaluate_conditions(conditions, self.context) is False

    def test_condition_evaluation_caching(self):
        """测试条件评估缓存"""
        condition = Mock()
        condition.matches.return_value = True
        condition.get_condition_message.return_value = "test_condition"

        # 第一次评估
        self.evaluator._evaluate_single_condition(condition, self.context)

        # 第二次评估应该使用缓存
        result = self.evaluator._evaluate_single_condition(condition, self.context)

        assert result is True
        # matches方法只应该被调用一次
        condition.matches.assert_called_once()

    def test_clear_cache(self):
        """测试清除缓存"""
        condition = Mock()
        condition.matches.return_value = True
        condition.get_condition_message.return_value = "test_condition"

        # 评估条件
        self.evaluator._evaluate_single_condition(condition, self.context)

        # 清除缓存
        self.evaluator.clear_cache()

        # 再次评估应该重新调用matches方法
        self.evaluator._evaluate_single_condition(condition, self.context)

        # matches方法应该被调用两次
        assert condition.matches.call_count == 2

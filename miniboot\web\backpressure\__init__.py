"""
背压控制模块

提供系统稳定性保障的背压控制功能,包括:
- 负载监控和指标收集
- 熔断器和优雅降级
- 自适应并发控制
- 可配置的背压策略

主要组件:
- LoadMonitor: 负载监控器,实时监控系统资源和性能指标
- CircuitBreaker: 熔断器,提供故障隔离和快速失败机制
- GracefulDegradationManager: 优雅降级管理器,支持分级降级策略
- AdaptiveConcurrencyManager: 自适应并发管理器,动态调整并发度
- BackpressureController: 背压控制器,统一管理所有背压控制组件
- BackpressureMetrics: 背压控制指标收集器,提供监控和分析数据
"""

from .breaker import CircuitBreaker, CircuitBreakerConfig, CircuitBreakerError, CircuitBreakerMetrics, CircuitBreakerState
from .limiter import ConcurrencyConfig, ConcurrencyLimiter, ConcurrencyLimitError, ConcurrencyMetrics, LimitingAlgorithm, RequestContext
from .limiter import RequestPriority
from .controller import BackpressureController, ProtectionAction, ProtectionDecision
from .degradation import DegradationLevel, DegradationManager, DegradationRule, DegradationStatus, DegradationType
from .shedder import LoadShedder, LoadSheddingError, RequestInfo
from .shedder import RequestPriority as SheddingRequestPriority
from .shedder import SheddingConfig, SheddingMetrics, SheddingStrategy
from .metrics import BackpressureEvent, BackpressureEventType, BackpressureMetrics, MetricsSnapshot, ComponentMetrics

# 导入已实现的组件
from .monitor import ApplicationMetrics, LoadMonitor, LoadStatus, SystemMetrics

# 版本信息
__version__ = "0.1.0"

# 公开API
__all__ = [
    # 核心组件
    "LoadMonitor",
    "CircuitBreaker",
    "BackpressureController",
    "BackpressureMetrics",
    "DegradationManager",
    "ConcurrencyLimiter",
    "LoadShedder",
    # 监控相关
    "SystemMetrics",
    "ApplicationMetrics",
    "LoadStatus",
    # 熔断器相关
    "CircuitBreakerState",
    "CircuitBreakerMetrics",
    "CircuitBreakerConfig",
    # 控制器相关
    "ProtectionAction",
    "ProtectionDecision",
    # 指标相关
    "BackpressureEvent",
    "BackpressureEventType",
    "MetricsSnapshot",
    "ComponentMetrics",
    # 降级相关
    "DegradationLevel",
    "DegradationType",
    "DegradationRule",
    "DegradationStatus",
    # 并发限制相关
    "ConcurrencyConfig",
    "ConcurrencyMetrics",
    "LimitingAlgorithm",
    "RequestContext",
    "RequestPriority",
    # 负载脱落相关
    "SheddingConfig",
    "SheddingMetrics",
    "SheddingStrategy",
    "RequestInfo",
    "SheddingRequestPriority",
    # 异常类
    "CircuitBreakerError",
    "ConcurrencyLimitError",
    "LoadSheddingError",
    # 版本信息
    "__version__",
]

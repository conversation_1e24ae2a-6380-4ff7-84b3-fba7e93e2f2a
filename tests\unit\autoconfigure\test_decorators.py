#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 条件装饰器测试
"""

from miniboot.autoconfigure.conditions import ConditionalOnBean as ConditionalOnBeanCondition
from miniboot.autoconfigure.conditions import ConditionalOnClass as ConditionalOnClassCondition
from miniboot.autoconfigure.conditions import ConditionalOnMissingBean as ConditionalOnMissingBeanCondition
from miniboot.autoconfigure.conditions import ConditionalOnProperty as ConditionalOnPropertyCondition
from miniboot.autoconfigure.conditions import ConditionalOnResource as ConditionalOnResourceCondition
from miniboot.autoconfigure.decorators import (
    ConditionalOnBean,
    ConditionalOnClass,
    ConditionalOnMissingBean,
    ConditionalOnProperty,
    ConditionalOnResource,
    get_conditions_from_class,
    has_conditions,
)


class TestConditionalDecorators:
    """条件装饰器测试"""

    def test_conditional_on_property_decorator(self):
        """测试@ConditionalOnProperty装饰器"""

        @ConditionalOnProperty(prefix="app", name="enabled", having_value="true")
        class TestConfig:
            pass

        # 检查条件是否被添加
        assert hasattr(TestConfig, "_autoconfigure_conditions")
        assert len(TestConfig._autoconfigure_conditions) == 1

        condition = TestConfig._autoconfigure_conditions[0]
        assert isinstance(condition, ConditionalOnPropertyCondition)
        assert condition.property_key == "app.enabled"
        assert condition.having_value == "true"

    def test_conditional_on_class_decorator(self):
        """测试@ConditionalOnClass装饰器"""

        @ConditionalOnClass("some.module.Class")
        class TestConfig:
            pass

        assert hasattr(TestConfig, "_autoconfigure_conditions")
        assert len(TestConfig._autoconfigure_conditions) == 1

        condition = TestConfig._autoconfigure_conditions[0]
        assert isinstance(condition, ConditionalOnClassCondition)
        assert condition.class_name == "some.module.Class"

    def test_conditional_on_bean_decorator(self):
        """测试@ConditionalOnBean装饰器"""

        @ConditionalOnBean(name="test_bean")
        class TestConfig:
            pass

        assert hasattr(TestConfig, "_autoconfigure_conditions")
        assert len(TestConfig._autoconfigure_conditions) == 1

        condition = TestConfig._autoconfigure_conditions[0]
        assert isinstance(condition, ConditionalOnBeanCondition)
        assert condition.name == "test_bean"

    def test_conditional_on_missing_bean_decorator(self):
        """测试@ConditionalOnMissingBean装饰器"""

        @ConditionalOnMissingBean(name="test_bean")
        class TestConfig:
            pass

        assert hasattr(TestConfig, "_autoconfigure_conditions")
        assert len(TestConfig._autoconfigure_conditions) == 1

        condition = TestConfig._autoconfigure_conditions[0]
        assert isinstance(condition, ConditionalOnMissingBeanCondition)
        assert condition.bean_condition.name == "test_bean"

    def test_conditional_on_resource_decorator(self):
        """测试@ConditionalOnResource装饰器"""

        @ConditionalOnResource("/path/to/resource.txt")
        class TestConfig:
            pass

        assert hasattr(TestConfig, "_autoconfigure_conditions")
        assert len(TestConfig._autoconfigure_conditions) == 1

        condition = TestConfig._autoconfigure_conditions[0]
        assert isinstance(condition, ConditionalOnResourceCondition)
        assert condition.resource_path == "/path/to/resource.txt"

    def test_multiple_conditions(self):
        """测试多个条件装饰器"""

        @ConditionalOnProperty(name="app.enabled", having_value="true")
        @ConditionalOnClass("some.module.Class")
        @ConditionalOnBean(name="test_bean")
        class TestConfig:
            pass

        assert hasattr(TestConfig, "_autoconfigure_conditions")
        assert len(TestConfig._autoconfigure_conditions) == 3

        # 检查条件类型
        condition_types = [type(c) for c in TestConfig._autoconfigure_conditions]
        assert ConditionalOnPropertyCondition in condition_types
        assert ConditionalOnClassCondition in condition_types
        assert ConditionalOnBeanCondition in condition_types

    def test_decorator_on_function(self):
        """测试装饰器应用于函数"""

        @ConditionalOnProperty(name="feature.enabled")
        def test_function():
            pass

        assert hasattr(test_function, "_autoconfigure_conditions")
        assert len(test_function._autoconfigure_conditions) == 1

        condition = test_function._autoconfigure_conditions[0]
        assert isinstance(condition, ConditionalOnPropertyCondition)

    def test_get_conditions_from_class_with_conditions(self):
        """测试从有条件的类中获取条件"""

        @ConditionalOnProperty(name="app.enabled")
        @ConditionalOnClass("some.module.Class")
        class TestConfig:
            @ConditionalOnBean(name="test_bean")
            def test_method(self):
                pass

        conditions = get_conditions_from_class(TestConfig)

        # 应该包含类级别和方法级别的条件
        assert len(conditions) == 3

        condition_types = [type(c) for c in conditions]
        assert ConditionalOnPropertyCondition in condition_types
        assert ConditionalOnClassCondition in condition_types
        assert ConditionalOnBeanCondition in condition_types

    def test_get_conditions_from_class_without_conditions(self):
        """测试从没有条件的类中获取条件"""

        class TestConfig:
            pass

        conditions = get_conditions_from_class(TestConfig)
        assert conditions == []

    def test_has_conditions_true(self):
        """测试has_conditions返回True"""

        @ConditionalOnProperty(name="app.enabled")
        class TestConfig:
            pass

        assert has_conditions(TestConfig) is True

    def test_has_conditions_false(self):
        """测试has_conditions返回False"""

        class TestConfig:
            pass

        assert has_conditions(TestConfig) is False

    def test_has_conditions_empty_list(self):
        """测试has_conditions处理空条件列表"""

        class TestConfig:
            pass

        # 手动添加空的条件列表
        TestConfig._autoconfigure_conditions = []

        assert has_conditions(TestConfig) is False

    def test_condition_accumulation(self):
        """测试条件累积（多次应用装饰器）"""

        @ConditionalOnProperty(name="prop1")
        class TestConfig:
            pass

        # 再次应用装饰器
        TestConfig = ConditionalOnProperty(name="prop2")(TestConfig)

        assert len(TestConfig._autoconfigure_conditions) == 2

        # 检查两个条件都存在
        property_keys = []
        for c in TestConfig._autoconfigure_conditions:
            if hasattr(c, "property_key"):
                property_keys.append(c.property_key)
        assert "prop1" in property_keys
        assert "prop2" in property_keys

    def test_decorator_preserves_class_functionality(self):
        """测试装饰器不影响类的正常功能"""

        @ConditionalOnProperty(name="app.enabled")
        class TestConfig:
            def __init__(self, value):
                self.value = value

            def get_value(self):
                return self.value

        # 类应该仍然可以正常实例化和使用
        instance = TestConfig("test")
        assert instance.get_value() == "test"

        # 条件信息应该存在
        assert hasattr(TestConfig, "_autoconfigure_conditions")

    def test_decorator_with_bean_type_class(self):
        """测试使用类类型的Bean条件装饰器"""

        class ServiceClass:
            pass

        @ConditionalOnBean(bean_type=ServiceClass)
        class TestConfig:
            pass

        condition = TestConfig._autoconfigure_conditions[0]
        assert isinstance(condition, ConditionalOnBeanCondition)
        assert condition.bean_type is ServiceClass

    def test_decorator_with_missing_bean_type_class(self):
        """测试使用类类型的MissingBean条件装饰器"""

        class ServiceClass:
            pass

        @ConditionalOnMissingBean(bean_type=ServiceClass)
        class TestConfig:
            pass

        condition = TestConfig._autoconfigure_conditions[0]
        assert isinstance(condition, ConditionalOnMissingBeanCondition)
        assert condition.bean_condition.bean_type is ServiceClass

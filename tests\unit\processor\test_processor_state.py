#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: ProcessorState枚举测试 - 验证FIX-3.3.3修复的状态定义规范化
"""

import unittest
from enum import Enum

from miniboot.processor.manager import ProcessorState


class ProcessorStateTestCase(unittest.TestCase):
    """ProcessorState枚举测试"""

    def test_processor_state_is_enum(self):
        """测试ProcessorState是Enum类型"""
        self.assertTrue(issubclass(ProcessorState, Enum))
        self.assertIsInstance(ProcessorState.ACTIVE, ProcessorState)

    def test_processor_state_values(self):
        """测试ProcessorState的值"""
        self.assertEqual(ProcessorState.ACTIVE.value, "ACTIVE")
        self.assertEqual(ProcessorState.DISABLED.value, "DISABLED")
        self.assertEqual(ProcessorState.ERROR.value, "ERROR")
        self.assertEqual(ProcessorState.CIRCUIT_OPEN.value, "CIRCUIT_OPEN")

    def test_processor_state_comparison(self):
        """测试ProcessorState的比较"""
        # 测试相等性
        self.assertEqual(ProcessorState.ACTIVE, ProcessorState.ACTIVE)
        self.assertNotEqual(ProcessorState.ACTIVE, ProcessorState.DISABLED)

        # 测试与字符串比较（应该不相等）
        self.assertNotEqual(ProcessorState.ACTIVE, "ACTIVE")
        self.assertEqual(ProcessorState.ACTIVE.value, "ACTIVE")

    def test_processor_state_membership(self):
        """测试ProcessorState的成员检查"""
        states = [ProcessorState.ACTIVE, ProcessorState.DISABLED]
        self.assertIn(ProcessorState.ACTIVE, states)
        self.assertNotIn(ProcessorState.ERROR, states)

    def test_is_available_method(self):
        """测试is_available方法"""
        self.assertTrue(ProcessorState.ACTIVE.is_available())
        self.assertFalse(ProcessorState.DISABLED.is_available())
        self.assertFalse(ProcessorState.ERROR.is_available())
        self.assertFalse(ProcessorState.CIRCUIT_OPEN.is_available())

    def test_can_transition_to_method(self):
        """测试can_transition_to方法"""
        # 从ACTIVE状态的转换
        self.assertTrue(ProcessorState.ACTIVE.can_transition_to(ProcessorState.DISABLED))
        self.assertTrue(ProcessorState.ACTIVE.can_transition_to(ProcessorState.ERROR))
        self.assertTrue(ProcessorState.ACTIVE.can_transition_to(ProcessorState.CIRCUIT_OPEN))
        self.assertFalse(ProcessorState.ACTIVE.can_transition_to(ProcessorState.ACTIVE))

        # 从DISABLED状态的转换
        self.assertTrue(ProcessorState.DISABLED.can_transition_to(ProcessorState.ACTIVE))
        self.assertFalse(ProcessorState.DISABLED.can_transition_to(ProcessorState.ERROR))
        self.assertFalse(ProcessorState.DISABLED.can_transition_to(ProcessorState.CIRCUIT_OPEN))

        # 从ERROR状态的转换
        self.assertTrue(ProcessorState.ERROR.can_transition_to(ProcessorState.ACTIVE))
        self.assertTrue(ProcessorState.ERROR.can_transition_to(ProcessorState.DISABLED))
        self.assertFalse(ProcessorState.ERROR.can_transition_to(ProcessorState.CIRCUIT_OPEN))

        # 从CIRCUIT_OPEN状态的转换
        self.assertTrue(ProcessorState.CIRCUIT_OPEN.can_transition_to(ProcessorState.ACTIVE))
        self.assertTrue(ProcessorState.CIRCUIT_OPEN.can_transition_to(ProcessorState.DISABLED))
        self.assertFalse(ProcessorState.CIRCUIT_OPEN.can_transition_to(ProcessorState.ERROR))

    def test_state_iteration(self):
        """测试状态枚举的迭代"""
        all_states = list(ProcessorState)
        expected_states = [ProcessorState.ACTIVE, ProcessorState.DISABLED, ProcessorState.ERROR, ProcessorState.CIRCUIT_OPEN]
        self.assertEqual(all_states, expected_states)

    def test_state_string_representation(self):
        """测试状态的字符串表示"""
        self.assertEqual(str(ProcessorState.ACTIVE), "ProcessorState.ACTIVE")
        self.assertEqual(repr(ProcessorState.ACTIVE), "<ProcessorState.ACTIVE: 'ACTIVE'>")

    def test_state_hash(self):
        """测试状态的哈希值"""
        # Enum成员应该是可哈希的
        state_set = {ProcessorState.ACTIVE, ProcessorState.DISABLED}
        self.assertEqual(len(state_set), 2)

        # 相同状态应该有相同的哈希值
        self.assertEqual(hash(ProcessorState.ACTIVE), hash(ProcessorState.ACTIVE))

    def test_state_documentation(self):
        """测试状态的文档字符串"""
        self.assertIsNotNone(ProcessorState.__doc__)
        self.assertIn("处理器状态枚举", ProcessorState.__doc__)

    def test_invalid_state_transition(self):
        """测试无效的状态转换"""
        # 测试不存在的状态转换
        self.assertFalse(ProcessorState.ACTIVE.can_transition_to(ProcessorState.ACTIVE))
        self.assertFalse(ProcessorState.DISABLED.can_transition_to(ProcessorState.DISABLED))
        self.assertFalse(ProcessorState.ERROR.can_transition_to(ProcessorState.ERROR))
        self.assertFalse(ProcessorState.CIRCUIT_OPEN.can_transition_to(ProcessorState.CIRCUIT_OPEN))

    def test_state_transition_chains(self):
        """测试状态转换链"""
        # 测试完整的状态转换链
        # ACTIVE -> ERROR -> ACTIVE
        self.assertTrue(ProcessorState.ACTIVE.can_transition_to(ProcessorState.ERROR))
        self.assertTrue(ProcessorState.ERROR.can_transition_to(ProcessorState.ACTIVE))

        # ACTIVE -> DISABLED -> ACTIVE
        self.assertTrue(ProcessorState.ACTIVE.can_transition_to(ProcessorState.DISABLED))
        self.assertTrue(ProcessorState.DISABLED.can_transition_to(ProcessorState.ACTIVE))

        # ACTIVE -> CIRCUIT_OPEN -> ACTIVE
        self.assertTrue(ProcessorState.ACTIVE.can_transition_to(ProcessorState.CIRCUIT_OPEN))
        self.assertTrue(ProcessorState.CIRCUIT_OPEN.can_transition_to(ProcessorState.ACTIVE))

    def test_type_safety(self):
        """测试类型安全性"""

        # 测试类型检查
        def process_state(state: ProcessorState) -> bool:
            return state.is_available()

        # 应该接受ProcessorState枚举
        self.assertTrue(process_state(ProcessorState.ACTIVE))
        self.assertFalse(process_state(ProcessorState.DISABLED))

        # 字符串不应该被接受（在类型检查器中会报错）
        # 但在运行时不会抛出异常，只是类型不匹配
        with self.assertRaises(AttributeError):
            process_state("ACTIVE")  # type: ignore

    def test_enum_uniqueness(self):
        """测试枚举值的唯一性"""
        values = [state.value for state in ProcessorState]
        self.assertEqual(len(values), len(set(values)), "ProcessorState values should be unique")

    def test_enum_completeness(self):
        """测试枚举的完整性"""
        # 确保所有预期的状态都存在
        expected_states = {"ACTIVE", "DISABLED", "ERROR", "CIRCUIT_OPEN"}
        actual_states = {state.value for state in ProcessorState}
        self.assertEqual(actual_states, expected_states)


if __name__ == "__main__":
    unittest.main()

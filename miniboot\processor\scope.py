#!/usr/bin/env python
"""
* @author: cz
* @description: 作用域 Bean 后置处理器

实现作用域 Bean 后置处理器,用于处理 Web 相关作用域的 Bean 创建和管理.
支持 request、session、application、websocket 等作用域.
"""

from typing import Any, Optional

from loguru import logger

from ..annotations.scope import get_scope_value, has_web_scope, is_scoped
from ..bean.scopes import ScopeRegistry
from ..errors import BeanCreationError
from ..errors import ProcessorExecutionError as BeanProcessingError
from .base import BeanPostProcessor, ProcessorOrder


class ScopeBeanPostProcessor(BeanPostProcessor):
    """作用域 Bean 后置处理器

    负责处理 Web 相关作用域的 Bean 创建和管理.
    """

    def __init__(self):
        """初始化作用域处理器"""
        self._scope_registry = ScopeRegistry()
        self._processed_beans: set[str] = set()

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """在 Bean 初始化前处理作用域

        检查 Bean 类是否有作用域注解,如果是 Web 相关作用域,
        则将 Bean 实例存储到相应的作用域上下文中.

        Args:
            bean: Bean 实例
            bean_name: Bean 名称

        Returns:
            处理后的 Bean 实例

        Raises:
            BeanProcessingError: 当作用域处理失败时
        """
        if bean is None or bean_name in self._processed_beans:
            return bean

        try:
            # 检查 Bean 类是否有作用域注解
            bean_class = bean.__class__
            if not is_scoped(bean_class):
                return bean

            # 获取作用域信息
            scope_value = get_scope_value(bean_class)
            if not scope_value:
                return bean

            # 只处理 Web 相关作用域
            if not has_web_scope(bean_class):
                return bean

            # 获取作用域管理器
            scope_manager = self._scope_registry.get_scope_manager(scope_value)
            if not scope_manager:
                logger.warning(f"No scope manager found for scope '{scope_value.value}' for bean '{bean_name}'")
                return bean

            # 获取当前作用域上下文
            scope_context = scope_manager.get_scope_context()
            if not scope_context:
                logger.warning(f"No active scope context for scope '{scope_value.value}' for bean '{bean_name}'")
                return bean

            # 检查作用域中是否已存在该 Bean
            existing_bean = scope_context.get_bean(bean_name)
            if existing_bean is not None:
                logger.debug(f"Bean '{bean_name}' already exists in {scope_value.value} scope, returning existing instance")
                return existing_bean

            # 将 Bean 存储到作用域上下文
            scope_context.put_bean(bean_name, bean)
            logger.debug(f"Stored bean '{bean_name}' in {scope_value.value} scope")

            # 标记为已处理
            self._processed_beans.add(bean_name)

            return bean

        except Exception as e:
            raise BeanProcessingError(
                f"Failed to process scope for bean '{bean_name}'", bean_name=bean_name, processor_name=self.__class__.__name__, cause=e
            ) from e

    def post_process_after_initialization(self, bean: Any, _bean_name: str) -> Any:
        """在 Bean 初始化后处理(作用域处理器不需要后处理)

        Args:
            bean: Bean 实例
            bean_name: Bean 名称

        Returns:
            原始 Bean 实例
        """
        return bean

    def get_order(self) -> int:
        """获取处理器执行顺序

        作用域处理器需要在其他处理器之后执行,
        确保 Bean 已经完全初始化后再进行作用域管理.

        Returns:
            执行顺序(数字越小优先级越高)
        """
        return ProcessorOrder.LIFECYCLE_PROCESSOR + 10  # 在生命周期处理器之后执行

    def supports(self, bean: Any, _bean_name: str) -> bool:
        """检查是否支持处理指定的 Bean

        Args:
            bean: Bean 实例
            bean_name: Bean 名称

        Returns:
            True 表示支持处理此 Bean
        """
        if bean is None:
            return False

        # 检查 Bean 类是否有 Web 相关作用域注解
        return has_web_scope(bean.__class__)

    def get_scoped_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从指定作用域获取 Bean

        Args:
            bean_name: Bean 名称
            scope_value: 作用域值

        Returns:
            Bean 实例,如果不存在则返回 None
        """
        scope_manager = self._scope_registry.get_scope_manager(scope_value)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.get_bean(bean_name)

    def remove_scoped_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从指定作用域移除 Bean

        Args:
            bean_name: Bean 名称
            scope_value: 作用域值

        Returns:
            被移除的 Bean 实例
        """
        scope_manager = self._scope_registry.get_scope_manager(scope_value)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.remove_bean(bean_name)


class ScopedBeanFactory:
    """作用域 Bean 工厂

    提供作用域 Bean 的创建和获取功能.
    """

    def __init__(self):
        """初始化作用域 Bean 工厂"""
        self._scope_registry = ScopeRegistry()

    def get_scoped_bean(self, bean_name: str, scope_value, bean_factory: callable) -> Any:
        """获取作用域 Bean

        如果作用域中已存在 Bean,则返回现有实例;
        否则使用 Bean 工厂创建新实例并存储到作用域中.

        Args:
            bean_name: Bean 名称
            scope_value: 作用域值
            bean_factory: Bean 工厂函数

        Returns:
            Bean 实例

        Raises:
            BeanCreationError: 当 Bean 创建失败时
        """
        try:
            # 获取作用域管理器
            scope_manager = self._scope_registry.get_scope_manager(scope_value)
            if not scope_manager:
                raise BeanCreationError(f"No scope manager found for scope '{scope_value.value}'", bean_name)

            # 获取当前作用域上下文
            scope_context = scope_manager.get_scope_context()
            if not scope_context:
                raise BeanCreationError(f"No active scope context for scope '{scope_value.value}'", bean_name)

            # 检查作用域中是否已存在该 Bean
            existing_bean = scope_context.get_bean(bean_name)
            if existing_bean is not None:
                logger.debug(f"Returning existing bean '{bean_name}' from {scope_value.value} scope")
                return existing_bean

            # 创建新的 Bean 实例
            logger.debug(f"Creating new bean '{bean_name}' for {scope_value.value} scope")
            new_bean = bean_factory()

            # 将 Bean 存储到作用域上下文
            scope_context.put_bean(bean_name, new_bean)
            logger.debug(f"Stored new bean '{bean_name}' in {scope_value.value} scope")

            return new_bean

        except Exception as e:
            raise BeanCreationError(f"Failed to get scoped bean '{bean_name}' for scope '{scope_value.value}'", bean_name, e) from e

    def remove_scoped_bean(self, bean_name: str, scope_value) -> Optional[Any]:
        """从作用域中移除 Bean

        Args:
            bean_name: Bean 名称
            scope_value: 作用域值

        Returns:
            被移除的 Bean 实例
        """
        scope_manager = self._scope_registry.get_scope_manager(scope_value)
        if not scope_manager:
            return None

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return None

        return scope_context.remove_bean(bean_name)

    def clear_scope(self, scope_value) -> None:
        """清空指定作用域的所有 Bean

        Args:
            scope_value: 作用域值
        """
        scope_manager = self._scope_registry.get_scope_manager(scope_value)
        if not scope_manager:
            return

        scope_context = scope_manager.get_scope_context()
        if not scope_context:
            return

        # 获取所有 Bean 名称并逐个移除
        bean_names = list(scope_context.get_bean_names())
        for bean_name in bean_names:
            scope_context.remove_bean(bean_name)

        logger.debug(f"Cleared all beans from {scope_value.value} scope")


# 便利函数
def create_scope_processor() -> ScopeBeanPostProcessor:
    """创建作用域 Bean 后置处理器

    Returns:
        作用域 Bean 后置处理器实例
    """
    return ScopeBeanPostProcessor()


def create_scoped_bean_factory() -> ScopedBeanFactory:
    """创建作用域 Bean 工厂

    Returns:
        作用域 Bean 工厂实例
    """
    return ScopedBeanFactory()

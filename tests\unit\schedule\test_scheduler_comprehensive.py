#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: MiniBootScheduler 综合单元测试
"""

import unittest
import asyncio

from miniboot.schedule import (
    MiniBootScheduler,
    SchedulerState,
    ScheduledConfig,
    SchedulerConfigFactory,
    ScheduleConfigurationError,
    SchedulerNotStartedError,
)


class TestMiniBootSchedulerCore(unittest.TestCase):
    """测试MiniBootScheduler核心功能"""

    def setUp(self):
        """设置测试环境"""
        self.scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.shutdown()

    def test_scheduler_initialization(self):
        """测试调度器初始化"""
        self.assertEqual(self.scheduler.max_workers, 2)
        self.assertEqual(self.scheduler.state, SchedulerState.STOPPED)
        self.assertIsNotNone(self.scheduler.task_registry)
        self.assertIsNotNone(self.scheduler._scheduler)
        self.assertFalse(self.scheduler.use_asyncio)

    def test_scheduler_with_properties(self):
        """测试使用配置对象初始化调度器"""
        properties = SchedulerConfigFactory.create_memory_config(max_workers=5)
        scheduler = MiniBootScheduler(properties=properties, use_asyncio=False)

        self.assertEqual(scheduler.max_workers, 5)
        self.assertEqual(scheduler.properties.concurrency.max_workers, 5)

        scheduler.shutdown()

    def test_scheduler_start_stop(self):
        """测试调度器启动和停止"""
        # 初始状态
        self.assertFalse(self.scheduler.is_running())
        self.assertEqual(self.scheduler.state, SchedulerState.STOPPED)

        # 启动调度器
        self.scheduler.start()
        self.assertTrue(self.scheduler.is_running())
        self.assertEqual(self.scheduler.state, SchedulerState.RUNNING)

        # 停止调度器
        self.scheduler.shutdown()
        self.assertFalse(self.scheduler.is_running())
        self.assertEqual(self.scheduler.state, SchedulerState.STOPPED)

    def test_scheduler_pause_resume(self):
        """测试调度器暂停和恢复"""
        self.scheduler.start()

        # 暂停调度器
        self.scheduler.pause()
        self.assertEqual(self.scheduler.state, SchedulerState.PAUSED)

        # 恢复调度器
        self.scheduler.resume()
        self.assertEqual(self.scheduler.state, SchedulerState.RUNNING)

        self.scheduler.shutdown()

    def test_scheduler_context_manager(self):
        """测试调度器上下文管理器"""
        with MiniBootScheduler(max_workers=2, use_asyncio=False) as scheduler:
            self.assertTrue(scheduler.is_running())

        # 退出上下文后应该自动关闭
        self.assertFalse(scheduler.is_running())

    def test_scheduler_info(self):
        """测试获取调度器信息"""
        info = self.scheduler.get_scheduler_info()

        self.assertIn("state", info)
        self.assertIn("max_workers", info)
        self.assertIn("timezone", info)
        self.assertIn("job_defaults", info)
        self.assertIn("total_tasks", info)
        self.assertIn("running_tasks", info)

        self.assertEqual(info["state"], SchedulerState.STOPPED)
        self.assertEqual(info["max_workers"], 2)
        self.assertEqual(info["total_tasks"], 0)

    def test_task_registration(self):
        """测试任务注册"""

        def test_task():
            return "test_result"

        config = ScheduledConfig(fixed_rate="1s")
        task_id = self.scheduler.register_task(test_task, config, "test_task")

        self.assertIsNotNone(task_id)
        self.assertEqual(len(self.scheduler.task_registry.get_all_tasks()), 1)

        # 获取任务
        task = self.scheduler.get_task(task_id)
        self.assertIsNotNone(task)
        self.assertEqual(task.name, "test_task")

    def test_task_unregistration(self):
        """测试任务注销"""

        def test_task():
            return "test_result"

        config = ScheduledConfig(fixed_rate="1s")
        task_id = self.scheduler.register_task(test_task, config, "test_task")

        # 注销任务
        success = self.scheduler.unregister_task(task_id)
        self.assertTrue(success)
        self.assertEqual(len(self.scheduler.task_registry.get_all_tasks()), 0)

        # 注销不存在的任务
        success = self.scheduler.unregister_task("non_existent")
        self.assertFalse(success)

    def test_task_execution_control(self):
        """测试任务执行控制"""

        def test_task():
            return "test_result"

        config = ScheduledConfig(fixed_rate="1s")
        task_id = self.scheduler.register_task(test_task, config, "test_task")

        self.scheduler.start()

        # 暂停任务
        success = self.scheduler.pause_task(task_id)
        self.assertTrue(success)

        # 恢复任务
        success = self.scheduler.resume_task(task_id)
        self.assertTrue(success)

        # 控制不存在的任务
        success = self.scheduler.pause_task("non_existent")
        self.assertFalse(success)

        self.scheduler.shutdown()

    def test_scheduler_statistics(self):
        """测试调度器统计"""

        def test_task():
            return "test_result"

        config = ScheduledConfig(fixed_rate="1s")
        self.scheduler.register_task(test_task, config, "test_task")

        stats = self.scheduler.get_execution_statistics()

        self.assertIn("total_executions", stats)
        self.assertIn("successful_executions", stats)
        self.assertIn("failed_executions", stats)
        self.assertIn("average_execution_time", stats)
        self.assertIn("last_execution_time", stats)

        # 初始统计应该为0
        self.assertEqual(stats["total_executions"], 0)
        self.assertEqual(stats["successful_executions"], 0)
        self.assertEqual(stats["failed_executions"], 0)

    def test_error_handling(self):
        """测试错误处理"""
        # 测试在未启动状态下执行操作
        with self.assertRaises(SchedulerNotStartedError):
            self.scheduler.pause_task("any_task")

        # 测试无效的任务配置
        with self.assertRaises(ScheduleConfigurationError):
            invalid_config = ScheduledConfig()  # 没有设置任何调度配置
            self.scheduler.register_task(lambda: None, invalid_config)

    def test_task_manager_integration(self):
        """测试任务管理器集成"""
        if self.scheduler.task_manager:
            # 测试任务管理器摘要
            summary = self.scheduler.get_task_manager_summary()
            self.assertIn("total_managed_tasks", summary)
            self.assertIn("total_executions", summary)
            self.assertIn("total_successes", summary)
            self.assertIn("total_failures", summary)

            # 测试任务指标
            metrics = self.scheduler.get_task_metrics("non_existent")
            self.assertIsNone(metrics)


class TestMiniBootSchedulerAsync(unittest.TestCase):
    """测试MiniBootScheduler异步功能"""

    def setUp(self):
        """设置测试环境"""
        self.scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)  # 使用Background调度器避免事件循环问题

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.shutdown()

    def test_async_task_registration(self):
        """测试异步任务注册"""

        async def async_task():
            await asyncio.sleep(0.01)
            return "async_result"

        config = ScheduledConfig(fixed_rate="2s")
        task_id = self.scheduler.register_task(async_task, config, "async_task")

        self.assertIsNotNone(task_id)
        task = self.scheduler.get_task(task_id)
        self.assertTrue(task.is_async)

    def test_mixed_task_types(self):
        """测试混合任务类型"""

        def sync_task():
            return "sync_result"

        async def async_task():
            await asyncio.sleep(0.01)
            return "async_result"

        # 注册同步和异步任务
        sync_id = self.scheduler.register_task(sync_task, ScheduledConfig(fixed_rate="1s"), "sync")
        async_id = self.scheduler.register_task(async_task, ScheduledConfig(fixed_rate="2s"), "async")

        self.assertIsNotNone(sync_id)
        self.assertIsNotNone(async_id)

        sync_task_obj = self.scheduler.get_task(sync_id)
        async_task_obj = self.scheduler.get_task(async_id)

        self.assertFalse(sync_task_obj.is_async)
        self.assertTrue(async_task_obj.is_async)


class TestMiniBootSchedulerEdgeCases(unittest.TestCase):
    """测试MiniBootScheduler边界情况"""

    def test_scheduler_double_start(self):
        """测试重复启动调度器"""
        scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)

        try:
            scheduler.start()
            self.assertTrue(scheduler.is_running())

            # 重复启动应该不会出错
            scheduler.start()
            self.assertTrue(scheduler.is_running())

        finally:
            scheduler.shutdown()

    def test_scheduler_double_shutdown(self):
        """测试重复关闭调度器"""
        scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)

        scheduler.start()
        scheduler.shutdown()
        self.assertFalse(scheduler.is_running())

        # 重复关闭应该不会出错
        scheduler.shutdown()
        self.assertFalse(scheduler.is_running())

    def test_task_registration_edge_cases(self):
        """测试任务注册边界情况"""
        scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)

        try:
            # 测试None任务
            with self.assertRaises((TypeError, ValueError)):
                scheduler.register_task(None, ScheduledConfig(fixed_rate="1s"))

            # 测试空名称
            def test_task():
                return "test"

            task_id = scheduler.register_task(test_task, ScheduledConfig(fixed_rate="1s"), "")
            self.assertIsNotNone(task_id)

        finally:
            scheduler.shutdown()

    def test_large_number_of_tasks(self):
        """测试大量任务注册"""
        scheduler = MiniBootScheduler(max_workers=5, use_asyncio=False)

        try:
            task_ids = []

            # 注册100个任务
            for i in range(100):

                def task_func(x=i):
                    return f"task_{x}"

                config = ScheduledConfig(fixed_rate="10s")  # 长间隔避免实际执行
                task_id = scheduler.register_task(task_func, config, f"task_{i}")
                task_ids.append(task_id)

            self.assertEqual(len(task_ids), 100)
            self.assertEqual(len(scheduler.task_registry.get_all_tasks()), 100)

            # 批量注销
            for task_id in task_ids:
                scheduler.unregister_task(task_id)

            self.assertEqual(len(scheduler.task_registry.get_all_tasks()), 0)

        finally:
            scheduler.shutdown()


if __name__ == "__main__":
    unittest.main()

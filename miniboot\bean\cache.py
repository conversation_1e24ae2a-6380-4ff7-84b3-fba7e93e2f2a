#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean三级缓存实现
"""

import threading
from collections import OrderedDict
from typing import Any, Callable, ClassVar, Dict, Optional, Set

from loguru import logger

try:
    from ..utils.singleton import SingletonMeta
except ImportError:
    from miniboot.utils.singleton import SingletonMeta


class _ThreeLevelCacheBase:
    """Bean三级缓存基础实现"""

    def __init__(self, enable_thread_safety: bool = True, max_size: int = 10000):
        """初始化三级缓存

        Args:
            enable_thread_safety: 是否启用线程安全，默认为True
            max_size: 缓存最大大小，防止内存泄漏，默认10000
        """
        self._max_size = max_size
        self._init_cache_data(enable_thread_safety)

    def _init_cache_data(self, enable_thread_safety: bool = True):
        """初始化缓存数据结构"""
        # 一级缓存：完全初始化的Bean实例
        self._singleton_objects: Dict[str, Any] = OrderedDict()

        # 二级缓存：早期Bean对象(已实例化但未完成依赖注入)
        self._early_singleton_objects: Dict[str, Any] = OrderedDict()

        # 三级缓存：Bean工厂函数
        self._singleton_factories: Dict[str, Callable[[], Any]] = OrderedDict()

        # 正在创建中的Bean集合，用于检测循环依赖
        self._currently_creating: Set[str] = set()

        # 已注册的单例Bean名称集合
        self._registered_singletons: Set[str] = set()

        # 线程安全锁
        self._lock = threading.RLock() if enable_thread_safety else None

        # 缓存大小控制
        self._access_order = OrderedDict()  # 用于LRU清理

        # 缓存统计信息
        self._cache_stats = {
            'hits_level1': 0,      # 一级缓存命中次数
            'hits_level2': 0,      # 二级缓存命中次数
            'hits_level3': 0,      # 三级缓存命中次数
            'misses': 0,           # 缓存未命中次数
            'total_requests': 0,   # 总请求次数
            'evictions': 0         # 缓存驱逐次数
        }

    def get(self, name: str) -> Optional[Any]:
        """从三级缓存获取Bean实例

        按照一级缓存 -> 二级缓存 -> 三级缓存的顺序查找Bean。
        如果在三级缓存中找到，会调用工厂函数创建Bean并移动到二级缓存。

        Args:
            name: Bean名称

        Returns:
            Optional[Any]: Bean实例，如果不存在则返回None
        """
        if self._lock:
            with self._lock:
                return self._get_bean_internal(name)
        else:
            return self._get_bean_internal(name)

    def _get_bean_internal(self, name: str) -> Optional[Any]:
        """内部获取Bean方法(无锁版本)"""
        self._cache_stats['total_requests'] += 1

        # 一级缓存：完全初始化的Bean
        if name in self._singleton_objects:
            self._cache_stats['hits_level1'] += 1
            logger.debug(f"Cache hit (L1): {name}")
            # 更新访问顺序
            if name in self._access_order:
                self._access_order.move_to_end(name)
            return self._singleton_objects[name]

        # 二级缓存：早期Bean对象
        if name in self._early_singleton_objects:
            self._cache_stats['hits_level2'] += 1
            # 更新访问顺序
            if name in self._access_order:
                self._access_order.move_to_end(name)
            return self._early_singleton_objects[name]

        # 三级缓存：Bean工厂函数
        if name in self._singleton_factories:
            self._cache_stats['hits_level3'] += 1
            factory = self._singleton_factories[name]

            # 调用工厂函数创建Bean
            bean = factory()

            # 移动到二级缓存
            self._early_singleton_objects[name] = bean
            del self._singleton_factories[name]

            # 更新访问顺序
            self._access_order[name] = True
            self._access_order.move_to_end(name)

            return bean

        # 缓存未命中
        self._cache_stats['misses'] += 1
        logger.debug(f"Cache miss: {name}")
        return None

    def put(self, name: str, bean: Any) -> None:
        """将Bean放入一级缓存(完成品缓存)

        这表示Bean已经完全初始化完成，包括依赖注入和初始化方法调用。
        会清理该Bean在二级和三级缓存中的记录。

        Args:
            name: Bean名称
            bean: 完全初始化的Bean实例
        """
        if self._lock:
            with self._lock:
                self._put_singleton_internal(name, bean)
        else:
            self._put_singleton_internal(name, bean)

    def _put_singleton_internal(self, name: str, bean: Any) -> None:
        """内部放入一级缓存方法(无锁版本)"""
        # 检查缓存大小并清理
        self._cleanup_if_needed()

        # 清理二级和三级缓存中的记录
        self._early_singleton_objects.pop(name, None)
        self._singleton_factories.pop(name, None)

        # 添加到一级缓存
        self._singleton_objects[name] = bean

        # 更新访问顺序（用于LRU）
        self._access_order[name] = True
        self._access_order.move_to_end(name)

        # 注册为单例Bean
        self._registered_singletons.add(name)

        # 从创建中集合移除
        self._currently_creating.discard(name)

        logger.debug(f"Bean cached (L1): {name}")

    def _cleanup_if_needed(self) -> None:
        """检查并清理缓存以防止内存泄漏"""
        total_size = (len(self._singleton_objects) +
                     len(self._early_singleton_objects) +
                     len(self._singleton_factories))

        if total_size > self._max_size:
            self._cleanup_lru_entries()

    def _cleanup_lru_entries(self) -> None:
        """清理最少使用的缓存条目"""
        # 计算需要清理的数量（清理10%）
        cleanup_count = max(1, self._max_size // 10)

        # 按访问顺序清理最少使用的条目
        for _ in range(cleanup_count):
            if not self._access_order:
                break

            # 获取最少使用的条目
            lru_name = next(iter(self._access_order))

            # 从所有缓存中移除
            self._singleton_objects.pop(lru_name, None)
            self._early_singleton_objects.pop(lru_name, None)
            self._singleton_factories.pop(lru_name, None)
            self._access_order.pop(lru_name, None)
            self._registered_singletons.discard(lru_name)

            # 更新统计
            self._cache_stats['evictions'] += 1

    def put_singleton(self, name: str, bean: Any) -> None:
        """将Bean放入一级缓存 - 兼容性方法

        这是put方法的别名，用于向后兼容。

        Args:
            name: Bean名称
            bean: Bean实例
        """
        self.put(name, bean)

    def put_early(self, name: str, bean: Any) -> None:
        """将Bean放入二级缓存(早期对象缓存)

        这表示Bean已经实例化但还未完成依赖注入。
        通常用于解决循环依赖问题。

        Args:
            name: Bean名称
            bean: 早期Bean对象
        """
        if self._lock:
            with self._lock:
                self._early_singleton_objects[name] = bean
        else:
            self._early_singleton_objects[name] = bean

    def put_factory(self, name: str, factory: Callable[[], Any]) -> None:
        """将Bean工厂函数放入三级缓存

        工厂函数用于延迟创建Bean实例，通常在Bean实例化后立即调用。

        Args:
            name: Bean名称
            factory: Bean工厂函数，调用时返回Bean实例
        """
        if self._lock:
            with self._lock:
                self._singleton_factories[name] = factory
        else:
            self._singleton_factories[name] = factory

    def contains(self, name: str) -> bool:
        """检查是否包含指定的单例Bean

        检查一级缓存中是否存在指定的Bean。

        Args:
            name: Bean名称

        Returns:
            bool: 如果存在返回True，否则返回False
        """
        if self._lock:
            with self._lock:
                return name in self._singleton_objects
        else:
            return name in self._singleton_objects

    def has(self, name: str) -> bool:
        """检查三级缓存中是否包含指定的Bean

        检查三级缓存中的任意一级是否存在指定的Bean。

        Args:
            name: Bean名称

        Returns:
            bool: 如果存在返回True，否则返回False
        """
        if self._lock:
            with self._lock:
                return (name in self._singleton_objects or
                       name in self._early_singleton_objects or
                       name in self._singleton_factories)
        else:
            return (name in self._singleton_objects or
                   name in self._early_singleton_objects or
                   name in self._singleton_factories)

    def creating(self, name: str) -> bool:
        """检查Bean是否正在创建中

        用于检测循环依赖。

        Args:
            name: Bean名称

        Returns:
            bool: 如果正在创建返回True，否则返回False
        """
        if self._lock:
            with self._lock:
                return name in self._currently_creating
        else:
            return name in self._currently_creating

    def set_creating(self, name: str, creating: bool) -> None:
        """设置Bean的创建状态

        Args:
            name: Bean名称
            creating: 是否正在创建
        """
        if self._lock:
            with self._lock:
                if creating:
                    self._currently_creating.add(name)
                else:
                    self._currently_creating.discard(name)
        else:
            if creating:
                self._currently_creating.add(name)
            else:
                self._currently_creating.discard(name)

    def remove(self, name: str) -> Optional[Any]:
        """从缓存中移除单例Bean

        从所有三级缓存中移除指定的Bean。

        Args:
            name: Bean名称

        Returns:
            Optional[Any]: 被移除的Bean实例，如果不存在则返回None
        """
        if self._lock:
            with self._lock:
                return self._remove_singleton_internal(name)
        else:
            return self._remove_singleton_internal(name)

    def _remove_singleton_internal(self, name: str) -> Optional[Any]:
        """内部移除单例Bean方法(无锁版本)"""
        bean = None

        # 从一级缓存移除
        if name in self._singleton_objects:
            bean = self._singleton_objects.pop(name)

        # 从二级缓存移除
        self._early_singleton_objects.pop(name, None)

        # 从三级缓存移除
        self._singleton_factories.pop(name, None)

        # 从注册集合移除
        self._registered_singletons.discard(name)

        # 从创建中集合移除
        self._currently_creating.discard(name)

        return bean

    def clear(self) -> None:
        """清空所有缓存"""
        if self._lock:
            with self._lock:
                self._clear_internal()
        else:
            self._clear_internal()

    def _clear_internal(self) -> None:
        """内部清空缓存方法(无锁版本)"""
        self._singleton_objects.clear()
        self._early_singleton_objects.clear()
        self._singleton_factories.clear()
        self._currently_creating.clear()
        self._registered_singletons.clear()
        self._access_order.clear()

        # 重置统计信息
        for key in self._cache_stats:
            self._cache_stats[key] = 0

    def names(self) -> list[str]:
        """获取所有单例Bean名称

        Returns:
            list[str]: 单例Bean名称列表
        """
        if self._lock:
            with self._lock:
                return list(self._registered_singletons)
        else:
            return list(self._registered_singletons)

    def stats(self) -> dict[str, int]:
        """获取缓存统计信息

        Returns:
            dict[str, int]: 缓存统计信息，包含各级缓存命中次数等
        """
        if self._lock:
            with self._lock:
                return self._cache_stats.copy()
        else:
            return self._cache_stats.copy()

    def info(self) -> dict[str, Any]:
        """获取缓存详细信息

        Returns:
            dict[str, Any]: 缓存详细信息，包含各级缓存大小和内容
        """
        if self._lock:
            with self._lock:
                return self._get_cache_info_internal()
        else:
            return self._get_cache_info_internal()

    def _get_cache_info_internal(self) -> dict[str, Any]:
        """内部获取缓存信息方法(无锁版本)"""
        return {
            'level1_size': len(self._singleton_objects),
            'level2_size': len(self._early_singleton_objects),
            'level3_size': len(self._singleton_factories),
            'currently_creating_size': len(self._currently_creating),
            'level1_keys': list(self._singleton_objects.keys()),
            'level2_keys': list(self._early_singleton_objects.keys()),
            'level3_keys': list(self._singleton_factories.keys()),
            'currently_creating': list(self._currently_creating),
            'stats': self._cache_stats.copy(),
            'singleton_mode': getattr(self, '_use_singleton', True),
            'instance_id': id(self)
        }


class _ThreeLevelCacheSingleton(_ThreeLevelCacheBase, metaclass=SingletonMeta):
    """单例模式的三级缓存"""

    # 类级别的初始化锁
    _init_lock = threading.RLock()

    def __init__(self, enable_thread_safety: bool = True, max_size: int = 10000):
        # 单例模式：只在首次创建时初始化，使用双重检查锁定
        if not hasattr(self, '_initialized'):
            with self._init_lock:
                # 双重检查：防止竞态条件
                if not hasattr(self, '_initialized'):
                    super().__init__(enable_thread_safety, max_size)
                    self._initialized = True

    def cleanup(self) -> None:
        """清理缓存数据，供单例重置时调用"""
        if hasattr(self, '_singleton_objects'):
            self._singleton_objects.clear()
            self._early_singleton_objects.clear()
            self._singleton_factories.clear()
            self._currently_creating.clear()
            self._registered_singletons.clear()
            self._access_order.clear()

            # 重置统计信息
            self._cache_stats = {
                'hits_level1': 0,
                'hits_level2': 0,
                'hits_level3': 0,
                'misses': 0,
                'total_requests': 0,
                'evictions': 0
            }


class ThreeLevelCache:
    """Bean三级缓存实现 - 支持单例和实例模式

    实现Spring经典的三级缓存机制，用于解决循环依赖问题：
    - 一级缓存(singleton_objects): 存储完全初始化的Bean实例
    - 二级缓存(early_singleton_objects): 存储早期Bean对象(已实例化但未完成依赖注入)
    - 三级缓存(singleton_factories): 存储Bean工厂函数，用于创建早期Bean对象

    缓存查找顺序: 一级缓存 -> 二级缓存 -> 三级缓存

    单例模式特性：
    - 默认使用单例模式，确保全局缓存一致性
    - 支持强制实例模式，用于测试或特殊场景
    - 可通过配置全局控制单例行为
    - 测试友好的重置机制
    """

    # 类变量：控制是否使用单例模式
    _use_singleton: ClassVar[bool] = True

    def __new__(cls, enable_thread_safety: bool = True, force_instance: bool = False, max_size: int = 10000):
        """创建缓存实例，根据配置决定是否使用单例"""
        if force_instance or not cls._use_singleton:
            # 强制实例模式或全局禁用单例：创建新实例
            return _ThreeLevelCacheBase(enable_thread_safety, max_size)
        else:
            # 单例模式：返回单例实例
            return _ThreeLevelCacheSingleton(enable_thread_safety, max_size)

    @classmethod
    def configure_singleton(cls, use_singleton: bool = True) -> None:
        """配置是否使用单例模式

        Args:
            use_singleton: 是否使用单例模式，默认为True
        """
        cls._use_singleton = use_singleton

    @classmethod
    def is_singleton_enabled(cls) -> bool:
        """检查是否启用了单例模式

        Returns:
            bool: 如果启用单例模式返回True，否则返回False
        """
        return cls._use_singleton

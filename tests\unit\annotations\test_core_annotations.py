#!/usr/bin/env python
"""
* @author: cz
* @description: 核心注解测试

测试核心注解的功能，包括@Component、@Service、@Repository、@Configuration、@Bean等。
"""

import unittest

from miniboot.annotations import Bean, Component, ComponentScan, Configuration, MiniBootApplication, Repository, Scope, Service


class TestCoreAnnotations(unittest.TestCase):
    """核心注解测试类"""

    def test_component_annotation_without_params(self):
        """测试@Component注解无参数使用"""

        @Component
        class TestService:
            pass

        # 验证注解属性
        self.assertTrue(hasattr(TestService, "__is_component__"))
        self.assertTrue(TestService.__is_component__)
        self.assertEqual(TestService.__component_name__, "testService")
        self.assertEqual(TestService.__component_scope__, Scope.SINGLETON)

        # 验证元数据
        self.assertTrue(hasattr(TestService, "__component_metadata__"))
        metadata = TestService.__component_metadata__
        self.assertEqual(metadata.name, "testService")
        self.assertEqual(metadata.scope, Scope.SINGLETON)
        self.assertFalse(metadata.lazy)
        self.assertFalse(metadata.primary)
        self.assertEqual(metadata.depends_on, [])

    def test_component_annotation_with_params(self):
        """测试@Component注解带参数使用"""

        @Component(name="customService", scope=Scope.PROTOTYPE, lazy=True, primary=True, depends_on=["dependency1", "dependency2"])
        class CustomService:
            pass

        # 验证注解属性
        self.assertTrue(CustomService.__is_component__)
        self.assertEqual(CustomService.__component_name__, "customService")
        self.assertEqual(CustomService.__component_scope__, Scope.PROTOTYPE)

        # 验证元数据
        metadata = CustomService.__component_metadata__
        self.assertEqual(metadata.name, "customService")
        self.assertEqual(metadata.scope, Scope.PROTOTYPE)
        self.assertTrue(metadata.lazy)
        self.assertTrue(metadata.primary)
        self.assertEqual(metadata.depends_on, ["dependency1", "dependency2"])

    def test_component_annotation_with_name_only(self):
        """测试@Component注解只指定名称"""

        @Component("namedService")
        class NamedService:
            pass

        self.assertTrue(NamedService.__is_component__)
        self.assertEqual(NamedService.__component_name__, "namedService")

        metadata = NamedService.__component_metadata__
        self.assertEqual(metadata.name, "namedService")

    def test_service_annotation(self):
        """测试@Service注解"""

        @Service
        class UserService:
            pass

        # 验证是组件
        self.assertTrue(UserService.__is_component__)
        self.assertEqual(UserService.__component_name__, "userService")

        # 验证是服务
        self.assertTrue(hasattr(UserService, "__is_service__"))
        self.assertTrue(UserService.__is_service__)

    def test_service_annotation_with_name(self):
        """测试@Service注解带名称"""

        @Service("customUserService")
        class CustomUserService:
            pass

        self.assertTrue(CustomUserService.__is_component__)
        self.assertTrue(CustomUserService.__is_service__)
        self.assertEqual(CustomUserService.__component_name__, "customUserService")

    def test_repository_annotation(self):
        """测试@Repository注解"""

        @Repository
        class UserRepository:
            pass

        # 验证是组件
        self.assertTrue(UserRepository.__is_component__)
        self.assertEqual(UserRepository.__component_name__, "userRepository")

        # 验证是仓储
        self.assertTrue(hasattr(UserRepository, "__is_repository__"))
        self.assertTrue(UserRepository.__is_repository__)

    def test_configuration_annotation(self):
        """测试@Configuration注解"""

        @Configuration
        class AppConfig:
            pass

        # 验证是配置类
        self.assertTrue(hasattr(AppConfig, "__is_configuration__"))
        self.assertTrue(AppConfig.__is_configuration__)
        self.assertTrue(AppConfig.__configuration__)

        # 验证也是组件
        self.assertTrue(AppConfig.__is_component__)
        self.assertEqual(AppConfig.__component_name__, "appConfig")

    def test_bean_annotation(self):
        """测试@Bean注解"""

        @Configuration
        class DatabaseConfig:
            @Bean
            def data_source(self):
                return "DataSource"

            @Bean(name="customDataSource", scope=Scope.PROTOTYPE, init_method="init", destroy_method="destroy", depends_on=["dependency"])
            def custom_data_source(self):
                return "CustomDataSource"

        # 验证第一个Bean方法
        data_source_method = DatabaseConfig.data_source
        self.assertTrue(hasattr(data_source_method, "__is_bean__"))
        self.assertTrue(data_source_method.__is_bean__)
        self.assertEqual(data_source_method.__bean_name__, "data_source")
        self.assertEqual(data_source_method.__bean_scope__, Scope.SINGLETON)

        # 验证Bean元数据
        metadata = data_source_method.__bean_metadata__
        self.assertEqual(metadata.name, "data_source")
        self.assertEqual(metadata.scope, Scope.SINGLETON)
        self.assertIsNone(metadata.init_method)
        self.assertIsNone(metadata.destroy_method)
        self.assertEqual(metadata.depends_on, [])

        # 验证第二个Bean方法
        custom_data_source_method = DatabaseConfig.custom_data_source
        self.assertTrue(custom_data_source_method.__is_bean__)
        self.assertEqual(custom_data_source_method.__bean_name__, "customDataSource")
        self.assertEqual(custom_data_source_method.__bean_scope__, Scope.PROTOTYPE)

        # 验证自定义Bean元数据
        custom_metadata = custom_data_source_method.__bean_metadata__
        self.assertEqual(custom_metadata.name, "customDataSource")
        self.assertEqual(custom_metadata.scope, Scope.PROTOTYPE)
        self.assertEqual(custom_metadata.init_method, "init")
        self.assertEqual(custom_metadata.destroy_method, "destroy")
        self.assertEqual(custom_metadata.depends_on, ["dependency"])

    def test_miniboot_application_annotation(self):
        """测试@MiniBootApplication注解"""

        @MiniBootApplication
        class Application:
            pass

        # 验证是应用入口
        self.assertTrue(hasattr(Application, "__is_miniboot_application__"))
        self.assertTrue(Application.__is_miniboot_application__)
        self.assertTrue(Application.__miniboot_application__)

        # 验证也是配置类
        self.assertTrue(Application.__is_configuration__)

        # 验证也是组件
        self.assertTrue(Application.__is_component__)

        # 验证有组件扫描
        self.assertTrue(hasattr(Application, "__is_component_scan__"))
        self.assertTrue(Application.__is_component_scan__)

    def test_miniboot_application_with_base_packages(self):
        """测试@MiniBootApplication注解带包扫描"""

        @MiniBootApplication(base_packages=["com.example", "com.test"])
        class Application:
            pass

        # 验证组件扫描元数据
        self.assertTrue(hasattr(Application, "__component_scan_metadata__"))
        metadata = Application.__component_scan_metadata__
        self.assertEqual(metadata.base_packages, ["com.example", "com.test"])

    def test_component_scan_annotation(self):
        """测试@ComponentScan注解"""

        @ComponentScan(base_packages=["com.example"], exclude_filters=["com.example.test"], include_filters=["com.example.core"], lazy_init=False)
        class ScanConfig:
            pass

        # 验证组件扫描标记
        self.assertTrue(hasattr(ScanConfig, "__is_component_scan__"))
        self.assertTrue(ScanConfig.__is_component_scan__)

        # 验证元数据
        metadata = ScanConfig.__component_scan_metadata__
        self.assertEqual(metadata.base_packages, ["com.example"])
        self.assertEqual(metadata.exclude_filters, ["com.example.test"])
        self.assertEqual(metadata.include_filters, ["com.example.core"])
        self.assertFalse(metadata.lazy_init)


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
"""
* @author: cz
* @description: 组件扫描器测试

测试组件扫描器的功能，包括ComponentScanner、Scan<PERSON>ilter、ScanResult等。
"""

import unittest
from unittest.mock import patch

from miniboot.annotations import (
    Async,
    AsyncEventListener,
    Autowired,
    Bean,
    Component,
    ComponentScan,
    ComponentScanner,
    Configuration,
    EventListener,
    MiniBootApplication,
    PostConstruct,
    PreDestroy,
    Repository,
    ScanFilter,
    ScanResult,
    Scheduled,
    Service,
    create_filter,
    create_scanner,
    scan_packages,
)


# 测试用的组件类
@Component
class TestComponent:
    @Autowired
    def set_dependency(self, dep):
        self.dep = dep

    @PostConstruct
    def init(self):
        pass

    @PreDestroy
    def cleanup(self):
        pass


@Service
class TestService:
    @Async
    def async_method(self):
        pass

    @Scheduled(cron="0 0 12 * * ?")
    def scheduled_method(self):
        pass


@Repository
class TestRepository:
    @EventListener
    def handle_event(self, event):
        pass

    @AsyncEventListener
    def handle_async_event(self, event):
        pass


@Configuration
class TestConfiguration:
    @Bean
    def test_bean(self):
        return "test_bean"

    @Bean(name="custom_bean")
    def create_custom_bean(self):
        return "custom_bean"


class PlainClass:
    """没有注解的普通类"""

    def normal_method(self):
        pass


class TestScanFilter(unittest.TestCase):
    """扫描过滤器测试类"""

    def test_scan_filter_creation(self):
        """测试扫描过滤器创建"""
        filter = ScanFilter()

        self.assertEqual(len(filter.include_patterns), 0)
        self.assertEqual(len(filter.exclude_patterns), 0)
        self.assertEqual(len(filter.include_annotations), 0)
        self.assertEqual(len(filter.exclude_annotations), 0)

    def test_scan_filter_patterns(self):
        """测试扫描过滤器模式"""
        filter = ScanFilter()

        # 添加包含模式
        filter.add_include_pattern("test")
        filter.add_include_pattern("example")
        self.assertEqual(len(filter.include_patterns), 2)
        self.assertIn("test", filter.include_patterns)
        self.assertIn("example", filter.include_patterns)

        # 添加排除模式
        filter.add_exclude_pattern("exclude")
        self.assertEqual(len(filter.exclude_patterns), 1)
        self.assertIn("exclude", filter.exclude_patterns)

    def test_scan_filter_annotations(self):
        """测试扫描过滤器注解"""
        filter = ScanFilter()

        # 添加包含注解
        filter.add_include_annotation("Component")
        filter.add_include_annotation("Service")
        self.assertEqual(len(filter.include_annotations), 2)
        self.assertIn("Component", filter.include_annotations)
        self.assertIn("Service", filter.include_annotations)

        # 添加排除注解
        filter.add_exclude_annotation("Repository")
        self.assertEqual(len(filter.exclude_annotations), 1)
        self.assertIn("Repository", filter.exclude_annotations)

    def test_should_include_module(self):
        """测试模块包含检查"""
        filter = ScanFilter()

        # 没有模式时，应该包含所有模块
        self.assertTrue(filter.should_include_module("test.module"))
        self.assertTrue(filter.should_include_module("example.module"))

        # 添加排除模式
        filter.add_exclude_pattern("exclude")
        self.assertFalse(filter.should_include_module("exclude.module"))
        self.assertTrue(filter.should_include_module("test.module"))

        # 添加包含模式
        filter.add_include_pattern("test")
        self.assertTrue(filter.should_include_module("test.module"))
        self.assertFalse(filter.should_include_module("example.module"))

    def test_should_include_class(self):
        """测试类包含检查"""
        filter = ScanFilter()

        # 没有注解过滤时，应该包含所有类
        self.assertTrue(filter.should_include_class(TestComponent, {"Component"}))
        self.assertTrue(filter.should_include_class(TestService, {"Service"}))

        # 添加排除注解
        filter.add_exclude_annotation("Repository")
        self.assertFalse(filter.should_include_class(TestRepository, {"Repository"}))
        self.assertTrue(filter.should_include_class(TestComponent, {"Component"}))

        # 添加包含注解
        filter.add_include_annotation("Component")
        self.assertTrue(filter.should_include_class(TestComponent, {"Component"}))
        self.assertFalse(filter.should_include_class(TestService, {"Service"}))


class TestScanResult(unittest.TestCase):
    """扫描结果测试类"""

    def test_scan_result_creation(self):
        """测试扫描结果创建"""
        result = ScanResult()

        self.assertEqual(len(result.components), 0)
        self.assertEqual(len(result.configurations), 0)
        self.assertEqual(len(result.bean_methods), 0)
        self.assertEqual(len(result.autowired_fields), 0)
        self.assertEqual(len(result.autowired_methods), 0)
        self.assertEqual(len(result.lifecycle_methods), 0)
        self.assertEqual(len(result.async_methods), 0)
        self.assertEqual(len(result.scheduled_methods), 0)
        self.assertEqual(len(result.event_listeners), 0)
        self.assertEqual(len(result.scanned_modules), 0)
        self.assertEqual(len(result.scan_errors), 0)

    def test_add_component(self):
        """测试添加组件"""
        result = ScanResult()

        result.add_component("test.TestComponent", TestComponent)
        self.assertEqual(len(result.components), 1)
        self.assertIn("test.TestComponent", result.components)
        self.assertEqual(result.components["test.TestComponent"], TestComponent)

    def test_add_configuration(self):
        """测试添加配置类"""
        result = ScanResult()

        result.add_configuration("test.TestConfiguration", TestConfiguration)
        self.assertEqual(len(result.configurations), 1)
        self.assertIn("test.TestConfiguration", result.configurations)

    def test_add_bean_method(self):
        """测试添加Bean方法"""
        result = ScanResult()

        result.add_bean_method("test.TestConfiguration.test_bean", TestConfiguration.test_bean)
        self.assertEqual(len(result.bean_methods), 1)
        self.assertIn("test.TestConfiguration.test_bean", result.bean_methods)

    def test_add_autowired_field(self):
        """测试添加自动装配字段"""
        result = ScanResult()

        result.add_autowired_field("test.TestComponent", "dependency")
        self.assertEqual(len(result.autowired_fields), 1)
        self.assertIn("test.TestComponent", result.autowired_fields)
        self.assertIn("dependency", result.autowired_fields["test.TestComponent"])

    def test_add_autowired_method(self):
        """测试添加自动装配方法"""
        result = ScanResult()

        result.add_autowired_method("test.TestComponent", "set_dependency")
        self.assertEqual(len(result.autowired_methods), 1)
        self.assertIn("test.TestComponent", result.autowired_methods)
        self.assertIn("set_dependency", result.autowired_methods["test.TestComponent"])

    def test_add_lifecycle_method(self):
        """测试添加生命周期方法"""
        result = ScanResult()

        result.add_lifecycle_method("test.TestComponent", "post_construct", "init")
        result.add_lifecycle_method("test.TestComponent", "pre_destroy", "cleanup")

        self.assertEqual(len(result.lifecycle_methods), 1)
        self.assertIn("test.TestComponent", result.lifecycle_methods)
        self.assertIn("post_construct", result.lifecycle_methods["test.TestComponent"])
        self.assertIn("pre_destroy", result.lifecycle_methods["test.TestComponent"])
        self.assertIn("init", result.lifecycle_methods["test.TestComponent"]["post_construct"])
        self.assertIn("cleanup", result.lifecycle_methods["test.TestComponent"]["pre_destroy"])

    def test_add_async_method(self):
        """测试添加异步方法"""
        result = ScanResult()

        result.add_async_method("test.TestService", "async_method")
        self.assertEqual(len(result.async_methods), 1)
        self.assertIn("test.TestService", result.async_methods)
        self.assertIn("async_method", result.async_methods["test.TestService"])

    def test_add_scheduled_method(self):
        """测试添加调度方法"""
        result = ScanResult()

        result.add_scheduled_method("test.TestService", "scheduled_method")
        self.assertEqual(len(result.scheduled_methods), 1)
        self.assertIn("test.TestService", result.scheduled_methods)
        self.assertIn("scheduled_method", result.scheduled_methods["test.TestService"])

    def test_add_event_listener(self):
        """测试添加事件监听器"""
        result = ScanResult()

        result.add_event_listener("test.TestRepository", "handle_event")
        self.assertEqual(len(result.event_listeners), 1)
        self.assertIn("test.TestRepository", result.event_listeners)
        self.assertIn("handle_event", result.event_listeners["test.TestRepository"])

    def test_add_error(self):
        """测试添加扫描错误"""
        result = ScanResult()

        result.add_error("测试错误")
        self.assertEqual(len(result.scan_errors), 1)
        self.assertIn("测试错误", result.scan_errors)

    def test_get_total_components(self):
        """测试获取组件总数"""
        result = ScanResult()

        result.add_component("test.TestComponent", TestComponent)
        result.add_configuration("test.TestConfiguration", TestConfiguration)

        self.assertEqual(result.get_total_components(), 2)

    def test_get_total_methods(self):
        """测试获取方法总数"""
        result = ScanResult()

        result.add_bean_method("test.bean", TestConfiguration.test_bean)
        result.add_autowired_method("test.TestComponent", "set_dependency")
        result.add_lifecycle_method("test.TestComponent", "post_construct", "init")
        result.add_async_method("test.TestService", "async_method")
        result.add_scheduled_method("test.TestService", "scheduled_method")
        result.add_event_listener("test.TestRepository", "handle_event")

        # 总共6个方法
        self.assertEqual(result.get_total_methods(), 6)


class TestComponentScanner(unittest.TestCase):
    """组件扫描器测试类"""

    def test_component_scanner_creation(self):
        """测试组件扫描器创建"""
        scanner = ComponentScanner()

        self.assertIsNotNone(scanner.scan_filter)
        self.assertTrue(scanner.cache_enabled)
        self.assertEqual(len(scanner._scan_cache), 0)

    def test_set_filter(self):
        """测试设置扫描过滤器"""
        scanner = ComponentScanner()
        filter = ScanFilter()

        result = scanner.set_filter(filter)
        self.assertEqual(scanner.scan_filter, filter)
        self.assertEqual(result, scanner)  # 支持链式调用

    def test_enable_cache(self):
        """测试启用/禁用缓存"""
        scanner = ComponentScanner()

        # 禁用缓存
        result = scanner.enable_cache(False)
        self.assertFalse(scanner.cache_enabled)
        self.assertEqual(result, scanner)  # 支持链式调用

        # 启用缓存
        scanner.enable_cache(True)
        self.assertTrue(scanner.cache_enabled)

    def test_clear_cache(self):
        """测试清空缓存"""
        scanner = ComponentScanner()
        scanner._scan_cache["test"] = ScanResult()

        result = scanner.clear_cache()
        self.assertEqual(len(scanner._scan_cache), 0)
        self.assertEqual(result, scanner)  # 支持链式调用

    def test_get_class_annotations(self):
        """测试获取类注解"""
        scanner = ComponentScanner()

        # 测试组件类
        annotations = scanner._get_class_annotations(TestComponent)
        self.assertIn("Component", annotations)

        # 测试服务类
        annotations = scanner._get_class_annotations(TestService)
        self.assertIn("Service", annotations)

        # 测试仓库类
        annotations = scanner._get_class_annotations(TestRepository)
        self.assertIn("Repository", annotations)

        # 测试配置类
        annotations = scanner._get_class_annotations(TestConfiguration)
        self.assertIn("Configuration", annotations)

        # 测试普通类
        annotations = scanner._get_class_annotations(PlainClass)
        self.assertEqual(len(annotations), 0)


class TestUtilityFunctions(unittest.TestCase):
    """工具函数测试类"""

    def test_create_scanner(self):
        """测试创建扫描器"""
        scanner = create_scanner()
        self.assertIsInstance(scanner, ComponentScanner)

    def test_create_filter(self):
        """测试创建过滤器"""
        filter = create_filter()
        self.assertIsInstance(filter, ScanFilter)

    @patch("miniboot.annotations.scanner.ComponentScanner.scan")
    def test_scan_packages(self, mock_scan):
        """测试扫描包函数"""
        mock_result = ScanResult()
        mock_scan.return_value = mock_result

        # 测试不带过滤器
        result = scan_packages("test.package")
        self.assertEqual(result, mock_result)
        mock_scan.assert_called_once_with("test.package")

        # 测试带过滤器
        mock_scan.reset_mock()
        filter = ScanFilter()
        result = scan_packages(["test.package1", "test.package2"], filter)
        self.assertEqual(result, mock_result)
        mock_scan.assert_called_once_with(["test.package1", "test.package2"])


class TestAdvancedScannerFeatures(unittest.TestCase):
    """高级扫描器功能测试类"""

    def setUp(self):
        """测试前准备"""
        self.scanner = ComponentScanner()

    def test_auto_scan_project(self):
        """测试自动扫描整个项目"""

        # 创建一个测试应用类
        @MiniBootApplication
        class TestApp:
            pass

        # 测试自动扫描项目
        result = self.scanner.auto_scan_project(TestApp, include_dependencies=True)

        self.assertIsInstance(result, ScanResult)
        # 应该扫描到一些模块
        self.assertGreaterEqual(len(result.scanned_modules), 0)

    def test_auto_scan_project_without_dependencies(self):
        """测试自动扫描项目不包含依赖"""

        @MiniBootApplication
        class TestApp:
            pass

        result = self.scanner.auto_scan_project(TestApp, include_dependencies=False)

        self.assertIsInstance(result, ScanResult)

    def test_discover_configurations(self):
        """测试发现配置类"""
        configurations = self.scanner.discover_configurations(["tests.unit.annotations"])

        self.assertIsInstance(configurations, list)
        # 可能会发现一些配置类
        self.assertGreaterEqual(len(configurations), 0)

    def test_get_project_root(self):
        """测试获取项目根目录"""
        # 测试完整模块路径
        root = self.scanner._get_project_root("tests.unit.annotations.test_scanner")
        self.assertEqual(root, "tests")

        # 测试单个模块
        root = self.scanner._get_project_root("scanner")
        self.assertEqual(root, "scanner")

    def test_scan_with_component_scan_annotation(self):
        """测试扫描带@ComponentScan注解的类"""

        @ComponentScan(base_packages=["tests.unit.annotations"])
        class ConfigWithScan:
            pass

        result = self.scanner.auto_scan_project(ConfigWithScan)
        self.assertIsInstance(result, ScanResult)

    def test_scan_error_handling(self):
        """测试扫描错误处理"""
        # 测试扫描不存在的包
        result = self.scanner.scan("non.existent.package")
        self.assertIsInstance(result, ScanResult)
        # 应该有错误记录
        self.assertGreaterEqual(len(result.scan_errors), 0)

    def test_scan_with_invalid_filter(self):
        """测试使用无效过滤器扫描"""

        # 创建一个会抛出异常的过滤器
        class BadFilter:
            def should_include_module(self, module_name):  # noqa: ARG002
                raise Exception("Filter error")

            def should_include_class(self, cls):  # noqa: ARG002
                return True

        self.scanner.set_filter(BadFilter())
        result = self.scanner.scan("tests.unit.annotations.test_scanner")

        # 应该处理错误并继续
        self.assertIsInstance(result, ScanResult)

    def test_optimization_settings(self):
        """测试优化设置"""
        # 测试启用优化
        self.scanner.enable_optimization = True
        result = self.scanner.scan("tests.unit.annotations.test_scanner")
        self.assertIsInstance(result, ScanResult)

        # 测试禁用优化
        self.scanner.enable_optimization = False
        result = self.scanner.scan("tests.unit.annotations.test_scanner")
        self.assertIsInstance(result, ScanResult)


if __name__ == "__main__":
    unittest.main()

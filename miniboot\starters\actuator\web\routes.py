#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Actuator 动态路由注册器

实现 Actuator 端点的动态路由注册功能，支持：
- 运行时动态添加、移除和管理端点路由
- 基础路径配置和路径映射
- FastAPI 应用集成
- 路由状态监控和管理
- 条件化路由注册

设计原则：
- 动态性：支持运行时路由的动态管理
- 配置驱动：通过配置控制路由注册行为
- 集成友好：与现有 Actuator 架构无缝集成
- 性能优化：避免重复注册，支持批量操作

使用示例：
    # 基本使用
    registrar = ActuatorRouteRegistrar(app, actuator_context)
    registrar.register_routes()

    # 动态添加端点路由
    registrar.register_endpoint_route("custom", custom_endpoint)

    # 刷新所有路由
    registrar.refresh_routes()

    # 获取路由状态
    status = registrar.get_route_status()
"""

import time
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Set

from loguru import logger

from miniboot.bean.base import InitializingBean

try:
    from fastapi import FastAPI, Request, Response
    from fastapi.routing import APIRoute
    FASTAPI_AVAILABLE = True
except ImportError:
    FastAPI = None
    Request = None
    Response = None
    APIRoute = None
    FASTAPI_AVAILABLE = False


@dataclass
class RouteInfo:
    """路由信息"""

    endpoint_id: str
    path: str
    method: str
    handler: Callable
    full_path: str
    operation_type: str = "READ"
    sensitive: bool = False
    registered_at: float = field(default_factory=time.time)

    def __post_init__(self):
        if self.registered_at == 0:
            self.registered_at = time.time()


@dataclass
class RouteRegistrationStats:
    """路由注册统计信息"""

    total_routes: int = 0
    registered_routes: int = 0
    failed_routes: int = 0
    last_registration_time: float = 0
    registration_errors: List[str] = field(default_factory=list)

    def reset(self):
        """重置统计信息"""
        self.total_routes = 0
        self.registered_routes = 0
        self.failed_routes = 0
        self.last_registration_time = 0
        self.registration_errors.clear()


class ActuatorRouteRegistrar(InitializingBean):
    """Actuator 动态路由注册器

    负责管理 Actuator 端点的动态路由注册，包括：
    - 从 ActuatorContext 获取端点信息
    - 动态注册端点路由到 FastAPI 应用
    - 支持基础路径配置和路径映射
    - 提供路由状态监控和管理功能

    实现 InitializingBean 接口，在 Bean 初始化完成后自动注册路由。
    """

    def __init__(self,
                 app: Optional[Any] = None,
                 actuator_context: Optional[Any] = None,
                 base_path: str = "/actuator"):
        """初始化路由注册器

        Args:
            app: FastAPI 应用实例
            actuator_context: Actuator 上下文实例
            base_path: 基础路径，默认为 "/actuator"
        """
        self._app = app
        self._actuator_context = actuator_context
        self._base_path = base_path.rstrip('/')

        # 路由管理
        self._registered_routes: Dict[str, RouteInfo] = {}
        self._route_handlers: Dict[str, Callable] = {}
        self._endpoint_route_mapping: Dict[str, List[str]] = {}

        # 统计信息
        self._stats = RouteRegistrationStats()

        # 配置选项
        self._auto_register = True
        self._enable_security = False
        self._enable_cors = True

        logger.debug(f"ActuatorRouteRegistrar initialized with base_path: {self._base_path}")

    def set_app(self, app: Any) -> None:
        """设置 FastAPI 应用实例

        Args:
            app: FastAPI 应用实例
        """
        if not FASTAPI_AVAILABLE:
            logger.warning("FastAPI is not available, route registration will be skipped")
            return

        self._app = app
        logger.debug("FastAPI application set for route registrar")

    def set_actuator_context(self, actuator_context: Any) -> None:
        """设置 Actuator 上下文

        Args:
            actuator_context: Actuator 上下文实例
        """
        self._actuator_context = actuator_context
        logger.debug("ActuatorContext set for route registrar")

    def set_base_path(self, base_path: str) -> None:
        """设置基础路径

        Args:
            base_path: 新的基础路径
        """
        old_path = self._base_path
        self._base_path = base_path.rstrip('/')
        logger.debug(f"Base path changed from '{old_path}' to '{self._base_path}'")

    def after_properties_set(self) -> None:
        """InitializingBean 接口实现：在属性设置完成后自动注册路由

        这个方法会在 Bean 的所有属性注入完成后自动调用，
        实现了 Spring Boot 风格的自动初始化机制。
        """
        logger.debug("ActuatorRouteRegistrar properties set, starting automatic route registration...")

        # 检查是否具备注册条件
        if not self._validate_dependencies():
            logger.info("Dependencies not ready, skipping automatic route registration")
            return

        # 自动注册路由
        try:
            success = self.register_routes()
            if success:
                logger.info("🚀 Actuator routes registered automatically via InitializingBean")
            else:
                logger.warning("⚠️ Automatic route registration failed, routes may need manual registration")
        except Exception as e:
            logger.error(f"❌ Error during automatic route registration: {e}")

    def register_routes(self) -> bool:
        """注册所有启用的端点路由

        从 ActuatorContext 获取所有启用的端点，并为每个端点注册路由。

        Returns:
            bool: 注册是否成功
        """
        if not self._validate_dependencies():
            return False

        start_time = time.time()
        self._stats.reset()

        try:
            # 获取所有端点
            endpoints = self._get_enabled_endpoints()
            self._stats.total_routes = len(endpoints)

            logger.info(f"Starting route registration for {len(endpoints)} endpoints")

            # 注册每个端点的路由
            for endpoint_id, endpoint in endpoints.items():
                try:
                    if self.register_endpoint_route(endpoint_id, endpoint):
                        self._stats.registered_routes += 1
                    else:
                        self._stats.failed_routes += 1

                except Exception as e:
                    self._stats.failed_routes += 1
                    error_msg = f"Failed to register route for endpoint '{endpoint_id}': {e}"
                    self._stats.registration_errors.append(error_msg)
                    logger.error(error_msg)

            # 更新统计信息
            self._stats.last_registration_time = time.time() - start_time

            logger.info(f"Route registration completed: {self._stats.registered_routes}/{self._stats.total_routes} "
                       f"routes registered in {self._stats.last_registration_time:.3f}s")

            return self._stats.failed_routes == 0

        except Exception as e:
            logger.error(f"Route registration failed: {e}")
            return False

    def register_endpoint_route(self, endpoint_id: str, endpoint: Any) -> bool:
        """注册单个端点的路由

        Args:
            endpoint_id: 端点ID
            endpoint: 端点实例

        Returns:
            bool: 注册是否成功
        """
        if not self._validate_dependencies():
            return False

        try:
            # 获取端点操作
            operations = endpoint.operations()
            if not operations:
                logger.debug(f"Endpoint '{endpoint_id}' has no operations, skipping route registration")
                return True

            registered_routes = []

            # 为每个操作注册路由
            for operation in operations:
                route_path = self._build_route_path(endpoint_id, operation.path)
                full_path = f"{self._base_path}{route_path}"

                # 创建路由处理器
                handler = self._create_route_handler(endpoint, operation)

                # 注册到 FastAPI
                if self._register_to_fastapi(full_path, operation.method, handler):
                    # 创建路由信息
                    route_info = RouteInfo(
                        endpoint_id=endpoint_id,
                        path=route_path,
                        method=operation.method,
                        handler=handler,
                        full_path=full_path,
                        operation_type=operation.operation_type.value,
                        sensitive=endpoint.sensitive
                    )

                    # 保存路由信息
                    route_key = f"{endpoint_id}:{operation.method}:{route_path}"
                    self._registered_routes[route_key] = route_info
                    self._route_handlers[route_key] = handler
                    registered_routes.append(route_key)

                    logger.debug(f"Registered route: {operation.method} {full_path}")
                else:
                    logger.error(f"Failed to register route: {operation.method} {full_path}")
                    return False

            # 更新端点路由映射
            self._endpoint_route_mapping[endpoint_id] = registered_routes

            logger.info(f"Successfully registered {len(registered_routes)} routes for endpoint '{endpoint_id}'")
            return True

        except Exception as e:
            logger.error(f"Failed to register routes for endpoint '{endpoint_id}': {e}")
            return False

    def unregister_endpoint_routes(self, endpoint_id: str) -> bool:
        """注销端点的所有路由

        Args:
            endpoint_id: 端点ID

        Returns:
            bool: 注销是否成功
        """
        try:
            route_keys = self._endpoint_route_mapping.get(endpoint_id, [])
            if not route_keys:
                logger.debug(f"No routes found for endpoint '{endpoint_id}'")
                return True

            # 移除路由信息
            for route_key in route_keys:
                self._registered_routes.pop(route_key, None)
                self._route_handlers.pop(route_key, None)

            # 移除端点映射
            self._endpoint_route_mapping.pop(endpoint_id, None)

            logger.info(f"Unregistered {len(route_keys)} routes for endpoint '{endpoint_id}'")
            return True

        except Exception as e:
            logger.error(f"Failed to unregister routes for endpoint '{endpoint_id}': {e}")
            return False

    def refresh_routes(self) -> bool:
        """刷新所有路由

        重新注册所有端点的路由，用于配置更新后的路由刷新。

        Returns:
            bool: 刷新是否成功
        """
        logger.info("Refreshing all Actuator routes")

        # 清除现有路由信息
        self._registered_routes.clear()
        self._route_handlers.clear()
        self._endpoint_route_mapping.clear()

        # 重新注册路由
        return self.register_routes()

    def get_registered_routes(self) -> Dict[str, RouteInfo]:
        """获取已注册的路由信息

        Returns:
            Dict[str, RouteInfo]: 路由信息字典
        """
        return self._registered_routes.copy()

    def get_endpoint_routes(self, endpoint_id: str) -> List[RouteInfo]:
        """获取指定端点的路由信息

        Args:
            endpoint_id: 端点ID

        Returns:
            List[RouteInfo]: 路由信息列表
        """
        route_keys = self._endpoint_route_mapping.get(endpoint_id, [])
        return [self._registered_routes[key] for key in route_keys if key in self._registered_routes]

    def get_route_status(self) -> Dict[str, Any]:
        """获取路由状态信息

        Returns:
            Dict[str, Any]: 路由状态信息
        """
        return {
            'base_path': self._base_path,
            'total_endpoints': len(self._endpoint_route_mapping),
            'total_routes': len(self._registered_routes),
            'registration_stats': {
                'total_routes': self._stats.total_routes,
                'registered_routes': self._stats.registered_routes,
                'failed_routes': self._stats.failed_routes,
                'last_registration_time': self._stats.last_registration_time,
                'error_count': len(self._stats.registration_errors)
            },
            'endpoints': list(self._endpoint_route_mapping.keys()),
            'fastapi_available': FASTAPI_AVAILABLE,
            'app_configured': self._app is not None,
            'context_configured': self._actuator_context is not None,
            'timestamp': time.time()
        }

    def _validate_dependencies(self) -> bool:
        """验证依赖项是否可用"""
        if not FASTAPI_AVAILABLE:
            logger.warning("FastAPI is not available, cannot register routes")
            return False

        if not self._app:
            logger.warning("FastAPI application is not set, cannot register routes")
            return False

        if not self._actuator_context:
            logger.warning("ActuatorContext is not set, cannot register routes")
            return False

        return True

    def _get_enabled_endpoints(self) -> Dict[str, Any]:
        """获取所有启用的端点"""
        try:
            all_endpoints = self._actuator_context.get_endpoints()
            enabled_endpoints = {
                endpoint_id: endpoint
                for endpoint_id, endpoint in all_endpoints.items()
                if endpoint.enabled
            }

            logger.debug(f"Found {len(enabled_endpoints)} enabled endpoints out of {len(all_endpoints)} total")
            return enabled_endpoints

        except Exception as e:
            logger.error(f"Failed to get enabled endpoints: {e}")
            return {}

    def _build_route_path(self, endpoint_id: str, operation_path: str = "") -> str:
        """构建路由路径"""
        if operation_path:
            if operation_path.startswith('/'):
                return f"/{endpoint_id}{operation_path}"
            else:
                return f"/{endpoint_id}/{operation_path}"
        else:
            return f"/{endpoint_id}"

    def _create_route_handler(self, endpoint: Any, operation: Any) -> Callable:
        """创建路由处理器"""
        async def route_handler(request: Request = None):
            """动态路由处理器"""
            try:
                # 调用端点操作
                result = endpoint.invoke(operation.operation_type)

                # 如果结果是协程，等待执行
                if hasattr(result, '__await__'):
                    result = await result

                return result

            except Exception as e:
                logger.error(f"Error in route handler for {endpoint.id}: {e}")
                return {"error": str(e), "endpoint": endpoint.id}

        return route_handler

    def _register_to_fastapi(self, path: str, method: str, handler: Callable) -> bool:
        """注册路由到 FastAPI 应用"""
        try:
            # 获取 FastAPI 方法
            method_lower = method.lower()
            if not hasattr(self._app, method_lower):
                logger.error(f"Unsupported HTTP method: {method}")
                return False

            # 注册路由
            route_func = getattr(self._app, method_lower)
            route_func(path)(handler)

            return True

        except Exception as e:
            logger.error(f"Failed to register route {method} {path}: {e}")
            return False

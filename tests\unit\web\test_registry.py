#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
控制器注册和路由管理测试案例

测试ControllerRegistry的控制器扫描、注册、路由解析、
FastAPI集成等功能的完整测试案例.

主要测试内容:
- 控制器注册基本功能测试
- 路由信息提取和解析测试
- FastAPI集成和路由应用测试
- 智能模式和传统模式测试
- 性能分析和优化测试
- 异常处理和错误恢复测试
"""

import asyncio
import time
import unittest
from unittest.mock import AsyncMock, Mock, patch
from typing import Any, Dict, List

from fastapi import FastAPI

from miniboot.web.registry import ControllerRegistry, ControllerInfo, RouteInfo
from miniboot.annotations.web import Controller, RestController, GetMapping, PostMapping


class ControllerRegistryBasicTestCase(unittest.TestCase):
    """控制器注册器基本功能测试类"""

    def setUp(self):
        """测试前置设置"""
        self.registry = ControllerRegistry()

    def test_controller_registry_initialization_traditional_mode(self):
        """测试传统模式下控制器注册器初始化"""
        # Arrange & Act
        registry = ControllerRegistry()

        # Assert
        self.assertIsInstance(registry.controllers, dict)
        self.assertIsInstance(registry.routes, list)
        self.assertEqual(len(registry.controllers), 0)
        self.assertEqual(len(registry.routes), 0)
        self.assertFalse(registry._auto_mode)
        self.assertIsNone(registry.app)

    def test_controller_registry_initialization_auto_mode(self):
        """测试智能模式下控制器注册器初始化"""
        # Arrange
        mock_scheduler = Mock()

        # Act
        registry = ControllerRegistry(smart_scheduler=mock_scheduler)

        # Assert
        self.assertTrue(registry._auto_mode)
        self.assertIs(registry.smart_scheduler, mock_scheduler)
        self.assertIsInstance(registry.controllers, dict)
        self.assertIsInstance(registry.routes, list)

    def test_set_fastapi_app(self):
        """测试设置FastAPI应用"""
        # Arrange
        registry = ControllerRegistry()
        fastapi_app = FastAPI()

        # Act
        registry.set_app(fastapi_app)

        # Assert
        self.assertIs(registry.app, fastapi_app)

    def test_get_controllers_empty(self):
        """测试获取空控制器列表"""
        # Arrange
        registry = ControllerRegistry()

        # Act
        controllers = registry.get_controllers()

        # Assert
        self.assertIsInstance(controllers, dict)
        self.assertEqual(len(controllers), 0)

    def test_get_routes_empty(self):
        """测试获取空路由列表"""
        # Arrange
        registry = ControllerRegistry()

        # Act
        routes = registry.get_routes()

        # Assert
        self.assertIsInstance(routes, list)
        self.assertEqual(len(routes), 0)

    def test_controller_not_exists(self):
        """测试控制器不存在检查"""
        # Arrange
        registry = ControllerRegistry()

        # Act
        controller_info = registry.get_controller("NonExistentController")

        # Assert
        self.assertIsNone(controller_info)


class ControllerRegistryRegistrationTestCase(unittest.TestCase):
    """控制器注册功能测试类"""

    def setUp(self):
        """测试前置设置"""
        self.registry = ControllerRegistry()
        self.fastapi_app = FastAPI()
        self.registry.set_app(self.fastapi_app)

    def test_register_basic_controller_success(self):
        """测试注册基本控制器成功"""
        # Arrange
        @Controller("/api/test")
        class TestController:
            @GetMapping("/hello")
            def hello(self):
                return {"message": "Hello, World!"}

        controller_instance = TestController()

        # Act
        success = self.registry.register(controller_instance, "TestController")

        # Assert
        self.assertTrue(success)
        self.assertIsNotNone(self.registry.get_controller("TestController"))

        controllers = self.registry.get_controllers()
        self.assertIn("TestController", controllers)

        controller_info = controllers["TestController"]
        self.assertIsInstance(controller_info, ControllerInfo)
        self.assertEqual(controller_info.name, "TestController")
        self.assertEqual(controller_info.path, "/api/test")
        self.assertFalse(controller_info.is_rest)

    def test_register_rest_controller_success(self):
        """测试注册REST控制器成功"""
        # Arrange
        @RestController("/api/users")
        class UserController:
            @GetMapping("")
            def list_users(self):
                return {"users": []}

            @PostMapping("")
            def create_user(self, user_data: dict):
                return {"created": True}

        controller_instance = UserController()

        # Act
        success = self.registry.register(controller_instance, "UserController")

        # Assert
        self.assertTrue(success)

        controllers = self.registry.get_controllers()
        controller_info = controllers["UserController"]
        self.assertTrue(controller_info.is_rest)
        self.assertEqual(controller_info.path, "/api/users")
        self.assertEqual(len(controller_info.methods), 2)

    def test_register_controller_without_name(self):
        """测试注册控制器不指定名称"""
        # Arrange
        @Controller("/api/auto")
        class AutoNameController:
            @GetMapping("/test")
            def test_method(self):
                pass

        controller_instance = AutoNameController()

        # Act
        success = self.registry.register(controller_instance, "TestController")

        # Assert
        self.assertTrue(success)
        self.assertTrue(self.registry.controller_exists("AutoNameController"))

    def test_register_duplicate_controller_failure(self):
        """测试注册重复控制器失败"""
        # Arrange
        @Controller("/api/duplicate")
        class DuplicateController:
            pass

        controller_instance1 = DuplicateController()
        controller_instance2 = DuplicateController()

        # Act
        success1 = self.registry.register(controller_instance1, "DuplicateController")
        success2 = self.registry.register(controller_instance2, "DuplicateController")

        # Assert
        self.assertTrue(success1)
        self.assertFalse(success2)  # 第二次注册应该失败

    def test_register_non_controller_class_failure(self):
        """测试注册非控制器类失败"""
        # Arrange
        class RegularClass:
            def regular_method(self):
                pass

        instance = RegularClass()

        # Act
        success = self.registry.register(instance, "RegularClass")

        # Assert
        self.assertFalse(success)
        self.assertIsNone(self.registry.get_controller("RegularClass"))

    def test_route_extraction_from_controller(self):
        """测试从控制器提取路由信息"""
        # Arrange
        @RestController("/api/products")
        class ProductController:
            @GetMapping("")
            def list_products(self):
                return {"products": []}

            @GetMapping("/{product_id}")
            def get_product(self, product_id: int):
                return {"product_id": product_id}

            @PostMapping("", consumes="application/json")
            def create_product(self, product_data: dict):
                return {"created": True}

        controller_instance = ProductController()

        # Act
        success = self.registry.register(controller_instance, "ProductController")

        # Assert
        self.assertTrue(success)

        routes = self.registry.get_routes()
        self.assertEqual(len(routes), 3)

        # 验证路由信息
        route_paths = [route.full_path for route in routes]
        # 路径可能包含尾部斜杠，检查是否包含基本路径
        self.assertTrue(any("/api/products" in path for path in route_paths))
        self.assertIn("/api/products/{product_id}", route_paths)

        # 验证HTTP方法
        route_methods = [route.http_method for route in routes]
        self.assertEqual(route_methods.count("GET"), 2)
        self.assertEqual(route_methods.count("POST"), 1)


class ControllerRegistryAsyncTestCase(unittest.IsolatedAsyncioTestCase):
    """控制器注册器异步功能测试类"""

    async def test_register_async_controller_success(self):
        """测试异步注册控制器成功"""
        # Arrange
        mock_scheduler = Mock()
        registry = ControllerRegistry(smart_scheduler=mock_scheduler)

        @Controller("/api/async")
        class AsyncController:
            @GetMapping("/test")
            async def async_test(self):
                return {"async": True}

        controller_instance = AsyncController()

        # Act
        success = await registry.register_async(controller_instance, "AsyncController")

        # Assert
        self.assertTrue(success)
        self.assertIsNotNone(registry.get_controller("AsyncController"))

    async def test_apply_to_app_traditional_mode(self):
        """测试传统模式下应用到FastAPI"""
        # Arrange
        registry = ControllerRegistry()
        fastapi_app = FastAPI()

        @Controller("/api/traditional")
        class TraditionalController:
            @GetMapping("/test")
            def test_method(self):
                return {"mode": "traditional"}

        controller_instance = TraditionalController()
        registry.register(controller_instance, "TraditionalController")

        # Act
        await registry.apply_to_app(fastapi_app)

        # Assert
        self.assertIs(registry.app, fastapi_app)

    async def test_apply_to_app_auto_mode(self):
        """测试智能模式下应用到FastAPI"""
        # Arrange
        mock_scheduler = Mock()
        registry = ControllerRegistry(smart_scheduler=mock_scheduler)
        fastapi_app = FastAPI()

        @RestController("/api/auto")
        class AutoController:
            @GetMapping("/test")
            def test_method(self):
                return {"mode": "auto"}

        controller_instance = AutoController()
        await registry.register_async(controller_instance, "AutoController")

        # Mock优化方法
        with patch.object(registry, '_optimize', return_value=registry.routes):
            # Act
            await registry.apply_to_app(fastapi_app)

            # Assert
            self.assertIs(registry.app, fastapi_app)


class ControllerRegistryPerformanceTestCase(unittest.TestCase):
    """控制器注册器性能测试类"""

    def test_controller_registration_performance(self):
        """测试控制器注册性能"""
        # Arrange
        registry = ControllerRegistry()
        fastapi_app = FastAPI()
        registry.set_app(fastapi_app)

        @Controller("/api/perf")
        class PerfController:
            @GetMapping("/test1")
            def test1(self):
                pass

            @GetMapping("/test2")
            def test2(self):
                pass

            @PostMapping("/test3")
            def test3(self):
                pass

        controller_instance = PerfController()

        # Act
        start_time = time.time()
        success = registry.register(controller_instance, "PerfController")
        end_time = time.time()

        # Assert
        self.assertTrue(success)
        registration_time = end_time - start_time
        self.assertLess(registration_time, 0.1)  # 注册时间应小于100ms

    def test_multiple_controllers_registration_performance(self):
        """测试多个控制器注册性能"""
        # Arrange
        registry = ControllerRegistry()
        fastapi_app = FastAPI()
        registry.set_app(fastapi_app)

        controllers = []
        for i in range(10):
            @Controller(f"/api/controller{i}")
            class DynamicController:
                @GetMapping("/test")
                def test_method(self):
                    return {"controller": i}

            controllers.append((DynamicController(), f"Controller{i}"))

        # Act
        start_time = time.time()
        for controller_instance, name in controllers:
            registry.register(controller_instance, name)
        end_time = time.time()

        # Assert
        total_time = end_time - start_time
        self.assertLess(total_time, 1.0)  # 10个控制器注册时间应小于1秒
        self.assertEqual(len(registry.get_controllers()), 10)

    def test_route_extraction_performance(self):
        """测试路由提取性能"""
        # Arrange
        registry = ControllerRegistry()

        @RestController("/api/routes")
        class RouteHeavyController:
            @GetMapping("/route1")
            def route1(self): pass

            @GetMapping("/route2")
            def route2(self): pass

            @PostMapping("/route3")
            def route3(self): pass

            @GetMapping("/route4/{id}")
            def route4(self, id: int): pass

            @PostMapping("/route5/{id}")
            def route5(self, id: int): pass

        controller_instance = RouteHeavyController()

        # Act
        start_time = time.time()
        success = registry.register(controller_instance, "RouteHeavyController")
        end_time = time.time()

        # Assert
        self.assertTrue(success)
        extraction_time = end_time - start_time
        self.assertLess(extraction_time, 0.05)  # 路由提取时间应小于50ms
        self.assertEqual(len(registry.get_routes()), 5)


class ControllerRegistryErrorHandlingTestCase(unittest.TestCase):
    """控制器注册器错误处理测试类"""

    def test_register_with_invalid_controller_instance(self):
        """测试注册无效控制器实例"""
        # Arrange
        registry = ControllerRegistry()

        # Act & Assert
        success = registry.register(None, "NullController")
        self.assertFalse(success)

        success = registry.register("not_an_instance", "StringController")
        self.assertFalse(success)

    def test_register_without_fastapi_app(self):
        """测试没有FastAPI应用时的注册"""
        # Arrange
        registry = ControllerRegistry()

        @Controller("/api/no-app")
        class NoAppController:
            @GetMapping("/test")
            def test_method(self):
                pass

        controller_instance = NoAppController()

        # Act
        success = registry.register(controller_instance, "NoAppController")

        # Assert
        # 即使没有FastAPI应用，注册也应该成功，只是不会应用路由
        self.assertTrue(success)
        self.assertIsNotNone(registry.get_controller("NoAppController"))


if __name__ == "__main__":
    unittest.main(verbosity=2)

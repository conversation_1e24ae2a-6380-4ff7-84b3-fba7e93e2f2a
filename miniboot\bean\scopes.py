#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean作用域管理系统
"""

import threading
import time
import weakref
from abc import ABC, abstractmethod
from collections import defaultdict
from contextlib import contextmanager
from typing import Any, Callable, Dict, List, Optional, Set

from ..utils.singleton import SingletonMeta
from .definition import BeanScope


class ScopeContext:
    """作用域上下文

    用于存储特定作用域的Bean实例和相关信息。
    支持Bean的创建、缓存、销毁和生命周期管理。
    """

    def __init__(self, scope_id: str, scope_type: BeanScope):
        """初始化作用域上下文

        Args:
            scope_id: 作用域标识符
            scope_type: 作用域类型
        """
        self.scope_id = scope_id
        self.scope_type = scope_type
        self.beans: Dict[str, Any] = {}
        self.destruction_callbacks: Dict[str, Callable[[], None]] = {}
        self.creation_time = time.time()
        self.last_access_time = time.time()
        self.access_count = 0
        self._lock = threading.RLock()

        # 统计信息
        self._stats = {
            'beans_created': 0,
            'beans_destroyed': 0,
            'total_accesses': 0
        }

    def get_bean(self, name: str) -> Optional[Any]:
        """获取作用域内的Bean

        Args:
            name: Bean名称

        Returns:
            Bean实例，如果不存在则返回None
        """
        with self._lock:
            self.last_access_time = time.time()
            self.access_count += 1
            self._stats['total_accesses'] += 1
            return self.beans.get(name)

    def put_bean(self, name: str, bean: Any, destruction_callback: Optional[Callable[[], None]] = None) -> None:
        """存储Bean到作用域

        Args:
            name: Bean名称
            bean: Bean实例
            destruction_callback: 销毁回调函数
        """
        with self._lock:
            self.beans[name] = bean
            if destruction_callback:
                self.destruction_callbacks[name] = destruction_callback
            self._stats['beans_created'] += 1

    def remove_bean(self, name: str) -> Optional[Any]:
        """从作用域中移除Bean

        Args:
            name: Bean名称

        Returns:
            被移除的Bean实例
        """
        with self._lock:
            bean = self.beans.pop(name, None)
            callback = self.destruction_callbacks.pop(name, None)

            if callback:
                try:
                    callback()
                except Exception as e:
                    # 记录错误但不抛出异常
                    pass

            if bean:
                self._stats['beans_destroyed'] += 1

            return bean

    def contains_bean(self, name: str) -> bool:
        """检查是否包含指定Bean

        Args:
            name: Bean名称

        Returns:
            bool: 如果包含返回True，否则返回False
        """
        with self._lock:
            return name in self.beans

    def get_bean_names(self) -> List[str]:
        """获取所有Bean名称

        Returns:
            List[str]: Bean名称列表
        """
        with self._lock:
            return list(self.beans.keys())

    def get_bean_count(self) -> int:
        """获取Bean数量

        Returns:
            int: Bean数量
        """
        with self._lock:
            return len(self.beans)

    def destroy(self) -> None:
        """销毁作用域上下文

        调用所有销毁回调并清理资源。
        """
        with self._lock:
            # 执行销毁回调
            for name, callback in self.destruction_callbacks.items():
                try:
                    callback()
                except Exception as e:
                    # 记录错误但继续销毁其他Bean
                    pass

            # 清理资源
            self.beans.clear()
            self.destruction_callbacks.clear()

    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            return {
                'scope_id': self.scope_id,
                'scope_type': self.scope_type.value,
                'creation_time': self.creation_time,
                'last_access_time': self.last_access_time,
                'access_count': self.access_count,
                'bean_count': len(self.beans),
                'stats': self._stats.copy()
            }

    def is_expired(self, timeout_seconds: float) -> bool:
        """检查上下文是否已过期

        Args:
            timeout_seconds: 超时时间（秒）

        Returns:
            bool: 如果已过期返回True，否则返回False
        """
        return time.time() - self.last_access_time > timeout_seconds

    def __str__(self) -> str:
        """字符串表示"""
        return f"ScopeContext(id='{self.scope_id}', type={self.scope_type}, beans={len(self.beans)})"

    def __repr__(self) -> str:
        """详细字符串表示"""
        return (
            f"ScopeContext(scope_id='{self.scope_id}', scope_type={self.scope_type}, "
            f"bean_count={len(self.beans)}, access_count={self.access_count})"
        )


class ScopeManager(ABC):
    """作用域管理器抽象基类

    定义作用域管理器的标准接口，用于管理特定作用域的Bean实例。
    不同的作用域类型需要实现不同的管理策略。
    """

    @abstractmethod
    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[ScopeContext]:
        """获取作用域上下文

        Args:
            scope_id: 作用域标识符，如果为None则使用当前上下文

        Returns:
            Optional[ScopeContext]: 作用域上下文，如果不存在则返回None
        """
        pass

    @abstractmethod
    def create_scope_context(self, scope_id: str) -> ScopeContext:
        """创建作用域上下文

        Args:
            scope_id: 作用域标识符

        Returns:
            ScopeContext: 新创建的作用域上下文
        """
        pass

    @abstractmethod
    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁作用域上下文

        Args:
            scope_id: 作用域标识符
        """
        pass

    @abstractmethod
    def get_scope_type(self) -> BeanScope:
        """获取作用域类型

        Returns:
            BeanScope: 作用域类型
        """
        pass

    def cleanup_expired_contexts(self, timeout_seconds: float = 3600) -> int:
        """清理过期的作用域上下文

        Args:
            timeout_seconds: 超时时间（秒），默认1小时

        Returns:
            int: 清理的上下文数量
        """
        return 0


class RequestScopeManager(ScopeManager):
    """请求作用域管理器

    管理HTTP请求作用域的Bean实例。
    每个HTTP请求都有独立的作用域上下文。
    """

    def __init__(self):
        """初始化请求作用域管理器"""
        self._contexts: Dict[str, ScopeContext] = {}
        self._thread_local = threading.local()
        self._lock = threading.RLock()

    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[ScopeContext]:
        """获取请求作用域上下文

        Args:
            scope_id: 请求标识符，如果为None则使用线程本地存储

        Returns:
            Optional[ScopeContext]: 请求作用域上下文
        """
        if scope_id:
            with self._lock:
                return self._contexts.get(scope_id)
        else:
            # 使用线程本地存储
            return getattr(self._thread_local, 'context', None)

    def create_scope_context(self, scope_id: str) -> ScopeContext:
        """创建请求作用域上下文

        Args:
            scope_id: 请求标识符

        Returns:
            ScopeContext: 新创建的请求作用域上下文
        """
        context = ScopeContext(scope_id, BeanScope.REQUEST)

        with self._lock:
            self._contexts[scope_id] = context

        # 设置到线程本地存储
        self._thread_local.context = context

        return context

    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁请求作用域上下文

        Args:
            scope_id: 请求标识符
        """
        with self._lock:
            context = self._contexts.pop(scope_id, None)
            if context:
                context.destroy()

        # 清理线程本地存储
        if hasattr(self._thread_local, 'context') and self._thread_local.context.scope_id == scope_id:
            delattr(self._thread_local, 'context')

    def get_scope_type(self) -> BeanScope:
        """获取作用域类型

        Returns:
            BeanScope: 请求作用域类型
        """
        return BeanScope.REQUEST

    def cleanup_expired_contexts(self, timeout_seconds: float = 3600) -> int:
        """清理过期的请求上下文

        Args:
            timeout_seconds: 超时时间（秒），默认1小时

        Returns:
            int: 清理的上下文数量
        """
        expired_ids = []

        with self._lock:
            for scope_id, context in self._contexts.items():
                if context.is_expired(timeout_seconds):
                    expired_ids.append(scope_id)

        for scope_id in expired_ids:
            self.destroy_scope_context(scope_id)

        return len(expired_ids)

    @contextmanager
    def request_scope(self, request_id: str):
        """请求作用域上下文管理器

        Args:
            request_id: 请求标识符
        """
        context = self.create_scope_context(request_id)
        try:
            yield context
        finally:
            self.destroy_scope_context(request_id)


class SessionScopeManager(ScopeManager):
    """会话作用域管理器

    管理HTTP会话作用域的Bean实例。
    每个HTTP会话都有独立的作用域上下文。
    """

    def __init__(self):
        """初始化会话作用域管理器"""
        self._contexts: Dict[str, ScopeContext] = {}
        self._lock = threading.RLock()

    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[ScopeContext]:
        """获取会话作用域上下文

        Args:
            scope_id: 会话标识符

        Returns:
            Optional[ScopeContext]: 会话作用域上下文
        """
        if not scope_id:
            return None

        with self._lock:
            return self._contexts.get(scope_id)

    def create_scope_context(self, scope_id: str) -> ScopeContext:
        """创建会话作用域上下文

        Args:
            scope_id: 会话标识符

        Returns:
            ScopeContext: 新创建的会话作用域上下文
        """
        context = ScopeContext(scope_id, BeanScope.SESSION)

        with self._lock:
            self._contexts[scope_id] = context

        return context

    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁会话作用域上下文

        Args:
            scope_id: 会话标识符
        """
        with self._lock:
            context = self._contexts.pop(scope_id, None)
            if context:
                context.destroy()

    def get_scope_type(self) -> BeanScope:
        """获取作用域类型

        Returns:
            BeanScope: 会话作用域类型
        """
        return BeanScope.SESSION

    def cleanup_expired_contexts(self, timeout_seconds: float = 7200) -> int:
        """清理过期的会话上下文

        Args:
            timeout_seconds: 超时时间（秒），默认2小时

        Returns:
            int: 清理的上下文数量
        """
        expired_ids = []

        with self._lock:
            for scope_id, context in self._contexts.items():
                if context.is_expired(timeout_seconds):
                    expired_ids.append(scope_id)

        for scope_id in expired_ids:
            self.destroy_scope_context(scope_id)

        return len(expired_ids)

    @contextmanager
    def session_scope(self, session_id: str):
        """会话作用域上下文管理器

        Args:
            session_id: 会话标识符
        """
        context = self.create_scope_context(session_id)
        try:
            yield context
        finally:
            # 会话作用域不会在上下文结束时自动销毁
            pass


class ApplicationScopeManager(ScopeManager):
    """应用作用域管理器

    管理应用级别作用域的Bean实例。
    整个应用只有一个作用域上下文。
    """

    def __init__(self):
        """初始化应用作用域管理器"""
        self._context: Optional[ScopeContext] = None
        self._lock = threading.RLock()

    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[ScopeContext]:
        """获取应用作用域上下文

        Args:
            scope_id: 忽略，应用作用域只有一个上下文

        Returns:
            Optional[ScopeContext]: 应用作用域上下文
        """
        with self._lock:
            return self._context

    def create_scope_context(self, scope_id: str) -> ScopeContext:
        """创建应用作用域上下文

        Args:
            scope_id: 应用标识符

        Returns:
            ScopeContext: 应用作用域上下文
        """
        with self._lock:
            if self._context is None:
                self._context = ScopeContext(scope_id, BeanScope.APPLICATION)
            return self._context

    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁应用作用域上下文

        Args:
            scope_id: 应用标识符
        """
        with self._lock:
            if self._context and self._context.scope_id == scope_id:
                self._context.destroy()
                self._context = None

    def get_scope_type(self) -> BeanScope:
        """获取作用域类型

        Returns:
            BeanScope: 应用作用域类型
        """
        return BeanScope.APPLICATION


class WebSocketScopeManager(ScopeManager):
    """WebSocket作用域管理器

    管理WebSocket会话作用域的Bean实例。
    每个WebSocket连接都有独立的作用域上下文。
    """

    def __init__(self):
        """初始化WebSocket作用域管理器"""
        self._contexts: Dict[str, ScopeContext] = {}
        self._lock = threading.RLock()

    def get_scope_context(self, scope_id: Optional[str] = None) -> Optional[ScopeContext]:
        """获取WebSocket作用域上下文

        Args:
            scope_id: WebSocket连接标识符

        Returns:
            Optional[ScopeContext]: WebSocket作用域上下文
        """
        if not scope_id:
            return None

        with self._lock:
            return self._contexts.get(scope_id)

    def create_scope_context(self, scope_id: str) -> ScopeContext:
        """创建WebSocket作用域上下文

        Args:
            scope_id: WebSocket连接标识符

        Returns:
            ScopeContext: 新创建的WebSocket作用域上下文
        """
        context = ScopeContext(scope_id, BeanScope.WEBSOCKET)

        with self._lock:
            self._contexts[scope_id] = context

        return context

    def destroy_scope_context(self, scope_id: str) -> None:
        """销毁WebSocket作用域上下文

        Args:
            scope_id: WebSocket连接标识符
        """
        with self._lock:
            context = self._contexts.pop(scope_id, None)
            if context:
                context.destroy()

    def get_scope_type(self) -> BeanScope:
        """获取作用域类型

        Returns:
            BeanScope: WebSocket作用域类型
        """
        return BeanScope.WEBSOCKET

    def cleanup_expired_contexts(self, timeout_seconds: float = 1800) -> int:
        """清理过期的WebSocket上下文

        Args:
            timeout_seconds: 超时时间（秒），默认30分钟

        Returns:
            int: 清理的上下文数量
        """
        expired_ids = []

        with self._lock:
            for scope_id, context in self._contexts.items():
                if context.is_expired(timeout_seconds):
                    expired_ids.append(scope_id)

        for scope_id in expired_ids:
            self.destroy_scope_context(scope_id)

        return len(expired_ids)

    @contextmanager
    def websocket_scope(self, connection_id: str):
        """WebSocket作用域上下文管理器

        Args:
            connection_id: WebSocket连接标识符
        """
        context = self.create_scope_context(connection_id)
        try:
            yield context
        finally:
            self.destroy_scope_context(connection_id)


class ScopeRegistry(metaclass=SingletonMeta):
    """作用域注册表 - 单例模式

    统一管理所有类型的作用域管理器。
    提供作用域管理器的注册、获取和Bean作用域操作的统一接口。
    使用单例模式确保全局唯一性和配置一致性。
    """

    def __init__(self):
        """初始化作用域注册表"""
        # 单例模式：只在首次创建时初始化
        if not hasattr(self, '_initialized'):
            self._managers: Dict[BeanScope, ScopeManager] = {}
            self._lock = threading.RLock()

            # 注册默认的作用域管理器
            self._register_default_managers()

            # 统计信息
            self._stats = {
                'total_contexts': 0,
                'active_contexts': 0,
                'cleanup_operations': 0
            }
            self._initialized = True

    def cleanup(self) -> None:
        """清理作用域注册表，供单例重置时调用

        这个方法会被SingletonMeta在重置实例时自动调用
        """
        if hasattr(self, '_managers'):
            # 清理所有管理器中的过期上下文
            for manager in self._managers.values():
                if hasattr(manager, 'cleanup_expired_contexts'):
                    try:
                        manager.cleanup_expired_contexts(0)  # 清理所有上下文
                    except Exception:
                        # 忽略清理过程中的异常
                        pass

            # 重新初始化
            self._managers.clear()
            self._register_default_managers()

            # 重置统计信息
            self._stats = {
                'total_contexts': 0,
                'active_contexts': 0,
                'cleanup_operations': 0
            }

    def _register_default_managers(self) -> None:
        """注册默认的作用域管理器"""
        self.register_scope_manager(BeanScope.REQUEST, RequestScopeManager())
        self.register_scope_manager(BeanScope.SESSION, SessionScopeManager())
        self.register_scope_manager(BeanScope.APPLICATION, ApplicationScopeManager())
        self.register_scope_manager(BeanScope.WEBSOCKET, WebSocketScopeManager())

    def register_scope_manager(self, scope_type: BeanScope, manager: ScopeManager) -> None:
        """注册作用域管理器

        Args:
            scope_type: 作用域类型
            manager: 作用域管理器
        """
        with self._lock:
            self._managers[scope_type] = manager

    def get_scope_manager(self, scope_type: BeanScope) -> Optional[ScopeManager]:
        """获取作用域管理器

        Args:
            scope_type: 作用域类型

        Returns:
            Optional[ScopeManager]: 作用域管理器，如果不存在则返回None
        """
        with self._lock:
            return self._managers.get(scope_type)

    def get_bean(self, scope_type: BeanScope, bean_name: str, scope_id: Optional[str] = None) -> Optional[Any]:
        """从指定作用域获取Bean

        Args:
            scope_type: 作用域类型
            bean_name: Bean名称
            scope_id: 作用域标识符

        Returns:
            Optional[Any]: Bean实例，如果不存在则返回None
        """
        manager = self.get_scope_manager(scope_type)
        if not manager:
            return None

        context = manager.get_scope_context(scope_id)
        if not context:
            return None

        return context.get_bean(bean_name)

    def put_bean(self, scope_type: BeanScope, bean_name: str, bean: Any,
                scope_id: Optional[str] = None, destruction_callback: Optional[Callable[[], None]] = None) -> bool:
        """将Bean存储到指定作用域

        Args:
            scope_type: 作用域类型
            bean_name: Bean名称
            bean: Bean实例
            scope_id: 作用域标识符
            destruction_callback: 销毁回调函数

        Returns:
            bool: 如果成功存储返回True，否则返回False
        """
        manager = self.get_scope_manager(scope_type)
        if not manager:
            return False

        context = manager.get_scope_context(scope_id)
        if not context and scope_id:
            context = manager.create_scope_context(scope_id)
            self._stats['total_contexts'] += 1
            self._stats['active_contexts'] += 1

        if context:
            context.put_bean(bean_name, bean, destruction_callback)
            return True

        return False

    def remove_bean(self, scope_type: BeanScope, bean_name: str, scope_id: Optional[str] = None) -> Optional[Any]:
        """从指定作用域移除Bean

        Args:
            scope_type: 作用域类型
            bean_name: Bean名称
            scope_id: 作用域标识符

        Returns:
            Optional[Any]: 被移除的Bean实例
        """
        manager = self.get_scope_manager(scope_type)
        if not manager:
            return None

        context = manager.get_scope_context(scope_id)
        if not context:
            return None

        return context.remove_bean(bean_name)

    def destroy_scope(self, scope_type: BeanScope, scope_id: str) -> None:
        """销毁指定作用域

        Args:
            scope_type: 作用域类型
            scope_id: 作用域标识符
        """
        manager = self.get_scope_manager(scope_type)
        if manager:
            manager.destroy_scope_context(scope_id)
            self._stats['active_contexts'] -= 1

    def cleanup_expired_scopes(self, timeout_seconds: float = 3600) -> int:
        """清理所有过期的作用域

        Args:
            timeout_seconds: 超时时间（秒），默认1小时

        Returns:
            int: 清理的作用域数量
        """
        total_cleaned = 0

        with self._lock:
            for manager in self._managers.values():
                cleaned = manager.cleanup_expired_contexts(timeout_seconds)
                total_cleaned += cleaned
                self._stats['active_contexts'] -= cleaned

        self._stats['cleanup_operations'] += 1
        return total_cleaned

    def get_registry_stats(self) -> Dict[str, Any]:
        """获取注册表统计信息

        Returns:
            Dict[str, Any]: 统计信息
        """
        with self._lock:
            manager_stats = {}
            for scope_type, manager in self._managers.items():
                if hasattr(manager, 'get_stats'):
                    manager_stats[scope_type.value] = manager.get_stats()

            return {
                'registered_scopes': list(self._managers.keys()),
                'manager_count': len(self._managers),
                'stats': self._stats.copy(),
                'manager_stats': manager_stats
            }

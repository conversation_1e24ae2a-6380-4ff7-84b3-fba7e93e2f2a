"""
诊断端点

提供运行时诊断信息和配置状态查看:
- 应用配置信息
- 运行时状态
- 组件状态
- 性能统计
- 环境信息
- 日志级别控制
"""

import os
import sys
import time
import platform
import psutil
from typing import Dict, List, Optional, Any
from dataclasses import dataclass
import asyncio

from loguru import logger


@dataclass
class DiagnosticInfo:
    """诊断信息"""

    category: str
    name: str
    value: Any
    description: str
    timestamp: float


class DiagEndpoint:
    """诊断端点"""

    def __init__(self, app_context=None, web_metrics=None, backpressure_metrics=None, scheduling_metrics=None, health_indicator=None):
        """
        初始化诊断端点

        Args:
            app_context: 应用上下文
            web_metrics: Web 指标收集器
            backpressure_metrics: 背压指标收集器
            scheduling_metrics: 调度指标收集器
            health_indicator: 健康指示器
        """
        self.app_context = app_context
        self.web_metrics = web_metrics
        self.backpressure_metrics = backpressure_metrics
        self.scheduling_metrics = scheduling_metrics
        self.health_indicator = health_indicator

        self.start_time = time.time()

        logger.info("DiagnosticEndpoint initialized")

    def get_application_info(self) -> Dict[str, Any]:
        """获取应用信息"""
        uptime = time.time() - self.start_time

        return {
            "name": "Mini-Boot Web Application",
            "version": "1.0.0",  # 可以从配置获取
            "start_time": self.start_time,
            "uptime": uptime,
            "uptime_formatted": self._format_uptime(uptime),
            "environment": os.getenv("ENVIRONMENT", "development"),
            "profile": os.getenv("PROFILE", "default"),
            "pid": os.getpid(),
            "working_directory": os.getcwd(),
        }

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            "platform": {
                "system": platform.system(),
                "release": platform.release(),
                "version": platform.version(),
                "machine": platform.machine(),
                "processor": platform.processor(),
                "architecture": platform.architecture(),
            },
            "python": {
                "version": sys.version,
                "executable": sys.executable,
                "path": sys.path[:5],  # 只显示前5个路径
            },
            "process": {
                "pid": os.getpid(),
                "ppid": os.getppid(),
                "threads": psutil.Process().num_threads(),
                "memory_info": psutil.Process().memory_info()._asdict(),
                "cpu_percent": psutil.Process().cpu_percent(),
                "create_time": psutil.Process().create_time(),
            },
            "system_resources": {
                "cpu_count": psutil.cpu_count(),
                "cpu_percent": psutil.cpu_percent(),
                "memory": psutil.virtual_memory()._asdict(),
                "disk": psutil.disk_usage("/")._asdict(),
                "boot_time": psutil.boot_time(),
            },
        }

    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        # 过滤敏感环境变量
        sensitive_keys = {"password", "secret", "key", "token", "auth"}

        env_vars = {}
        for key, value in os.environ.items():
            if any(sensitive in key.lower() for sensitive in sensitive_keys):
                env_vars[key] = "***HIDDEN***"
            else:
                env_vars[key] = value

        return {
            "environment_variables": env_vars,
            "java_home": os.getenv("JAVA_HOME"),
            "path": os.getenv("PATH", "").split(os.pathsep)[:10],  # 只显示前10个路径
            "user": os.getenv("USER", os.getenv("USERNAME")),
            "home": os.getenv("HOME", os.getenv("USERPROFILE")),
        }

    def get_configuration_info(self) -> Dict[str, Any]:
        """获取配置信息"""
        config_info = {
            "profiles": {"active": os.getenv("PROFILE", "default"), "available": ["default", "dev", "test", "prod"]},
            "logging": {
                "level": "INFO",  # 可以从日志配置获取
                "handlers": ["console", "file"],  # 可以从日志配置获取
            },
        }

        # 如果有应用上下文,获取更多配置信息
        if self.app_context:
            try:
                # 这里可以添加从应用上下文获取配置的逻辑
                config_info["application"] = {"context_type": type(self.app_context).__name__, "initialized": True}
            except Exception as e:
                logger.warning(f"Failed to get application context info: {e}")

        return config_info

    def get_components_status(self) -> Dict[str, Any]:
        """获取组件状态"""
        components = {}

        # Web 指标组件
        if self.web_metrics:
            try:
                summary = self.web_metrics.get_metrics_summary()
                components["web_metrics"] = {
                    "status": "UP",
                    "total_requests": summary.total_requests,
                    "active_connections": summary.active_connections,
                    "avg_response_time": summary.avg_response_time,
                }
            except Exception as e:
                components["web_metrics"] = {"status": "DOWN", "error": str(e)}

        # 背压控制组件
        if self.backpressure_metrics:
            try:
                summary = self.backpressure_metrics.get_metrics_summary()
                components["backpressure"] = {
                    "status": "UP",
                    "stability_score": summary.system_stability_score,
                    "circuit_breakers": summary.circuit_breaker_status,
                    "load_level": summary.avg_load_level,
                }
            except Exception as e:
                components["backpressure"] = {"status": "DOWN", "error": str(e)}

        # 智能调度组件
        if self.scheduling_metrics:
            try:
                summary = self.scheduling_metrics.get_metrics_summary()
                components["scheduling"] = {
                    "status": "UP",
                    "total_tasks": summary.total_tasks,
                    "classification_accuracy": summary.classification_accuracy,
                    "strategy_effectiveness": summary.strategy_effectiveness,
                }
            except Exception as e:
                components["scheduling"] = {"status": "DOWN", "error": str(e)}

        # 健康检查组件
        if self.health_indicator:
            try:
                health_status = self.health_indicator.get_overall_status()
                components["health_indicator"] = {"status": health_status.value, "components_count": len(self.health_indicator.health_checks)}
            except Exception as e:
                components["health_indicator"] = {"status": "DOWN", "error": str(e)}

        return components

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        performance = {}

        # Web 性能
        if self.web_metrics:
            try:
                summary = self.web_metrics.get_metrics_summary()
                performance["web"] = {
                    "requests_per_second": summary.requests_per_second,
                    "avg_response_time": summary.avg_response_time,
                    "p95_response_time": summary.p95_response_time,
                    "error_rate": summary.error_rate,
                }
            except Exception as e:
                performance["web"] = {"error": str(e)}

        # 背压性能
        if self.backpressure_metrics:
            try:
                summary = self.backpressure_metrics.get_metrics_summary()
                performance["backpressure"] = {
                    "protection_effectiveness": summary.protection_effectiveness,
                    "system_stability_score": summary.system_stability_score,
                }
            except Exception as e:
                performance["backpressure"] = {"error": str(e)}

        # 调度性能
        if self.scheduling_metrics:
            try:
                summary = self.scheduling_metrics.get_metrics_summary()
                performance["scheduling"] = {
                    "performance_improvement": summary.performance_improvement,
                    "async_adoption_rate": summary.async_adoption_rate,
                }
            except Exception as e:
                performance["scheduling"] = {"error": str(e)}

        return performance

    def get_runtime_info(self) -> Dict[str, Any]:
        """获取运行时信息"""
        return {
            "threads": {"count": psutil.Process().num_threads(), "active_count": len(psutil.Process().threads())},
            "memory": {
                "heap_size": psutil.Process().memory_info().rss,
                "heap_used": psutil.Process().memory_percent(),
                "gc_info": "Not available in Python",  # Python 的 GC 信息获取比较复杂
            },
            "file_descriptors": {
                "open": psutil.Process().num_fds() if hasattr(psutil.Process(), "num_fds") else "N/A",
                "max": "N/A",  # 需要系统调用获取
            },
        }

    def _format_uptime(self, uptime_seconds: float) -> str:
        """格式化运行时间"""
        days = int(uptime_seconds // 86400)
        hours = int((uptime_seconds % 86400) // 3600)
        minutes = int((uptime_seconds % 3600) // 60)
        seconds = int(uptime_seconds % 60)

        if days > 0:
            return f"{days}d {hours}h {minutes}m {seconds}s"
        elif hours > 0:
            return f"{hours}h {minutes}m {seconds}s"
        elif minutes > 0:
            return f"{minutes}m {seconds}s"
        else:
            return f"{seconds}s"

    async def get_full_diagnostic_info(self) -> Dict[str, Any]:
        """获取完整诊断信息"""
        return {
            "timestamp": time.time(),
            "application": self.get_application_info(),
            "system": self.get_system_info(),
            "environment": self.get_environment_info(),
            "configuration": self.get_configuration_info(),
            "components": self.get_components_status(),
            "performance": self.get_performance_summary(),
            "runtime": self.get_runtime_info(),
        }

    async def get_health_check_info(self) -> Dict[str, Any]:
        """获取健康检查信息"""
        if not self.health_indicator:
            return {"error": "Health indicator not available"}

        try:
            health_results = await self.health_indicator.check_health()
            return self.health_indicator.to_dict()
        except Exception as e:
            return {"error": f"Health check failed: {str(e)}"}

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "diagnostic_endpoint": {
                "status": "UP",
                "components_available": {
                    "web_metrics": self.web_metrics is not None,
                    "backpressure_metrics": self.backpressure_metrics is not None,
                    "scheduling_metrics": self.scheduling_metrics is not None,
                    "health_indicator": self.health_indicator is not None,
                },
                "uptime": time.time() - self.start_time,
            }
        }

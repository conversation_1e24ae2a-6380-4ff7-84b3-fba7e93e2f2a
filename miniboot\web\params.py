#!/usr/bin/env python
"""
智能参数绑定器模块

支持异步参数解析和验证的智能参数处理系统.

主要功能:
- SmartParameterBinder - 智能参数绑定器
- 异步参数解析和验证
- 与智能调度器集成
- 参数处理性能优化
- 复杂参数类型智能处理
"""

import asyncio
import inspect
import json
import time
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
from typing import (Any, Callable, Dict, List, Optional, Type, Union,
                    get_type_hints)

from fastapi import HTTPException, Request
from loguru import logger
from pydantic import BaseModel, ValidationError

from .scheduling.classifier import TaskType
from .scheduling.scheduler import TaskScheduler, TaskRequest


class ParameterComplexity(Enum):
    """参数复杂度级别"""

    SIMPLE = "simple"  # 简单类型 (str, int, float, bool)
    MEDIUM = "medium"  # 中等复杂度 (list, dict, 简单对象)
    COMPLEX = "complex"  # 复杂类型 (嵌套对象, 大型数据结构)
    VERY_COMPLEX = "very_complex"  # 极复杂类型 (深度嵌套, 大量数据)


class ParameterSource(Enum):
    """参数来源"""

    PATH = "path"  # 路径参数
    QUERY = "query"  # 查询参数
    HEADER = "header"  # 请求头
    BODY = "body"  # 请求体
    FORM = "form"  # 表单数据
    FILE = "file"  # 文件上传


@dataclass
class ParameterInfo:
    """参数信息"""

    name: str
    param_type: Type
    source: ParameterSource
    required: bool = True
    default_value: Any = None
    complexity: ParameterComplexity = ParameterComplexity.SIMPLE
    validation_rules: List[Callable] = field(default_factory=list)

    # 性能统计
    parse_count: int = 0
    total_parse_time: float = 0.0
    avg_parse_time: float = 0.0
    validation_failures: int = 0


@dataclass
class ParameterParsingResult:
    """参数解析结果"""

    success: bool
    value: Any = None
    error: Optional[str] = None
    parse_time: float = 0.0
    validation_time: float = 0.0


class ParameterParser:
    """异步参数解析器"""

    def __init__(self, smart_scheduler: Optional[TaskScheduler] = None):
        self.smart_scheduler = smart_scheduler
        self._type_parsers: Dict[Type, Callable] = {}
        self._setup_default_parsers()

        logger.debug("ParameterParser initialized")

    def _setup_default_parsers(self):
        """设置默认解析器"""
        self._type_parsers.update(
            {
                str: self._parse_value,
                int: self._parse_value,
                float: self._parse_value,
                bool: self._parse_value,
                list: self._parse_value,
                dict: self._parse_value,
            }
        )

    async def parse_parameter(self, param_info: ParameterInfo, raw_value: Any, request: Request) -> ParameterParsingResult:
        """解析参数

        Args:
            param_info: 参数信息
            raw_value: 原始值
            request: HTTP请求

        Returns:
            解析结果
        """
        start_time = time.time()

        try:
            # 根据复杂度选择解析策略
            if param_info.complexity in [ParameterComplexity.COMPLEX, ParameterComplexity.VERY_COMPLEX] and self.smart_scheduler:
                # 使用智能调度器处理复杂参数
                result = await self._parse_with_scheduler(param_info, raw_value, request)
            else:
                # 直接解析简单参数
                result = await self._parse_directly(param_info, raw_value)

            parse_time = time.time() - start_time

            # 更新统计
            param_info.parse_count += 1
            param_info.total_parse_time += parse_time
            param_info.avg_parse_time = param_info.total_parse_time / param_info.parse_count

            return ParameterParsingResult(success=result is not None, value=result, parse_time=parse_time)

        except Exception as e:
            parse_time = time.time() - start_time
            logger.error(f"Parameter parsing failed for {param_info.name}: {e}")

            return ParameterParsingResult(success=False, error=str(e), parse_time=parse_time)

    async def _parse_with_scheduler(self, param_info: ParameterInfo, raw_value: Any, request: Request) -> Any:
        """使用调度器解析复杂参数"""
        task_request = TaskRequest(
            task_id=f"parse_{param_info.name}_{id(raw_value)}",
            function=self._parse_directly,
            args=(param_info, raw_value),
            metadata={
                "parameter_name": param_info.name,
                "complexity": param_info.complexity.value,
                "task_type": TaskType.CPU_BOUND.value if param_info.complexity == ParameterComplexity.VERY_COMPLEX else TaskType.LIGHTWEIGHT.value,
            },
        )

        return await self.smart_scheduler.schedule_task(task_request)

    async def _parse_directly(self, param_info: ParameterInfo, raw_value: Any) -> Any:
        """直接解析参数"""
        if raw_value is None:
            if param_info.required:
                raise ValueError(f"Required parameter {param_info.name} is missing")
            return param_info.default_value

        # 获取类型解析器
        parser = self._type_parsers.get(param_info.param_type)
        if parser:
            return await parser(raw_value, param_info)

        # 处理 Pydantic 模型
        if isinstance(param_info.param_type, type) and issubclass(param_info.param_type, BaseModel):
            return await self._parse_pydantic_model(raw_value, param_info.param_type)

        # 默认返回原始值
        return raw_value

    async def _parse_value(self, value: Any, param_info: ParameterInfo) -> Any:
        """通用值解析器"""
        target_type = param_info.param_type

        try:
            if target_type == str:
                return str(value)
            elif target_type == int:
                return int(value)
            elif target_type == float:
                return float(value)
            elif target_type == bool:
                return self._parse_bool_value(value)
            elif target_type == list:
                return self._parse_list_value(value)
            elif target_type == dict:
                return self._parse_dict_value(value)
            else:
                return value
        except (ValueError, TypeError) as e:
            raise ValueError(f"Cannot convert {value} to {target_type.__name__} for parameter {param_info.name}: {e}")

    def _parse_bool_value(self, value: Any) -> bool:
        """解析布尔值"""
        if isinstance(value, bool):
            return value
        if isinstance(value, str):
            return value.lower() in ("true", "1", "yes", "on")
        return bool(value)

    def _parse_list_value(self, value: Any) -> List:
        """解析列表值"""
        if isinstance(value, list):
            return value
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                return value.split(",")
        return [value]

    def _parse_dict_value(self, value: Any) -> Dict:
        """解析字典值"""
        if isinstance(value, dict):
            return value
        if isinstance(value, str):
            try:
                return json.loads(value)
            except json.JSONDecodeError:
                raise ValueError(f"Cannot parse {value} as JSON")
        raise ValueError(f"Cannot convert {type(value)} to dict")

    async def _parse_pydantic_model(self, value: Any, model_class: Type[BaseModel]) -> BaseModel:
        """解析 Pydantic 模型"""
        try:
            if isinstance(value, dict):
                return model_class(**value)
            elif isinstance(value, str):
                data = json.loads(value)
                return model_class(**data)
            else:
                return model_class(value)
        except (ValidationError, json.JSONDecodeError) as e:
            raise ValueError(f"Cannot parse value as {model_class.__name__}: {e}")


class AsyncParameterValidator:
    """异步参数验证器"""

    def __init__(self, smart_scheduler: Optional[TaskScheduler] = None):
        self.smart_scheduler = smart_scheduler
        logger.debug("AsyncParameterValidator initialized")

    async def validate_parameter(self, param_info: ParameterInfo, value: Any) -> ParameterParsingResult:
        """验证参数

        Args:
            param_info: 参数信息
            value: 参数值

        Returns:
            验证结果
        """
        start_time = time.time()

        try:
            # 执行验证规则
            for rule in param_info.validation_rules:
                if asyncio.iscoroutinefunction(rule):
                    await rule(value)
                else:
                    rule(value)

            validation_time = time.time() - start_time

            return ParameterParsingResult(success=True, value=value, validation_time=validation_time)

        except Exception as e:
            validation_time = time.time() - start_time
            param_info.validation_failures += 1

            logger.warning(f"Parameter validation failed for {param_info.name}: {e}")

            return ParameterParsingResult(success=False, error=str(e), validation_time=validation_time)


class ParameterBinder:
    """智能参数绑定器

    支持异步参数解析和验证的智能参数处理系统.
    """

    def __init__(self, smart_scheduler: Optional[TaskScheduler] = None):
        """初始化智能参数绑定器

        Args:
            smart_scheduler: 智能调度器实例
        """
        self.smart_scheduler = smart_scheduler

        # 组件
        self.parser = ParameterParser(smart_scheduler)
        self.validator = AsyncParameterValidator(smart_scheduler)

        # 参数信息缓存
        self._parameter_cache: Dict[str, List[ParameterInfo]] = {}

        # 统计信息
        self._stats = {
            "total_bindings": 0,
            "successful_bindings": 0,
            "failed_bindings": 0,
            "cache_hits": 0,
            "cache_misses": 0,
            "avg_binding_time": 0.0,
        }

        logger.info("ParameterBinder initialized")

    async def bind_parameters(self, function: Callable, request: Request, path_params: Dict[str, Any] = None) -> Dict[str, Any]:
        """绑定函数参数

        Args:
            function: 目标函数
            request: HTTP请求
            path_params: 路径参数

        Returns:
            绑定的参数字典
        """
        start_time = time.time()

        try:
            # 获取函数参数信息
            param_infos = await self._get_parameter_infos(function)

            # 提取请求数据
            request_data = await self._extract_request_data(request, path_params or {})

            # 绑定参数
            bound_params = {}

            for param_info in param_infos:
                # 获取原始值
                raw_value = self._get_raw_value(param_info, request_data)

                # 解析参数
                parse_result = await self.parser.parse_parameter(param_info, raw_value, request)

                if not parse_result.success:
                    if param_info.required:
                        raise HTTPException(status_code=400, detail=f"Parameter parsing failed for {param_info.name}: {parse_result.error}")
                    continue

                # 验证参数
                if param_info.validation_rules:
                    validation_result = await self.validator.validate_parameter(param_info, parse_result.value)

                    if not validation_result.success:
                        raise HTTPException(status_code=400, detail=f"Parameter validation failed for {param_info.name}: {validation_result.error}")

                bound_params[param_info.name] = parse_result.value

            # 更新统计
            binding_time = time.time() - start_time
            self._update_stats(True, binding_time)

            return bound_params

        except Exception as e:
            binding_time = time.time() - start_time
            self._update_stats(False, binding_time)

            logger.error(f"Parameter binding failed: {e}")
            raise

    async def _get_parameter_infos(self, function: Callable) -> List[ParameterInfo]:
        """获取函数参数信息"""
        function_key = f"{function.__module__}.{function.__name__}"

        # 检查缓存
        if function_key in self._parameter_cache:
            self._stats["cache_hits"] += 1
            return self._parameter_cache[function_key]

        self._stats["cache_misses"] += 1

        # 分析函数签名
        signature = inspect.signature(function)
        type_hints = get_type_hints(function)

        param_infos = []

        for param_name, param in signature.parameters.items():
            # 跳过特殊参数
            if param_name in ["self", "cls", "request"]:
                continue

            # 确定参数类型
            param_type = type_hints.get(param_name, str)

            # 确定参数来源
            source = self._determine_parameter_source(param_name, param_type)

            # 确定复杂度
            complexity = self._determine_parameter_complexity(param_type)

            # 创建参数信息
            param_info = ParameterInfo(
                name=param_name,
                param_type=param_type,
                source=source,
                required=param.default == inspect.Parameter.empty,
                default_value=param.default if param.default != inspect.Parameter.empty else None,
                complexity=complexity,
            )

            param_infos.append(param_info)

        # 缓存结果
        self._parameter_cache[function_key] = param_infos

        return param_infos

    def _determine_parameter_source(self, param_name: str, param_type: Type) -> ParameterSource:
        """确定参数来源"""
        # 简单的启发式规则
        if param_name in ["id", "user_id", "item_id"]:
            return ParameterSource.PATH
        elif param_type in [dict, list] or (hasattr(param_type, "__bases__") and BaseModel in param_type.__bases__):
            return ParameterSource.BODY
        else:
            return ParameterSource.QUERY

    def _determine_parameter_complexity(self, param_type: Type) -> ParameterComplexity:
        """确定参数复杂度"""
        if param_type in [str, int, float, bool]:
            return ParameterComplexity.SIMPLE
        elif param_type in [list, dict]:
            return ParameterComplexity.MEDIUM
        elif hasattr(param_type, "__bases__") and BaseModel in param_type.__bases__:
            return ParameterComplexity.COMPLEX
        else:
            return ParameterComplexity.MEDIUM

    async def _extract_request_data(self, request: Request, path_params: Dict[str, Any]) -> Dict[str, Any]:
        """提取请求数据"""
        data = {"path": path_params, "query": dict(request.query_params), "headers": dict(request.headers), "body": None, "form": None}

        # 提取请求体
        try:
            if request.headers.get("content-type", "").startswith("application/json"):
                data["body"] = await request.json()
            elif request.headers.get("content-type", "").startswith("application/x-www-form-urlencoded"):
                form_data = await request.form()
                data["form"] = dict(form_data)
        except Exception as e:
            logger.debug(f"Failed to extract request body: {e}")

        return data

    def _get_raw_value(self, param_info: ParameterInfo, request_data: Dict[str, Any]) -> Any:
        """获取参数原始值"""
        source_data = request_data.get(param_info.source.value, {})

        if param_info.source == ParameterSource.BODY:
            return request_data.get("body")
        elif param_info.source == ParameterSource.FORM:
            return request_data.get("form")
        else:
            return source_data.get(param_info.name)

    def _update_stats(self, success: bool, binding_time: float):
        """更新统计信息"""
        self._stats["total_bindings"] += 1

        if success:
            self._stats["successful_bindings"] += 1
        else:
            self._stats["failed_bindings"] += 1

        # 更新平均绑定时间
        total = self._stats["total_bindings"]
        current_avg = self._stats["avg_binding_time"]
        new_avg = (current_avg * (total - 1) + binding_time) / total
        self._stats["avg_binding_time"] = new_avg

    def get_complexity(self, param_type: Type) -> ParameterComplexity:
        """获取参数复杂度(简化版)"""
        return self._determine_parameter_complexity(param_type)

    def get_perf(self) -> Dict[str, Any]:
        """获取性能统计(简化版)"""
        return {
            "total_bindings": self._stats["total_bindings"],
            "success_rate": (self._stats["successful_bindings"] / max(1, self._stats["total_bindings"])) * 100,
            "avg_binding_time": self._stats["avg_binding_time"],
        }

    def clear_cache(self):
        """清理缓存"""
        self._parameter_cache.clear()
        logger.info("Parameter cache cleared")

    def add_validation_rule(self, function: Callable, param_name: str, rule: Callable):
        """添加验证规则"""
        function_key = f"{function.__module__}.{function.__name__}"

        if function_key in self._parameter_cache:
            for param_info in self._parameter_cache[function_key]:
                if param_info.name == param_name:
                    param_info.validation_rules.append(rule)
                    break

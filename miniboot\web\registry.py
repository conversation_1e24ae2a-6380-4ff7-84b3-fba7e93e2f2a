#!/usr/bin/env python
"""
统一控制器注册表模块

提供自适应的控制器注册、管理和路由映射功能.
支持传统模式和智能模式的自动切换.

主要功能:
- ControllerRegistry - 统一控制器注册表(自适应)
- 控制器信息管理
- 路由信息提取和注册
- 智能方法分析和优化(可选)
- 参数绑定处理
- 响应处理
"""

import asyncio
import inspect
import time
from concurrent.futures import ThreadPoolExecutor
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Set, get_type_hints

from fastapi import FastAPI
from loguru import logger

from ..annotations.web import controller_path as get_controller_path
from ..annotations.web import has_route as has_route_mapping
from ..annotations.web import is_controller, is_rest_controller
from ..annotations.web import path_variables as get_path_variables
from ..annotations.web import request_body as get_request_body
from ..annotations.web import request_headers as get_request_headers
from ..annotations.web import request_params as get_request_params
from ..annotations.web import response_body as get_response_body
from ..annotations.web import response_status as get_response_status
from ..annotations.web import route_info as get_route_info
from .scheduling.classifier import TaskType
# 智能调度相关导入
from .scheduling.scheduler import TaskScheduler, TaskRequest


@dataclass
class ControllerInfo:
    """控制器信息"""

    name: str
    instance: Any
    controller_class: type
    path: str
    is_rest: bool
    methods: list[str] = field(default_factory=list)

    # 智能功能扩展(可选)
    method_classifications: Dict[str, Any] = field(default_factory=dict)
    performance_profiles: Dict[str, Dict[str, Any]] = field(default_factory=dict)
    optimization_hints: Dict[str, List[str]] = field(default_factory=dict)
    registration_time: float = 0.0
    analysis_time: float = 0.0
    is_optimized: bool = False


@dataclass
class RouteInfo:
    """路由信息"""

    controller_name: str
    method_name: str
    path: str
    http_method: str
    handler: Callable
    full_path: str
    parameters: dict[str, Any] = field(default_factory=dict)
    response_info: Optional[dict[str, Any]] = None

    # 智能功能扩展(可选)
    task_type: Any = None  # TaskType when available
    estimated_duration: float = 0.0
    complexity_score: float = 0.0
    io_operations: bool = False
    cpu_intensive: bool = False
    recommended_strategy: str = "auto"
    cache_eligible: bool = False
    batch_eligible: bool = False


class ControllerRegistry:
    """统一控制器注册表

    自适应的控制器注册表,根据是否提供智能调度器自动选择工作模式:
    - 传统模式:基础的控制器注册和路由映射
    - 智能模式:增加智能分析、优化和调度功能

    保持完全的API兼容性,用户无需修改现有代码.
    """

    def __init__(self, app: Optional[FastAPI] = None, smart_scheduler: Optional[TaskScheduler] = None):
        """初始化统一控制器注册表

        Args:
            app: FastAPI应用实例
            smart_scheduler: 智能调度器实例(可选)
        """
        self.app = app
        self.smart_scheduler = smart_scheduler
        self._auto_mode = smart_scheduler is not None

        # 基础功能
        self.controllers: dict[str, ControllerInfo] = {}
        self.routes: list[RouteInfo] = []
        self._route_cache: dict[str, RouteInfo] = {}

        # 智能功能(仅在自动模式下启用)
        if self._auto_mode:
            self._init_smart()
        else:
            self._init_basic()

        logger.info(f"ControllerRegistry initialized in {'auto' if self._auto_mode else 'traditional'} mode")

    def _init_basic(self):
        """初始化传统模式功能"""
        # 传统模式不需要额外初始化
        pass

    def _init_smart(self):
        """初始化自动模式功能"""
        if not self._auto_mode:
            return

        # 智能功能
        self._smart_controllers: Dict[str, ControllerInfo] = {}
        self._smart_routes: Dict[str, RouteInfo] = {}
        self._analysis_cache: Dict[str, Dict[str, Any]] = {}

        # 性能统计
        self._registration_stats = {
            "total_controllers": 0,
            "total_routes": 0,
            "analysis_time": 0.0,
            "optimization_time": 0.0,
            "cache_hits": 0,
            "cache_misses": 0,
        }

        # 异步处理
        self._executor = ThreadPoolExecutor(max_workers=4, thread_name_prefix="smart-registry")
        self._analysis_tasks: Set[asyncio.Task] = set()

    def set_app(self, app: FastAPI):
        """设置FastAPI应用实例

        Args:
            app: FastAPI应用实例
        """
        self.app = app

    def register(self, controller_instance: Any, controller_name: str) -> bool:
        """注册控制器(统一接口)

        Args:
            controller_instance: 控制器实例
            controller_name: 控制器名称

        Returns:
            是否注册成功
        """
        if self._auto_mode:
            # 自动模式:异步注册,但提供同步接口
            try:
                # 在当前事件循环中运行异步注册
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果事件循环正在运行,创建任务
                    task = loop.create_task(self._register_async(controller_instance, controller_name))
                    # 等待任务完成(这里可能需要优化)
                    return True  # 暂时返回True,实际注册在后台进行
                else:
                    # 如果没有事件循环,使用run_until_complete
                    return loop.run_until_complete(self._register_async(controller_instance, controller_name))
            except Exception as e:
                logger.error(f"Auto registration failed, falling back to traditional: {e}")
                return self._register_sync(controller_instance, controller_name)
        else:
            return self._register_sync(controller_instance, controller_name)

    async def register_async(self, controller_instance: Any, controller_name: str) -> bool:
        """异步注册控制器

        Args:
            controller_instance: 控制器实例
            controller_name: 控制器名称

        Returns:
            是否注册成功
        """
        if self._auto_mode:
            return await self._register_async(controller_instance, controller_name)
        else:
            return self._register_sync(controller_instance, controller_name)

    def _register_sync(self, controller_instance: Any, controller_name: str) -> bool:
        """同步注册控制器（传统模式）"""
        start_time = time.time()

        try:
            # 验证控制器
            controller_class = controller_instance.__class__
            if not is_controller(controller_class):
                logger.error(f"Class {controller_class.__name__} is not a valid controller")
                return False

            # 生成控制器名称
            if not controller_name:
                controller_name = controller_class.__name__

            # 检查是否已注册
            if controller_name in self.controllers:
                logger.warning(f"Controller {controller_name} already registered")
                return False

            # 创建控制器信息
            controller_info = ControllerInfo(
                name=controller_name,
                instance=controller_instance,
                controller_class=controller_class,
                path=get_controller_path(controller_class),
                is_rest=is_rest_controller(controller_class),
                registration_time=start_time,
            )

            # 提取路由信息
            routes = self._get_routes(controller_info)
            controller_info.methods = [route.method_name for route in routes]

            # 注册控制器
            self.controllers[controller_name] = controller_info
            self.routes.extend(routes)

            # 注册路由到FastAPI
            if self.app:
                self._apply_routes(routes)

            logger.info(f"Registered controller: {controller_name} with {len(routes)} routes (traditional mode)")
            return True

        except Exception as e:
            logger.error(f"Failed to register controller {controller_name}: {e}")
            return False

    async def _register_async(self, controller_instance: Any, controller_name: str) -> bool:
        """异步注册控制器（智能模式，包含分析和优化）"""
        if not self._auto_mode:
            return self._register_sync(controller_instance, controller_name)

        start_time = time.time()

        try:
            # 首先进行同步注册
            success = self._register_sync(controller_instance, controller_name)
            if not success:
                return False

            # 获取注册后的控制器信息
            controller_info = self.controllers[controller_name]

            # 异步进行智能分析
            analysis_task = asyncio.create_task(self._analyze(controller_info))
            self._analysis_tasks.add(analysis_task)

            # 等待分析完成
            await analysis_task
            self._analysis_tasks.discard(analysis_task)

            # 更新统计信息
            self._registration_stats["total_controllers"] += 1
            self._registration_stats["total_routes"] += len(controller_info.methods)

            logger.info(f"Registered controller: {controller_name} with auto analysis")
            return True

        except Exception as e:
            logger.error(f"Failed to register controller {controller_name} in auto mode: {e}")
            return False

    def _get_routes(self, controller_info: ControllerInfo) -> List[RouteInfo]:
        """提取路由信息(传统模式)"""
        routes = []
        controller_class = controller_info.controller_class
        controller_path = controller_info.path

        for method_name in dir(controller_info.instance):
            if method_name.startswith("_"):
                continue

            method = getattr(controller_info.instance, method_name)
            if not callable(method) or not has_route_mapping(method):
                continue

            try:
                # 获取路由信息
                route_info = get_route_info(method)
                if not route_info:
                    continue

                # 构建完整路径
                method_path = route_info.get("path", "")
                if method_path.startswith("/"):
                    full_path = method_path
                else:
                    full_path = f"{controller_path.rstrip('/')}/{method_path.lstrip('/')}"

                # 创建路由信息
                route = RouteInfo(
                    controller_name=controller_info.name,
                    method_name=method_name,
                    path=method_path,
                    http_method=route_info.get("method", "GET"),
                    handler=method,
                    full_path=full_path,
                    parameters=self._get_params(method),
                    response_info=self._get_response(method),
                )

                routes.append(route)

            except Exception as e:
                logger.error(f"Failed to extract route info for {method_name}: {e}")
                continue

        return routes

    def _get_params(self, method: Callable) -> dict[str, Any]:
        """提取方法参数信息"""
        try:
            parameters = {}

            # 获取路径变量
            path_vars = get_path_variables(method)
            if path_vars:
                parameters["path_variables"] = path_vars

            # 获取请求参数
            request_params = get_request_params(method)
            if request_params:
                parameters["request_params"] = request_params

            # 获取请求头
            headers = get_request_headers(method)
            if headers:
                parameters["headers"] = headers

            # 获取请求体
            body = get_request_body(method)
            if body:
                parameters["request_body"] = body

            return parameters

        except Exception as e:
            logger.error(f"Failed to extract parameters for method {method.__name__}: {e}")
            return {}

    def _get_response(self, method: Callable) -> Optional[dict[str, Any]]:
        """提取响应信息"""
        try:
            response_info = {}

            # 获取响应状态
            status = get_response_status(method)
            if status:
                response_info["status"] = status

            # 获取响应体
            body = get_response_body(method)
            if body:
                response_info["body"] = body

            return response_info if response_info else None

        except Exception as e:
            logger.error(f"Failed to extract response info for method {method.__name__}: {e}")
            return None

    def _apply_routes(self, routes: List[RouteInfo]):
        """将路由注册到FastAPI应用"""
        if not self.app:
            return

        for route in routes:
            try:
                # 根据HTTP方法注册路由
                method = route.http_method.lower()
                if hasattr(self.app, method):
                    route_func = getattr(self.app, method)
                    route_func(route.full_path)(route.handler)
                    logger.debug(f"Registered route: {method.upper()} {route.full_path}")
                else:
                    logger.warning(f"Unsupported HTTP method: {route.http_method}")

            except Exception as e:
                logger.error(f"Failed to register route {route.full_path}: {e}")

    async def _analyze(self, controller_info: ControllerInfo):
        """异步分析控制器(智能模式)"""
        if not self._auto_mode:
            return

        start_time = time.time()

        try:
            # 分析每个方法
            for method_name in controller_info.methods:
                method = getattr(controller_info.instance, method_name)

                # 生成任务请求用于分类
                task_request = TaskRequest(
                    task_id=f"{controller_info.name}.{method_name}",
                    task_type="web_request",
                    priority=1,
                    metadata={
                        "controller": controller_info.name,
                        "method": method_name,
                        "http_method": "GET",  # 默认值,实际应从路由信息获取
                        "path": controller_info.path,
                    },
                )

                # 使用智能调度器进行分类
                if self.smart_scheduler and hasattr(self.smart_scheduler, "_task_classifier") and self.smart_scheduler._task_classifier:
                    classification = await self.smart_scheduler._task_classifier.classify_task(task_request)
                    controller_info.method_classifications[method_name] = classification

                # 生成性能概况
                profile = await self._profile(method, method_name)
                controller_info.performance_profiles[method_name] = profile

                # 生成优化建议
                hints = self._hints(classification, profile)
                controller_info.optimization_hints[method_name] = hints

            # 更新分析时间
            controller_info.analysis_time = time.time() - start_time
            controller_info.is_optimized = True

            # 更新统计信息
            self._registration_stats["analysis_time"] += controller_info.analysis_time

            logger.debug(f"Completed auto analysis for controller: {controller_info.name}")

        except Exception as e:
            logger.error(f"Failed to analyze controller {controller_info.name}: {e}")

    async def _profile(self, method: Callable, method_name: str) -> Dict[str, Any]:
        """生成方法性能概况"""
        try:
            profile = {
                "estimated_duration": 0.01,  # 默认估计时间
                "complexity_score": 0.1,  # 默认复杂度
                "io_operations": False,  # 是否有IO操作
                "cpu_intensive": False,  # 是否CPU密集
                "memory_usage": "low",  # 内存使用情况
                "cache_eligible": True,  # 是否可缓存
                "batch_eligible": False,  # 是否可批处理
            }

            # 分析方法签名
            sig = inspect.signature(method)
            param_count = len(sig.parameters)

            # 基于参数数量调整复杂度
            if param_count > 5:
                profile["complexity_score"] = 0.3
            elif param_count > 10:
                profile["complexity_score"] = 0.5

            # 分析方法体(简单的启发式分析)
            if hasattr(method, "__code__"):
                code = method.__code__

                # 基于代码行数估计复杂度
                if code.co_code:
                    bytecode_size = len(code.co_code)
                    if bytecode_size > 100:
                        profile["complexity_score"] = min(0.8, profile["complexity_score"] + 0.2)
                        profile["estimated_duration"] = 0.05

                    if bytecode_size > 500:
                        profile["cpu_intensive"] = True
                        profile["estimated_duration"] = 0.1

            return profile

        except Exception as e:
            logger.error(f"Failed to generate performance profile for {method_name}: {e}")
            return {"estimated_duration": 0.01, "complexity_score": 0.1}

    def _hints(self, classification: Any, profile: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        hints = []

        try:
            # 基于分类生成建议
            if classification and hasattr(classification, "name"):
                if "IO" in classification.name:
                    hints.append("Consider using async/await for IO operations")
                    hints.append("Add connection pooling for database operations")

                if "CPU" in classification.name:
                    hints.append("Consider using background tasks for CPU-intensive operations")
                    hints.append("Implement result caching to avoid repeated calculations")

            # 基于性能概况生成建议
            if profile.get("complexity_score", 0) > 0.5:
                hints.append("High complexity detected - consider breaking into smaller methods")

            if profile.get("cpu_intensive"):
                hints.append("CPU-intensive operation - consider async execution")

            if profile.get("cache_eligible"):
                hints.append("Response caching recommended for this endpoint")

        except Exception as e:
            logger.error(f"Failed to generate optimization hints: {e}")

        return hints

    # 公共API方法(保持兼容性)

    def get_controller(self, controller_name: str) -> Optional[ControllerInfo]:
        """获取控制器信息

        Args:
            controller_name: 控制器名称

        Returns:
            控制器信息,如果不存在则返回None
        """
        return self.controllers.get(controller_name)

    def get_controllers(self) -> dict[str, ControllerInfo]:
        """获取所有控制器

        Returns:
            控制器字典
        """
        return self.controllers.copy()

    def get_routes(self, controller_name: Optional[str] = None) -> list[RouteInfo]:
        """获取路由信息

        Args:
            controller_name: 控制器名称,如果为None则返回所有路由

        Returns:
            路由信息列表
        """
        if controller_name:
            return [route for route in self.routes if route.controller_name == controller_name]
        return self.routes.copy()

    def routes(self, controller_name: str) -> list[RouteInfo]:
        """获取指定控制器的路由信息

        Args:
            controller_name: 控制器名称

        Returns:
            路由信息列表
        """
        return [route for route in self.routes if route.controller_name == controller_name]

    def remove(self, controller_name: str) -> bool:
        """注销控制器

        Args:
            controller_name: 控制器名称

        Returns:
            是否注销成功
        """
        if controller_name not in self.controllers:
            return False

        # 移除控制器
        del self.controllers[controller_name]

        # 移除相关路由
        self.routes = [route for route in self.routes if route.controller_name != controller_name]

        # 清理智能功能相关数据
        if self._auto_mode:
            self._smart_controllers.pop(controller_name, None)
            # 清理路由缓存
            keys_to_remove = [key for key in self._smart_routes.keys() if key.startswith(f"{controller_name}.")]
            for key in keys_to_remove:
                del self._smart_routes[key]

        logger.info(f"Removed controller: {controller_name}")
        return True

    def clear(self):
        """清空所有注册信息"""
        self.controllers.clear()
        self.routes.clear()
        self._route_cache.clear()

        if self._auto_mode:
            self._smart_controllers.clear()
            self._smart_routes.clear()
            self._analysis_cache.clear()

            # 取消所有分析任务
            for task in self._analysis_tasks:
                if not task.done():
                    task.cancel()
            self._analysis_tasks.clear()

        logger.info("Cleared all controller registrations")

    # 智能功能专用方法(仅在智能模式下可用)

    def get_stats(self) -> Dict[str, Any]:
        """获取智能功能统计信息

        Returns:
            统计信息字典,如果不在自动模式则返回空字典
        """
        if not self._auto_mode:
            return {}
        return self._registration_stats.copy()

    def hints(self, controller_name: str, method_name: Optional[str] = None) -> Dict[str, List[str]]:
        """获取优化建议

        Args:
            controller_name: 控制器名称
            method_name: 方法名称,如果为None则返回所有方法的建议

        Returns:
            优化建议字典
        """
        if not self._auto_mode:
            return {}

        controller = self.controllers.get(controller_name)
        if not controller:
            return {}

        if method_name:
            hints = controller.optimization_hints.get(method_name, [])
            return {method_name: hints}
        else:
            return controller.optimization_hints.copy()

    def profiles(self, controller_name: str, method_name: Optional[str] = None) -> Dict[str, Dict[str, Any]]:
        """获取性能概况

        Args:
            controller_name: 控制器名称
            method_name: 方法名称,如果为None则返回所有方法的概况

        Returns:
            性能概况字典
        """
        if not self._auto_mode:
            return {}

        controller = self.controllers.get(controller_name)
        if not controller:
            return {}

        if method_name:
            profile = controller.performance_profiles.get(method_name, {})
            return {method_name: profile}
        else:
            return controller.performance_profiles.copy()

    async def apply_to_app(self, app: FastAPI):
        """异步注册所有路由到FastAPI应用(智能模式专用)

        Args:
            app: FastAPI应用实例
        """
        self.app = app

        if self._auto_mode:
            # 自动模式:可以进行路由优化
            optimized_routes = await self._optimize()
            self._apply_routes(optimized_routes)
        else:
            # 传统模式:直接注册
            self._apply_routes(self.routes)

    async def _optimize(self) -> List[RouteInfo]:
        """优化路由(自动模式)"""
        if not self._auto_mode:
            return self.routes

        # 这里可以实现路由优化逻辑
        # 例如:根据性能概况重新排序路由、合并相似路由等
        return self.routes

    async def cleanup(self):
        """异步清理资源"""
        if self._auto_mode:
            # 取消所有分析任务
            for task in self._analysis_tasks:
                if not task.done():
                    task.cancel()

            # 等待任务完成
            if self._analysis_tasks:
                await asyncio.gather(*self._analysis_tasks, return_exceptions=True)

            # 关闭线程池
            if hasattr(self, "_executor"):
                self._executor.shutdown(wait=True)

        logger.info("ControllerRegistry cleanup completed")

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: MiniBootScheduler核心调度器 - 基于APScheduler实现定时任务调度
"""

import asyncio
import threading
from datetime import datetime, timezone
from enum import Enum
from typing import Any, Callable, Optional

from apscheduler.events import (EVENT_JOB_ERROR, EVENT_JOB_EXECUTED,
                                EVENT_JOB_MISSED)
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.schedulers.background import BackgroundScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.date import DateTrigger
from apscheduler.triggers.interval import IntervalTrigger

from ..errors import SchedulerConfigurationError as ScheduleConfigurationError
from ..errors import SchedulerNotStartedError
from ..errors import TaskExecutionError as ScheduleExecutionError
from .properties import SchedulerConfigFactory, SchedulerProperties
from .task import ScheduledTask, TaskRegistry, TaskStatus, TaskType


class SchedulerState(Enum):
    """调度器状态枚举

    定义调度器的各种运行状态,提供类型安全的状态管理和状态转换验证.
    """

    STOPPED = "stopped"
    """调度器已停止,不执行任何任务"""

    STARTING = "starting"
    """调度器正在启动中"""

    RUNNING = "running"
    """调度器正在运行,正常执行任务"""

    PAUSED = "paused"
    """调度器已暂停,暂时停止执行任务"""

    STOPPING = "stopping"
    """调度器正在停止中"""

    def is_active(self) -> bool:
        """检查调度器是否处于活动状态

        Returns:
            True如果调度器可以执行任务,False否则
        """
        return self in [SchedulerState.RUNNING, SchedulerState.STARTING]

    def can_schedule_task(self) -> bool:
        """检查是否可以调度新任务

        Returns:
            True如果可以调度任务,False否则
        """
        return self in [SchedulerState.RUNNING, SchedulerState.PAUSED]

    def can_transition_to(self, target_state: "SchedulerState") -> bool:
        """检查是否可以转换到目标状态

        Args:
            target_state: 目标状态

        Returns:
            True如果可以转换,False否则
        """
        # 定义允许的状态转换
        allowed_transitions = {
            SchedulerState.STOPPED: {SchedulerState.STARTING},
            SchedulerState.STARTING: {SchedulerState.RUNNING, SchedulerState.STOPPED},
            SchedulerState.RUNNING: {SchedulerState.PAUSED, SchedulerState.STOPPING},
            SchedulerState.PAUSED: {SchedulerState.RUNNING, SchedulerState.STOPPING},
            SchedulerState.STOPPING: {SchedulerState.STOPPED},
        }

        return target_state in allowed_transitions.get(self, set())


class MiniBootScheduler:
    """MiniBootScheduler核心调度器"""

    def __init__(
        self,
        max_workers: int = 10,
        timezone: Optional[str] = None,
        job_defaults: Optional[dict[str, Any]] = None,
        use_asyncio: bool = True,
        properties: Optional[SchedulerProperties] = None,
    ):
        """
        初始化调度器

        Args:
            max_workers: 最大工作线程数
            timezone: 时区设置
            job_defaults: 作业默认配置
            use_asyncio: 是否使用AsyncIO调度器
            properties: 调度器属性配置(如果提供,将覆盖其他参数)
        """
        # 如果提供了properties,使用配置对象
        if properties:
            self.properties = properties
            self.max_workers = properties.concurrency.max_workers
            self.timezone = properties.timezone
            # 如果明确指定use_asyncio参数,使用该参数;否则根据执行器判断
            if "use_asyncio" in locals():
                self.use_asyncio = use_asyncio
            else:
                self.use_asyncio = "asyncio" in properties.executors
            self.job_defaults = properties.job_defaults
        else:
            # 使用传统参数创建默认配置
            self.properties = SchedulerConfigFactory.memory(max_workers)
            if timezone:
                self.properties.timezone = timezone
            if job_defaults:
                self.properties.job_defaults.update(job_defaults)

            self.max_workers = max_workers
            self.timezone = timezone
            self.use_asyncio = use_asyncio
            self.job_defaults = self.properties.job_defaults

        self.state = SchedulerState.STOPPED

        # 任务注册表
        self.task_registry = TaskRegistry()

        # 创建调度器
        self._scheduler = self._create_scheduler()

        # 任务执行统计
        self._execution_stats = {
            "total_executions": 0,
            "successful_executions": 0,
            "failed_executions": 0,
            "missed_executions": 0,
            "average_execution_time": 0.0,
            "last_execution_time": None,
        }

        # 线程锁
        self._lock = threading.RLock()

        # 设置事件监听器
        self._setup_event_listeners()

        # 创建任务管理器
        from .manager import TaskManager

        self.task_manager = TaskManager(self)

    def _create_scheduler(self):
        """创建APScheduler实例"""
        # 使用配置对象创建调度器
        config = self.properties.get_apscheduler_config()

        # 检测测试环境
        import sys

        is_test_env = "unittest" in sys.modules or "pytest" in sys.modules

        # 如果使用BackgroundScheduler,需要过滤掉asyncio执行器
        if not self.use_asyncio or is_test_env:
            # 过滤掉asyncio执行器
            if "executors" in config and "asyncio" in config["executors"]:
                del config["executors"]["asyncio"]

            # 确保默认执行器不是asyncio
            if config.get("default_executor") == "asyncio":
                config["default_executor"] = "default"

            return BackgroundScheduler(**config)
        else:
            return AsyncIOScheduler(**config)

    def _setup_event_listeners(self):
        """设置事件监听器"""
        self._scheduler.add_listener(self._on_job_executed, EVENT_JOB_EXECUTED)

        self._scheduler.add_listener(self._on_job_error, EVENT_JOB_ERROR)

        self._scheduler.add_listener(self._on_job_missed, EVENT_JOB_MISSED)

    def _on_job_executed(self, event):
        """作业执行成功事件处理"""
        from datetime import datetime, timezone

        with self._lock:
            self._execution_stats["total_executions"] += 1
            self._execution_stats["successful_executions"] += 1
            self._execution_stats["last_execution_time"] = datetime.now(timezone.utc).isoformat()

            # 更新平均执行时间(简单实现)
            if hasattr(event, "duration") and event.duration:
                total_executions = self._execution_stats["total_executions"]
                current_avg = self._execution_stats["average_execution_time"]
                self._execution_stats["average_execution_time"] = (current_avg * (total_executions - 1) + event.duration) / total_executions

        # 更新任务状态
        task_id = event.job_id
        task = self.task_registry.get_task(task_id)
        if task:
            task.mark_execution_success()

    def _on_job_error(self, event):
        """作业执行错误事件处理"""
        from datetime import datetime, timezone

        with self._lock:
            self._execution_stats["total_executions"] += 1
            self._execution_stats["failed_executions"] += 1
            self._execution_stats["last_execution_time"] = datetime.now(timezone.utc).isoformat()

        # 更新任务状态
        task_id = event.job_id
        task = self.task_registry.get_task(task_id)
        if task:
            task.mark_execution_failure(event.exception)

    def _on_job_missed(self, event):
        """作业错过执行事件处理"""
        with self._lock:
            self._execution_stats["missed_executions"] += 1

        # 更新任务状态
        task_id = event.job_id
        task = self.task_registry.get_task(task_id)
        if task:
            task.mark_execution_failure(ScheduleExecutionError(f"任务错过执行: {task_id}"))

    def start(self) -> None:
        """启动调度器"""
        if self.state == SchedulerState.RUNNING:
            return

        with self._lock:
            # 验证状态转换
            if not self.state.can_transition_to(SchedulerState.STARTING):
                raise ScheduleConfigurationError(f"Cannot start scheduler from state {self.state.value}")

            self.state = SchedulerState.STARTING
            try:
                self._scheduler.start()
                self.state = SchedulerState.RUNNING

                # 初始化任务管理器
                if self.task_manager is None:
                    try:
                        from .task_manager import TaskManager

                        self.task_manager = TaskManager(self)
                    except Exception as e:
                        # 如果导入失败,设置为None,但不影响调度器启动
                        print(f"警告: 任务管理器初始化失败: {e}")
                        self.task_manager = None
                        self.task_manager = None

            except Exception as e:
                self.state = SchedulerState.STOPPED
                raise ScheduleConfigurationError(f"调度器启动失败: {e}") from e

    def shutdown(self, wait: bool = True) -> None:
        """关闭调度器"""
        if self.state == SchedulerState.STOPPED:
            return

        with self._lock:
            # 验证状态转换
            if not self.state.can_transition_to(SchedulerState.STOPPING):
                raise ScheduleConfigurationError(f"Cannot shutdown scheduler from state {self.state.value}")

            self.state = SchedulerState.STOPPING
            try:
                self._scheduler.shutdown(wait=wait)
                self.state = SchedulerState.STOPPED
            except Exception as e:
                self.state = SchedulerState.STOPPED  # 确保状态正确
                raise ScheduleConfigurationError(f"调度器关闭失败: {e}") from e

    def pause(self) -> None:
        """暂停调度器"""
        if self.state != SchedulerState.RUNNING:
            raise SchedulerNotStartedError("调度器未运行,无法暂停")

        with self._lock:
            # 验证状态转换
            if not self.state.can_transition_to(SchedulerState.PAUSED):
                raise ScheduleConfigurationError(f"Cannot pause scheduler from state {self.state.value}")

            self._scheduler.pause()
            self.state = SchedulerState.PAUSED

    def resume(self) -> None:
        """恢复调度器"""
        if self.state != SchedulerState.PAUSED:
            raise SchedulerNotStartedError("调度器未暂停,无法恢复")

        with self._lock:
            # 验证状态转换
            if not self.state.can_transition_to(SchedulerState.RUNNING):
                raise ScheduleConfigurationError(f"Cannot resume scheduler from state {self.state.value}")

            self._scheduler.resume()
            self.state = SchedulerState.RUNNING

    def is_running(self) -> bool:
        """检查调度器是否运行中"""
        return self.state == SchedulerState.RUNNING

    def is_paused(self) -> bool:
        """检查调度器是否已暂停"""
        return self.state == SchedulerState.PAUSED

    def schedule_task(self, task: ScheduledTask) -> str:
        """调度任务"""
        if not self.state.can_schedule_task():
            raise SchedulerNotStartedError(f"调度器状态 {self.state.value} 不支持调度任务")

        # 注册任务
        task_id = self.task_registry.register_task(task)

        try:
            # 创建触发器
            trigger = self._create_trigger(task)

            # 创建作业包装器
            job_func = self._create_job_wrapper(task)

            # 确定执行器
            executor = "asyncio" if task.is_async and self.use_asyncio else "default"

            # 添加作业到调度器
            self._scheduler.add_job(func=job_func, trigger=trigger, id=task_id, name=task.name, executor=executor, replace_existing=True)

            return task_id

        except Exception as e:
            # 如果调度失败,从注册表中移除任务
            self.task_registry.unregister_task(task_id)
            raise ScheduleConfigurationError(f"任务调度失败: {e}") from e

    def register_task(self, func, config, name: str = None) -> str:
        """注册任务(别名方法,兼容测试)"""
        from .tasks import TaskFactory

        # 如果调度器未启动,先启动它
        if not self.is_running():
            self.start()

        # 创建任务对象
        if callable(func):
            task = TaskFactory.create_lambda_task(func, config, name or "test_task")
        else:
            raise ValueError("Invalid task function")

        return self.schedule_task(task)

    def unregister_task(self, task_id: str) -> bool:
        """注销任务(别名方法,兼容测试)"""
        return self.unschedule_task(task_id)

    def _create_trigger(self, task: ScheduledTask):
        """创建触发器"""
        if not task.config:
            raise ScheduleConfigurationError(f"任务缺少配置: {task.task_id}")

        config = task.config

        if task.task_type == TaskType.CRON:
            cron_expr = config.cron
            # 处理6字段cron表达式(包含秒),转换为5字段
            parts = cron_expr.strip().split()
            if len(parts) == 6:
                # 去掉秒字段,只保留分钟开始的5个字段
                cron_expr = " ".join(parts[1:])
            return CronTrigger.from_crontab(cron_expr, timezone=self.timezone)

        elif task.task_type == TaskType.FIXED_RATE:
            # 使用解析后的秒数值
            seconds = getattr(config, "_fixed_rate_seconds", None)
            if seconds is None:
                seconds = config._parse_duration(config.fixed_rate)
            return IntervalTrigger(seconds=seconds, timezone=self.timezone)

        elif task.task_type == TaskType.FIXED_DELAY:
            # 固定延迟需要特殊处理,这里先用间隔触发器
            # 使用解析后的秒数值
            seconds = getattr(config, "_fixed_delay_seconds", None)
            if seconds is None:
                seconds = config._parse_duration(config.fixed_delay)
            return IntervalTrigger(seconds=seconds, timezone=self.timezone)

        elif task.task_type == TaskType.ONE_TIME:
            # 一次性任务,立即执行
            return DateTrigger(run_date=datetime.now(timezone.utc), timezone=self.timezone)

        else:
            raise ScheduleConfigurationError(f"不支持的任务类型: {task.task_type}")

    def _create_job_wrapper(self, task: ScheduledTask) -> Callable:
        """创建作业包装器"""

        async def async_job_wrapper():
            """异步作业包装器"""
            task.mark_execution_start()
            try:
                result = await task.execute()
                return result
            except Exception as e:
                task.mark_execution_failure(e)
                raise

        def sync_job_wrapper():
            """同步作业包装器"""
            try:
                # 直接运行execute方法,不管是否为协程
                result = asyncio.run(task.execute())
                return result
            except Exception:
                # 重新抛出异常,让APScheduler处理
                raise

        # 根据任务类型返回相应的包装器
        if hasattr(task, "is_async") and task.is_async and self.use_asyncio:
            return async_job_wrapper
        else:
            return sync_job_wrapper

    def unschedule_task(self, task_id: str) -> bool:
        """取消调度任务"""
        try:
            # 从调度器中移除作业
            self._scheduler.remove_job(task_id)

            # 从注册表中移除任务
            task = self.task_registry.get_task(task_id)
            if task:
                task.mark_cancelled()

            return self.task_registry.unregister_task(task_id)

        except Exception:
            return False

    def pause_task(self, task_id: str) -> bool:
        """暂停任务"""
        if not self.is_running():
            from .exceptions import SchedulerNotStartedError

            raise SchedulerNotStartedError("Cannot pause task when scheduler is not running", operation="pause_task")

        try:
            self._scheduler.pause_job(task_id)
            return True
        except Exception:
            return False

    def resume_task(self, task_id: str) -> bool:
        """恢复任务"""
        try:
            self._scheduler.resume_job(task_id)
            return True
        except Exception:
            return False

    def get_task(self, task_id: str) -> Optional[ScheduledTask]:
        """获取任务"""
        return self.task_registry.get_task(task_id)

    def all_tasks(self) -> dict[str, ScheduledTask]:
        """获取所有任务"""
        return self.task_registry.get_all_tasks()

    def running_tasks(self) -> dict[str, ScheduledTask]:
        """获取运行中的任务"""
        return self.task_registry.get_tasks_by_status(TaskStatus.RUNNING)

    def get_scheduled_jobs(self) -> list[dict[str, Any]]:
        """获取调度器中的所有作业信息"""
        jobs = []
        for job in self._scheduler.get_jobs():
            jobs.append(
                {
                    "id": job.id,
                    "name": job.name,
                    "next_run_time": job.next_run_time.isoformat() if job.next_run_time else None,
                    "trigger": str(job.trigger),
                    "executor": job.executor,
                    "max_instances": job.max_instances,
                    "misfire_grace_time": job.misfire_grace_time,
                }
            )
        return jobs

    def get_execution_stats(self) -> dict[str, Any]:
        """获取执行统计信息"""
        with self._lock:
            stats = self._execution_stats.copy()

        # 添加任务注册表统计
        registry_stats = self.task_registry.get_stats()
        stats.update(
            {
                "scheduler_state": self.state,
                "total_registered_tasks": registry_stats["total_tasks"],
                "tasks_by_type": registry_stats["tasks_by_type"],
                "tasks_by_status": registry_stats["tasks_by_status"],
                "active_jobs": len(self._scheduler.get_jobs()) if self._scheduler else 0,
            }
        )

        return stats

    def get_execution_statistics(self) -> dict[str, Any]:
        """获取执行统计信息(别名方法)"""
        return self.get_execution_stats()

    def clear_all_tasks(self) -> None:
        """清空所有任务"""
        # 移除所有作业
        self._scheduler.remove_all_jobs()

        # 清空任务注册表
        self.task_registry.clear()

        # 重置统计信息
        with self._lock:
            self._execution_stats = {
                "total_executions": 0,
                "successful_executions": 0,
                "failed_executions": 0,
                "missed_executions": 0,
                "average_execution_time": 0.0,
                "last_execution_time": None,
            }

    def execute_task_now(self, task_id: str) -> bool:
        """立即执行任务"""
        try:
            job = self._scheduler.get_job(task_id)
            if job:
                job.modify(next_run_time=datetime.now(timezone.utc))
                return True
            return False
        except Exception:
            return False

    def get_scheduler_info(self) -> dict[str, Any]:
        """获取调度器信息"""
        # 获取任务统计信息
        total_tasks = len(self.task_registry.get_all_tasks())
        running_tasks = (
            len([task for task in self.task_registry.get_all_tasks().values() if task.status.value == "running"])
            if hasattr(self.task_registry.get_all_tasks(), "values")
            else 0
        )

        return {
            "state": self.state,
            "max_workers": self.max_workers,
            "timezone": self.timezone,
            "use_asyncio": self.use_asyncio,
            "job_defaults": self.job_defaults,
            "scheduler_type": type(self._scheduler).__name__,
            "running": self.is_running(),
            "paused": self.is_paused(),
            "total_tasks": total_tasks,
            "running_tasks": running_tasks,
        }

    def __enter__(self):
        """上下文管理器入口"""
        self.start()
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.shutdown()

    def __str__(self) -> str:
        return f"MiniBootScheduler(state={self.state}, tasks={len(self.get_all_tasks())})"

    def __repr__(self) -> str:
        return self.__str__()

    # 任务管理器便捷方法
    def add_managed_task(self, task: ScheduledTask, max_retries: int = 3, retry_delay: float = 1.0) -> str:
        """添加带管理功能的任务"""
        if not self.is_running():
            raise SchedulerNotStartedError("调度器未启动")

        return self.task_manager.add_task(task, max_retries, retry_delay)

    def managed_method(self, method, instance=None, config=None, name=None, description=None, max_retries=3, retry_delay=1.0) -> str:
        """创建并添加带管理功能的方法任务"""
        if not self.is_running():
            raise SchedulerNotStartedError("调度器未启动")

        return self.task_manager.create_method(
            method=method, instance=instance, config=config, name=name, description=description, max_retries=max_retries, retry_delay=retry_delay
        )

    def managed_lambda(self, func, config=None, name=None, description=None, args=(), kwargs=None, max_retries=3, retry_delay=1.0) -> str:
        """创建并添加带管理功能的Lambda任务"""
        if not self.is_running():
            raise SchedulerNotStartedError("调度器未启动")

        return self.task_manager.create_lambda(
            func=func, config=config, name=name, description=description, args=args, kwargs=kwargs, max_retries=max_retries, retry_delay=retry_delay
        )

    def metrics(self, task_id: str):
        """获取任务执行指标"""
        if self.task_manager:
            return self.task_manager.metrics(task_id)
        return None

    def all_metrics(self):
        """获取所有任务的执行指标"""
        if self.task_manager:
            return self.task_manager.all_metrics()
        return {}

    def get_task_manager_summary(self):
        """获取任务管理器摘要"""
        if self.task_manager:
            return self.task_manager.get_task_summary()
        return {
            "total_managed_tasks": 0,
            "total_executions": 0,
            "total_successes": 0,
            "total_failures": 0,
            "average_success_rate": 0.0,
            "fixed_delay_tasks": 0,
        }


# 为了兼容性,创建TaskScheduler别名
TaskScheduler = MiniBootScheduler

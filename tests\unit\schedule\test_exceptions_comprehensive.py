#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 异常处理综合单元测试
"""

import unittest
from datetime import datetime, timezone

from miniboot.schedule import ScheduleException, ScheduleConfigurationError, ScheduleExecutionError, SchedulerNotStartedError, TaskRegistrationError


class TestScheduleException(unittest.TestCase):
    """测试ScheduleException基础异常"""

    def test_basic_exception(self):
        """测试基础异常创建"""
        error = ScheduleException("Test error message")

        self.assertEqual(str(error), "Test error message")
        self.assertEqual(error.message, "Test error message")
        self.assertIsNone(error.error_code)
        self.assertIsNone(error.details)

    def test_exception_with_code(self):
        """测试带错误码的异常"""
        error = ScheduleException(message="Test error with code", error_code="TEST_001")

        self.assertEqual(error.message, "Test error with code")
        self.assertEqual(error.error_code, "TEST_001")

    def test_exception_with_details(self):
        """测试带详细信息的异常"""
        details = {"component": "scheduler", "operation": "start", "timestamp": datetime.now(timezone.utc).isoformat()}

        error = ScheduleException(message="Test error with details", error_code="TEST_002", details=details)

        self.assertEqual(error.details, details)
        self.assertIn("component", error.details)
        self.assertIn("operation", error.details)
        self.assertIn("timestamp", error.details)

    def test_exception_str_representation(self):
        """测试异常字符串表示"""
        # 基础异常
        error1 = ScheduleException("Simple error")
        self.assertEqual(str(error1), "Simple error")

        # 带错误码的异常
        ScheduleException("Error with code", error_code="ERR_001")
        self.assertIn("Simple error", str(error1))

        # 带详细信息的异常
        error3 = ScheduleException("Error with details", error_code="ERR_002", details={"key": "value"})
        self.assertIn("Error with details", str(error3))

    def test_exception_inheritance(self):
        """测试异常继承"""
        error = ScheduleException("Test error")

        self.assertIsInstance(error, Exception)
        self.assertIsInstance(error, ScheduleException)


class TestScheduleConfigurationError(unittest.TestCase):
    """测试ScheduleConfigurationError配置异常"""

    def test_configuration_error_basic(self):
        """测试基础配置错误"""
        error = ScheduleConfigurationError("Invalid configuration")

        self.assertIsInstance(error, ScheduleException)
        self.assertEqual(error.message, "Invalid configuration")

    def test_configuration_error_with_config_name(self):
        """测试带配置名称的配置错误"""
        error = ScheduleConfigurationError(message="Invalid value", config_name="max_workers")

        self.assertEqual(error.config_name, "max_workers")
        self.assertIn("max_workers", str(error))

    def test_configuration_error_with_expected_value(self):
        """测试带期望值的配置错误"""
        error = ScheduleConfigurationError(message="Invalid value", config_name="max_workers", expected_value="positive integer")

        self.assertEqual(error.expected_value, "positive integer")
        self.assertIn("positive integer", str(error))

    def test_configuration_error_with_actual_value(self):
        """测试带实际值的配置错误"""
        error = ScheduleConfigurationError(message="Invalid value", config_name="max_workers", expected_value="positive integer", actual_value=-5)

        self.assertEqual(error.actual_value, -5)
        self.assertIn("-5", str(error))

    def test_configuration_error_full(self):
        """测试完整的配置错误"""
        error = ScheduleConfigurationError(
            message="Invalid configuration value",
            error_code="CONFIG_001",
            config_name="fixed_rate",
            expected_value="time string (e.g., '5s', '10m')",
            actual_value="invalid_time",
            details={"component": "ScheduledConfig", "validation_rule": "time_format"},
        )

        self.assertEqual(error.config_name, "fixed_rate")
        self.assertEqual(error.expected_value, "time string (e.g., '5s', '10m')")
        self.assertEqual(error.actual_value, "invalid_time")
        self.assertEqual(error.error_code, "CONFIG_001")

        error_str = str(error)
        self.assertIn("fixed_rate", error_str)
        self.assertIn("invalid_time", error_str)


class TestScheduleExecutionError(unittest.TestCase):
    """测试ScheduleExecutionError执行异常"""

    def test_execution_error_basic(self):
        """测试基础执行错误"""
        error = ScheduleExecutionError("Task execution failed")

        self.assertIsInstance(error, ScheduleException)
        self.assertEqual(error.message, "Task execution failed")

    def test_execution_error_with_task_info(self):
        """测试带任务信息的执行错误"""
        error = ScheduleExecutionError(message="Task execution failed", task_name="test_task", task_id="task_123")

        self.assertEqual(error.task_name, "test_task")
        self.assertEqual(error.task_id, "task_123")
        self.assertIn("test_task", str(error))
        self.assertIn("task_123", str(error))

    def test_execution_error_with_execution_time(self):
        """测试带执行时间的执行错误"""
        execution_time = datetime.now(timezone.utc).isoformat()

        error = ScheduleExecutionError(message="Task execution failed", task_name="test_task", execution_time=execution_time)

        self.assertEqual(error.execution_time, execution_time)
        self.assertIn(execution_time, str(error))

    def test_execution_error_with_retry_info(self):
        """测试带重试信息的执行错误"""
        error = ScheduleExecutionError(message="Task execution failed after retries", task_name="test_task", retry_count=3, max_retries=5)

        self.assertEqual(error.retry_count, 3)
        self.assertEqual(error.max_retries, 5)
        self.assertIn("3", str(error))
        self.assertIn("5", str(error))

    def test_execution_error_full(self):
        """测试完整的执行错误"""
        execution_time = datetime.now(timezone.utc).isoformat()

        error = ScheduleExecutionError(
            message="Task execution failed with exception",
            error_code="EXEC_001",
            task_name="critical_task",
            task_id="task_456",
            execution_time=execution_time,
            retry_count=2,
            max_retries=3,
            details={"exception_type": "ValueError", "exception_message": "Invalid input data", "stack_trace": "..."},
        )

        self.assertEqual(error.task_name, "critical_task")
        self.assertEqual(error.task_id, "task_456")
        self.assertEqual(error.execution_time, execution_time)
        self.assertEqual(error.retry_count, 2)
        self.assertEqual(error.max_retries, 3)

        error_str = str(error)
        self.assertIn("critical_task", error_str)
        self.assertIn("task_456", error_str)


class TestSchedulerNotStartedError(unittest.TestCase):
    """测试SchedulerNotStartedError调度器未启动异常"""

    def test_not_started_error_basic(self):
        """测试基础未启动错误"""
        error = SchedulerNotStartedError("Scheduler is not started")

        self.assertIsInstance(error, ScheduleException)
        self.assertEqual(error.message, "Scheduler is not started")

    def test_not_started_error_with_operation(self):
        """测试带操作信息的未启动错误"""
        error = SchedulerNotStartedError(message="Cannot perform operation on stopped scheduler", operation="pause_task")

        self.assertEqual(error.operation, "pause_task")
        self.assertIn("pause_task", str(error))

    def test_not_started_error_with_scheduler_state(self):
        """测试带调度器状态的未启动错误"""
        error = SchedulerNotStartedError(message="Scheduler is not in running state", operation="resume_task", scheduler_state="STOPPED")

        self.assertEqual(error.scheduler_state, "STOPPED")
        self.assertIn("STOPPED", str(error))

    def test_not_started_error_full(self):
        """测试完整的未启动错误"""
        error = SchedulerNotStartedError(
            message="Cannot execute operation on inactive scheduler",
            error_code="SCHED_001",
            operation="add_task",
            scheduler_state="PAUSED",
            details={"required_state": "RUNNING", "suggestion": "Call start() method first"},
        )

        self.assertEqual(error.operation, "add_task")
        self.assertEqual(error.scheduler_state, "PAUSED")
        self.assertEqual(error.error_code, "SCHED_001")

        error_str = str(error)
        self.assertIn("add_task", error_str)
        self.assertIn("PAUSED", error_str)


class TestTaskRegistrationError(unittest.TestCase):
    """测试TaskRegistrationError任务注册异常"""

    def test_registration_error_basic(self):
        """测试基础注册错误"""
        error = TaskRegistrationError("Task registration failed")

        self.assertIsInstance(error, ScheduleException)
        self.assertEqual(error.message, "Task registration failed")

    def test_registration_error_with_task_info(self):
        """测试带任务信息的注册错误"""
        error = TaskRegistrationError(message="Duplicate task registration", task_name="duplicate_task", task_id="task_789")

        self.assertEqual(error.task_name, "duplicate_task")
        self.assertEqual(error.task_id, "task_789")
        self.assertIn("duplicate_task", str(error))
        self.assertIn("task_789", str(error))

    def test_registration_error_with_reason(self):
        """测试带失败原因的注册错误"""
        error = TaskRegistrationError(message="Task registration failed", task_name="invalid_task", reason="Invalid task configuration")

        self.assertEqual(error.reason, "Invalid task configuration")
        self.assertIn("Invalid task configuration", str(error))

    def test_registration_error_full(self):
        """测试完整的注册错误"""
        error = TaskRegistrationError(
            message="Failed to register task in scheduler",
            error_code="REG_001",
            task_name="problematic_task",
            task_id="task_999",
            reason="Task with same ID already exists",
            details={
                "existing_task_name": "existing_task",
                "registration_time": datetime.now(timezone.utc).isoformat(),
                "conflict_resolution": "Use different task name or ID",
            },
        )

        self.assertEqual(error.task_name, "problematic_task")
        self.assertEqual(error.task_id, "task_999")
        self.assertEqual(error.reason, "Task with same ID already exists")
        self.assertEqual(error.error_code, "REG_001")

        error_str = str(error)
        self.assertIn("problematic_task", error_str)
        self.assertIn("task_999", error_str)
        self.assertIn("Task with same ID already exists", error_str)


class TestExceptionChaining(unittest.TestCase):
    """测试异常链"""

    def test_exception_chaining(self):
        """测试异常链"""
        try:
            # 模拟原始异常
            raise ValueError("Original error")
        except ValueError as original_error:
            # 包装为调度异常
            schedule_error = ScheduleExecutionError(message="Task execution failed due to value error", task_name="test_task")

            # 测试异常链
            try:
                raise schedule_error from original_error
            except ScheduleExecutionError as chained_error:
                self.assertEqual(chained_error.task_name, "test_task")
                self.assertIsInstance(chained_error.__cause__, ValueError)
                self.assertEqual(str(chained_error.__cause__), "Original error")

    def test_nested_exception_handling(self):
        """测试嵌套异常处理"""

        def failing_task():
            raise RuntimeError("Task internal error")

        def task_wrapper():
            try:
                failing_task()
            except RuntimeError as e:
                raise ScheduleExecutionError(
                    message="Task wrapper caught runtime error", task_name="wrapped_task", details={"original_error": str(e)}
                ) from e

        def scheduler_wrapper():
            try:
                task_wrapper()
            except ScheduleExecutionError as e:
                raise SchedulerNotStartedError(
                    message="Scheduler failed to execute task", operation="execute_task", details={"task_error": str(e)}
                ) from e

        # 测试完整的异常链
        with self.assertRaises(SchedulerNotStartedError) as context:
            scheduler_wrapper()

        error = context.exception
        self.assertEqual(error.operation, "execute_task")
        self.assertIsInstance(error.__cause__, ScheduleExecutionError)
        self.assertIsInstance(error.__cause__.__cause__, RuntimeError)


class TestExceptionUtilities(unittest.TestCase):
    """测试异常工具方法"""

    def test_error_details_formatting(self):
        """测试错误详情格式化"""
        details = {"timestamp": "2024-01-01T00:00:00Z", "component": "scheduler", "operation": "start", "parameters": {"max_workers": 10}}

        error = ScheduleConfigurationError(message="Configuration validation failed", details=details)

        # 检查详情是否正确存储
        self.assertEqual(error.details, details)
        self.assertIn("timestamp", error.details)
        self.assertIn("component", error.details)

    def test_error_code_consistency(self):
        """测试错误码一致性"""
        # 测试不同类型异常的错误码
        config_error = ScheduleConfigurationError("Config error", error_code="CONFIG_001")

        exec_error = ScheduleExecutionError("Execution error", error_code="EXEC_001")

        reg_error = TaskRegistrationError("Registration error", error_code="REG_001")

        # 检查错误码格式
        self.assertTrue(config_error.error_code.startswith("CONFIG_"))
        self.assertTrue(exec_error.error_code.startswith("EXEC_"))
        self.assertTrue(reg_error.error_code.startswith("REG_"))


if __name__ == "__main__":
    unittest.main()

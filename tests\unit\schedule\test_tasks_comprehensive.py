#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 任务系统综合单元测试
"""

import asyncio
import unittest

from miniboot.schedule import (
    LambdaTask,
    MethodTask,
    ScheduledConfig,
    ScheduledTask,
    SimpleTask,
    TaskFactory,
    TaskIdGenerator,
    TaskRegistry,
    TaskStatus,
    TaskType,
)


class TestTaskIdGenerator(unittest.TestCase):
    """测试TaskIdGenerator"""

    def test_generate_unique_ids(self):
        """测试生成唯一ID"""
        ids = set()

        # 生成1000个ID，应该都是唯一的
        for _ in range(1000):
            task_id = TaskIdGenerator.generate()
            self.assertNotIn(task_id, ids)
            ids.add(task_id)

        self.assertEqual(len(ids), 1000)

    def test_id_format(self):
        """测试ID格式"""
        task_id = TaskIdGenerator.generate()

        # 检查格式：task_YYYYMMDD_HHMMSS_NNNNNN
        self.assertTrue(task_id.startswith("task_"))
        parts = task_id.split("_")
        self.assertEqual(len(parts), 4)

        # 检查日期部分
        date_part = parts[1]
        self.assertEqual(len(date_part), 8)
        self.assertTrue(date_part.isdigit())

        # 检查时间部分
        time_part = parts[2]
        self.assertEqual(len(time_part), 6)
        self.assertTrue(time_part.isdigit())

        # 检查序号部分
        seq_part = parts[3]
        self.assertEqual(len(seq_part), 6)
        self.assertTrue(seq_part.isdigit())

    def test_thread_safety(self):
        """测试线程安全"""
        import threading

        ids = set()
        lock = threading.Lock()

        def generate_ids():
            for _ in range(100):
                task_id = TaskIdGenerator.generate()
                with lock:
                    ids.add(task_id)

        # 创建10个线程同时生成ID
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=generate_ids)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 应该生成1000个唯一ID
        self.assertEqual(len(ids), 1000)


class TestScheduledTask(unittest.TestCase):
    """测试ScheduledTask"""

    def test_task_creation(self):
        """测试任务创建"""

        def test_func():
            return "test_result"

        config = ScheduledConfig(fixed_rate="1s")
        task = SimpleTask(test_func, config, "test_task", TaskType.FIXED_RATE)

        self.assertEqual(task.name, "test_task")
        self.assertEqual(task.task_type, TaskType.FIXED_RATE)
        self.assertEqual(task.status, TaskStatus.CREATED)
        self.assertFalse(task.is_async)
        self.assertIsNotNone(task.task_id)
        self.assertIsNotNone(task.created_at)

    def test_async_task_creation(self):
        """测试异步任务创建"""

        async def async_func():
            await asyncio.sleep(0.01)
            return "async_result"

        config = ScheduledConfig(fixed_rate="2s")
        task = SimpleTask(async_func, config, "async_task", TaskType.FIXED_RATE)

        self.assertTrue(task.is_async)
        self.assertEqual(task.name, "async_task")

    def test_task_execution(self):
        """测试任务执行"""

        def test_func():
            return "execution_result"

        config = ScheduledConfig(fixed_rate="1s")
        task = SimpleTask(test_func, config, "test_task", TaskType.FIXED_RATE)

        # 同步执行
        result = asyncio.run(task.execute())
        self.assertEqual(result, "execution_result")

    def test_async_task_execution(self):
        """测试异步任务执行"""

        async def async_func():
            await asyncio.sleep(0.01)
            return "async_execution_result"

        config = ScheduledConfig(fixed_rate="2s")
        task = SimpleTask(async_func, config, "async_task", TaskType.FIXED_RATE)

        # 异步执行
        result = asyncio.run(task.execute())
        self.assertEqual(result, "async_execution_result")

    def test_task_status_tracking(self):
        """测试任务状态跟踪"""

        def test_func():
            return "test_result"

        config = ScheduledConfig(fixed_rate="1s")
        task = SimpleTask(test_func, config, "test_task", TaskType.FIXED_RATE)

        # 初始状态
        self.assertEqual(task.status, TaskStatus.CREATED)
        self.assertEqual(task.execution_count, 0)

        # 标记开始执行
        task.mark_execution_start()
        self.assertEqual(task.status, TaskStatus.RUNNING)
        self.assertIsNotNone(task.last_execution_time)

        # 标记执行成功
        task.mark_execution_success()
        self.assertEqual(task.status, TaskStatus.COMPLETED)
        self.assertEqual(task.execution_count, 1)

        # 标记执行失败
        error = Exception("Test error")
        task.mark_execution_failure(error)
        self.assertEqual(task.status, TaskStatus.FAILED)
        self.assertEqual(task.last_error, error)

    def test_task_validation(self):
        """测试任务验证"""
        config = ScheduledConfig(fixed_rate="1s")

        # 测试无效函数
        with self.assertRaises((TypeError, ValueError)):
            ScheduledTask(func=None, config=config, name="invalid_task", task_type=TaskType.FIXED_RATE)

        # 测试无效配置
        with self.assertRaises((TypeError, ValueError)):
            ScheduledTask(func=lambda: None, config=None, name="invalid_config_task", task_type=TaskType.FIXED_RATE)


class TestMethodTask(unittest.TestCase):
    """测试MethodTask"""

    def setUp(self):
        """设置测试环境"""

        class TestService:
            def __init__(self):
                self.call_count = 0

            def test_method(self):
                self.call_count += 1
                return f"method_result_{self.call_count}"

            async def async_method(self):
                self.call_count += 1
                await asyncio.sleep(0.01)
                return f"async_method_result_{self.call_count}"

        self.service = TestService()

    def test_method_task_creation(self):
        """测试方法任务创建"""
        config = ScheduledConfig(fixed_rate="1s")
        task = MethodTask(method=self.service.test_method, instance=self.service, config=config, name="method_task")

        self.assertEqual(task.name, "method_task")
        self.assertEqual(task.task_type, TaskType.FIXED_RATE)
        self.assertFalse(task.is_async)
        self.assertEqual(task.instance, self.service)
        self.assertEqual(task.method, self.service.test_method)

    def test_method_task_execution(self):
        """测试方法任务执行"""
        config = ScheduledConfig(fixed_rate="1s")
        task = MethodTask(method=self.service.test_method, instance=self.service, config=config, name="method_task")

        # 执行任务
        result = asyncio.run(task.execute())
        self.assertEqual(result, "method_result_1")
        self.assertEqual(self.service.call_count, 1)

        # 再次执行
        result = asyncio.run(task.execute())
        self.assertEqual(result, "method_result_2")
        self.assertEqual(self.service.call_count, 2)

    def test_async_method_task_execution(self):
        """测试异步方法任务执行"""
        config = ScheduledConfig(fixed_rate="2s")
        task = MethodTask(method=self.service.async_method, instance=self.service, config=config, name="async_method_task")

        self.assertTrue(task.is_async)

        # 执行异步任务
        result = asyncio.run(task.execute())
        self.assertEqual(result, "async_method_result_1")
        self.assertEqual(self.service.call_count, 1)


class TestLambdaTask(unittest.TestCase):
    """测试LambdaTask"""

    def test_lambda_task_creation(self):
        """测试Lambda任务创建"""

        def func():
            return "lambda_result"

        config = ScheduledConfig(cron="0 * * * *")

        task = LambdaTask(func=func, config=config, name="lambda_task")

        self.assertEqual(task.name, "lambda_task")
        self.assertEqual(task.task_type, TaskType.CRON)
        self.assertFalse(task.is_async)
        self.assertEqual(task.func, func)

    def test_lambda_task_execution(self):
        """测试Lambda任务执行"""
        call_count = 0

        def counter_func():
            nonlocal call_count
            call_count += 1
            return f"count_{call_count}"

        config = ScheduledConfig(fixed_rate="1s")
        task = LambdaTask(func=counter_func, config=config, name="counter_task")

        # 执行任务
        result = asyncio.run(task.execute())
        self.assertEqual(result, "count_1")
        self.assertEqual(call_count, 1)

        # 再次执行
        result = asyncio.run(task.execute())
        self.assertEqual(result, "count_2")
        self.assertEqual(call_count, 2)

    def test_async_lambda_task(self):
        """测试异步Lambda任务"""

        async def async_lambda():
            await asyncio.sleep(0.01)
            return "async_lambda_result"

        config = ScheduledConfig(fixed_delay="3s")
        task = LambdaTask(func=async_lambda, config=config, name="async_lambda_task")

        self.assertTrue(task.is_async)
        self.assertEqual(task.task_type, TaskType.FIXED_DELAY)

        # 执行异步Lambda任务
        result = asyncio.run(task.execute())
        self.assertEqual(result, "async_lambda_result")


class TestTaskFactory(unittest.TestCase):
    """测试TaskFactory"""

    def setUp(self):
        """设置测试环境"""

        class TestService:
            def test_method(self):
                return "factory_method_result"

        self.service = TestService()

    def test_create_method_task(self):
        """测试创建方法任务"""
        config = ScheduledConfig(fixed_rate="1s")
        task = TaskFactory.create_method_task(method=self.service.test_method, instance=self.service, config=config, name="factory_method_task")

        self.assertIsInstance(task, MethodTask)
        self.assertEqual(task.name, "factory_method_task")
        self.assertEqual(task.task_type, TaskType.FIXED_RATE)

    def test_create_lambda_task(self):
        """测试创建Lambda任务"""

        def func():
            return "factory_lambda_result"

        config = ScheduledConfig(cron="0 0 * * *")

        task = TaskFactory.create_lambda_task(func=func, config=config, name="factory_lambda_task")

        self.assertIsInstance(task, LambdaTask)
        self.assertEqual(task.name, "factory_lambda_task")
        self.assertEqual(task.task_type, TaskType.CRON)

    def test_create_from_scheduled_method(self):
        """测试从@Scheduled方法创建任务"""
        from miniboot.schedule import Scheduled

        class ScheduledService:
            @Scheduled(fixed_rate="2s")
            def scheduled_method(self):
                return "scheduled_result"

        service = ScheduledService()
        task = TaskFactory.create_from_scheduled_method(method=service.scheduled_method, instance=service)

        self.assertIsInstance(task, MethodTask)
        self.assertEqual(task.task_type, TaskType.FIXED_RATE)
        self.assertIn("scheduled_method", task.name)


class TestTaskRegistry(unittest.TestCase):
    """测试TaskRegistry"""

    def setUp(self):
        """设置测试环境"""
        self.registry = TaskRegistry()

    def test_task_registration(self):
        """测试任务注册"""
        config = ScheduledConfig(fixed_rate="1s")
        task = TaskFactory.create_lambda_task(func=lambda: "test", config=config, name="test_task")

        # 注册任务
        self.registry.register(task)

        # 验证注册
        self.assertEqual(len(self.registry.get_all_tasks()), 1)
        self.assertIn(task.task_id, self.registry._tasks)

        # 获取任务
        retrieved_task = self.registry.get_task(task.task_id)
        self.assertEqual(retrieved_task, task)

    def test_task_unregistration(self):
        """测试任务注销"""
        config = ScheduledConfig(fixed_rate="1s")
        task = TaskFactory.create_lambda_task(func=lambda: "test", config=config, name="test_task")

        # 注册并注销任务
        self.registry.register(task)
        success = self.registry.unregister(task.task_id)

        self.assertTrue(success)
        self.assertEqual(len(self.registry.get_all_tasks()), 0)
        self.assertNotIn(task.task_id, self.registry._tasks)

        # 注销不存在的任务
        success = self.registry.unregister("non_existent")
        self.assertFalse(success)

    def test_task_filtering(self):
        """测试任务过滤"""
        # 创建不同类型的任务
        fixed_rate_task = TaskFactory.create_lambda_task(func=lambda: "fixed_rate", config=ScheduledConfig(fixed_rate="1s"), name="fixed_rate_task")

        cron_task = TaskFactory.create_lambda_task(func=lambda: "cron", config=ScheduledConfig(cron="0 * * * *"), name="cron_task")

        # 注册任务
        self.registry.register(fixed_rate_task)
        self.registry.register(cron_task)

        # 按类型过滤
        fixed_rate_tasks = self.registry.get_tasks_by_type(TaskType.FIXED_RATE)
        cron_tasks = self.registry.get_tasks_by_type(TaskType.CRON)

        self.assertEqual(len(fixed_rate_tasks), 1)
        self.assertEqual(len(cron_tasks), 1)
        self.assertIn(fixed_rate_task.task_id, fixed_rate_tasks)
        self.assertIn(cron_task.task_id, cron_tasks)
        self.assertEqual(fixed_rate_tasks[fixed_rate_task.task_id], fixed_rate_task)
        self.assertEqual(cron_tasks[cron_task.task_id], cron_task)

        # 按状态过滤
        created_tasks = self.registry.get_tasks_by_status(TaskStatus.CREATED)
        self.assertEqual(len(created_tasks), 2)

    def test_registry_statistics(self):
        """测试注册表统计"""
        # 初始统计
        stats = self.registry.get_statistics()
        self.assertEqual(stats["total_tasks"], 0)
        self.assertEqual(stats["tasks_by_type"], {})
        self.assertEqual(stats["tasks_by_status"], {})

        # 添加任务后的统计
        config = ScheduledConfig(fixed_rate="1s")
        task = TaskFactory.create_lambda_task(func=lambda: "test", config=config, name="test_task")
        self.registry.register(task)

        stats = self.registry.get_statistics()
        self.assertEqual(stats["total_tasks"], 1)
        self.assertIn(TaskType.FIXED_RATE, stats["tasks_by_type"])
        self.assertIn(TaskStatus.CREATED, stats["tasks_by_status"])


if __name__ == "__main__":
    unittest.main()

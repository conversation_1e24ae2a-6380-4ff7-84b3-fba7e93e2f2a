#!/usr/bin/env python
"""
全局异常处理器模块

提供统一的异常处理和响应转换功能.

主要功能:
- GlobalExceptionHandler - 全局异常处理器
- 异常处理中间件
- 异常日志记录
- 响应包装器
"""

import asyncio
import traceback
from datetime import datetime
from typing import Any, Callable, Dict, Optional, Type, Union

from fastapi import HTTPException, Request, Response
from fastapi.responses import JSONResponse
from loguru import logger

from ..errors import (MiniBootError, get_decorator_metrics,
                      handle_context_exceptions, performance_monitor)
from .response import (ApiResponse, BusinessError, ErrorType, ExceptionHandler,
                       SystemError, ValidationError)


class GlobalExceptionHandler:
    """全局异常处理器

    统一处理各种异常类型,返回标准化响应.
    """

    def __init__(self, include_traceback: bool = False, log_exceptions: bool = True):
        """初始化全局异常处理器

        Args:
            include_traceback: 是否在响应中包含堆栈跟踪
            log_exceptions: 是否记录异常日志
        """
        self.include_traceback = include_traceback
        self.log_exceptions = log_exceptions

        # 异常处理器映射
        self._handlers: Dict[Type[Exception], Callable] = {
            # Mini-Boot框架异常
            MiniBootError: self._handle_miniboot_error,
            # 业务异常
            BusinessError: self._handle_business,
            ValidationError: self._handle_validation_error,
            SystemError: self._handle_system_error,
            # HTTP异常
            HTTPException: self._handle_http_exception,
            # 标准异常
            ValueError: self._handle_value_error,
            TypeError: self._handle_type_error,
            KeyError: self._handle_key_error,
            AttributeError: self._handle_attribute_error,
            FileNotFoundError: self._handle_file_not_found_error,
            PermissionError: self._handle_permission_error,
            TimeoutError: self._handle_timeout_error,
            ConnectionError: self._handle_connection_error,
        }

        logger.info("GlobalExceptionHandler initialized")

    @handle_context_exceptions
    @performance_monitor(slow_threshold=0.5)
    async def handle_exception(self, request: Request, exception: Exception) -> JSONResponse:
        """处理异常并返回JSON响应

        Args:
            request: HTTP请求对象
            exception: 异常对象

        Returns:
            JSON响应
        """
        request_id = getattr(request.state, "request_id", None)

        # 记录异常日志
        if self.log_exceptions:
            self._log_exception(request, exception, request_id)

        # 获取异常处理器
        handler = self._get_exception_handler(exception)

        # 处理异常
        try:
            api_response = await handler(request, exception, request_id)
        except Exception as e:
            logger.error(f"Exception handler failed: {e}")
            api_response = self._create_fallback_response(exception, request_id)

        return JSONResponse(status_code=api_response.code, content=api_response.dict())

    def _get_exception_handler(self, exception: Exception) -> Callable:
        """获取异常处理器

        Args:
            exception: 异常对象

        Returns:
            异常处理器函数
        """
        exception_type = type(exception)

        # 精确匹配
        if exception_type in self._handlers:
            return self._handlers[exception_type]

        # 继承匹配
        for exc_type, handler in self._handlers.items():
            if isinstance(exception, exc_type):
                return handler

        # 默认处理器
        return self._handle_generic_exception

    async def _handle_miniboot_error(self, request: Request, exception: MiniBootError, request_id: Optional[str]) -> ApiResponse:
        """处理Mini-Boot框架异常"""
        return ApiResponse.error(
            message=str(exception),
            error_code=getattr(exception, "error_code", "MB-ERR-001"),
            details={
                "error_type": exception.__class__.__name__,
                "module": getattr(exception, "module", "unknown"),
                "context": getattr(exception, "context", {}),
                "request_id": request_id,
            },
        )

    async def _handle_business(self, request: Request, exception: BusinessError, request_id: Optional[str]) -> ApiResponse:
        """处理业务异常"""
        return exception.to_response(request_id)

    async def _handle_validation_error(self, request: Request, exception: ValidationError, request_id: Optional[str]) -> ApiResponse:
        """处理校验异常"""
        return exception.to_response(request_id)

    async def _handle_system_error(self, request: Request, exception: SystemError, request_id: Optional[str]) -> ApiResponse:
        """处理系统异常"""
        return exception.to_response(request_id)

    async def _handle_http_exception(self, request: Request, exception: HTTPException, request_id: Optional[str]) -> ApiResponse:
        """处理HTTP异常"""
        return ApiResponse.error(
            message=exception.detail, code=exception.status_code, error_code="HTTP_ERROR", error_type=ErrorType.SYSTEM_ERROR, request_id=request_id
        )

    async def _handle_value_error(self, request: Request, exception: ValueError, request_id: Optional[str]) -> ApiResponse:
        """处理值错误"""
        return ApiResponse.validation_error(errors=[{"message": str(exception)}], message="参数值错误", request_id=request_id)

    async def _handle_type_error(self, request: Request, exception: TypeError, request_id: Optional[str]) -> ApiResponse:
        """处理类型错误"""
        return ApiResponse.validation_error(errors=[{"message": str(exception)}], message="参数类型错误", request_id=request_id)

    async def _handle_key_error(self, request: Request, exception: KeyError, request_id: Optional[str]) -> ApiResponse:
        """处理键错误"""
        return ApiResponse.validation_error(
            errors=[{"field": str(exception), "message": "缺少必需参数"}], message="缺少必需参数", request_id=request_id
        )

    async def _handle_attribute_error(self, request: Request, exception: AttributeError, request_id: Optional[str]) -> ApiResponse:
        """处理属性错误"""
        return ApiResponse.error(
            message="属性访问错误",
            code=500,
            error_code="ATTRIBUTE_ERROR",
            error_type=ErrorType.SYSTEM_ERROR,
            request_id=request_id,
            metadata={"detail": str(exception)},
        )

    async def _handle_file_not_found_error(self, request: Request, exception: FileNotFoundError, request_id: Optional[str]) -> ApiResponse:
        """处理文件未找到错误"""
        return ApiResponse.not_found(message="文件不存在", request_id=request_id)

    async def _handle_permission_error(self, request: Request, exception: PermissionError, request_id: Optional[str]) -> ApiResponse:
        """处理权限错误"""
        return ApiResponse.forbidden(message="权限不足", request_id=request_id)

    async def _handle_timeout_error(self, request: Request, exception: TimeoutError, request_id: Optional[str]) -> ApiResponse:
        """处理超时错误"""
        return ApiResponse.timeout_error(message="操作超时", request_id=request_id)

    async def _handle_connection_error(self, request: Request, exception: ConnectionError, request_id: Optional[str]) -> ApiResponse:
        """处理连接错误"""
        return ApiResponse.error(
            message="连接失败", code=503, error_code="CONNECTION_ERROR", error_type=ErrorType.NETWORK_ERROR, request_id=request_id
        )

    async def _handle_generic_exception(self, request: Request, exception: Exception, request_id: Optional[str]) -> ApiResponse:
        """处理通用异常"""
        metadata = {"exception_type": type(exception).__name__, "timestamp": datetime.now().isoformat()}

        if self.include_traceback:
            metadata["traceback"] = traceback.format_exc()

        return ApiResponse.internal_error(message=str(exception) or "未知错误", request_id=request_id)

    def _create_fallback_response(self, exception: Exception, request_id: Optional[str]) -> ApiResponse:
        """创建回退响应"""
        return ApiResponse.internal_error(message="异常处理器发生错误", request_id=request_id)

    def _log_exception(self, request: Request, exception: Exception, request_id: Optional[str]):
        """记录异常日志"""
        try:
            log_data = {
                "request_id": request_id,
                "method": request.method,
                "url": str(request.url),
                "exception_type": type(exception).__name__,
                "exception_message": str(exception),
                "timestamp": datetime.now().isoformat(),
            }

            # 根据异常类型选择日志级别
            if isinstance(exception, (BusinessError, ValidationError)):
                logger.warning(f"Business/Validation exception: {log_data}")
            elif isinstance(exception, SystemError):
                logger.error(f"System exception: {log_data}")
            else:
                logger.error(f"Unhandled exception: {log_data}")
                # 只在有活跃异常上下文时记录堆栈跟踪
                tb = traceback.format_exc()
                if tb and tb.strip() != "NoneType: None":
                    logger.error(tb)

        except Exception as e:
            logger.error(f"Failed to log exception: {e}")

    def register_handler(self, exception_type: Type[Exception], handler: Callable):
        """注册自定义异常处理器

        Args:
            exception_type: 异常类型
            handler: 处理器函数
        """
        self._handlers[exception_type] = handler
        logger.info(f"Registered custom handler for {exception_type.__name__}")

    def unregister_handler(self, exception_type: Type[Exception]):
        """注销异常处理器

        Args:
            exception_type: 异常类型
        """
        if exception_type in self._handlers:
            del self._handlers[exception_type]
            logger.info(f"Unregistered handler for {exception_type.__name__}")

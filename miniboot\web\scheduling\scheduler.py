#!/usr/bin/env python
"""
智能任务调度器

根据任务特性选择最优执行策略的智能调度器.

主要功能:
- 智能任务分类和特性分析
- 动态执行策略选择
- 性能监控和优化
- 自适应学习和策略调整
"""

import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from threading import Lock
from typing import Any, Callable, Dict, List, Optional, Union

from loguru import logger

from ..properties import AsyncOptimizationConfig


class SchedulerState(Enum):
    """调度器状态"""

    STOPPED = "stopped"
    STARTING = "starting"
    RUNNING = "running"
    STOPPING = "stopping"
    ERROR = "error"


@dataclass
class TaskRequest:
    """任务请求"""

    task_id: str
    function: Callable
    args: tuple = ()
    kwargs: dict = field(default_factory=dict)
    priority: int = 0
    timeout: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)
    timestamp: float = field(default_factory=time.time)


@dataclass
class SchedulingDecision:
    """调度决策"""

    strategy: str
    executor: str
    estimated_duration: float
    confidence: float
    reason: str
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SchedulingMetrics:
    """调度指标"""

    total_tasks: int = 0
    successful_tasks: int = 0
    failed_tasks: int = 0
    avg_execution_time: float = 0.0
    strategy_usage: Dict[str, int] = field(default_factory=dict)
    performance_improvement: float = 0.0
    last_optimization_time: Optional[float] = None


class TaskScheduler:
    """智能任务调度器

    根据任务特性和历史性能数据,智能选择最优的执行策略.
    支持同步、异步、线程池等多种执行模式.
    """

    def __init__(self, config: Optional[AsyncOptimizationConfig] = None):
        """初始化智能任务调度器

        Args:
            config: 异步优化配置
        """
        self.config = config or AsyncOptimizationConfig()

        # 状态管理
        self._state = SchedulerState.STOPPED
        self._is_initialized = False

        # 组件初始化(延迟加载)
        self._task_classifier = None
        self._performance_profiler = None
        self._benefit_evaluator = None
        self._strategy_selector = None
        self._learning_engine = None

        # 指标统计
        self._metrics = SchedulingMetrics()
        self._task_history: List[Dict[str, Any]] = []

        # 线程安全锁
        self._lock = Lock()

        # 执行器池
        self._executors: Dict[str, Any] = {}

        logger.info(f"SmartTaskScheduler initialized with config: {self.config}")

    def start(self) -> None:
        """启动调度器（同步方法）"""
        if self._state == SchedulerState.RUNNING:
            logger.warning("TaskScheduler is already running")
            return

        try:
            self._state = SchedulerState.STARTING
            # 对于同步启动，我们只是标记为运行状态
            # 实际的组件初始化在需要时进行
            self._state = SchedulerState.RUNNING
            logger.debug("TaskScheduler started")
        except Exception as e:
            self._state = SchedulerState.ERROR
            logger.error(f"Failed to start TaskScheduler: {e}")
            raise

    def stop(self) -> None:
        """停止调度器"""
        if self._state == SchedulerState.STOPPED:
            logger.warning("TaskScheduler is already stopped")
            return

        try:
            self._state = SchedulerState.STOPPING
            # 清理资源
            self._executors.clear()
            self._state = SchedulerState.STOPPED
            logger.debug("TaskScheduler stopped")
        except Exception as e:
            logger.error(f"Failed to stop TaskScheduler: {e}")
            raise

    async def initialize(self) -> None:
        """异步初始化调度器(简化方法名)"""
        if self._is_initialized:
            logger.warning("SmartTaskScheduler is already initialized")
            return

        try:
            self._state = SchedulerState.STARTING

            # 初始化组件
            await self._init_components()

            # 初始化执行器
            await self._init_executors()

            # 加载历史数据
            await self._load_historical_data()

            self._state = SchedulerState.RUNNING
            self._is_initialized = True

            logger.info("✅ SmartTaskScheduler initialized successfully")

        except Exception as e:
            self._state = SchedulerState.ERROR
            logger.error(f"❌ Failed to initialize SmartTaskScheduler: {e}")
            raise

    async def _init_components(self) -> None:
        """初始化子组件"""
        try:
            # 延迟导入避免循环依赖
            if self.config.classification_enabled:
                from .task_classifier import TaskClassifier

                self._task_classifier = TaskClassifier(self.config)

            if self.config.performance_profiling:
                from .performance_profiler import PerformanceProfiler

                self._performance_profiler = PerformanceProfiler(self.config)

            from .evaluator import BenefitEvaluator

            self._benefit_evaluator = BenefitEvaluator(self.config)

            from .selector import StrategySelector

            self._strategy_selector = StrategySelector(self.config)

            if self.config.adaptive_learning:
                from .learning import LearningEngine

                self._learning_engine = LearningEngine(self.config)

            logger.debug("All scheduler components initialized")

        except ImportError as e:
            logger.warning(f"Some scheduler components not available: {e}")
        except Exception as e:
            logger.error(f"Failed to initialize scheduler components: {e}")
            raise

    async def _init_executors(self) -> None:
        """初始化执行器池"""
        try:
            # 默认同步执行器
            self._executors["sync"] = None

            # 异步执行器
            self._executors["async"] = asyncio.get_event_loop()

            # 线程池执行器
            import concurrent.futures

            max_workers = getattr(self.config, "max_workers", 10)
            self._executors["thread_pool"] = concurrent.futures.ThreadPoolExecutor(max_workers=max_workers, thread_name_prefix="smart_scheduler")

            logger.debug(f"Initialized {len(self._executors)} executors")

        except Exception as e:
            logger.error(f"Failed to initialize executors: {e}")
            raise

    async def _load_historical_data(self) -> None:
        """加载历史性能数据"""
        try:
            # 这里可以从持久化存储加载历史数据
            # 暂时使用空实现
            logger.debug("Historical data loading completed")

        except Exception as e:
            logger.warning(f"Failed to load historical data: {e}")

    async def schedule_task(self, task_request: TaskRequest) -> Any:
        """调度任务执行

        Args:
            task_request: 任务请求

        Returns:
            任务执行结果
        """
        if not self._is_initialized or self._state != SchedulerState.RUNNING:
            raise RuntimeError("SmartTaskScheduler is not running")

        start_time = time.time()

        try:
            # 1. 任务分类和特性分析
            task_characteristics = await self._analyze_task(task_request)

            # 2. 制定调度决策
            decision = await self._make_scheduling_decision(task_request, task_characteristics)

            # 3. 执行任务
            result = await self._execute_task(task_request, decision)

            # 4. 记录执行结果
            execution_time = time.time() - start_time
            await self._record_execution(task_request, decision, execution_time, True)

            return result

        except Exception as e:
            execution_time = time.time() - start_time
            await self._record_execution(task_request, None, execution_time, False, str(e))
            raise

    async def _analyze_task(self, task_request: TaskRequest) -> Dict[str, Any]:
        """分析任务特性"""
        characteristics = {
            "function_name": task_request.function.__name__,
            "is_coroutine": asyncio.iscoroutinefunction(task_request.function),
            "arg_count": len(task_request.args),
            "kwarg_count": len(task_request.kwargs),
            "priority": task_request.priority,
            "has_timeout": task_request.timeout is not None,
            "estimated_complexity": "medium",  # 默认值
        }

        # 使用任务分类器进行详细分析
        if self._task_classifier:
            try:
                classification = await self._task_classifier.classify_task(task_request)
                characteristics.update(classification)
            except Exception as e:
                logger.warning(f"Task classification failed: {e}")

        return characteristics

    async def _make_scheduling_decision(self, task_request: TaskRequest, characteristics: Dict[str, Any]) -> SchedulingDecision:
        """制定调度决策"""
        try:
            # 使用策略选择器
            if self._strategy_selector:
                decision = await self._strategy_selector.select_strategy(task_request, characteristics)
                return decision

            # 回退到简单策略
            if characteristics.get("is_coroutine", False):
                strategy = "async"
                executor = "async"
            elif characteristics.get("estimated_complexity") == "high":
                strategy = "thread_pool"
                executor = "thread_pool"
            else:
                strategy = "sync"
                executor = "sync"

            return SchedulingDecision(strategy=strategy, executor=executor, estimated_duration=0.1, confidence=0.5, reason="fallback_strategy")

        except Exception as e:
            logger.error(f"Failed to make scheduling decision: {e}")
            # 最安全的回退策略
            return SchedulingDecision(strategy="sync", executor="sync", estimated_duration=0.1, confidence=0.1, reason=f"error_fallback: {e}")

    async def _execute_task(self, task_request: TaskRequest, decision: SchedulingDecision) -> Any:
        """执行任务"""
        executor = self._executors.get(decision.executor)
        strategy_name = decision.strategy if isinstance(decision.strategy, str) else decision.strategy.value

        if strategy_name == "sync":
            # 同步执行
            return task_request.function(*task_request.args, **task_request.kwargs)

        elif strategy_name == "async":
            # 异步执行
            if asyncio.iscoroutinefunction(task_request.function):
                return await task_request.function(*task_request.args, **task_request.kwargs)
            else:
                # 同步函数在异步环境中执行
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(None, lambda: task_request.function(*task_request.args, **task_request.kwargs))

        elif strategy_name == "thread_pool":
            # 线程池执行
            if executor:
                loop = asyncio.get_event_loop()
                return await loop.run_in_executor(executor, lambda: task_request.function(*task_request.args, **task_request.kwargs))
            else:
                # 回退到同步执行
                return task_request.function(*task_request.args, **task_request.kwargs)

        else:
            raise ValueError(f"Unknown execution strategy: {strategy_name}")

    async def _record_execution(
        self, task_request: TaskRequest, decision: Optional[SchedulingDecision], execution_time: float, success: bool, error: Optional[str] = None
    ) -> None:
        """记录执行结果"""
        with self._lock:
            self._metrics.total_tasks += 1

            if success:
                self._metrics.successful_tasks += 1
            else:
                self._metrics.failed_tasks += 1

            # 更新平均执行时间
            total_successful = self._metrics.successful_tasks
            if total_successful > 0:
                self._metrics.avg_execution_time = (self._metrics.avg_execution_time * (total_successful - 1) + execution_time) / total_successful

            # 更新策略使用统计
            if decision:
                strategy = decision.strategy
                self._metrics.strategy_usage[strategy] = self._metrics.strategy_usage.get(strategy, 0) + 1

            # 记录历史数据
            execution_record = {
                "task_id": task_request.task_id,
                "function_name": task_request.function.__name__,
                "strategy": decision.strategy if decision else "unknown",
                "execution_time": execution_time,
                "success": success,
                "error": error,
                "timestamp": time.time(),
            }

            self._task_history.append(execution_record)

            # 限制历史记录大小
            max_history = getattr(self.config, "performance_history_size", 1000)
            if len(self._task_history) > max_history:
                self._task_history.pop(0)

    async def cleanup_async(self) -> None:
        """异步清理资源"""
        try:
            self._state = SchedulerState.STOPPING

            # 关闭执行器
            for name, executor in self._executors.items():
                if name == "thread_pool" and executor:
                    executor.shutdown(wait=True)

            # 清理组件
            if self._learning_engine:
                await self._learning_engine.save_model()

            self._state = SchedulerState.STOPPED
            self._is_initialized = False

            logger.info("✅ SmartTaskScheduler cleaned up successfully")

        except Exception as e:
            logger.error(f"❌ Error during SmartTaskScheduler cleanup: {e}")

    def get_metrics(self) -> SchedulingMetrics:
        """获取调度指标"""
        with self._lock:
            return SchedulingMetrics(**self._metrics.__dict__)

    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        metrics = self.get_metrics()

        success_rate = 0.0
        if metrics.total_tasks > 0:
            success_rate = (metrics.successful_tasks / metrics.total_tasks) * 100

        return {
            "state": self._state.value,
            "initialized": self._is_initialized,
            "total_tasks": metrics.total_tasks,
            "success_rate": round(success_rate, 2),
            "avg_execution_time": round(metrics.avg_execution_time, 4),
            "strategy_usage": metrics.strategy_usage,
            "performance_improvement": round(metrics.performance_improvement, 2),
            "components": {
                "task_classifier": self._task_classifier is not None,
                "performance_profiler": self._performance_profiler is not None,
                "benefit_evaluator": self._benefit_evaluator is not None,
                "strategy_selector": self._strategy_selector is not None,
                "learning_engine": self._learning_engine is not None,
            },
        }

    def is_running(self) -> bool:
        """检查调度器是否运行中"""
        return self._state == SchedulerState.RUNNING and self._is_initialized

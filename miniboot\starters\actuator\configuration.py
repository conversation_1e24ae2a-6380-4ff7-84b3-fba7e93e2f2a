#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Actuator Starter 主自动配置类

负责 Actuator Starter 的核心配置和组件装配。
基于条件注解实现智能化的组件加载，符合新的架构设计。

主要功能：
- 注册核心 Bean：ActuatorContext、EndpointRegistry
- 条件化配置：基于属性配置启用/禁用功能
- 集成管理：协调各个模块的自动配置
"""

from typing import Optional

from loguru import logger

from miniboot.annotations import (Autowired, Bean, ConditionalOnProperty,
                                  Configuration)
from miniboot.annotations.config import ConfigurationProperties
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
# 导入监控接口
from miniboot.monitoring.interfaces import EndpointProvider, MonitoringContext

from .properties import ActuatorProperties


@ConditionalOnProperty(name="miniboot.starters.actuator.enabled", match_if_missing=True)
class ActuatorStarterAutoConfiguration(AutoConfiguration):
    """Actuator Starter 主自动配置类

    负责配置 Actuator Starter 的核心组件，包括：
    - ActuatorContext：纯监控上下文
    - EndpointRegistry：端点注册表
    - ActuatorProperties：配置属性

    采用条件化配置，支持灵活的功能启用/禁用。
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="actuator-starter-auto-configuration",
            description="Actuator Starter 主自动配置",
            priority=100,  # 高优先级，作为基础设施
            auto_configure_after=[],  # 不依赖其他配置
        )

    @Bean
    @ConfigurationProperties(prefix="starters.actuator")
    def actuator_properties(self) -> ActuatorProperties:
        """创建 Actuator 配置属性 Bean

        Returns:
            ActuatorProperties: Actuator 配置属性实例
        """
        logger.debug("Creating ActuatorProperties bean")
        return ActuatorProperties()

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.context.enabled", match_if_missing=True)
    def monitoring_context(self, actuator_properties: ActuatorProperties) -> MonitoringContext:
        """创建监控上下文 Bean - 返回接口类型

        Args:
            actuator_properties: Actuator 配置属性

        Returns:
            MonitoringContext: 监控上下文接口实现
        """
        from miniboot.starters.actuator.context import ActuatorContext

        # ActuatorContext 实现 MonitoringContext 接口
        context = ActuatorContext(properties=actuator_properties, auto_load_config=False)

        logger.info(f"Created MonitoringContext implementation: {context.__class__.__name__} "
                   f"with {len(context.get_endpoints())} endpoints")
        return context

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.endpoints.enabled", match_if_missing=True)
    def endpoint_registry(self) -> "EndpointRegistry":
        """创建端点注册表 Bean

        Returns:
            EndpointRegistry: 端点注册表实例
        """
        from miniboot.starters.actuator.endpoints import EndpointRegistry

        registry = EndpointRegistry()
        logger.debug("Created EndpointRegistry bean")
        return registry

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.metrics.enabled", match_if_missing=True)
    def metrics_registry(self) -> "MetricsRegistry":
        """创建指标注册中心 Bean

        Returns:
            MetricsRegistry: 指标注册中心实例
        """
        from .endpoints.metrics import MetricsRegistry

        registry = MetricsRegistry()
        logger.debug("Created MetricsRegistry bean")
        return registry

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.health.enabled", match_if_missing=True)
    def health_endpoint_provider(self) -> EndpointProvider:
        """创建健康检查端点提供者 Bean - 返回接口类型

        Returns:
            EndpointProvider: 健康检查端点提供者接口实现
        """
        from .endpoints.health import HealthEndpoint

        endpoint = HealthEndpoint()
        logger.debug("Created HealthEndpoint as EndpointProvider")
        return endpoint

    @Bean
    @ConditionalOnProperty(name="miniboot.starters.actuator.info.enabled", match_if_missing=True)
    def info_endpoint_provider(self) -> EndpointProvider:
        """创建信息端点提供者 Bean - 返回接口类型

        Returns:
            EndpointProvider: 信息端点提供者接口实现
        """
        from .endpoints.info import InfoEndpoint

        endpoint = InfoEndpoint()
        logger.debug("Created InfoEndpoint as EndpointProvider")
        return endpoint

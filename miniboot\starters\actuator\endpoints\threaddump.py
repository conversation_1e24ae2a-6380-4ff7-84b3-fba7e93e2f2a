#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 线程转储端点

提供线程转储端点,支持线程信息收集.
"""

import threading
from datetime import datetime
from typing import Any, Dict


class ThreadCollector:
    """线程收集器"""

    def collect_thread_dump(self) -> Dict[str, Any]:
        """收集线程转储信息"""
        import threading
        import time

        threads = []
        for thread in threading.enumerate():
            thread_info = {"id": thread.ident, "name": thread.name, "daemon": thread.daemon, "alive": thread.is_alive()}
            threads.append(thread_info)

        return {"timestamp": datetime.now().isoformat(), "total_threads": len(threads), "thread_dumps": threads}


class ThreadDumpEndpoint:
    """线程转储端点"""

    def __init__(self):
        self.collector = ThreadCollector()
        self.enabled = True
        self.sensitive = False
        self.endpoint_id = "threaddump"
        self.id = "threaddump"  # 添加 id 属性

    async def threaddump(self) -> Dict[str, Any]:
        """获取线程转储"""
        return self.collector.collect_thread_dump()

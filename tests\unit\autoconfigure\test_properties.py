#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Starter配置属性基类测试
"""

from dataclasses import dataclass

import pytest

from miniboot.annotations import ConfigurationProperties
from miniboot.autoconfigure.properties import StarterProperties


@ConfigurationProperties(prefix="test.app")
@dataclass
class MockAppProperties(StarterProperties):
    """测试应用配置属性"""

    timeout: int = 30
    max_retries: int = 3
    debug_mode: bool = False


@dataclass
class MockSimpleProperties(StarterProperties):
    """简单配置属性（无注解）"""

    value: str = "default"


class TestStarterProperties:
    """Starter配置属性基类测试"""

    def test_is_enabled_default_true(self):
        """测试默认启用状态"""
        props = MockAppProperties()
        assert props.is_enabled() is True
        assert props.enabled is True

    def test_is_enabled_set_false(self):
        """测试设置为禁用状态"""
        props = MockAppProperties(enabled=False)
        assert props.is_enabled() is False
        assert props.enabled is False

    def test_get_prefix_with_annotation(self):
        """测试从注解获取配置前缀"""
        # 模拟@ConfigurationProperties注解
        MockAppProperties._configuration_prefix = "test.app"

        props = MockAppProperties()
        assert props.get_prefix() == "test.app"

    def test_get_prefix_without_annotation(self):
        """测试无注解时从类名推断前缀"""
        props = MockSimpleProperties()
        # MockSimpleProperties -> mock_simple
        assert props.get_prefix() == "mock_simple"

    def test_get_prefix_complex_class_name(self):
        """测试复杂类名的前缀推断"""

        @dataclass
        class DatabaseConnectionProperties(StarterProperties):
            pass

        props = DatabaseConnectionProperties()
        # DatabaseConnectionProperties -> database_connection
        assert props.get_prefix() == "database_connection"

    def test_validate_success(self):
        """测试验证成功"""
        props = MockAppProperties()
        # 应该不抛出异常
        props.validate()

    def test_validate_custom_validation(self):
        """测试自定义验证逻辑"""

        @dataclass
        class CustomProperties(StarterProperties):
            min_value: int = 0

            def validate(self):
                super().validate()
                if self.min_value < 0:
                    raise ValueError("min_value must be non-negative")

        # 测试验证成功
        props = CustomProperties(min_value=5)
        props.validate()  # 应该不抛出异常

        # 测试验证失败
        props_invalid = CustomProperties(min_value=-1)
        with pytest.raises(ValueError, match="min_value must be non-negative"):
            props_invalid.validate()

    def test_to_dict(self):
        """测试转换为字典"""
        props = MockAppProperties(enabled=True, timeout=60, max_retries=5, debug_mode=True)

        expected_dict = {"enabled": True, "timeout": 60, "max_retries": 5, "debug_mode": True}

        assert props.to_dict() == expected_dict

    def test_update_from_dict(self):
        """测试从字典更新属性"""
        props = MockAppProperties()

        update_data = {"timeout": 120, "debug_mode": True, "max_retries": 10}

        props.update_from_dict(update_data)

        assert props.timeout == 120
        assert props.debug_mode is True
        assert props.max_retries == 10
        assert props.enabled is True  # 未更新的属性保持默认值

    def test_update_from_dict_ignore_unknown(self):
        """测试从字典更新时忽略未知属性"""
        props = MockAppProperties()

        update_data = {"timeout": 90, "unknown_property": "should_be_ignored"}

        props.update_from_dict(update_data)

        assert props.timeout == 90
        assert not hasattr(props, "unknown_property")

    def test_get_property_value(self):
        """测试获取属性值"""
        props = MockAppProperties(timeout=45)

        assert props.get_property_value("timeout") == 45
        assert props.get_property_value("enabled") is True
        assert props.get_property_value("nonexistent", "default") == "default"
        assert props.get_property_value("nonexistent") is None

    def test_set_property_value(self):
        """测试设置属性值"""
        props = MockAppProperties()

        props.set_property_value("timeout", 100)
        assert props.timeout == 100

        props.set_property_value("debug_mode", True)
        assert props.debug_mode is True

    def test_set_property_value_nonexistent(self):
        """测试设置不存在的属性值"""
        props = MockAppProperties()

        with pytest.raises(AttributeError, match="Property 'nonexistent' does not exist"):
            props.set_property_value("nonexistent", "value")

    def test_get_non_default_properties(self):
        """测试获取非默认值的属性"""
        props = MockAppProperties(
            timeout=60,  # 非默认值
            debug_mode=True,  # 非默认值
            max_retries=3,  # 默认值
        )

        non_default = props.get_non_default_properties()

        expected = {"timeout": 60, "debug_mode": True}

        assert non_default == expected

    def test_merge_properties_same_type(self):
        """测试合并相同类型的属性"""
        props1 = MockAppProperties(timeout=30, debug_mode=False)
        props2 = MockAppProperties(timeout=60, max_retries=5, enabled=False)

        props1.merge_properties(props2)

        assert props1.timeout == 60
        assert props1.max_retries == 5
        assert props1.enabled is False
        assert props1.debug_mode is False  # 保持原值

    def test_merge_properties_different_type(self):
        """测试合并不同类型的属性时抛出异常"""
        props1 = MockAppProperties()
        props2 = MockSimpleProperties()

        with pytest.raises(TypeError, match="Cannot merge properties of different types"):
            props1.merge_properties(props2)

    def test_camel_to_snake_conversion(self):
        """测试驼峰命名转下划线命名"""
        props = MockAppProperties()

        assert props._camel_to_snake("TestApp") == "test_app"
        assert props._camel_to_snake("DatabaseConnection") == "database_connection"
        assert props._camel_to_snake("HTTPClient") == "http_client"
        assert props._camel_to_snake("simple") == "simple"
        assert props._camel_to_snake("XMLParser") == "xml_parser"

    def test_str_representation(self):
        """测试字符串表示"""
        props = MockAppProperties(timeout=45, debug_mode=True)
        str_repr = str(props)

        assert "MockAppProperties" in str_repr
        assert "timeout" in str_repr
        assert "45" in str_repr
        assert "debug_mode" in str_repr
        assert "True" in str_repr

    def test_repr_representation(self):
        """测试详细字符串表示"""
        props = MockAppProperties(timeout=45)
        repr_str = repr(props)

        # repr应该与str相同，都包含类名和属性信息
        assert "MockAppProperties" in repr_str
        assert "timeout" in repr_str
        assert "45" in repr_str

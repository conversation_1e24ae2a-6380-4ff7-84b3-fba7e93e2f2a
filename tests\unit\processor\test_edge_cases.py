#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean处理器模块边界条件和异常处理测试
"""

import unittest
from unittest.mock import Mock

from miniboot.errors.processor import BeanProcessingError
from miniboot.processor.autowired import AutowiredAnnotationProcessor
from miniboot.processor.base import BeanPostProcessor
from miniboot.processor.configuration import ConfigurationPropertiesProcessor
from miniboot.processor.event import EventListenerProcessor
from miniboot.processor.lifecycle import LifecycleAnnotationProcessor
from miniboot.processor.manager import (BeanPostProcessorManager,
                                        ProcessorConfig, ProcessorState)
from miniboot.processor.registry import BeanPostProcessorRegistry
from miniboot.processor.schedule import ScheduledAnnotationProcessor
from miniboot.processor.value import ValueAnnotationProcessor


class EdgeCaseBean:
    """边界条件测试Bean"""

    def __init__(self):
        self.value = None
        self.repository = None
        self.config_value = None


class TestProcessorEdgeCases(unittest.TestCase):
    """处理器边界条件测试类"""

    def setUp(self):
        """测试前置设置"""
        self.bean = EdgeCaseBean()
        self.bean_name = "edgeCaseBean"

    def test_autowired_processor_with_none_bean_factory(self):
        """测试自动装配处理器没有Bean工厂的情况"""
        processor = AutowiredAnnotationProcessor()  # 没有设置Bean工厂

        # 应该不支持任何Bean
        self.assertFalse(processor.supports(self.bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_before_initialization(self.bean, self.bean_name)
        self.assertIs(result, self.bean)

    def test_value_processor_with_none_environment(self):
        """测试值注入处理器没有环境的情况"""
        processor = ValueAnnotationProcessor()  # 没有设置环境

        # 应该不支持任何Bean
        self.assertFalse(processor.supports(self.bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_before_initialization(self.bean, self.bean_name)
        self.assertIs(result, self.bean)

    def test_config_processor_with_none_environment(self):
        """测试配置属性处理器没有环境的情况"""
        processor = ConfigurationPropertiesProcessor()  # 没有设置环境

        # 应该不支持任何Bean
        self.assertFalse(processor.supports(self.bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_before_initialization(self.bean, self.bean_name)
        self.assertIs(result, self.bean)

    def test_event_processor_with_none_publisher(self):
        """测试事件监听处理器没有发布器的情况"""
        processor = EventListenerProcessor()  # 没有设置事件发布器

        # 应该不支持任何Bean
        self.assertFalse(processor.supports(self.bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_after_initialization(self.bean, self.bean_name)
        self.assertIs(result, self.bean)

    def test_scheduled_processor_with_none_scheduler(self):
        """测试定时任务处理器没有调度器的情况"""
        processor = ScheduledAnnotationProcessor()  # 没有设置任务调度器

        # 应该不支持任何Bean
        self.assertFalse(processor.supports(self.bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_after_initialization(self.bean, self.bean_name)
        self.assertIs(result, self.bean)

    def test_processor_with_empty_bean_name(self):
        """测试处理器处理空Bean名称"""
        processor = LifecycleAnnotationProcessor()

        # 空字符串Bean名称
        result = processor.post_process_after_initialization(self.bean, "")
        self.assertIs(result, self.bean)

        # None Bean名称
        result = processor.post_process_after_initialization(self.bean, None)
        self.assertIs(result, self.bean)

    def test_processor_with_special_characters_in_bean_name(self):
        """测试处理器处理特殊字符Bean名称"""
        processor = LifecycleAnnotationProcessor()

        special_names = [
            "bean-with-dashes",
            "bean_with_underscores",
            "bean.with.dots",
            "bean@with@symbols",
            "bean with spaces",
            "bean123",
            "123bean",
            "中文Bean名称",
        ]

        for name in special_names:
            with self.subTest(bean_name=name):
                result = processor.post_process_after_initialization(self.bean, name)
                self.assertIs(result, self.bean)

    def test_processor_manager_with_invalid_processor_names(self):
        """测试处理器管理器处理无效处理器名称"""
        manager = BeanPostProcessorManager()

        # 测试不存在的处理器名称
        self.assertFalse(manager.enable_processor("NonExistentProcessor"))
        self.assertFalse(manager.disable_processor("NonExistentProcessor"))
        self.assertIsNone(manager.get_processor_config("NonExistentProcessor"))
        self.assertIsNone(manager.get_processor_state("NonExistentProcessor"))
        self.assertFalse(manager.reset_processor_metrics("NonExistentProcessor"))

        # 测试空字符串和None
        self.assertFalse(manager.enable_processor(""))
        self.assertFalse(manager.enable_processor(None))

    def test_processor_manager_with_extreme_config_values(self):
        """测试处理器管理器处理极端配置值"""
        manager = BeanPostProcessorManager()

        # 极端配置值
        extreme_config = ProcessorConfig(
            timeout_seconds=0.001,  # 极小超时
            retry_count=1000,  # 极大重试次数
            retry_delay=0.0,  # 零延迟
            error_threshold=0,  # 零错误阈值
            circuit_breaker_timeout=86400.0,  # 24小时超时
        )

        # 这些配置应该被接受而不抛出异常
        processor = Mock(spec=BeanPostProcessor)
        processor.__class__.__name__ = "ExtremeProcessor"

        manager.register_processor(processor, extreme_config)

        stored_config = manager.get_processor_config("ExtremeProcessor")
        self.assertEqual(stored_config.timeout_seconds, 0.001)
        self.assertEqual(stored_config.retry_count, 1000)
        self.assertEqual(stored_config.error_threshold, 0)

    def test_processor_registry_thread_safety_edge_cases(self):
        """测试处理器注册表线程安全边界条件"""
        registry = BeanPostProcessorRegistry()

        # 创建不同类型的处理器
        processor1_class = type(
            "TestProcessor1",
            (BeanPostProcessor,),
            {
                "get_order": lambda _self: 100,
                "supports": lambda _self, _bean, _bean_name: True,
                "post_process_before_initialization": lambda _self, _bean, _bean_name: _bean,
                "post_process_after_initialization": lambda _self, _bean, _bean_name: _bean,
            },
        )
        processor1 = processor1_class()

        processor2_class = type(
            "TestProcessor2",
            (BeanPostProcessor,),
            {
                "get_order": lambda _self: 200,
                "supports": lambda _self, _bean, _bean_name: True,
                "post_process_before_initialization": lambda _self, _bean, _bean_name: _bean,
                "post_process_after_initialization": lambda _self, _bean, _bean_name: _bean,
            },
        )
        processor2 = processor2_class()

        # 两个不同类型的处理器都应该成功注册
        registry.register_processor(processor1)
        self.assertEqual(registry.get_processor_count(), 1)

        registry.register_processor(processor2)
        self.assertEqual(registry.get_processor_count(), 2)

    def test_processor_order_boundary_values(self):
        """测试处理器执行顺序边界值"""
        registry = BeanPostProcessorRegistry()

        # 测试极端顺序值
        extreme_orders = [
            -2147483648,  # 最小整数
            2147483647,  # 最大整数
            0,  # 零
            -1,  # 负数
            1,  # 正数
        ]

        processors = []
        for i, order in enumerate(extreme_orders):
            processor = Mock(spec=BeanPostProcessor)
            processor.__class__.__name__ = f"OrderProcessor{i}"
            processor.get_order.return_value = order
            processor.supports.return_value = True
            processor.post_process_before_initialization.return_value = self.bean
            processor.post_process_after_initialization.return_value = self.bean

            processors.append(processor)
            registry.register_processor(processor)

        # 验证所有处理器都被注册
        self.assertEqual(registry.get_processor_count(), len(extreme_orders))

        # 验证处理器按顺序执行
        result = registry.apply_before_initialization(self.bean, self.bean_name)
        self.assertIs(result, self.bean)

    def test_bean_processing_error_edge_cases(self):
        """测试Bean处理错误的边界条件"""
        # 测试最小错误信息
        error1 = BeanProcessingError("")
        self.assertEqual(str(error1), "")

        # 测试空字符串消息
        error2 = BeanProcessingError("")
        self.assertEqual(str(error2), "")

        # 测试极长错误信息
        long_message = "x" * 10000
        error3 = BeanProcessingError(long_message)
        self.assertEqual(str(error3), long_message)

        # 测试特殊字符
        special_message = "错误信息\n\t\r包含特殊字符"
        error4 = BeanProcessingError(special_message)
        self.assertEqual(str(error4), special_message)

    def test_processor_supports_method_edge_cases(self):
        """测试处理器supports方法的边界条件"""
        processor = LifecycleAnnotationProcessor()

        # 测试各种类型的对象
        test_objects = [
            None,
            "",
            0,
            [],
            {},
            set(),
            lambda: None,
            type,
            object(),
            Exception(),
        ]

        for obj in test_objects:
            with self.subTest(obj=obj):
                # 不应该抛出异常
                result = processor.supports(obj, self.bean_name)
                self.assertIsInstance(result, bool)

    def test_processor_manager_circuit_breaker_edge_cases(self):
        """测试处理器管理器熔断器边界条件"""
        manager = BeanPostProcessorManager()

        # 配置熔断器
        config = ProcessorConfig(
            circuit_breaker_enabled=True,
            error_threshold=1,  # 极低阈值
            circuit_breaker_timeout=0.1,  # 极短超时
        )

        # 创建会失败的处理器
        failing_processor = Mock(spec=BeanPostProcessor)
        failing_processor.__class__.__name__ = "FailingProcessor"
        failing_processor.supports.return_value = True
        failing_processor.post_process_before_initialization.side_effect = RuntimeError("Test error")

        manager.register_processor(failing_processor, config)

        # 触发熔断器
        with self.assertRaises(BeanProcessingError):
            manager.apply_before_initialization(self.bean, self.bean_name)

        # 验证熔断器状态
        state = manager.get_processor_state("FailingProcessor")
        self.assertEqual(state, ProcessorState.CIRCUIT_OPEN)

        # 等待熔断器超时
        import time

        time.sleep(0.2)

        # 熔断器应该重置
        # 注意：这里需要再次调用来触发状态检查
        import contextlib

        with contextlib.suppress(Exception):
            manager.apply_before_initialization(self.bean, self.bean_name)


class TestProcessorPerformance(unittest.TestCase):
    """处理器性能测试类"""

    def test_processor_registry_performance_with_many_processors(self):
        """测试处理器注册表在大量处理器下的性能"""
        registry = BeanPostProcessorRegistry()

        # 注册大量处理器
        processor_count = 100
        for i in range(processor_count):
            processor = Mock(spec=BeanPostProcessor)
            processor.__class__.__name__ = f"PerformanceProcessor{i}"
            processor.get_order.return_value = i
            processor.supports.return_value = True
            processor.post_process_before_initialization.return_value = EdgeCaseBean()
            processor.post_process_after_initialization.return_value = EdgeCaseBean()

            registry.register_processor(processor)

        # 验证所有处理器都被注册
        self.assertEqual(registry.get_processor_count(), processor_count)

        # 测试处理性能
        bean = EdgeCaseBean()

        import time

        start_time = time.time()

        # 执行多次处理
        for _ in range(10):
            registry.apply_before_initialization(bean, "performanceBean")
            registry.apply_after_initialization(bean, "performanceBean")

        end_time = time.time()
        execution_time = end_time - start_time

        # 验证执行时间合理（应该在1秒内完成）
        self.assertLess(execution_time, 1.0)

    def test_processor_manager_metrics_performance(self):
        """测试处理器管理器指标收集性能"""
        manager = BeanPostProcessorManager()

        # 注册多个处理器
        for i in range(10):
            # 创建不同类型的处理器以避免重复注册
            processor_class = type(
                f"MetricsProcessor{i}",
                (BeanPostProcessor,),
                {
                    "get_order": lambda _self, i=i: i * 10,
                    "supports": lambda _self, _bean, _bean_name: True,
                    "post_process_before_initialization": lambda _self, _bean, _bean_name: bean,
                    "post_process_after_initialization": lambda _self, _bean, _bean_name: bean,
                },
            )
            processor = processor_class()

            manager.register_processor(processor)

        bean = EdgeCaseBean()

        # 执行大量处理以生成指标
        for _ in range(100):
            manager.apply_before_initialization(bean, "metricsBean")
            manager.apply_after_initialization(bean, "metricsBean")

        # 验证指标收集
        all_metrics = manager.get_all_processor_metrics()
        self.assertEqual(len(all_metrics), 10)

        for metrics in all_metrics.values():
            self.assertEqual(metrics.total_executions, 200)  # 100次 * 2个方法
            self.assertGreaterEqual(metrics.total_execution_time, 0)  # 可能为0（执行太快）
            self.assertGreaterEqual(metrics.average_execution_time, 0)  # 可能为0
            self.assertEqual(metrics.error_count, 0)


if __name__ == "__main__":
    unittest.main()

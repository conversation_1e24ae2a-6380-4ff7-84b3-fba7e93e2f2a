#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WebAutoConfiguration 使用示例

演示如何使用 Actuator Web 自动配置类来启用 Web 集成功能。
这个示例展示了条件化配置的工作原理和 Bean 的自动注册。
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到 Python 路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from miniboot.starters.actuator.autoconfigure.web import WebAutoConfiguration
from miniboot.starters.actuator.properties import ActuatorProperties


def main():
    """主函数：演示 WebAutoConfiguration 的使用"""
    
    print("🚀 WebAutoConfiguration 使用示例")
    print("=" * 50)
    
    # 1. 创建配置属性
    print("\n📋 1. 创建 Actuator 配置属性")
    actuator_properties = ActuatorProperties()
    actuator_properties.enabled = True
    actuator_properties.web.enabled = True
    actuator_properties.web.base_path = "/actuator"
    actuator_properties.web.endpoints.health = True
    actuator_properties.web.endpoints.info = True
    actuator_properties.web.endpoints.metrics = True
    
    print(f"   ✅ Actuator 启用: {actuator_properties.enabled}")
    print(f"   ✅ Web 集成启用: {actuator_properties.web.enabled}")
    print(f"   ✅ 基础路径: {actuator_properties.web.base_path}")
    print(f"   ✅ 启用端点: health={actuator_properties.web.endpoints.health}, "
          f"info={actuator_properties.web.endpoints.info}, "
          f"metrics={actuator_properties.web.endpoints.metrics}")
    
    # 2. 创建自动配置实例
    print("\n🔧 2. 创建 WebAutoConfiguration 实例")
    web_config = WebAutoConfiguration()
    print("   ✅ WebAutoConfiguration 实例创建成功")
    
    # 3. 创建 WebIntegrationChecker Bean
    print("\n🔍 3. 创建 WebIntegrationChecker Bean")
    try:
        checker = web_config.web_integration_checker()
        print("   ✅ WebIntegrationChecker Bean 创建成功")
        
        # 获取集成状态
        status = checker.get_integration_status()
        print(f"   📊 FastAPI 可用: {status.fastapi_available}")
        print(f"   📊 Web 模块启用: {status.web_module_enabled}")
        print(f"   📊 应该集成: {status.should_integrate}")
        print(f"   📊 集成原因: {status.integration_reason}")
        
    except Exception as e:
        print(f"   ❌ WebIntegrationChecker 创建失败: {e}")
        return
    
    # 4. 创建 ActuatorRouteRegistrar Bean（如果条件满足）
    print("\n🛣️  4. 创建 ActuatorRouteRegistrar Bean")
    try:
        registrar = web_config.actuator_route_registrar(actuator_properties, checker)
        
        if registrar:
            print("   ✅ ActuatorRouteRegistrar Bean 创建成功")
            print(f"   📊 基础路径: {registrar._base_path}")
            print(f"   📊 ActuatorContext 配置: {registrar._actuator_context is not None}")
            
            # 获取路由状态
            route_status = registrar.get_route_status()
            print(f"   📊 总端点数: {route_status['total_endpoints']}")
            print(f"   📊 总路由数: {route_status['total_routes']}")
            print(f"   📊 FastAPI 可用: {route_status['fastapi_available']}")
            print(f"   📊 应用已配置: {route_status['app_configured']}")
            print(f"   📊 上下文已配置: {route_status['context_configured']}")
            
        else:
            print("   ⚠️  ActuatorRouteRegistrar Bean 未创建（条件不满足）")
            
    except Exception as e:
        print(f"   ❌ ActuatorRouteRegistrar 创建失败: {e}")
    
    # 5. 测试中间件配置（占位符）
    print("\n🔌 5. 测试中间件配置")
    try:
        middleware = web_config.actuator_web_middleware()
        if middleware:
            print("   ✅ ActuatorWebMiddleware Bean 创建成功")
        else:
            print("   ℹ️  ActuatorWebMiddleware 未实现（返回 None）")
    except Exception as e:
        print(f"   ❌ ActuatorWebMiddleware 创建失败: {e}")
    
    # 6. 演示条件化配置
    print("\n⚙️  6. 演示条件化配置")
    print("   📝 当前配置条件:")
    print("      - @ConditionalOnClass(name='fastapi.FastAPI'): FastAPI 必须可用")
    print("      - @ConditionalOnProperty(name='starters.web.enabled', match_if_missing=False): Web 模块必须启用")
    print("      - @ConditionalOnProperty(name='starters.actuator.web.enabled', match_if_missing=True): Actuator Web 必须启用")
    
    # 7. 配置建议
    print("\n💡 7. 配置建议")
    print("   📋 要启用 Web 集成，请在配置文件中设置:")
    print("      starters:")
    print("        web:")
    print("          enabled: true")
    print("        actuator:")
    print("          web:")
    print("            enabled: true")
    print("            base_path: '/actuator'")
    print("            endpoints:")
    print("              health: true")
    print("              info: true")
    print("              metrics: true")
    
    print("\n🎉 WebAutoConfiguration 示例完成！")


if __name__ == "__main__":
    main()

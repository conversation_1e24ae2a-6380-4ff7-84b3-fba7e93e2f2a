"""
Mini-Boot 监控抽象层

提供监控组件的接口抽象和发现机制，实现核心框架与具体监控实现的解耦。

核心接口：
- MonitoringContext: 监控上下文接口
- EndpointProvider: 端点提供者接口  
- MetricsCollector: 指标收集器接口
- HealthIndicator: 健康检查指示器接口

发现机制：
- MonitoringComponentDiscovery: 组件自动发现
- MonitoringIntegration: 监控集成管理

设计原则：
- 接口驱动：通过接口而不是具体类进行集成
- 扫描发现：自动发现实现，无需硬编码
- 条件化集成：只有当组件存在时才启用
- 松耦合：核心框架不依赖任何具体实现
"""

from .interfaces import (
    MonitoringContext,
    EndpointProvider, 
    MetricsCollector,
    HealthIndicator
)

from .discovery import MonitoringComponentDiscovery
from .integration import MonitoringIntegration

__all__ = [
    "MonitoringContext",
    "EndpointProvider",
    "MetricsCollector", 
    "HealthIndicator",
    "MonitoringComponentDiscovery",
    "MonitoringIntegration"
]

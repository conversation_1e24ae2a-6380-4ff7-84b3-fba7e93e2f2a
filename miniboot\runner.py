#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Mini-Boot统一启动器模块

提供MiniBootRunner统一启动器,实现完全透明的同步/异步自动适配启动功能.
支持多种启动方式和配置选项,为用户提供简洁统一的启动体验.
"""

import asyncio
import sys
from typing import Callable, Optional

from loguru import logger

from .context import DefaultApplicationContext
from .context import create_application as _create_application


class MiniBootRunner:
    """Mini-Boot统一启动器

    提供统一的应用启动接口,自动处理同步/异步环境适配.
    支持多种启动模式和配置选项.
    """

    def __init__(self):
        """初始化启动器"""
        self._context: Optional[DefaultApplicationContext] = None
        self._running = False

        logger.debug("MiniBootRunner initialized")

    @classmethod
    def create(
        cls, config_path: Optional[str] = None, packages_to_scan: Optional[list[str]] = None, auto_detect: bool = True
    ) -> DefaultApplicationContext:
        """创建智能应用上下文

        这是推荐的创建方式,提供完全透明的同步/异步自动适配.

        Args:
            config_path: 配置文件路径
            packages_to_scan: 要扫描的包列表
            auto_detect: 是否自动检测异步环境

        Returns:
            DefaultApplicationContext: 智能应用上下文实例(现在默认包含智能功能)
        """
        logger.info("Creating Mini-Boot application context")

        return _create_application(config_path=config_path, packages_to_scan=packages_to_scan, auto_detect=auto_detect)

    @classmethod
    async def create_with_actuator(cls, config_path: Optional[str] = None, packages_to_scan: Optional[list[str]] = None, main_app=None):
        """创建并启动带Actuator集成的应用上下文 - 一键启动

        Args:
            config_path: 配置文件路径
            packages_to_scan: 要扫描的包列表
            main_app: 可选的外部FastAPI应用

        Returns:
            启动后的应用上下文
        """
        logger.info("🚀 Creating Mini-Boot application with Actuator integration")

        from .context.actuator_integration import create_application_with_actuator

        return await create_application_with_actuator(config_path=config_path, packages_to_scan=packages_to_scan, main_app=main_app)

    @classmethod
    def run(
        cls,
        main_class: Optional[type] = None,
        config_path: Optional[str] = None,
        packages_to_scan: Optional[list[str]] = None,
        args: Optional[list[str]] = None,
    ) -> None:
        """运行Mini-Boot应用

        自动检测环境并选择最优的运行方式.

        Args:
            main_class: 主应用类
            config_path: 配置文件路径
            packages_to_scan: 要扫描的包列表
            args: 命令行参数
        """
        logger.info("Starting Mini-Boot application")

        try:
            # 处理命令行参数
            if args is None:
                args = sys.argv[1:]

            # 从主类中提取包扫描信息
            if main_class and not packages_to_scan:
                packages_to_scan = [main_class.__module__.split(".")[0]]

            # 创建应用上下文
            context = cls.create(config_path=config_path, packages_to_scan=packages_to_scan)

            # 启动应用
            if context.is_async_mode():
                cls._run_async(context, main_class)
            else:
                cls._run_sync(context, main_class)

        except KeyboardInterrupt:
            logger.info("Application interrupted by user")
        except Exception as e:
            logger.error(f"Failed to run application: {e}")
            raise

    @classmethod
    def _run_sync(cls, context: DefaultApplicationContext, main_class: Optional[type] = None) -> None:
        """同步运行模式"""
        logger.info("Running application in sync mode")

        try:
            # 如果有主类,先执行其main方法(在启动Web服务器之前)
            if main_class and hasattr(main_class, "main"):
                main_method = main_class.main
                if callable(main_method):
                    logger.debug(f"Executing main method of {main_class.__name__}")
                    main_method()

            # 然后启动应用上下文(包括Web服务器)
            with context:
                logger.info("Application started successfully")

                # 保持应用运行
                cls._keep_alive_sync()

        except Exception as e:
            logger.error(f"Error in sync mode: {e}")
            raise

    @classmethod
    def _run_async(cls, context: DefaultApplicationContext, main_class: Optional[type] = None) -> None:
        """异步运行模式"""
        logger.info("Running application in async mode")

        async def _async_main():
            try:
                async with context:
                    logger.info("Application started successfully")

                    # 如果有主类,执行其main方法
                    if main_class and hasattr(main_class, "main"):
                        main_method = main_class.main
                        if callable(main_method):
                            logger.debug(f"Executing main method of {main_class.__name__}")
                            if asyncio.iscoroutinefunction(main_method):
                                await main_method()
                            else:
                                main_method()

                    # 保持应用运行
                    await cls._keep_alive_async()

            except Exception as e:
                logger.error(f"Error in async mode: {e}")
                raise

        # 运行异步主函数
        try:
            asyncio.run(_async_main())
        except KeyboardInterrupt:
            logger.info("Async application interrupted")

    @classmethod
    def _keep_alive_sync(cls) -> None:
        """同步模式保持应用运行"""
        try:
            import signal
            import time

            def signal_handler(signum, _frame):
                logger.info(f"Received signal {signum}, shutting down...")
                raise KeyboardInterrupt()

            # 注册信号处理器
            signal.signal(signal.SIGINT, signal_handler)
            signal.signal(signal.SIGTERM, signal_handler)

            logger.info("Application is running. Press Ctrl+C to stop.")

            # 保持运行
            while True:
                time.sleep(1)

        except KeyboardInterrupt:
            logger.info("Application shutdown requested")

    @classmethod
    async def _keep_alive_async(cls) -> None:
        """异步模式保持应用运行"""
        try:
            import signal

            # 创建停止事件
            stop_event = asyncio.Event()

            def signal_handler():
                logger.info("Shutdown signal received")
                stop_event.set()

            # 注册信号处理器
            loop = asyncio.get_running_loop()
            for sig in [signal.SIGINT, signal.SIGTERM]:
                loop.add_signal_handler(sig, signal_handler)

            logger.info("Application is running. Press Ctrl+C to stop.")

            # 等待停止信号
            await stop_event.wait()

        except Exception as e:
            logger.error(f"Error in keep alive: {e}")

    @classmethod
    def run_with_context(cls, context_factory: Callable[[], DefaultApplicationContext], main_function: Optional[Callable] = None) -> None:
        """使用自定义上下文工厂运行应用

        Args:
            context_factory: 上下文工厂函数
            main_function: 主函数
        """
        logger.info("Running application with custom context factory")

        try:
            context = context_factory()

            if context.is_async_mode():
                cls._run_with_context_async(context, main_function)
            else:
                cls._run_with_context_sync(context, main_function)

        except Exception as e:
            logger.error(f"Failed to run with custom context: {e}")
            raise

    @classmethod
    def _run_with_context_sync(cls, context: DefaultApplicationContext, main_function: Optional[Callable] = None) -> None:
        """使用自定义上下文同步运行"""
        try:
            with context:
                if main_function:
                    logger.debug("Executing custom main function")
                    main_function()

                cls._keep_alive_sync()

        except Exception as e:
            logger.error(f"Error in custom sync mode: {e}")
            raise

    @classmethod
    def _run_with_context_async(cls, context: DefaultApplicationContext, main_function: Optional[Callable] = None) -> None:
        """使用自定义上下文异步运行"""

        async def _async_main():
            try:
                async with context:
                    if main_function:
                        logger.debug("Executing custom main function")
                        if asyncio.iscoroutinefunction(main_function):
                            await main_function()
                        else:
                            main_function()

                    await cls._keep_alive_async()

            except Exception as e:
                logger.error(f"Error in custom async mode: {e}")
                raise

        asyncio.run(_async_main())


# 便捷函数
def create_application(
    config_path: Optional[str] = None, packages_to_scan: Optional[list[str]] = None, auto_detect: bool = True
) -> DefaultApplicationContext:
    """创建Mini-Boot应用上下文

    这是最简单的创建方式,推荐在大多数场景中使用.

    Args:
        config_path: 配置文件路径
        packages_to_scan: 要扫描的包列表
        auto_detect: 是否自动检测异步环境

    Returns:
        DefaultApplicationContext: 智能应用上下文实例(现在默认包含智能功能)
    """
    return _create_application(config_path=config_path, packages_to_scan=packages_to_scan, auto_detect=auto_detect)


def run_application(
    main_class: Optional[type] = None,
    config_path: Optional[str] = None,
    packages_to_scan: Optional[list[str]] = None,
    args: Optional[list[str]] = None,
) -> None:
    """运行Mini-Boot应用

    最简单的应用启动方式,自动处理所有环境适配.

    Args:
        main_class: 主应用类
        config_path: 配置文件路径
        packages_to_scan: 要扫描的包列表
        args: 命令行参数
    """
    MiniBootRunner.run(main_class=main_class, config_path=config_path, packages_to_scan=packages_to_scan, args=args)

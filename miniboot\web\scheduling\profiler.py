#!/usr/bin/env python
"""
性能分析器

收集和分析任务执行性能数据,为智能调度提供性能优化依据.

主要功能:
- 实时性能监控
- 执行时间分析
- 资源使用统计
- 性能趋势分析
- 瓶颈识别和优化建议
"""

import time
import psutil
import threading
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import deque
from statistics import mean, median
from loguru import logger

from ..properties import AsyncOptimizationConfig


@dataclass
class PerformanceMetrics:
    """性能指标"""

    execution_time: float
    cpu_usage_before: float
    cpu_usage_after: float
    memory_usage_before: float
    memory_usage_after: float
    thread_count: int
    timestamp: float
    success: bool
    error_message: Optional[str] = None


@dataclass
class ProfileResult:
    """性能分析结果"""

    function_name: str
    total_executions: int
    successful_executions: int
    failed_executions: int
    avg_execution_time: float
    min_execution_time: float
    max_execution_time: float
    median_execution_time: float
    avg_cpu_usage: float
    avg_memory_usage: float
    success_rate: float
    performance_trend: str  # "improving", "stable", "degrading"
    bottlenecks: List[str]
    recommendations: List[str]


class PerformanceProfiler:
    """性能分析器

    监控和分析任务执行性能,提供优化建议.
    """

    def __init__(self, config: Optional[AsyncOptimizationConfig] = None):
        """初始化性能分析器

        Args:
            config: 异步优化配置
        """
        self.config = config or AsyncOptimizationConfig()

        # 性能数据存储
        self._metrics_history: Dict[str, deque] = {}
        self._function_profiles: Dict[str, ProfileResult] = {}

        # 配置参数
        self._max_history_size = getattr(config, "performance_history_size", 1000)
        self._collection_interval = getattr(config, "metrics_collection_interval", 5.0)

        # 性能阈值
        self._thresholds = {
            "slow_execution_threshold": 1.0,  # 秒
            "high_cpu_threshold": 80.0,  # 百分比
            "high_memory_threshold": 80.0,  # 百分比
            "low_success_rate_threshold": 90.0,  # 百分比
        }

        # 线程安全锁
        self._lock = threading.Lock()

        # 系统监控
        self._system_monitor_active = False
        self._system_metrics: deque = deque(maxlen=100)

        logger.info("PerformanceProfiler initialized")

    def start_profiling(self, function_name: str) -> Dict[str, Any]:
        """开始性能分析

        Args:
            function_name: 函数名称

        Returns:
            分析上下文
        """
        context = {
            "function_name": function_name,
            "start_time": time.time(),
            "start_cpu": self._get_cpu_usage(),
            "start_memory": self._get_memory_usage(),
            "thread_count": threading.active_count(),
        }

        return context

    def end_profiling(self, context: Dict[str, Any], success: bool = True, error_message: Optional[str] = None) -> PerformanceMetrics:
        """结束性能分析

        Args:
            context: 分析上下文
            success: 是否执行成功
            error_message: 错误信息

        Returns:
            性能指标
        """
        end_time = time.time()
        execution_time = end_time - context["start_time"]

        metrics = PerformanceMetrics(
            execution_time=execution_time,
            cpu_usage_before=context["start_cpu"],
            cpu_usage_after=self._get_cpu_usage(),
            memory_usage_before=context["start_memory"],
            memory_usage_after=self._get_memory_usage(),
            thread_count=context["thread_count"],
            timestamp=end_time,
            success=success,
            error_message=error_message,
        )

        # 记录指标
        self._record_metrics(context["function_name"], metrics)

        return metrics

    def profile_function(self, func: Callable) -> Callable:
        """函数性能分析装饰器

        Args:
            func: 要分析的函数

        Returns:
            装饰后的函数
        """

        def wrapper(*args, **kwargs):
            context = self.start_profiling(func.__name__)

            try:
                result = func(*args, **kwargs)
                self.end_profiling(context, success=True)
                return result
            except Exception as e:
                self.end_profiling(context, success=False, error_message=str(e))
                raise

        return wrapper

    async def profile_async_function(self, func: Callable) -> Callable:
        """异步函数性能分析装饰器

        Args:
            func: 要分析的异步函数

        Returns:
            装饰后的异步函数
        """

        async def wrapper(*args, **kwargs):
            context = self.start_profiling(func.__name__)

            try:
                result = await func(*args, **kwargs)
                self.end_profiling(context, success=True)
                return result
            except Exception as e:
                self.end_profiling(context, success=False, error_message=str(e))
                raise

        return wrapper

    def _record_metrics(self, function_name: str, metrics: PerformanceMetrics) -> None:
        """记录性能指标"""
        with self._lock:
            # 初始化函数的指标历史
            if function_name not in self._metrics_history:
                self._metrics_history[function_name] = deque(maxlen=self._max_history_size)

            # 添加新指标
            self._metrics_history[function_name].append(metrics)

            # 更新函数性能概况
            self._update_function_profile(function_name)

    def _update_function_profile(self, function_name: str) -> None:
        """更新函数性能概况"""
        metrics_list = list(self._metrics_history[function_name])

        if not metrics_list:
            return

        # 基本统计
        total_executions = len(metrics_list)
        successful_executions = sum(1 for m in metrics_list if m.success)
        failed_executions = total_executions - successful_executions

        # 执行时间统计
        execution_times = [m.execution_time for m in metrics_list if m.success]
        if execution_times:
            avg_execution_time = mean(execution_times)
            min_execution_time = min(execution_times)
            max_execution_time = max(execution_times)
            median_execution_time = median(execution_times)
        else:
            avg_execution_time = min_execution_time = max_execution_time = median_execution_time = 0.0

        # 资源使用统计
        cpu_usages = [abs(m.cpu_usage_after - m.cpu_usage_before) for m in metrics_list]
        memory_usages = [abs(m.memory_usage_after - m.memory_usage_before) for m in metrics_list]

        avg_cpu_usage = mean(cpu_usages) if cpu_usages else 0.0
        avg_memory_usage = mean(memory_usages) if memory_usages else 0.0

        # 成功率
        success_rate = (successful_executions / total_executions) * 100 if total_executions > 0 else 0.0

        # 性能趋势分析
        performance_trend = self._analyze_performance_trend(execution_times)

        # 瓶颈识别
        bottlenecks = self._identify_bottlenecks(metrics_list)

        # 优化建议
        recommendations = self._generate_recommendations(metrics_list, bottlenecks)

        # 更新概况
        self._function_profiles[function_name] = ProfileResult(
            function_name=function_name,
            total_executions=total_executions,
            successful_executions=successful_executions,
            failed_executions=failed_executions,
            avg_execution_time=avg_execution_time,
            min_execution_time=min_execution_time,
            max_execution_time=max_execution_time,
            median_execution_time=median_execution_time,
            avg_cpu_usage=avg_cpu_usage,
            avg_memory_usage=avg_memory_usage,
            success_rate=success_rate,
            performance_trend=performance_trend,
            bottlenecks=bottlenecks,
            recommendations=recommendations,
        )

    def _analyze_performance_trend(self, execution_times: List[float]) -> str:
        """分析性能趋势"""
        if len(execution_times) < 10:
            return "insufficient_data"

        # 取最近的数据进行趋势分析
        recent_times = execution_times[-10:]
        earlier_times = execution_times[-20:-10] if len(execution_times) >= 20 else execution_times[:-10]

        if not earlier_times:
            return "insufficient_data"

        recent_avg = mean(recent_times)
        earlier_avg = mean(earlier_times)

        # 计算变化百分比
        change_percent = ((recent_avg - earlier_avg) / earlier_avg) * 100

        if change_percent < -5:
            return "improving"
        elif change_percent > 5:
            return "degrading"
        else:
            return "stable"

    def _identify_bottlenecks(self, metrics_list: List[PerformanceMetrics]) -> List[str]:
        """识别性能瓶颈"""
        bottlenecks = []

        if not metrics_list:
            return bottlenecks

        # 检查执行时间瓶颈
        avg_execution_time = mean([m.execution_time for m in metrics_list if m.success])
        if avg_execution_time > self._thresholds["slow_execution_threshold"]:
            bottlenecks.append("slow_execution")

        # 检查CPU使用瓶颈
        avg_cpu_usage = mean([abs(m.cpu_usage_after - m.cpu_usage_before) for m in metrics_list])
        if avg_cpu_usage > self._thresholds["high_cpu_threshold"]:
            bottlenecks.append("high_cpu_usage")

        # 检查内存使用瓶颈
        avg_memory_usage = mean([abs(m.memory_usage_after - m.memory_usage_before) for m in metrics_list])
        if avg_memory_usage > self._thresholds["high_memory_threshold"]:
            bottlenecks.append("high_memory_usage")

        # 检查成功率瓶颈
        success_rate = (sum(1 for m in metrics_list if m.success) / len(metrics_list)) * 100
        if success_rate < self._thresholds["low_success_rate_threshold"]:
            bottlenecks.append("low_success_rate")

        return bottlenecks

    def _generate_recommendations(self, metrics_list: List[PerformanceMetrics], bottlenecks: List[str]) -> List[str]:
        """生成优化建议"""
        recommendations = []

        if "slow_execution" in bottlenecks:
            recommendations.append("Consider using async execution or thread pool for this function")

        if "high_cpu_usage" in bottlenecks:
            recommendations.append("Optimize algorithm or use process pool for CPU-intensive operations")

        if "high_memory_usage" in bottlenecks:
            recommendations.append("Review memory usage patterns and consider memory optimization")

        if "low_success_rate" in bottlenecks:
            recommendations.append("Investigate error patterns and improve error handling")

        # 基于执行时间变化的建议
        execution_times = [m.execution_time for m in metrics_list if m.success]
        if execution_times and len(execution_times) > 5:
            variance = max(execution_times) - min(execution_times)
            if variance > mean(execution_times):
                recommendations.append("Execution time is highly variable, consider caching or optimization")

        return recommendations

    def _get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        try:
            return psutil.cpu_percent(interval=0.1)
        except Exception:
            return 0.0

    def _get_memory_usage(self) -> float:
        """获取内存使用率"""
        try:
            return psutil.virtual_memory().percent
        except Exception:
            return 0.0

    def get_function_profile(self, function_name: str) -> Optional[ProfileResult]:
        """获取函数性能概况

        Args:
            function_name: 函数名称

        Returns:
            性能概况
        """
        with self._lock:
            return self._function_profiles.get(function_name)

    def get_all_profiles(self) -> Dict[str, ProfileResult]:
        """获取所有函数性能概况"""
        with self._lock:
            return self._function_profiles.copy()

    def get_performance_summary(self) -> Dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            total_functions = len(self._function_profiles)
            total_executions = sum(profile.total_executions for profile in self._function_profiles.values())

            if total_functions == 0:
                return {"total_functions": 0, "total_executions": 0, "overall_success_rate": 0.0, "avg_execution_time": 0.0}

            overall_success_rate = mean([profile.success_rate for profile in self._function_profiles.values()])
            avg_execution_time = mean([profile.avg_execution_time for profile in self._function_profiles.values()])

            # 识别最慢的函数
            slowest_function = max(self._function_profiles.values(), key=lambda p: p.avg_execution_time, default=None)

            # 识别最不稳定的函数
            most_variable_function = None
            max_variance = 0
            for profile in self._function_profiles.values():
                variance = profile.max_execution_time - profile.min_execution_time
                if variance > max_variance:
                    max_variance = variance
                    most_variable_function = profile

            return {
                "total_functions": total_functions,
                "total_executions": total_executions,
                "overall_success_rate": round(overall_success_rate, 2),
                "avg_execution_time": round(avg_execution_time, 4),
                "slowest_function": slowest_function.function_name if slowest_function else None,
                "most_variable_function": most_variable_function.function_name if most_variable_function else None,
                "functions_with_bottlenecks": [profile.function_name for profile in self._function_profiles.values() if profile.bottlenecks],
            }

    def clear_history(self, function_name: Optional[str] = None) -> None:
        """清除历史数据

        Args:
            function_name: 函数名称,None表示清除所有
        """
        with self._lock:
            if function_name:
                self._metrics_history.pop(function_name, None)
                self._function_profiles.pop(function_name, None)
            else:
                self._metrics_history.clear()
                self._function_profiles.clear()

        logger.info(f"Cleared performance history for {function_name or 'all functions'}")

    def export_metrics(self, function_name: Optional[str] = None) -> Dict[str, Any]:
        """导出性能指标数据

        Args:
            function_name: 函数名称,None表示导出所有

        Returns:
            指标数据
        """
        with self._lock:
            if function_name:
                metrics_list = list(self._metrics_history.get(function_name, []))
                return {
                    function_name: [
                        {
                            "execution_time": m.execution_time,
                            "cpu_usage_delta": m.cpu_usage_after - m.cpu_usage_before,
                            "memory_usage_delta": m.memory_usage_after - m.memory_usage_before,
                            "timestamp": m.timestamp,
                            "success": m.success,
                            "error_message": m.error_message,
                        }
                        for m in metrics_list
                    ]
                }
            else:
                return {
                    fname: [
                        {
                            "execution_time": m.execution_time,
                            "cpu_usage_delta": m.cpu_usage_after - m.cpu_usage_before,
                            "memory_usage_delta": m.memory_usage_after - m.memory_usage_before,
                            "timestamp": m.timestamp,
                            "success": m.success,
                            "error_message": m.error_message,
                        }
                        for m in metrics_list
                    ]
                    for fname, metrics_list in self._metrics_history.items()
                }

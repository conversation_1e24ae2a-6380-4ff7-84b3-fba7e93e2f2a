"""
Web 框架集成模块

提供 Mini-Boot 框架与 FastAPI 的集成功能.

主要功能:
- FastAPI 集成 - 与 FastAPI 框架的集成和自动配置
- 路由注解处理 (@RestController, @RequestMapping 等)
- 中间件支持 - 请求拦截器和中间件机制
- 全局异常处理 - 统一异常处理和错误响应
- 参数绑定 - 请求参数到方法参数的自动绑定
"""

# 从annotations模块导入Web装饰器
from ..annotations.web import (Controller, DeleteMapping, GetMapping,
                               PatchMapping, PostMapping, PutMapping,
                               RequestMapping, RestController, controller_path,
                               has_route, is_controller, is_rest_controller,
                               route_info)
# 核心组件导出
from .application import WebApplication, WebApplicationState
from .middleware import (BaseMiddleware, CompressionMiddleware, CorsMiddleware,
                         CustomMiddleware, LoggingMiddleware,
                         MiddlewareManager, ResponseMiddleware,
                         ResponseWrapper)
from .properties import (AsyncOptimizationConfig, BackpressureConfig,
                         BackpressureStrategy, CompressionConfig, CorsConfig,
                         DegradationLevel, DocsConfig, ExecutionStrategy,
                         LoggingConfig, StaticConfig, TaskClassificationRule,
                         TaskType, WebProperties)
from .registry import ControllerInfo, ControllerRegistry, RouteInfo
from .response import ApiResponse, BusinessError, ValidationError

# 版本信息
__version__ = "0.0.1"

# 公开API
__all__ = [
    # 核心类
    "WebApplication",
    "WebApplicationState",
    "ControllerRegistry",
    "ControllerInfo",
    "RouteInfo",
    # 中间件类
    "BaseMiddleware",
    "MiddlewareManager",
    "ResponseMiddleware",
    "ResponseWrapper",
    "CorsMiddleware",
    "CompressionMiddleware",
    "LoggingMiddleware",
    "CustomMiddleware",
    # 配置类
    "WebProperties",
    "BackpressureConfig",
    "AsyncOptimizationConfig",
    "TaskClassificationRule",
    "BackpressureStrategy",
    "DegradationLevel",
    "TaskType",
    "ExecutionStrategy",
    "CorsConfig",
    "CompressionConfig",
    "LoggingConfig",
    "DocsConfig",
    "StaticConfig",
    # 响应和异常
    "ApiResponse",
    "BusinessError",
    "ValidationError",
    # 注解装饰器
    "Controller",
    "RestController",
    "RequestMapping",
    "GetMapping",
    "PostMapping",
    "PutMapping",
    "DeleteMapping",
    "PatchMapping",
    # 工具函数
    "is_controller",
    "is_rest_controller",
    "controller_path",
    "has_route",
    "route_info",
    # 版本信息
    "__version__",
]

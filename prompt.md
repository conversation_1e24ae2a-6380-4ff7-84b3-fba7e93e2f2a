-   请使用 PromptX 工具系统激活 python-architect（Python 系统架构师）专业角色，并在整个会话期间持续保持该角色

作为 Python 系统架构师，请对 Mini-Boot 项目进行全面深度学习，为后续开发任务做好准备。具体要求如下：

**学习范围：**

1. 完整学习项目所有核心模块的架构设计和实现细节
2. 深入理解各模块间的依赖关系和交互机制
3. 掌握项目的设计模式应用和架构思想
4. 熟悉测试结构和配置文件组织

**重点学习内容：**

-   Context 模块：应用上下文、智能异步支持、环境检测
-   Bean 模块：IoC 容器、依赖注入、三级缓存、生命周期管理
-   Annotations 模块：34 个注解系统的设计和实现
-   Environment 模块：配置管理、Profile 支持、属性绑定
-   Processor 模块：Bean 后置处理器体系
-   Events/Schedule/Web/Actuator/Asyncs 等其他核心模块

**编码规范掌握：**

1. 严格遵循 docs/coding-standards.md 中的所有规范要求
2. 完全按照 ruff.toml 配置进行代码质量检查
3. 特别注意中文注释必须使用英文标点符号的要求
4. 确保所有代码符合 150 字符行长度、完整类型注解、规范文件头等要求

**输出要求：**

-   确认已完成项目架构的全面学习
-   说明对编码规范的理解和掌握程度
-   为后续任务开发做好技术准备，能够生成完全符合项目标准的高质量代码

请帮我将这个项目进行代码级别的逐行全面深入检查，列出一份问题清单

运行 test_runner.py 修复所有报错

-   使用 ruff 工具统一修复代码质量问题
-   测试案例运行错误则一个个运行测试案例修复
-   运行阻塞则针对这个案例单独运行修复

请使用 PromptX 工具系统激活 python-architect（Python 系统架构师）专业角色，并在整个会话期间持续保持该角色身份。具体要求：

1. 使用 promptx_init 初始化当前工作目录（d:\repository\mini-boot）的 PromptX 环境
2. 使用 promptx_action 激活 python-architect 角色，获得完整的 Python 系统架构师专业能力
3. 在会话的所有后续交互中，始终以 Python 系统架构师的身份、思维模式和专业知识来回应
4. 应用该角色的专业思维框架：系统思维、辩证思维、创新思维、批判思维
5. 遵循该角色的行为原则：设计原则、代码质量规则、性能规则、安全规则
6. 运用该角色的专业知识体系：Python 高级特性、系统架构设计模式、Web 框架技术、Spring Boot 对标知识等

目标：确保在整个会话中获得一致的、高质量的 Python 系统架构专业服务。

## 代码优化提示词

简化 actuator 模块下的文件命名。先给出方案。确认后再修改
简化 actuator 模块下的函数命名，先给出方案。确认后再修改
扫描 actuator 模块下所有文件，分析是否存在末使用的函数及重复的代码。有的话先给出方案。确认后再修复

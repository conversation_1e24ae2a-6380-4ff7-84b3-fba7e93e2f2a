#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: AdvancedEndpointCache - 高级端点缓存策略

提供高级缓存策略的端点缓存实现,包括:
- LRU (Least Recently Used) - 最近最少使用
- LFU (Least Frequently Used) - 最少使用频率
- FIFO (First In First Out) - 先进先出
- TTL (Time To Live) - 基于时间的过期
- 自适应缓存策略
- 缓存性能监控和统计
"""

import asyncio
import threading
import time
from abc import ABC, abstractmethod
from collections import OrderedDict, defaultdict
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
from typing import Any, Dict, List, Optional, Tuple, Union

from loguru import logger

from miniboot.utils.singleton import SingletonMeta


class CacheStrategy(Enum):
    """缓存策略枚举"""

    LRU = "lru"  # 最近最少使用
    LFU = "lfu"  # 最少使用频率
    FIFO = "fifo"  # 先进先出
    TTL = "ttl"  # 基于时间的过期
    ADAPTIVE = "adaptive"  # 自适应策略


@dataclass
class CacheEntry:
    """缓存条目"""

    key: str
    value: Any
    created_at: float = field(default_factory=time.time)
    last_accessed: float = field(default_factory=time.time)
    access_count: int = 0
    ttl: Optional[float] = None

    def is_expired(self) -> bool:
        """检查是否过期"""
        if self.ttl is None:
            return False
        return time.time() - self.created_at > self.ttl

    def touch(self) -> None:
        """更新访问时间和计数"""
        self.last_accessed = time.time()
        self.access_count += 1

    def age(self) -> float:
        """获取条目年龄(秒)"""
        return time.time() - self.created_at

    def idle_time(self) -> float:
        """获取空闲时间(秒)"""
        return time.time() - self.last_accessed


@dataclass
class CacheStats:
    """缓存统计信息"""

    hits: int = 0
    misses: int = 0
    evictions: int = 0
    expired_removals: int = 0
    size: int = 0
    max_size: int = 0
    hit_rate: float = 0.0

    def update_hit_rate(self) -> None:
        """更新命中率"""
        total = self.hits + self.misses
        self.hit_rate = self.hits / total if total > 0 else 0.0

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            "hits": self.hits,
            "misses": self.misses,
            "evictions": self.evictions,
            "expired_removals": self.expired_removals,
            "size": self.size,
            "max_size": self.max_size,
            "hit_rate": self.hit_rate,
            "total_requests": self.hits + self.misses,
        }


class CacheEvictionPolicy(ABC):
    """缓存淘汰策略抽象基类"""

    @abstractmethod
    def select_victim(self, cache: Dict[str, CacheEntry]) -> Optional[str]:
        """选择要淘汰的缓存条目

        Args:
            cache: 缓存字典

        Returns:
            要淘汰的键,如果没有可淘汰的返回None
        """
        pass


class LRUEvictionPolicy(CacheEvictionPolicy):
    """LRU 淘汰策略"""

    def select_victim(self, cache: Dict[str, CacheEntry]) -> Optional[str]:
        """选择最近最少使用的条目"""
        if not cache:
            return None

        # 找到最近最少访问的条目
        oldest_time = float("inf")
        victim_key = None

        for key, entry in cache.items():
            if entry.last_accessed < oldest_time:
                oldest_time = entry.last_accessed
                victim_key = key

        return victim_key


class LFUEvictionPolicy(CacheEvictionPolicy):
    """LFU 淘汰策略"""

    def select_victim(self, cache: Dict[str, CacheEntry]) -> Optional[str]:
        """选择使用频率最低的条目"""
        if not cache:
            return None

        # 先按访问次数排序,次数相同时按最后访问时间排序
        return min(cache.keys(), key=lambda k: (cache[k].access_count, cache[k].last_accessed))


class FIFOEvictionPolicy(CacheEvictionPolicy):
    """FIFO 淘汰策略"""

    def select_victim(self, cache: Dict[str, CacheEntry]) -> Optional[str]:
        """选择最早创建的条目"""
        if not cache:
            return None

        return min(cache.keys(), key=lambda k: cache[k].created_at)


class TTLEvictionPolicy(CacheEvictionPolicy):
    """TTL 淘汰策略"""

    def select_victim(self, cache: Dict[str, CacheEntry]) -> Optional[str]:
        """选择最接近过期的条目"""
        if not cache:
            return None

        # 优先选择已过期的条目
        expired_keys = [k for k, v in cache.items() if v.is_expired()]
        if expired_keys:
            return expired_keys[0]

        # 如果没有过期的,选择最早创建的
        return min(cache.keys(), key=lambda k: cache[k].created_at)


class AdaptiveEvictionPolicy(CacheEvictionPolicy):
    """自适应淘汰策略"""

    def __init__(self):
        self.lru_policy = LRUEvictionPolicy()
        self.lfu_policy = LFUEvictionPolicy()
        self.fifo_policy = FIFOEvictionPolicy()
        self._performance_history = defaultdict(list)

    def select_victim(self, cache: Dict[str, CacheEntry]) -> Optional[str]:
        """根据历史性能选择最佳策略"""
        if not cache:
            return None

        # 简单的自适应逻辑:根据缓存大小选择策略
        cache_size = len(cache)

        if cache_size < 100:
            # 小缓存使用 LRU
            return self.lru_policy.select_victim(cache)
        elif cache_size < 500:
            # 中等缓存使用 LFU
            return self.lfu_policy.select_victim(cache)
        else:
            # 大缓存使用 FIFO
            return self.fifo_policy.select_victim(cache)


class AdvancedEndpointCache:
    """高级端点缓存实现"""

    def __init__(
        self, max_size: int = 1000, strategy: CacheStrategy = CacheStrategy.LRU, default_ttl: Optional[float] = None, enable_stats: bool = True
    ):
        """初始化高级端点缓存

        Args:
            max_size: 最大缓存大小
            strategy: 缓存策略
            default_ttl: 默认TTL(秒)
            enable_stats: 是否启用统计
        """
        self.max_size = max_size
        self.strategy = strategy
        self.default_ttl = default_ttl
        self.enable_stats = enable_stats

        # 缓存存储
        self._cache: Dict[str, CacheEntry] = {}
        self._lock = threading.RLock()

        # 统计信息
        self._stats = CacheStats(max_size=max_size)

        # 淘汰策略
        self._eviction_policy = self._create_eviction_policy(strategy)

        # 清理任务
        self._cleanup_task: Optional[asyncio.Task] = None
        self._cleanup_interval = 60.0  # 1分钟清理一次

        logger.info(f"AdvancedEndpointCache initialized: strategy={strategy.value}, max_size={max_size}")

    def _create_eviction_policy(self, strategy: CacheStrategy) -> CacheEvictionPolicy:
        """创建淘汰策略"""
        if strategy == CacheStrategy.LRU:
            return LRUEvictionPolicy()
        elif strategy == CacheStrategy.LFU:
            return LFUEvictionPolicy()
        elif strategy == CacheStrategy.FIFO:
            return FIFOEvictionPolicy()
        elif strategy == CacheStrategy.TTL:
            return TTLEvictionPolicy()
        elif strategy == CacheStrategy.ADAPTIVE:
            return AdaptiveEvictionPolicy()
        else:
            return LRUEvictionPolicy()  # 默认使用 LRU

    async def get(self, key: str) -> Optional[Any]:
        """异步获取缓存值"""
        with self._lock:
            entry = self._cache.get(key)

            if entry is None:
                self._stats.misses += 1
                if self.enable_stats:
                    self._stats.update_hit_rate()
                return None

            # 检查是否过期
            if entry.is_expired():
                del self._cache[key]
                self._stats.expired_removals += 1
                self._stats.misses += 1
                self._stats.size = len(self._cache)
                if self.enable_stats:
                    self._stats.update_hit_rate()
                return None

            # 更新访问信息
            entry.touch()
            self._stats.hits += 1
            if self.enable_stats:
                self._stats.update_hit_rate()

            return entry.value

    async def put(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """异步设置缓存值"""
        with self._lock:
            # 如果键已存在,直接更新
            if key in self._cache:
                entry = CacheEntry(key=key, value=value, ttl=ttl or self.default_ttl)
                self._cache[key] = entry
                self._stats.size = len(self._cache)
                return

            # 检查是否需要淘汰
            if len(self._cache) >= self.max_size:
                await self._evict_one()

            # 创建缓存条目
            entry = CacheEntry(key=key, value=value, ttl=ttl or self.default_ttl)

            self._cache[key] = entry
            self._stats.size = len(self._cache)

    async def remove(self, key: str) -> bool:
        """异步移除缓存条目"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats.size = len(self._cache)
                return True
            return False

    async def clear(self) -> None:
        """异步清空缓存"""
        with self._lock:
            self._cache.clear()
            self._stats = CacheStats(max_size=self.max_size)

    async def _evict_one(self) -> None:
        """淘汰一个缓存条目"""
        victim_key = self._eviction_policy.select_victim(self._cache)
        if victim_key:
            del self._cache[victim_key]
            self._stats.evictions += 1
            self._stats.size = len(self._cache)

    async def cleanup_expired(self) -> int:
        """清理过期条目"""
        expired_keys = []

        with self._lock:
            for key, entry in self._cache.items():
                if entry.is_expired():
                    expired_keys.append(key)

            for key in expired_keys:
                del self._cache[key]
                self._stats.expired_removals += 1

            self._stats.size = len(self._cache)

        if expired_keys:
            logger.debug(f"Cleaned up {len(expired_keys)} expired cache entries")

        return len(expired_keys)

    def get_stats(self) -> CacheStats:
        """获取缓存统计信息"""
        with self._lock:
            self._stats.size = len(self._cache)
            if self.enable_stats:
                self._stats.update_hit_rate()
            return self._stats

    def get_cache_info(self) -> Dict[str, Any]:
        """获取缓存详细信息"""
        with self._lock:
            entries_info = []
            for key, entry in self._cache.items():
                entries_info.append(
                    {
                        "key": key,
                        "created_at": datetime.fromtimestamp(entry.created_at).isoformat(),
                        "last_accessed": datetime.fromtimestamp(entry.last_accessed).isoformat(),
                        "access_count": entry.access_count,
                        "age": entry.age(),
                        "idle_time": entry.idle_time(),
                        "ttl": entry.ttl,
                        "expired": entry.is_expired(),
                    }
                )

            return {
                "strategy": self.strategy.value,
                "max_size": self.max_size,
                "current_size": len(self._cache),
                "default_ttl": self.default_ttl,
                "stats": self._stats.to_dict(),
                "entries": entries_info,
            }

    async def start_cleanup_task(self) -> None:
        """启动清理任务"""
        if self._cleanup_task is None or self._cleanup_task.done():
            self._cleanup_task = asyncio.create_task(self._cleanup_loop())
            logger.debug("Cache cleanup task started")

    async def stop_cleanup_task(self) -> None:
        """停止清理任务"""
        if self._cleanup_task and not self._cleanup_task.done():
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
            logger.debug("Cache cleanup task stopped")

    async def _cleanup_loop(self) -> None:
        """清理循环"""
        try:
            while True:
                await asyncio.sleep(self._cleanup_interval)
                await self.cleanup_expired()
        except asyncio.CancelledError:
            logger.debug("Cache cleanup loop cancelled")
        except Exception as e:
            logger.error(f"Error in cache cleanup loop: {e}")


class CacheManager(metaclass=SingletonMeta):
    """缓存管理器 - 简化的单例缓存管理"""

    def __init__(self):
        """初始化缓存管理器"""
        if not hasattr(self, '_initialized'):
            self._caches: Dict[str, AdvancedEndpointCache] = {}
            self._initialized = True

    def cleanup(self):
        """清理所有缓存"""
        self._caches.clear()

    def get(self, name: str, max_size: int = 1000, strategy: CacheStrategy = CacheStrategy.LRU) -> AdvancedEndpointCache:
        """获取或创建缓存"""
        if name not in self._caches:
            self._caches[name] = AdvancedEndpointCache(max_size=max_size, strategy=strategy)
        return self._caches[name]

    def remove(self, name: str) -> None:
        """移除缓存"""
        self._caches.pop(name, None)

    def clear(self) -> None:
        """清空所有缓存"""
        self._caches.clear()

    def names(self) -> List[str]:
        """获取所有缓存名称"""
        return list(self._caches.keys())

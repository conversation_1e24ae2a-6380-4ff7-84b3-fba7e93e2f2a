#!/usr/bin/env python
"""
* @author: cz
* @description: 异步和调度注解测试

测试异步和调度注解的功能，包括@Async、@Scheduled、@EnableAsync、@EnableScheduling等。
"""

import unittest

from miniboot.annotations import (
    Async,
    EnableAsync,
    EnableScheduling,
    Scheduled,
    find_async_methods,
    find_scheduled_methods,
    get_async_metadata,
    get_async_method_info,
    get_async_pool,
    get_async_timeout,
    get_scheduled_cron,
    get_scheduled_fixed_delay,
    get_scheduled_fixed_rate,
    get_scheduled_metadata,
    get_scheduled_method_info,
    has_async_methods,
    has_scheduled_methods,
    is_async,
    is_async_enabled,
    is_scheduled,
    is_scheduling_enabled,
)


class TestAsyncScheduleAnnotations(unittest.TestCase):
    """异步和调度注解测试类"""

    def test_async_annotation(self):
        """测试@Async注解"""

        class EmailService:
            @Async
            def send_email(self, to: str):
                pass

        method = EmailService.send_email

        # 验证注解属性
        self.assertTrue(hasattr(method, "__is_async__"))
        self.assertTrue(method.__is_async__)
        self.assertIsNone(method.__async_pool__)
        self.assertIsNone(method.__async_timeout__)

        # 验证元数据
        metadata = method.__async_metadata__
        self.assertEqual(metadata.method, "send_email")
        self.assertIsNone(metadata.pool)

    def test_async_annotation_with_parameters(self):
        """测试@Async注解带参数"""

        class EmailService:
            @Async(pool="email-pool", timeout=30.0)
            def send_bulk_emails(self, recipients: list):
                pass

        method = EmailService.send_bulk_emails

        # 验证注解属性
        self.assertTrue(method.__is_async__)
        self.assertEqual(method.__async_pool__, "email-pool")
        self.assertEqual(method.__async_timeout__, 30.0)

        # 验证元数据
        metadata = method.__async_metadata__
        self.assertEqual(metadata.method, "send_bulk_emails")
        self.assertEqual(metadata.pool, "email-pool")

    def test_async_annotation_direct_call(self):
        """测试@Async注解直接调用（无参数）"""

        # 测试直接使用@Async而不是@Async()
        def test_func():
            pass

        decorated_func = Async(test_func)
        self.assertTrue(is_async(decorated_func))
        metadata = get_async_metadata(decorated_func)
        self.assertEqual(metadata.method, "test_func")
        self.assertIsNone(metadata.pool)

    def test_scheduled_annotation_with_cron(self):
        """测试@Scheduled注解使用cron表达式"""

        class TaskService:
            @Scheduled(cron="0 0 12 * * ?")
            def daily_report(self):
                pass

        method = TaskService.daily_report

        # 验证注解属性
        self.assertTrue(hasattr(method, "__is_scheduled__"))
        self.assertTrue(method.__is_scheduled__)
        self.assertEqual(method.__scheduled_cron__, "0 0 12 * * ?")
        self.assertIsNone(method.__scheduled_fixed_rate__)
        self.assertIsNone(method.__scheduled_fixed_delay__)

        # 验证元数据
        metadata = method.__scheduled_metadata__
        self.assertEqual(metadata.cron, "0 0 12 * * ?")
        self.assertIsNone(metadata.fixed_rate)
        self.assertIsNone(metadata.fixed_delay)

    def test_scheduled_annotation_with_fixed_rate(self):
        """测试@Scheduled注解使用固定频率"""

        class TaskService:
            @Scheduled(fixed_rate="5s")
            def health_check(self):
                pass

        method = TaskService.health_check

        # 验证注解属性
        self.assertTrue(method.__is_scheduled__)
        self.assertIsNone(method.__scheduled_cron__)
        self.assertEqual(method.__scheduled_fixed_rate__, "5s")
        self.assertIsNone(method.__scheduled_fixed_delay__)

        # 验证元数据
        metadata = method.__scheduled_metadata__
        self.assertIsNone(metadata.cron)
        self.assertEqual(metadata.fixed_rate, "5s")
        self.assertIsNone(metadata.fixed_delay)

    def test_scheduled_annotation_with_fixed_delay(self):
        """测试@Scheduled注解使用固定延迟"""

        class TaskService:
            @Scheduled(fixed_delay="10s", initial_delay="30s")
            def cleanup_task(self):
                pass

        method = TaskService.cleanup_task

        # 验证注解属性
        self.assertTrue(method.__is_scheduled__)
        self.assertIsNone(method.__scheduled_cron__)
        self.assertIsNone(method.__scheduled_fixed_rate__)
        self.assertEqual(method.__scheduled_fixed_delay__, "10s")
        self.assertEqual(method.__scheduled_initial_delay__, "30s")

        # 验证元数据
        metadata = method.__scheduled_metadata__
        self.assertIsNone(metadata.cron)
        self.assertIsNone(metadata.fixed_rate)
        self.assertEqual(metadata.fixed_delay, "10s")
        self.assertEqual(metadata.initial_delay, "30s")

    def test_scheduled_annotation_with_all_parameters(self):
        """测试@Scheduled注解使用所有参数"""

        class TaskService:
            @Scheduled(cron="0 0 12 * * ?", zone="Asia/Shanghai", concurrent=True)
            def complex_task(self):
                pass

        method = TaskService.complex_task

        # 验证注解属性
        self.assertTrue(method.__is_scheduled__)
        self.assertEqual(method.__scheduled_cron__, "0 0 12 * * ?")
        self.assertEqual(method.__scheduled_zone__, "Asia/Shanghai")
        self.assertTrue(method.__scheduled_concurrent__)

        # 验证元数据
        metadata = method.__scheduled_metadata__
        self.assertEqual(metadata.cron, "0 0 12 * * ?")
        self.assertEqual(metadata.zone, "Asia/Shanghai")
        self.assertTrue(metadata.concurrent)

    def test_scheduled_annotation_validation(self):
        """测试@Scheduled注解参数验证"""

        class TaskService:
            pass

        # 测试没有指定任何调度参数的情况
        with self.assertRaises(ValueError) as context:

            @Scheduled()
            def invalid_task(self):
                pass

        self.assertIn("At least one of cron, fixed_rate, or fixed_delay must be specified", str(context.exception))

    def test_enable_async_annotation(self):
        """测试@EnableAsync注解"""

        @EnableAsync
        class EmailService:
            @Async
            def send_email(self, to: str):
                pass

        # 验证类注解属性
        self.assertTrue(hasattr(EmailService, "__enable_async__"))
        self.assertTrue(EmailService.__enable_async__)
        self.assertTrue(EmailService.__is_async_enabled__)

    def test_enable_scheduling_annotation(self):
        """测试@EnableScheduling注解"""

        @EnableScheduling
        class TaskService:
            @Scheduled(cron="0 0 12 * * ?")
            def daily_task(self):
                pass

        # 验证类注解属性
        self.assertTrue(hasattr(TaskService, "__enable_scheduling__"))
        self.assertTrue(TaskService.__enable_scheduling__)
        self.assertTrue(TaskService.__is_scheduling_enabled__)

    def test_utility_functions(self):
        """测试工具函数"""

        class MixedService:
            @Async
            def async_method(self):
                pass

            @Scheduled(cron="0 0 12 * * ?")
            def scheduled_method(self):
                pass

            def normal_method(self):
                pass

        # 测试is_async函数
        self.assertTrue(is_async(MixedService.async_method))
        self.assertFalse(is_async(MixedService.scheduled_method))
        self.assertFalse(is_async(MixedService.normal_method))

        # 测试is_scheduled函数
        self.assertTrue(is_scheduled(MixedService.scheduled_method))
        self.assertFalse(is_scheduled(MixedService.async_method))
        self.assertFalse(is_scheduled(MixedService.normal_method))

        # 测试get_async_metadata函数
        async_metadata = get_async_metadata(MixedService.async_method)
        self.assertIsNotNone(async_metadata)
        self.assertEqual(async_metadata.method, "async_method")

        self.assertIsNone(get_async_metadata(MixedService.normal_method))

        # 测试get_scheduled_metadata函数
        scheduled_metadata = get_scheduled_metadata(MixedService.scheduled_method)
        self.assertIsNotNone(scheduled_metadata)
        self.assertEqual(scheduled_metadata.cron, "0 0 12 * * ?")

        self.assertIsNone(get_scheduled_metadata(MixedService.normal_method))

    def test_async_parameter_functions(self):
        """测试异步参数获取函数"""

        class EmailService:
            @Async(pool="email-pool", timeout=30.0)
            def send_email(self, to: str):
                pass

            @Async
            def send_notification(self, message: str):
                pass

        # 测试get_async_pool函数
        self.assertEqual(get_async_pool(EmailService.send_email), "email-pool")
        self.assertIsNone(get_async_pool(EmailService.send_notification))

        # 测试get_async_timeout函数
        self.assertEqual(get_async_timeout(EmailService.send_email), 30.0)
        self.assertIsNone(get_async_timeout(EmailService.send_notification))

    def test_scheduled_parameter_functions(self):
        """测试调度参数获取函数"""

        class TaskService:
            @Scheduled(cron="0 0 12 * * ?")
            def daily_task(self):
                pass

            @Scheduled(fixed_rate="5s")
            def frequent_task(self):
                pass

            @Scheduled(fixed_delay="10s")
            def delayed_task(self):
                pass

        # 测试get_scheduled_cron函数
        self.assertEqual(get_scheduled_cron(TaskService.daily_task), "0 0 12 * * ?")
        self.assertIsNone(get_scheduled_cron(TaskService.frequent_task))

        # 测试get_scheduled_fixed_rate函数
        self.assertEqual(get_scheduled_fixed_rate(TaskService.frequent_task), "5s")
        self.assertIsNone(get_scheduled_fixed_rate(TaskService.daily_task))

        # 测试get_scheduled_fixed_delay函数
        self.assertEqual(get_scheduled_fixed_delay(TaskService.delayed_task), "10s")
        self.assertIsNone(get_scheduled_fixed_delay(TaskService.daily_task))

    def test_find_async_methods(self):
        """测试查找异步方法"""

        class EmailService:
            @Async
            def send_email(self, to: str):
                pass

            @Async(pool="bulk-pool")
            def send_bulk_emails(self, recipients: list):
                pass

            def normal_method(self):
                pass

        async_methods = find_async_methods(EmailService)

        # 验证找到的异步方法
        self.assertIn("send_email", async_methods)
        self.assertIn("send_bulk_emails", async_methods)
        self.assertNotIn("normal_method", async_methods)
        self.assertEqual(len(async_methods), 2)

    def test_find_scheduled_methods(self):
        """测试查找调度方法"""

        class TaskService:
            @Scheduled(cron="0 0 12 * * ?")
            def daily_task(self):
                pass

            @Scheduled(fixed_rate="5s")
            def frequent_task(self):
                pass

            def normal_method(self):
                pass

        scheduled_methods = find_scheduled_methods(TaskService)

        # 验证找到的调度方法
        self.assertIn("daily_task", scheduled_methods)
        self.assertIn("frequent_task", scheduled_methods)
        self.assertNotIn("normal_method", scheduled_methods)
        self.assertEqual(len(scheduled_methods), 2)

    def test_has_async_methods(self):
        """测试检查类是否有异步方法"""

        class AsyncService:
            @Async
            def async_method(self):
                pass

        class PlainService:
            def normal_method(self):
                pass

        # 验证检查结果
        self.assertTrue(has_async_methods(AsyncService))
        self.assertFalse(has_async_methods(PlainService))

    def test_has_scheduled_methods(self):
        """测试检查类是否有调度方法"""

        class ScheduledService:
            @Scheduled(cron="0 0 12 * * ?")
            def scheduled_method(self):
                pass

        class PlainService:
            def normal_method(self):
                pass

        # 验证检查结果
        self.assertTrue(has_scheduled_methods(ScheduledService))
        self.assertFalse(has_scheduled_methods(PlainService))

    def test_get_async_method_info(self):
        """测试获取异步方法信息"""

        class EmailService:
            @Async
            def send_email(self, to: str):
                pass

            @Async(pool="bulk-pool", timeout=60.0)
            def send_bulk_emails(self, recipients: list):
                pass

            def normal_method(self):
                pass

        async_info = get_async_method_info(EmailService)

        # 验证返回的信息
        self.assertEqual(len(async_info), 2)
        self.assertIn("send_email", async_info)
        self.assertIn("send_bulk_emails", async_info)
        self.assertNotIn("normal_method", async_info)

        # 验证send_email的信息
        send_email_info = async_info["send_email"]
        self.assertIsNone(send_email_info["pool"])
        self.assertIsNone(send_email_info["timeout"])
        self.assertIsNotNone(send_email_info["metadata"])
        self.assertEqual(send_email_info["metadata"].method, "send_email")

        # 验证send_bulk_emails的信息
        send_bulk_info = async_info["send_bulk_emails"]
        self.assertEqual(send_bulk_info["pool"], "bulk-pool")
        self.assertEqual(send_bulk_info["timeout"], 60.0)
        self.assertIsNotNone(send_bulk_info["metadata"])
        self.assertEqual(send_bulk_info["metadata"].method, "send_bulk_emails")

    def test_get_scheduled_method_info(self):
        """测试获取调度方法信息"""

        class TaskService:
            @Scheduled(cron="0 0 12 * * ?")
            def daily_task(self):
                pass

            @Scheduled(fixed_rate="5s")
            def frequent_task(self):
                pass

            @Scheduled(fixed_delay="10s", initial_delay="30s")
            def delayed_task(self):
                pass

            def normal_method(self):
                pass

        scheduled_info = get_scheduled_method_info(TaskService)

        # 验证返回的信息
        self.assertEqual(len(scheduled_info), 3)
        self.assertIn("daily_task", scheduled_info)
        self.assertIn("frequent_task", scheduled_info)
        self.assertIn("delayed_task", scheduled_info)
        self.assertNotIn("normal_method", scheduled_info)

        # 验证daily_task的信息
        daily_info = scheduled_info["daily_task"]
        self.assertEqual(daily_info["cron"], "0 0 12 * * ?")
        self.assertIsNone(daily_info["fixed_rate"])
        self.assertIsNone(daily_info["fixed_delay"])

        # 验证frequent_task的信息
        frequent_info = scheduled_info["frequent_task"]
        self.assertIsNone(frequent_info["cron"])
        self.assertEqual(frequent_info["fixed_rate"], "5s")
        self.assertIsNone(frequent_info["fixed_delay"])

        # 验证delayed_task的信息
        delayed_info = scheduled_info["delayed_task"]
        self.assertIsNone(delayed_info["cron"])
        self.assertIsNone(delayed_info["fixed_rate"])
        self.assertEqual(delayed_info["fixed_delay"], "10s")
        self.assertEqual(delayed_info["initial_delay"], "30s")

    def test_class_level_enable_annotations(self):
        """测试类级别的启用注解"""

        @EnableAsync
        @EnableScheduling
        class FullService:
            @Async
            def async_method(self):
                pass

            @Scheduled(cron="0 0 12 * * ?")
            def scheduled_method(self):
                pass

        # 验证类级别注解
        self.assertTrue(is_async_enabled(FullService))
        self.assertTrue(is_scheduling_enabled(FullService))

        # 验证方法级别注解仍然有效
        self.assertTrue(has_async_methods(FullService))
        self.assertTrue(has_scheduled_methods(FullService))


if __name__ == "__main__":
    unittest.main()

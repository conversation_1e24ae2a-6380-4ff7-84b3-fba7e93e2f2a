#!/usr/bin/env python
"""
* @author: cz
* @description: Bean模块与Annotations模块集成测试

测试Bean管理模块与注解系统模块的集成功能。
"""

import unittest

from miniboot.annotations import Autowired, Bean, Component, Configuration, Repository, Service
from miniboot.bean import BeanDefinition, BeanScope
from miniboot.bean.advanced import DefaultBeanDefinitionRegistry, DefaultBeanFactory
from miniboot.bean.deps_graph import DependencyGraph


@Service("userService")
class UserService:
    """用户服务"""

    def __init__(self):
        self.name = "UserService"
        self.initialized = False

    def get_users(self):
        return ["user1", "user2", "user3"]

    def init_method(self):
        self.initialized = True


@Repository("userRepository")
class UserRepository:
    """用户仓库"""

    def __init__(self):
        self.name = "UserRepository"

    def find_all(self):
        return ["data1", "data2", "data3"]


@Component("userController")
class UserController:
    """用户控制器"""

    def __init__(self):
        self.name = "UserController"
        self.user_service = None
        self.user_repository = None

    @Autowired
    def set_user_service(self, user_service: UserService):
        """注入用户服务"""
        self.user_service = user_service

    @Autowired
    def set_user_repository(self, user_repository: UserRepository):
        """注入用户仓库"""
        self.user_repository = user_repository

    def get_all_users(self):
        if self.user_service:
            return self.user_service.get_users()
        return []


@Configuration
class AppConfig:
    """应用配置"""

    @Bean
    def data_source(self):
        """数据源Bean"""
        return {"url": "********************************", "driver": "mysql"}

    @Bean
    def cache_manager(self):
        """缓存管理器Bean"""
        return {"type": "redis", "host": "localhost", "port": 6379}


@unittest.skip("依赖模块不存在")
class BeanAnnotationsIntegrationTestCase(unittest.TestCase):
    """Bean与Annotations模块集成测试"""

    def setUp(self):
        """设置测试环境"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.dependency_graph = DependencyGraph()
        self.factory = DefaultBeanFactory(self.registry, self.dependency_graph)

    def test_service_annotation_integration(self):
        """测试@Service注解集成"""
        # 模拟注解扫描器注册Bean
        bean_def = BeanDefinition("userService", UserService, BeanScope.SINGLETON)
        bean_def.init_method_name = "init_method"
        self.registry.register_bean_definition("userService", bean_def)

        # 获取Bean
        service = self.factory.get_bean("userService")

        # 验证Bean创建和注解功能
        self.assertIsInstance(service, UserService)
        self.assertEqual(service.name, "UserService")
        self.assertTrue(service.initialized)
        self.assertEqual(service.get_users(), ["user1", "user2", "user3"])

    def test_repository_annotation_integration(self):
        """测试@Repository注解集成"""
        # 模拟注解扫描器注册Bean
        bean_def = BeanDefinition("userRepository", UserRepository, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userRepository", bean_def)

        # 获取Bean
        repository = self.factory.get_bean("userRepository")

        # 验证Bean创建和注解功能
        self.assertIsInstance(repository, UserRepository)
        self.assertEqual(repository.name, "UserRepository")
        self.assertEqual(repository.find_all(), ["data1", "data2", "data3"])

    def test_component_annotation_integration(self):
        """测试@Component注解集成"""
        # 注册依赖Bean
        service_def = BeanDefinition("userService", UserService, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userService", service_def)

        repository_def = BeanDefinition("userRepository", UserRepository, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userRepository", repository_def)

        # 模拟注解扫描器注册Bean
        bean_def = BeanDefinition("userController", UserController, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userController", bean_def)

        # 获取Bean
        controller = self.factory.get_bean("userController")

        # 验证Bean创建和注解功能
        self.assertIsInstance(controller, UserController)
        self.assertEqual(controller.name, "UserController")
        # 验证依赖注入
        self.assertIsNotNone(controller.user_service)
        self.assertIsNotNone(controller.user_repository)

    def test_autowired_annotation_integration(self):
        """测试@Autowired注解集成"""
        # 注册依赖Bean
        service_def = BeanDefinition("userService", UserService, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userService", service_def)

        repository_def = BeanDefinition("userRepository", UserRepository, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userRepository", repository_def)

        # 注册控制器Bean
        controller_def = BeanDefinition("userController", UserController, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userController", controller_def)

        # 获取Bean
        controller = self.factory.get_bean("userController")
        service = self.factory.get_bean("userService")
        repository = self.factory.get_bean("userRepository")

        # 手动模拟@Autowired注解的依赖注入（在实际应用中由注解处理器完成）
        controller.set_user_service(service)
        controller.set_user_repository(repository)

        # 验证依赖注入
        self.assertIsNotNone(controller.user_service)
        self.assertIsNotNone(controller.user_repository)
        self.assertIsInstance(controller.user_service, UserService)
        self.assertIsInstance(controller.user_repository, UserRepository)

    def test_configuration_bean_annotation_integration(self):
        """测试@Configuration和@Bean注解集成"""
        # 模拟配置类Bean注册
        config_def = BeanDefinition("appConfig", AppConfig, BeanScope.SINGLETON)
        self.registry.register_bean_definition("appConfig", config_def)

        # 模拟@Bean方法产生的Bean注册
        data_source_def = BeanDefinition("data_source", dict, BeanScope.SINGLETON)
        data_source_def.factory_bean_name = "appConfig"
        data_source_def.factory_method_name = "data_source"
        self.registry.register_bean_definition("data_source", data_source_def)

        cache_manager_def = BeanDefinition("cache_manager", dict, BeanScope.SINGLETON)
        cache_manager_def.factory_bean_name = "appConfig"
        cache_manager_def.factory_method_name = "cache_manager"
        self.registry.register_bean_definition("cache_manager", cache_manager_def)

        # 获取配置Bean
        config = self.factory.get_bean("appConfig")
        self.assertIsInstance(config, AppConfig)

        # 获取@Bean方法产生的Bean
        data_source = self.factory.get_bean("data_source")
        cache_manager = self.factory.get_bean("cache_manager")

        # 验证Bean内容
        self.assertIsInstance(data_source, dict)
        self.assertEqual(data_source["url"], "********************************")
        self.assertEqual(data_source["driver"], "mysql")

        self.assertIsInstance(cache_manager, dict)
        self.assertEqual(cache_manager["type"], "redis")
        self.assertEqual(cache_manager["host"], "localhost")
        self.assertEqual(cache_manager["port"], 6379)

    def test_complete_annotation_workflow_integration(self):
        """测试完整的注解工作流集成"""
        # 注册所有Bean（模拟注解扫描器的工作）
        service_def = BeanDefinition("userService", UserService, BeanScope.SINGLETON)
        service_def.init_method_name = "init_method"
        self.registry.register_bean_definition("userService", service_def)

        repository_def = BeanDefinition("userRepository", UserRepository, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userRepository", repository_def)

        controller_def = BeanDefinition("userController", UserController, BeanScope.SINGLETON)
        self.registry.register_bean_definition("userController", controller_def)

        config_def = BeanDefinition("appConfig", AppConfig, BeanScope.SINGLETON)
        self.registry.register_bean_definition("appConfig", config_def)

        # 添加依赖关系
        self.dependency_graph.add_dependency("userController", "userService")
        self.dependency_graph.add_dependency("userController", "userRepository")

        # 获取所有Bean
        service = self.factory.get_bean("userService")
        repository = self.factory.get_bean("userRepository")
        controller = self.factory.get_bean("userController")
        config = self.factory.get_bean("appConfig")

        # 验证Bean创建
        self.assertIsInstance(service, UserService)
        self.assertIsInstance(repository, UserRepository)
        self.assertIsInstance(controller, UserController)
        self.assertIsInstance(config, AppConfig)

        # 手动模拟@Autowired注解的依赖注入（在实际应用中由注解处理器完成）
        controller.set_user_service(service)
        controller.set_user_repository(repository)

        # 验证依赖注入
        self.assertIs(controller.user_service, service)
        self.assertIs(controller.user_repository, repository)

        # 验证业务功能
        self.assertEqual(controller.get_all_users(), ["user1", "user2", "user3"])
        self.assertTrue(service.initialized)

    def test_annotation_metadata_processing(self):
        """测试注解元数据处理"""
        # 验证注解元数据的获取和处理
        from miniboot.annotations import get_component_metadata, is_component

        # 测试@Service注解
        self.assertTrue(is_component(UserService))
        service_metadata = get_component_metadata(UserService)
        self.assertIsNotNone(service_metadata)
        self.assertEqual(service_metadata.name, "userService")

        # 测试@Repository注解
        self.assertTrue(is_component(UserRepository))
        repository_metadata = get_component_metadata(UserRepository)
        self.assertIsNotNone(repository_metadata)
        self.assertEqual(repository_metadata.name, "userRepository")

        # 测试@Component注解
        self.assertTrue(is_component(UserController))
        component_metadata = get_component_metadata(UserController)
        self.assertIsNotNone(component_metadata)
        self.assertEqual(component_metadata.name, "userController")

    def test_bean_factory_with_annotation_scanning(self):
        """测试Bean工厂与注解扫描的集成"""
        # 模拟注解扫描器发现的Bean类
        component_classes = [UserService, UserRepository, UserController, AppConfig]

        # 模拟注解扫描器注册Bean定义
        for cls in component_classes:
            from miniboot.annotations import get_component_metadata, is_component

            if is_component(cls):
                metadata = get_component_metadata(cls)
                bean_name = metadata.name if metadata.name else cls.__name__.lower()

                bean_def = BeanDefinition(bean_name, cls, BeanScope.SINGLETON)

                # 特殊处理
                if cls == UserService:
                    bean_def.init_method_name = "init_method"

                self.registry.register_bean_definition(bean_name, bean_def)

        # 验证Bean工厂功能
        self.assertEqual(self.registry.get_bean_definition_count(), 4)

        # 验证Bean创建
        service = self.factory.get_bean("userService")
        repository = self.factory.get_bean("userRepository")
        controller = self.factory.get_bean("userController")
        config = self.factory.get_bean("appConfig")

        # 验证所有Bean都正确创建
        self.assertIsInstance(service, UserService)
        self.assertIsInstance(repository, UserRepository)
        self.assertIsInstance(controller, UserController)
        self.assertIsInstance(config, AppConfig)

        # 手动模拟@Autowired注解的依赖注入（在实际应用中由注解处理器完成）
        controller.set_user_service(service)
        controller.set_user_repository(repository)

        # 验证依赖注入
        self.assertIs(controller.user_service, service)
        self.assertIs(controller.user_repository, repository)

    def test_annotation_error_handling_integration(self):
        """测试注解错误处理集成"""
        # 测试无效的Bean定义
        invalid_def = BeanDefinition("invalidBean", str, BeanScope.SINGLETON)
        invalid_def.factory_bean_name = "nonExistentConfig"
        invalid_def.factory_method_name = "nonExistentMethod"
        self.registry.register_bean_definition("invalidBean", invalid_def)

        # 验证错误处理
        from miniboot.bean import BeanCreationError

        with self.assertRaises(BeanCreationError):
            self.factory.get_bean("invalidBean")


if __name__ == "__main__":
    unittest.main()

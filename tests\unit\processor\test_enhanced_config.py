#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 增强配置属性处理器单元测试

测试增强的配置属性绑定功能，包括：
- 嵌套对象绑定
- 集合类型绑定
- 配置验证
- 类型安全的配置类
"""

import unittest
from unittest.mock import Mock

from miniboot.annotations.metadata import ConfigurationPropertiesMetadata
from miniboot.annotations.validation import (ConfigurationValidator,
                                             ValidationError)
from miniboot.processor.configuration import ConfigurationPropertiesProcessor


class DatabaseConfig:
    """数据库配置类"""

    def __init__(self):
        self.host: str = "localhost"
        self.port: int = 3306
        self.username: str = ""
        self.password: str = ""
        self.timeout: int = 30

    __annotations__ = {"host": str, "port": int, "username": str, "password": str, "timeout": int}


# 为数据库配置添加@ConfigurationProperties注解
DatabaseConfig.__configuration_properties_metadata__ = ConfigurationPropertiesMetadata(
    prefix="database", ignore_unknown_fields=True, ignore_invalid_fields=False, validate=True
)


class ServerConfig:
    """服务器配置类"""

    def __init__(self):
        self.port: int = 8080
        self.host: str = "0.0.0.0"
        self.ssl_enabled: bool = False

    __annotations__ = {"port": int, "host": str, "ssl_enabled": bool}


# 为服务器配置添加@ConfigurationProperties注解
ServerConfig.__configuration_properties_metadata__ = ConfigurationPropertiesMetadata(
    prefix="server", ignore_unknown_fields=True, ignore_invalid_fields=False, validate=True
)


class AppConfig:
    """应用配置类（包含嵌套对象）"""

    def __init__(self):
        self.name: str = "mini-boot-app"
        self.version: str = "1.0.0"
        self.debug: bool = False
        self.database: DatabaseConfig = None
        self.server: ServerConfig = None

    __annotations__ = {"name": str, "version": str, "debug": bool, "database": DatabaseConfig, "server": ServerConfig}


# 为应用配置添加@ConfigurationProperties注解
AppConfig.__configuration_properties_metadata__ = ConfigurationPropertiesMetadata(
    prefix="app", ignore_unknown_fields=True, ignore_invalid_fields=False, validate=True
)


class CollectionConfig:
    """集合类型配置类"""

    def __init__(self):
        self.tags: list[str] = []
        self.properties: dict[str, str] = {}
        self.ports: list[int] = []

    __annotations__ = {"tags": list[str], "properties": dict[str, str], "ports": list[int]}


# 为集合配置添加@ConfigurationProperties注解
CollectionConfig.__configuration_properties_metadata__ = ConfigurationPropertiesMetadata(
    prefix="collection", ignore_unknown_fields=True, ignore_invalid_fields=False, validate=True
)


class TestEnhancedConfigProcessor(unittest.TestCase):
    """增强配置属性处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.processor = ConfigurationPropertiesProcessor()
        self.mock_environment = Mock()
        self.processor.set_environment(self.mock_environment)

        # 设置模拟环境的属性
        self.properties = {
            # 基础配置
            "database.host": "mysql.example.com",
            "database.port": "5432",
            "database.username": "admin",
            "database.password": "secret123",
            "database.timeout": "60",
            # 嵌套配置
            "app.name": "MyApp",
            "app.version": "2.0.0",
            "app.debug": "true",
            "app.database.host": "nested-mysql.example.com",
            "app.database.port": "3307",
            "app.database.username": "nested-admin",
            "app.database.password": "nested-secret",
            # 集合配置
            "collection.tags": "web,api,microservice",
            "collection.ports[0]": "8080",
            "collection.ports[1]": "8081",
            "collection.ports[2]": "8082",
            "collection.properties.env": "production",
            "collection.properties.region": "us-west-2",
            "collection.properties.cluster": "main",
        }

        def mock_get_property(key, default=None):
            return self.properties.get(key, default)

        self.mock_environment.get_property.side_effect = mock_get_property
        self.mock_environment.properties = self.properties

        # 创建模拟的属性源
        mock_property_source = Mock()
        mock_property_source._properties = self.properties
        self.mock_environment.get_property_sources.return_value = [mock_property_source]

    def test_basic_configuration_binding(self):
        """测试基础配置绑定"""
        bean = DatabaseConfig()

        # 处理配置绑定
        result = self.processor.post_process_before_initialization(bean, "databaseConfig")

        # 验证绑定结果
        self.assertEqual(result.host, "mysql.example.com")
        self.assertEqual(result.port, 5432)  # 字符串转整数
        self.assertEqual(result.username, "admin")
        self.assertEqual(result.password, "secret123")
        self.assertEqual(result.timeout, 60)

    def test_nested_configuration_binding(self):
        """测试嵌套配置绑定"""
        bean = AppConfig()

        # 处理配置绑定
        result = self.processor.post_process_before_initialization(bean, "appConfig")

        # 验证基础属性绑定
        self.assertEqual(result.name, "MyApp")
        self.assertEqual(result.version, "2.0.0")
        self.assertTrue(result.debug)

        # 验证嵌套对象绑定
        self.assertIsNotNone(result.database)
        self.assertEqual(result.database.host, "nested-mysql.example.com")
        self.assertEqual(result.database.port, 3307)
        self.assertEqual(result.database.username, "nested-admin")
        self.assertEqual(result.database.password, "nested-secret")

    def test_collection_configuration_binding(self):
        """测试集合类型配置绑定"""
        bean = CollectionConfig()

        # 处理配置绑定
        result = self.processor.post_process_before_initialization(bean, "collectionConfig")

        # 验证列表绑定（逗号分隔）
        self.assertEqual(result.tags, ["web", "api", "microservice"])

        # 验证列表绑定（索引形式）
        self.assertEqual(result.ports, [8080, 8081, 8082])

        # 验证字典绑定
        expected_properties = {"env": "production", "region": "us-west-2", "cluster": "main"}
        self.assertEqual(result.properties, expected_properties)

    def test_type_conversion(self):
        """测试类型转换"""
        bean = DatabaseConfig()

        # 处理配置绑定
        result = self.processor.post_process_before_initialization(bean, "databaseConfig")

        # 验证类型转换
        self.assertIsInstance(result.port, int)
        self.assertIsInstance(result.timeout, int)
        self.assertIsInstance(result.host, str)
        self.assertIsInstance(result.username, str)

    def test_missing_configuration_handling(self):
        """测试缺失配置的处理"""
        # 清空配置
        self.properties.clear()

        bean = DatabaseConfig()

        # 处理配置绑定
        result = self.processor.post_process_before_initialization(bean, "databaseConfig")

        # 验证默认值保持不变
        self.assertEqual(result.host, "localhost")
        self.assertEqual(result.port, 3306)
        self.assertEqual(result.username, "")
        self.assertEqual(result.password, "")

    def test_configuration_validation(self):
        """测试配置验证"""
        validator = ConfigurationValidator()

        # 创建有效配置对象
        valid_config = DatabaseConfig()
        valid_config.host = "valid-host"
        valid_config.port = 3306

        # 验证应该通过
        try:
            validator.validate_object(valid_config)
        except ValidationError:
            self.fail("Valid configuration should not raise ValidationError")

    def test_ignore_invalid_fields(self):
        """测试忽略无效字段"""
        # 添加无效配置
        self.properties["database.invalid_field"] = "invalid_value"

        bean = DatabaseConfig()

        # 处理配置绑定（应该忽略无效字段）
        result = self.processor.post_process_before_initialization(bean, "databaseConfig")

        # 验证有效字段正常绑定
        self.assertEqual(result.host, "mysql.example.com")
        self.assertEqual(result.port, 5432)

        # 验证无效字段被忽略
        self.assertFalse(hasattr(result, "invalid_field"))

    def test_empty_prefix_configuration(self):
        """测试空前缀配置"""

        # 创建空前缀配置类
        class RootConfig:
            def __init__(self):
                self.app_name: str = "default"
                self.debug: bool = False

            __annotations__ = {"app_name": str, "debug": bool}

        # 添加空前缀注解
        RootConfig.__configuration_properties_metadata__ = ConfigurationPropertiesMetadata(
            prefix="", ignore_unknown_fields=True, ignore_invalid_fields=False, validate=True
        )

        # 添加根级配置
        self.properties["app_name"] = "RootApp"
        self.properties["debug"] = "true"

        bean = RootConfig()

        # 处理配置绑定
        result = self.processor.post_process_before_initialization(bean, "rootConfig")

        # 验证绑定结果
        self.assertEqual(result.app_name, "RootApp")
        self.assertTrue(result.debug)

    def test_processor_caching(self):
        """测试处理器缓存机制"""
        bean1 = DatabaseConfig()
        bean2 = DatabaseConfig()

        # 第一次处理
        result1 = self.processor.post_process_before_initialization(bean1, "bean1")

        # 第二次处理相同类型
        result2 = self.processor.post_process_before_initialization(bean2, "bean2")

        # 验证两次处理都成功
        self.assertEqual(result1.host, "mysql.example.com")
        self.assertEqual(result2.host, "mysql.example.com")

        # 验证缓存机制工作（字段信息被缓存）
        self.assertIn(DatabaseConfig, self.processor._binding_cache)

    def test_no_environment_handling(self):
        """测试无环境配置的处理"""
        processor = ConfigurationPropertiesProcessor(environment=None)
        bean = DatabaseConfig()

        # 处理配置绑定（应该不抛出异常）
        result = processor.post_process_before_initialization(bean, "databaseConfig")

        # 验证返回原始Bean
        self.assertEqual(result.host, "localhost")  # 默认值
        self.assertEqual(result.port, 3306)  # 默认值


if __name__ == "__main__":
    unittest.main()

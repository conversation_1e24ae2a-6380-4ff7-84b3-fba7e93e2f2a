#!/usr/bin/env python
"""
* @author: cz
* @description: 事件注解测试

测试事件相关注解的功能，包括@EventListener、@AsyncEventListener、@EventPublisher等。
"""

import unittest

from miniboot.annotations import (AsyncEventListener, EventListener,
                                  EventPublisher, event_condition, event_order,
                                  event_type, has_injects, has_listeners,
                                  injects, is_async_listener,
                                  is_event_listener, listener_info,
                                  listener_metadata, listeners,
                                  needs_publisher)


# 测试用的事件类
class UserCreatedEvent:
    def __init__(self, user_id: int, username: str):
        self.user_id = user_id
        self.username = username


class OrderCreatedEvent:
    def __init__(self, order_id: int, amount: float):
        self.order_id = order_id
        self.amount = amount


class TestEventAnnotations(unittest.TestCase):
    """事件注解测试类"""

    def test_event_listener_annotation(self):
        """测试@EventListener注解"""

        class UserService:
            @EventListener
            def handle_user_created(self, event: UserCreatedEvent):
                pass

        method = UserService.handle_user_created

        # 验证注解属性
        self.assertTrue(hasattr(method, "__is_event_listener__"))
        self.assertTrue(method.__is_event_listener__)
        self.assertEqual(method.__event_type__, UserCreatedEvent)
        self.assertIsNone(method.__event_condition__)
        self.assertEqual(method.__event_order__, 0)
        self.assertFalse(method.__event_async__)

        # 验证元数据
        metadata = method.__event_listener_metadata__
        self.assertEqual(metadata.method, "handle_user_created")
        self.assertEqual(metadata.event_type, UserCreatedEvent)
        self.assertIsNone(metadata.condition)
        self.assertEqual(metadata.order, 0)
        self.assertFalse(metadata.async_exec)

    def test_event_listener_with_parameters(self):
        """测试@EventListener注解带参数"""

        class UserService:
            @EventListener(event_type=UserCreatedEvent, condition="event.user_id > 0", order=1, async_exec=True)
            def handle_user_created_with_params(self, event):
                pass

        method = UserService.handle_user_created_with_params

        # 验证注解属性
        self.assertTrue(method.__is_event_listener__)
        self.assertEqual(method.__event_type__, UserCreatedEvent)
        self.assertEqual(method.__event_condition__, "event.user_id > 0")
        self.assertEqual(method.__event_order__, 1)
        self.assertTrue(method.__event_async__)

        # 验证元数据
        metadata = method.__event_listener_metadata__
        self.assertEqual(metadata.method, "handle_user_created_with_params")
        self.assertEqual(metadata.event_type, UserCreatedEvent)
        self.assertEqual(metadata.condition, "event.user_id > 0")
        self.assertEqual(metadata.order, 1)
        self.assertTrue(metadata.async_exec)

    def test_event_listener_type_inference(self):
        """测试@EventListener注解的事件类型推断"""

        class OrderService:
            @EventListener
            def handle_order_created(self, event: OrderCreatedEvent):
                pass

            @EventListener
            def handle_any_event(self, event):
                pass

        # 有类型注解的方法应该能推断出事件类型
        method1 = OrderService.handle_order_created
        self.assertEqual(method1.__event_type__, OrderCreatedEvent)

        # 没有类型注解的方法事件类型为None
        method2 = OrderService.handle_any_event
        self.assertIsNone(method2.__event_type__)

    def test_async_event_listener_annotation(self):
        """测试@AsyncEventListener注解"""

        class EmailService:
            @AsyncEventListener
            async def send_welcome_email(self, event: UserCreatedEvent):
                pass

        method = EmailService.send_welcome_email

        # 验证注解属性
        self.assertTrue(method.__is_event_listener__)
        self.assertEqual(method.__event_type__, UserCreatedEvent)
        self.assertIsNone(method.__event_condition__)
        self.assertEqual(method.__event_order__, 0)
        self.assertTrue(method.__event_async__)  # AsyncEventListener默认为异步

        # 验证元数据
        metadata = method.__event_listener_metadata__
        self.assertEqual(metadata.method, "send_welcome_email")
        self.assertEqual(metadata.event_type, UserCreatedEvent)
        self.assertTrue(metadata.async_exec)

    def test_async_event_listener_with_parameters(self):
        """测试@AsyncEventListener注解带参数"""

        class NotificationService:
            @AsyncEventListener(condition="event.user_id > 100", order=2)
            async def send_notification(self, event: UserCreatedEvent):
                pass

        method = NotificationService.send_notification

        # 验证注解属性
        self.assertTrue(method.__is_event_listener__)
        self.assertEqual(method.__event_type__, UserCreatedEvent)
        self.assertEqual(method.__event_condition__, "event.user_id > 100")
        self.assertEqual(method.__event_order__, 2)
        self.assertTrue(method.__event_async__)

    def test_event_publisher_annotation(self):
        """测试@EventPublisher注解"""

        class UserService:
            @EventPublisher
            def set_event_publisher(self, publisher):
                self.event_publisher = publisher

        method = UserService.set_event_publisher

        # 验证注解属性
        self.assertTrue(hasattr(method, "__is_event_publisher__"))
        self.assertTrue(method.__is_event_publisher__)
        self.assertTrue(method.__event_publisher_inject__)

    def test_utility_functions(self):
        """测试工具函数"""

        class MixedService:
            @EventListener
            def handle_user_event(self, event: UserCreatedEvent):
                pass

            @AsyncEventListener
            def handle_order_event_async(self, event: OrderCreatedEvent):
                pass

            @EventPublisher
            def set_publisher(self, publisher):
                pass

            def normal_method(self):
                pass

        # 测试is_event_listener函数
        self.assertTrue(is_event_listener(MixedService.handle_user_event))
        self.assertTrue(is_event_listener(MixedService.handle_order_event_async))
        self.assertFalse(is_event_listener(MixedService.set_publisher))
        self.assertFalse(is_event_listener(MixedService.normal_method))

        # 测试needs_publisher函数
        self.assertTrue(needs_publisher(MixedService.set_publisher))
        self.assertFalse(needs_publisher(MixedService.handle_user_event))
        self.assertFalse(needs_publisher(MixedService.normal_method))

        # 测试is_async_listener函数
        self.assertFalse(is_async_listener(MixedService.handle_user_event))
        self.assertTrue(is_async_listener(MixedService.handle_order_event_async))

        # 测试listener_metadata函数
        metadata1 = listener_metadata(MixedService.handle_user_event)
        self.assertIsNotNone(metadata1)
        self.assertEqual(metadata1.method, "handle_user_event")

        metadata2 = listener_metadata(MixedService.normal_method)
        self.assertIsNone(metadata2)

    def test_event_parameter_functions(self):
        """测试事件参数获取函数"""

        class EventService:
            @EventListener(event_type=UserCreatedEvent, condition="event.user_id > 0", order=5)
            def handle_user_event(self, event):
                pass

            @AsyncEventListener
            def handle_async_event(self, event: OrderCreatedEvent):
                pass

        # 测试event_type函数
        self.assertEqual(event_type(EventService.handle_user_event), UserCreatedEvent)
        self.assertEqual(event_type(EventService.handle_async_event), OrderCreatedEvent)

        # 测试event_condition函数
        self.assertEqual(event_condition(EventService.handle_user_event), "event.user_id > 0")
        self.assertIsNone(event_condition(EventService.handle_async_event))

        # 测试event_order函数
        self.assertEqual(event_order(EventService.handle_user_event), 5)
        self.assertEqual(event_order(EventService.handle_async_event), 0)

    def test_find_event_listeners(self):
        """测试查找事件监听器"""

        class EventService:
            @EventListener
            def handle_user_created(self, event: UserCreatedEvent):
                pass

            @AsyncEventListener
            def handle_order_created(self, event: OrderCreatedEvent):
                pass

            @EventPublisher
            def set_publisher(self, publisher):
                pass

            def normal_method(self):
                pass

        event_listeners_result = listeners(EventService)

        # 验证找到的事件监听器
        self.assertIn("handle_user_created", event_listeners_result)
        self.assertIn("handle_order_created", event_listeners_result)
        self.assertNotIn("set_publisher", event_listeners_result)
        self.assertNotIn("normal_method", event_listeners_result)
        self.assertEqual(len(event_listeners_result), 2)

    def test_find_event_publisher_injects(self):
        """测试查找事件发布器注入"""

        class EventService:
            @EventListener
            def handle_event(self, event: UserCreatedEvent):
                pass

            @EventPublisher
            def set_publisher(self, publisher):
                pass

            @EventPublisher
            def inject_publisher(self, publisher):
                pass

            def normal_method(self):
                pass

        publisher_injects_result = injects(EventService)

        # 验证找到的发布器注入
        self.assertIn("set_publisher", publisher_injects_result)
        self.assertIn("inject_publisher", publisher_injects_result)
        self.assertNotIn("handle_event", publisher_injects_result)
        self.assertNotIn("normal_method", publisher_injects_result)
        self.assertEqual(len(publisher_injects_result), 2)

    def test_has_event_listeners(self):
        """测试检查类是否有事件监听器"""

        class EventService:
            @EventListener
            def handle_event(self, event: UserCreatedEvent):
                pass

        class PlainService:
            def normal_method(self):
                pass

        # 验证检查结果
        self.assertTrue(has_listeners(EventService))
        self.assertFalse(has_listeners(PlainService))

    def test_has_event_publisher_injects(self):
        """测试检查类是否有事件发布器注入"""

        class EventService:
            @EventPublisher
            def set_publisher(self, publisher):
                pass

        class PlainService:
            def normal_method(self):
                pass

        # 验证检查结果
        self.assertTrue(has_injects(EventService))
        self.assertFalse(has_injects(PlainService))

    def test_get_event_listener_info(self):
        """测试获取事件监听器信息"""

        class EventService:
            @EventListener(condition="event.user_id > 0", order=1)
            def handle_user_event(self, event: UserCreatedEvent):
                pass

            @AsyncEventListener(order=2)
            def handle_order_event(self, event: OrderCreatedEvent):
                pass

            def normal_method(self):
                pass

        listener_info_result = listener_info(EventService)

        # 验证返回的信息
        self.assertEqual(len(listener_info_result), 2)
        self.assertIn("handle_user_event", listener_info_result)
        self.assertIn("handle_order_event", listener_info_result)
        self.assertNotIn("normal_method", listener_info_result)

        # 验证handle_user_event的信息
        user_info = listener_info_result["handle_user_event"]
        self.assertEqual(user_info["event_type"], UserCreatedEvent)
        self.assertEqual(user_info["condition"], "event.user_id > 0")
        self.assertEqual(user_info["order"], 1)
        self.assertFalse(user_info["async_exec"])
        self.assertIsNotNone(user_info["metadata"])

        # 验证handle_order_event的信息
        order_info = listener_info_result["handle_order_event"]
        self.assertEqual(order_info["event_type"], OrderCreatedEvent)
        self.assertIsNone(order_info["condition"])
        self.assertEqual(order_info["order"], 2)
        self.assertTrue(order_info["async_exec"])
        self.assertIsNotNone(order_info["metadata"])


if __name__ == "__main__":
    unittest.main()

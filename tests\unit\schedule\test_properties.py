#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 配置属性绑定单元测试
"""

import os
import tempfile
import unittest
from pathlib import Path
from unittest.mock import patch

import yaml

from miniboot.schedule import (
    ExecutorType,
    JobStoreType,
    SchedulerConfigurationProperties,
    SchedulerEnvironmentConfigLoader,
    create_development_scheduler_properties,
    create_production_scheduler_properties,
    create_scheduler_properties_from_config,
    load_scheduler_config_from_yaml,
)


class TestSchedulerConfigurationProperties(unittest.TestCase):
    """测试SchedulerConfigurationProperties"""

    def setUp(self):
        """设置测试环境"""
        self.sample_config = {
            "miniboot": {
                "scheduler": {
                    "enabled": True,
                    "timezone": "Asia/Shanghai",
                    "daemon": True,
                    "concurrency": {"max-workers": 12, "max-instances": 4, "coalesce": True, "misfire-grace-time": 60},
                    "job-store": {"type": "memory"},
                    "executors": {"custom": {"type": "threadpool", "max-workers": 8, "thread-name-prefix": "custom-"}},
                },
                "logging": {"level": "DEBUG"},
            }
        }

    def test_from_yaml_config_basic(self):
        """测试基础YAML配置加载"""
        properties = SchedulerConfigurationProperties.from_yaml_config(self.sample_config)

        self.assertEqual(properties.timezone, "Asia/Shanghai")
        self.assertTrue(properties.daemon)
        self.assertEqual(properties.concurrency.max_workers, 12)
        self.assertEqual(properties.concurrency.max_instances, 4)
        self.assertTrue(properties.concurrency.coalesce)
        self.assertEqual(properties.concurrency.misfire_grace_time, 60)
        self.assertEqual(properties.job_store.type, JobStoreType.MEMORY)
        self.assertEqual(properties.logging_level, "DEBUG")

    def test_from_yaml_config_disabled_scheduler(self):
        """测试禁用调度器的配置"""
        config = {"miniboot": {"scheduler": {"enabled": False, "timezone": "UTC"}}}

        properties = SchedulerConfigurationProperties.from_yaml_config(config)
        self.assertEqual(properties.timezone, "UTC")
        self.assertTrue(properties.daemon)

    def test_from_yaml_config_database_job_store(self):
        """测试数据库作业存储配置"""
        config = {
            "miniboot": {
                "scheduler": {
                    "job-store": {
                        "type": "database",
                        "database": {
                            "url": "sqlite:///test.db",
                            "table-prefix": "test_",
                            "metadata": {"create-tables": True},
                            "connection-pool": {"pool-size": 8, "max-overflow": 15},
                        },
                    }
                }
            }
        }

        properties = SchedulerConfigurationProperties.from_yaml_config(config)
        self.assertEqual(properties.job_store.type, JobStoreType.DATABASE)
        self.assertEqual(properties.job_store.url, "sqlite:///test.db")
        self.assertEqual(properties.job_store.table_prefix, "test_")
        self.assertTrue(properties.job_store.create_tables)
        self.assertEqual(properties.job_store.pool_size, 8)
        self.assertEqual(properties.job_store.max_overflow, 15)

    def test_from_yaml_config_custom_executors(self):
        """测试自定义执行器配置"""
        config = {
            "miniboot": {
                "scheduler": {
                    "executors": {
                        "thread-pool": {"type": "threadpool", "max-workers": 15, "thread-name-prefix": "thread-"},
                        "process-pool": {"type": "processpool", "max-workers": 4},
                        "async-io": {"type": "asyncio", "max-workers": 20},
                    }
                }
            }
        }

        properties = SchedulerConfigurationProperties.from_yaml_config(config)

        # 验证自定义执行器
        self.assertIn("thread-pool", properties.executors)
        self.assertIn("process-pool", properties.executors)
        self.assertIn("async-io", properties.executors)

        thread_executor = properties.executors["thread-pool"]
        self.assertEqual(thread_executor.type, ExecutorType.THREAD_POOL)
        self.assertEqual(thread_executor.max_workers, 15)
        self.assertEqual(thread_executor.pool_kwargs["thread_name_prefix"], "thread-")

        process_executor = properties.executors["process-pool"]
        self.assertEqual(process_executor.type, ExecutorType.PROCESS_POOL)
        self.assertEqual(process_executor.max_workers, 4)

        async_executor = properties.executors["async-io"]
        self.assertEqual(async_executor.type, ExecutorType.ASYNCIO)
        self.assertEqual(async_executor.max_workers, 20)

    def test_create_development_config(self):
        """测试开发环境配置"""
        dev_properties = SchedulerConfigurationProperties.create_development_config(self.sample_config)

        # 开发环境应该限制工作线程数
        self.assertLessEqual(dev_properties.concurrency.max_workers, 5)

        # 如果启用了调试，日志级别应该是DEBUG
        debug_config = {"miniboot": {"scheduler": {"development": {"debug": True}}}}

        debug_properties = SchedulerConfigurationProperties.create_development_config(debug_config)
        self.assertEqual(debug_properties.logging_level, "DEBUG")

    def test_create_production_config(self):
        """测试生产环境配置"""
        prod_properties = SchedulerConfigurationProperties.create_production_config(self.sample_config)

        # 生产环境应该启用合并策略
        self.assertTrue(prod_properties.concurrency.coalesce)
        self.assertEqual(prod_properties.concurrency.misfire_grace_time, 60)

        # 测试性能配置
        perf_config = {"miniboot": {"scheduler": {"concurrency": {"max-workers": 5}, "performance": {"thread-pool": {"core-pool-size": 20}}}}}

        perf_properties = SchedulerConfigurationProperties.create_production_config(perf_config)
        self.assertGreaterEqual(perf_properties.concurrency.max_workers, 20)

    def test_validate_configuration(self):
        """测试配置验证"""
        # 有效配置
        self.assertTrue(SchedulerConfigurationProperties.validate_configuration(self.sample_config))

        # 无效的max_workers
        invalid_config1 = {"miniboot": {"scheduler": {"concurrency": {"max-workers": 0}}}}
        self.assertFalse(SchedulerConfigurationProperties.validate_configuration(invalid_config1))

        # 无效的作业存储类型
        invalid_config2 = {"miniboot": {"scheduler": {"job-store": {"type": "invalid_type"}}}}
        self.assertFalse(SchedulerConfigurationProperties.validate_configuration(invalid_config2))

        # 无效的执行器类型
        invalid_config3 = {"miniboot": {"scheduler": {"executors": {"invalid": {"type": "invalid_executor"}}}}}
        self.assertFalse(SchedulerConfigurationProperties.validate_configuration(invalid_config3))

    def test_get_configuration_summary(self):
        """测试配置摘要"""
        summary = SchedulerConfigurationProperties.get_configuration_summary(self.sample_config)

        self.assertTrue(summary["enabled"])
        self.assertEqual(summary["timezone"], "Asia/Shanghai")
        self.assertEqual(summary["max_workers"], 12)
        self.assertEqual(summary["job_store_type"], "memory")
        self.assertIn("custom", summary["executors"])
        self.assertTrue(summary["monitoring_enabled"])
        self.assertTrue(summary["task_management_enabled"])


class TestSchedulerEnvironmentConfigLoader(unittest.TestCase):
    """测试SchedulerEnvironmentConfigLoader"""

    def test_load_from_environment(self):
        """测试从环境变量加载配置"""
        env_vars = {
            "MINIBOOT_SCHEDULER_ENABLED": "true",
            "MINIBOOT_SCHEDULER_TIMEZONE": "UTC",
            "MINIBOOT_SCHEDULER_MAX_WORKERS": "20",
            "MINIBOOT_SCHEDULER_MAX_INSTANCES": "5",
            "MINIBOOT_SCHEDULER_JOB_STORE_TYPE": "database",
            "MINIBOOT_SCHEDULER_DATABASE_URL": "sqlite:///env_test.db",
            "MINIBOOT_SCHEDULER_TABLE_PREFIX": "env_",
            "MINIBOOT_SCHEDULER_CREATE_TABLES": "true",
            "MINIBOOT_SCHEDULER_POOL_SIZE": "8",
            "MINIBOOT_SCHEDULER_MAX_OVERFLOW": "12",
        }

        with patch.dict(os.environ, env_vars):
            config = SchedulerEnvironmentConfigLoader.load_from_environment()

            scheduler_config = config["miniboot"]["scheduler"]
            self.assertTrue(scheduler_config["enabled"])
            self.assertEqual(scheduler_config["timezone"], "UTC")

            concurrency = scheduler_config["concurrency"]
            self.assertEqual(concurrency["max-workers"], 20)
            self.assertEqual(concurrency["max-instances"], 5)

            job_store = scheduler_config["job-store"]
            self.assertEqual(job_store["type"], "database")

            database_config = job_store["database"]
            self.assertEqual(database_config["url"], "sqlite:///env_test.db")
            self.assertEqual(database_config["table-prefix"], "env_")
            self.assertTrue(database_config["metadata"]["create-tables"])
            self.assertEqual(database_config["connection-pool"]["pool-size"], 8)
            self.assertEqual(database_config["connection-pool"]["max-overflow"], 12)

    def test_merge_configurations(self):
        """测试配置合并"""
        config1 = {"miniboot": {"scheduler": {"enabled": True, "concurrency": {"max-workers": 10}}}}

        config2 = {"miniboot": {"scheduler": {"timezone": "UTC", "concurrency": {"max-instances": 5}}}}

        merged = SchedulerEnvironmentConfigLoader.merge_configurations(config1, config2)

        scheduler_config = merged["miniboot"]["scheduler"]
        self.assertTrue(scheduler_config["enabled"])
        self.assertEqual(scheduler_config["timezone"], "UTC")

        concurrency = scheduler_config["concurrency"]
        self.assertEqual(concurrency["max-workers"], 10)
        self.assertEqual(concurrency["max-instances"], 5)


class TestConfigurationFunctions(unittest.TestCase):
    """测试配置函数"""

    def setUp(self):
        """设置测试环境"""
        self.sample_config = {"miniboot": {"scheduler": {"enabled": True, "timezone": "Asia/Shanghai", "concurrency": {"max-workers": 8}}}}

    def test_create_scheduler_properties_from_config(self):
        """测试从配置创建调度器属性"""
        properties = create_scheduler_properties_from_config(self.sample_config)

        self.assertEqual(properties.timezone, "Asia/Shanghai")
        self.assertEqual(properties.concurrency.max_workers, 8)

    def test_create_development_scheduler_properties(self):
        """测试创建开发环境调度器属性"""
        properties = create_development_scheduler_properties(self.sample_config)

        self.assertLessEqual(properties.concurrency.max_workers, 5)

    def test_create_production_scheduler_properties(self):
        """测试创建生产环境调度器属性"""
        properties = create_production_scheduler_properties(self.sample_config)

        self.assertTrue(properties.concurrency.coalesce)
        self.assertEqual(properties.concurrency.misfire_grace_time, 60)

    def test_load_scheduler_config_from_yaml(self):
        """测试从YAML文件加载调度器配置"""
        # 创建临时YAML文件
        with tempfile.NamedTemporaryFile(mode="w", suffix=".yml", delete=False) as f:
            yaml.dump(self.sample_config, f)
            temp_file = f.name

        try:
            properties = load_scheduler_config_from_yaml(temp_file)
            self.assertEqual(properties.timezone, "Asia/Shanghai")
            self.assertEqual(properties.concurrency.max_workers, 8)
        finally:
            Path(temp_file).unlink()

    def test_load_scheduler_config_from_yaml_file_not_found(self):
        """测试加载不存在的YAML文件"""
        with self.assertRaises(RuntimeError):
            load_scheduler_config_from_yaml("non_existent_file.yml")


if __name__ == "__main__":
    unittest.main()

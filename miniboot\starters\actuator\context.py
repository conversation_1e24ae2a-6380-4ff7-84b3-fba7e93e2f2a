#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Actuator核心上下文 - 接口驱动重构版本

提供Actuator模块的核心上下文，实现MonitoringContext接口，专注于端点管理和数据采集。

核心特性:
- 实现MonitoringContext接口，支持接口驱动的集成
- 纯监控上下文，无Web框架依赖
- 智能端点注册和管理
- 高性能异步端点处理
- 完整的生命周期管理
- 条件化初始化逻辑
- 向后兼容性支持
"""

import asyncio
import time
from typing import TYPE_CHECKING, Any, Dict, Optional, Union

from loguru import logger

# 导入监控接口
from miniboot.monitoring.interfaces import (EndpointInfo, MetricsData,
                                            MonitoringContext)

from .endpoints import Endpoint, EndpointRegistry
from .properties import ActuatorProperties


class ActuatorContext(MonitoringContext):
    """Actuator核心上下文 - 纯监控上下文重构版本

    专注于端点管理和数据采集的纯监控上下文，不包含Web框架依赖。
    提供高性能的端点注册、管理和数据获取功能。
    """

    def __init__(self, properties: Optional[ActuatorProperties] = None, auto_load_config: bool = True):
        """初始化Actuator上下文

        Args:
            properties: Actuator配置属性
            auto_load_config: 是否自动从配置文件加载配置
        """
        # 初始化时间记录
        self._init_start_time = time.time()

        # 配置加载
        if properties is None and auto_load_config:
            try:
                from miniboot.env.environment import Environment
                env = Environment()
                properties = ActuatorProperties()
                logger.debug("Auto-loaded ActuatorProperties from environment")
            except Exception as e:
                logger.warning(f"Failed to auto-load config, using defaults: {e}")
                properties = ActuatorProperties()

        self._properties = properties or ActuatorProperties()

        # 核心组件初始化
        self._endpoint_registry = EndpointRegistry()
        self._is_started = False
        self._startup_time: Optional[float] = None

        # 性能监控
        self._metrics = {
            "endpoints_registered": 0,
            "total_requests": 0,
            "failed_requests": 0,
            "avg_response_time": 0.0
        }

        logger.info(f"ActuatorContext initialized with properties: enabled={self._properties.enabled}")

    @property
    def properties(self) -> ActuatorProperties:
        """获取配置属性"""
        return self._properties

    @property
    def endpoint_registry(self) -> EndpointRegistry:
        """获取端点注册表"""
        return self._endpoint_registry

    @property
    def is_started(self) -> bool:
        """检查是否已启动"""
        return self._is_started

    @property
    def startup_time(self) -> Optional[float]:
        """获取启动时间"""
        return self._startup_time

    def register_endpoint(self, endpoint: Endpoint) -> bool:
        """注册端点

        Args:
            endpoint: 要注册的端点

        Returns:
            bool: 注册是否成功
        """
        try:
            if not self._properties.enabled:
                logger.debug(f"Actuator disabled, skipping endpoint registration: {endpoint.id}")
                return False

            self._endpoint_registry.register(endpoint)
            self._metrics["endpoints_registered"] += 1
            logger.debug(f"Endpoint registered successfully: {endpoint.id}")
            return True

        except Exception as e:
            logger.error(f"Error registering endpoint {endpoint.id}: {e}")
            return False

    def unregister_endpoint(self, endpoint_id: str) -> bool:
        """注销端点

        Args:
            endpoint_id: 端点ID

        Returns:
            bool: 注销是否成功
        """
        try:
            success = self._endpoint_registry.unregister(endpoint_id)
            if success:
                self._metrics["endpoints_registered"] -= 1
                logger.debug(f"Endpoint unregistered successfully: {endpoint_id}")
            else:
                logger.warning(f"Failed to unregister endpoint: {endpoint_id}")

            return success

        except Exception as e:
            logger.error(f"Error unregistering endpoint {endpoint_id}: {e}")
            return False

    def get_endpoint(self, endpoint_id: str) -> Optional[Endpoint]:
        """获取端点

        Args:
            endpoint_id: 端点ID

        Returns:
            Optional[Endpoint]: 端点实例，如果不存在则返回None
        """
        return self._endpoint_registry.get_endpoint(endpoint_id)

    def get_endpoints(self) -> Dict[str, Endpoint]:
        """获取所有端点

        Returns:
            Dict[str, Endpoint]: 端点字典
        """
        return self._endpoint_registry.get_endpoints()

    async def invoke_endpoint(self, endpoint_id: str, **kwargs) -> Any:
        """调用端点

        Args:
            endpoint_id: 端点ID
            **kwargs: 端点参数

        Returns:
            Any: 端点返回值
        """
        start_time = time.time()

        try:
            endpoint = self.get_endpoint(endpoint_id)
            if not endpoint:
                raise ValueError(f"Endpoint not found: {endpoint_id}")

            if not endpoint.enabled:
                raise ValueError(f"Endpoint disabled: {endpoint_id}")

            # 调用端点
            if asyncio.iscoroutinefunction(endpoint.invoke):
                result = await endpoint.invoke(**kwargs)
            else:
                result = endpoint.invoke(**kwargs)

            # 更新指标
            self._metrics["total_requests"] += 1
            response_time = time.time() - start_time
            self._update_avg_response_time(response_time)

            logger.debug(f"Endpoint invoked successfully: {endpoint_id} (took {response_time:.3f}s)")
            return result

        except Exception as e:
            self._metrics["failed_requests"] += 1
            logger.error(f"Error invoking endpoint {endpoint_id}: {e}")
            raise

    def _update_avg_response_time(self, response_time: float):
        """更新平均响应时间"""
        total_requests = self._metrics["total_requests"]
        if total_requests == 1:
            self._metrics["avg_response_time"] = response_time
        else:
            current_avg = self._metrics["avg_response_time"]
            self._metrics["avg_response_time"] = (current_avg * (total_requests - 1) + response_time) / total_requests

    async def start(self) -> bool:
        """启动Actuator上下文

        Returns:
            bool: 启动是否成功
        """
        try:
            if self._is_started:
                logger.warning("ActuatorContext already started")
                return True

            if not self._properties.enabled:
                logger.info("ActuatorContext disabled, skipping startup")
                return False

            self._startup_time = time.time()
            self._is_started = True

            startup_duration = self._startup_time - self._init_start_time
            logger.info(f"ActuatorContext started successfully (took {startup_duration:.3f}s)")
            return True

        except Exception as e:
            logger.error(f"Failed to start ActuatorContext: {e}")
            return False

    async def stop(self) -> bool:
        """停止Actuator上下文

        Returns:
            bool: 停止是否成功
        """
        try:
            if not self._is_started:
                logger.warning("ActuatorContext not started")
                return True

            self._is_started = False
            self._startup_time = None

            logger.info("ActuatorContext stopped successfully")
            return True

        except Exception as e:
            logger.error(f"Failed to stop ActuatorContext: {e}")
            return False

    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标

        Returns:
            Dict[str, Any]: 性能指标
        """
        return {
            **self._metrics,
            "is_started": self._is_started,
            "startup_time": self._startup_time,
            "init_start_time": self._init_start_time,
            "uptime": time.time() - self._startup_time if self._startup_time else 0,
            "endpoints_count": len(self._endpoint_registry.get_endpoints())
        }

    def get_health(self) -> Dict[str, Any]:
        """获取健康状态

        Returns:
            Dict[str, Any]: 健康状态
        """
        return {
            "status": "UP" if self._is_started else "DOWN",
            "details": {
                "enabled": self._properties.enabled,
                "endpoints_registered": self._metrics["endpoints_registered"],
                "total_requests": self._metrics["total_requests"],
                "failed_requests": self._metrics["failed_requests"],
                "success_rate": (
                    (self._metrics["total_requests"] - self._metrics["failed_requests"]) /
                    self._metrics["total_requests"] * 100
                ) if self._metrics["total_requests"] > 0 else 100.0
            }
        }

    # ==================== MonitoringContext 接口实现 ====================

    def get_endpoints(self) -> Dict[str, EndpointInfo]:
        """获取所有端点信息 - MonitoringContext 接口实现

        Returns:
            Dict[str, EndpointInfo]: 端点名称到端点信息的映射
        """
        endpoint_infos = {}

        try:
            endpoints = self._endpoint_registry.get_endpoints()
            for name, endpoint in endpoints.items():
                # 转换为标准的 EndpointInfo 格式
                endpoint_infos[name] = EndpointInfo(
                    name=name,
                    path=getattr(endpoint, 'path', f'/actuator/{name}'),
                    methods=getattr(endpoint, 'methods', ['GET']),
                    description=getattr(endpoint, 'description', f'{name} endpoint'),
                    enabled=getattr(endpoint, 'enabled', True),
                    sensitive=getattr(endpoint, 'sensitive', False)
                )
        except Exception as e:
            logger.warning(f"Error getting endpoint information: {e}")

        return endpoint_infos

    def get_metrics(self) -> Dict[str, MetricsData]:
        """获取所有指标数据 - MonitoringContext 接口实现

        Returns:
            Dict[str, MetricsData]: 指标名称到指标数据的映射
        """
        metrics_data = {}

        try:
            # 转换内部指标为标准格式
            for metric_name, metric_value in self._metrics.items():
                metrics_data[metric_name] = MetricsData(
                    name=metric_name,
                    value=metric_value,
                    unit=self._get_metric_unit(metric_name),
                    tags={"source": "actuator_context"},
                    timestamp=time.time()
                )
        except Exception as e:
            logger.warning(f"Error getting metrics data: {e}")

        return metrics_data

    def is_started(self) -> bool:
        """检查监控上下文是否已启动 - MonitoringContext 接口实现

        Returns:
            bool: 是否已启动
        """
        return self._is_started

    def _get_metric_unit(self, metric_name: str) -> Optional[str]:
        """获取指标单位"""
        unit_mapping = {
            "endpoints_registered": "count",
            "total_requests": "count",
            "failed_requests": "count",
            "avg_response_time": "ms",
            "startup_time": "ms"
        }
        return unit_mapping.get(metric_name)

    def __repr__(self) -> str:
        return (f"ActuatorContext(enabled={self._properties.enabled}, "
                f"started={self._is_started}, endpoints={len(self._endpoint_registry.get_endpoints())})")

#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean模块工具类和辅助函数
"""

import inspect
import threading
from collections import defaultdict, deque
from typing import Any, Dict, List, Optional, Set, Tuple, Type, get_type_hints

# 导入标准化的异常类
from ..errors import BeanCircularDependencyError as CircularDependencyError
from ..errors import BeanCreationError
from ..errors import BeanNotFoundError as NoSuchBeanDefinitionError
from ..errors import MultipleBeanFoundError as NoUniqueBeanDefinitionError
# 导入依赖图
from .graph import DependencyGraph


class BeanUtils:
    """Bean工具类

    提供Bean相关的工具方法，包括类型检查、名称生成、反射操作等。
    """

    @staticmethod
    def generate_bean_name(bean_class: Type) -> str:
        """生成Bean名称

        根据类名生成默认的Bean名称（首字母小写）。

        Args:
            bean_class: Bean类

        Returns:
            str: 生成的Bean名称
        """
        class_name = bean_class.__name__
        return class_name[0].lower() + class_name[1:] if class_name else ""

    @staticmethod
    def is_concrete_class(bean_class: Type) -> bool:
        """检查是否为具体类

        Args:
            bean_class: 要检查的类

        Returns:
            bool: 如果是具体类返回True，否则返回False
        """
        return (
            inspect.isclass(bean_class) and
            not inspect.isabstract(bean_class) and
            bean_class is not object
        )

    @staticmethod
    def is_instantiable(bean_class: Type) -> bool:
        """检查类是否可实例化

        Args:
            bean_class: 要检查的类

        Returns:
            bool: 如果可实例化返回True，否则返回False
        """
        try:
            if not BeanUtils.is_concrete_class(bean_class):
                return False

            # 检查构造函数
            init_method = getattr(bean_class, '__init__', None)
            if init_method:
                sig = inspect.signature(init_method)
                # 检查是否有必需的参数（除了self）
                for param in sig.parameters.values():
                    if param.name != 'self' and param.default == inspect.Parameter.empty:
                        return False

            return True
        except Exception:
            return False

    @staticmethod
    def get_constructor_parameters(bean_class: Type) -> List[Tuple[str, Type, bool]]:
        """获取构造函数参数信息

        Args:
            bean_class: Bean类

        Returns:
            List[Tuple[str, Type, bool]]: 参数信息列表 (参数名, 类型, 是否必需)
        """
        try:
            init_method = getattr(bean_class, '__init__', None)
            if not init_method:
                return []

            sig = inspect.signature(init_method)
            type_hints = get_type_hints(init_method)

            parameters = []
            for param_name, param in sig.parameters.items():
                if param_name == 'self':
                    continue

                param_type = type_hints.get(param_name, param.annotation)
                if param_type == inspect.Parameter.empty:
                    param_type = Any

                is_required = param.default == inspect.Parameter.empty
                parameters.append((param_name, param_type, is_required))

            return parameters
        except Exception:
            return []

    @staticmethod
    def get_autowired_fields(bean_class: Type) -> List[Tuple[str, Type, bool]]:
        """获取@Autowired注解的字段信息

        Args:
            bean_class: Bean类

        Returns:
            List[Tuple[str, Type, bool]]: 字段信息列表 (字段名, 类型, 是否必需)
        """
        fields = []

        try:
            # 检查类是否有__autowired_fields__属性
            if hasattr(bean_class, '__autowired_fields__'):
                autowired_fields = bean_class.__autowired_fields__
                type_hints = get_type_hints(bean_class)

                for field_name, metadata in autowired_fields.items():
                    field_type = type_hints.get(field_name, Any)
                    required = metadata.required if metadata else True
                    fields.append((field_name, field_type, required))
        except Exception:
            pass

        return fields

    @staticmethod
    def has_lifecycle_methods(bean_class: Type) -> Dict[str, bool]:
        """检查Bean是否有生命周期方法

        Args:
            bean_class: Bean类

        Returns:
            Dict[str, bool]: 生命周期方法存在情况
        """
        result = {
            'has_init_method': False,
            'has_destroy_method': False,
            'implements_initializing_bean': False,
            'implements_disposable_bean': False
        }

        try:
            # 检查是否实现了生命周期接口
            from .base import DisposableBean, InitializingBean

            result['implements_initializing_bean'] = issubclass(bean_class, InitializingBean)
            result['implements_disposable_bean'] = issubclass(bean_class, DisposableBean)

            # 检查常见的生命周期方法
            result['has_init_method'] = hasattr(bean_class, 'init') or hasattr(bean_class, 'initialize')
            result['has_destroy_method'] = hasattr(bean_class, 'destroy') or hasattr(bean_class, 'cleanup')

        except Exception:
            pass

        return result


class ReflectionUtils:
    """反射工具类

    提供反射相关的工具方法。
    """

    @staticmethod
    def invoke_method(obj: Any, method_name: str, *args, **kwargs) -> Any:
        """调用对象的方法

        Args:
            obj: 对象实例
            method_name: 方法名称
            *args: 位置参数
            **kwargs: 关键字参数

        Returns:
            Any: 方法返回值

        Raises:
            AttributeError: 如果方法不存在
            TypeError: 如果方法不可调用
        """
        method = getattr(obj, method_name)
        if not callable(method):
            raise TypeError(f"'{method_name}' is not callable")

        return method(*args, **kwargs)

    @staticmethod
    def set_field_value(obj: Any, field_name: str, value: Any) -> None:
        """设置对象的字段值

        Args:
            obj: 对象实例
            field_name: 字段名称
            value: 字段值
        """
        setattr(obj, field_name, value)

    @staticmethod
    def get_field_value(obj: Any, field_name: str, default: Any = None) -> Any:
        """获取对象的字段值

        Args:
            obj: 对象实例
            field_name: 字段名称
            default: 默认值

        Returns:
            Any: 字段值
        """
        return getattr(obj, field_name, default)

    @staticmethod
    def has_method(obj: Any, method_name: str) -> bool:
        """检查对象是否有指定方法

        Args:
            obj: 对象实例
            method_name: 方法名称

        Returns:
            bool: 如果有方法返回True，否则返回False
        """
        return hasattr(obj, method_name) and callable(getattr(obj, method_name))

    @staticmethod
    def has_field(obj: Any, field_name: str) -> bool:
        """检查对象是否有指定字段

        Args:
            obj: 对象实例
            field_name: 字段名称

        Returns:
            bool: 如果有字段返回True，否则返回False
        """
        return hasattr(obj, field_name)

    @staticmethod
    def get_class_hierarchy(cls: Type) -> List[Type]:
        """获取类的继承层次结构

        Args:
            cls: 类

        Returns:
            List[Type]: 继承层次结构列表（从子类到父类）
        """
        hierarchy = []
        for base_cls in inspect.getmro(cls):
            if base_cls is not object:
                hierarchy.append(base_cls)
        return hierarchy


# 工厂函数
def create_dependency_graph() -> DependencyGraph:
    """创建依赖关系图 - 统一入口

    Returns:
        DependencyGraph: 依赖关系图实例
    """
    return DependencyGraph()


def validate_bean_definition(bean_definition: "BeanDefinition") -> List[str]:
    """验证Bean定义

    Args:
        bean_definition: Bean定义

    Returns:
        List[str]: 验证错误列表，如果为空则验证通过
    """
    errors = []

    try:
        # 检查Bean名称
        if not bean_definition.bean_name or not bean_definition.bean_name.strip():
            errors.append("Bean name cannot be empty")

        # 检查Bean类
        if not bean_definition.bean_class:
            errors.append("Bean class cannot be None")
        elif not BeanUtils.is_concrete_class(bean_definition.bean_class):
            errors.append(f"Bean class {bean_definition.bean_class} is not a concrete class")

        # 检查作用域
        if not bean_definition.scope:
            errors.append("Bean scope cannot be None")

        # 检查构造函数参数
        if bean_definition.has_constructor_args():
            for i, arg in enumerate(bean_definition.constructor_args):
                if arg.value is None and arg.ref is None:
                    errors.append(f"Constructor argument at index {i} has neither value nor ref")
                elif arg.value is not None and arg.ref is not None:
                    errors.append(f"Constructor argument at index {i} has both value and ref")

        # 检查属性值
        for prop in bean_definition.property_values:
            if prop.value is None and prop.ref is None:
                errors.append(f"Property '{prop.name}' has neither value nor ref")
            elif prop.value is not None and prop.ref is not None:
                errors.append(f"Property '{prop.name}' has both value and ref")

    except Exception as e:
        errors.append(f"Validation error: {str(e)}")

    return errors


def format_bean_info(bean: Any, bean_name: str) -> str:
    """格式化Bean信息

    Args:
        bean: Bean实例
        bean_name: Bean名称

    Returns:
        str: 格式化的Bean信息
    """
    bean_type = type(bean).__name__
    bean_module = type(bean).__module__

    info_parts = [
        f"Bean Name: {bean_name}",
        f"Bean Type: {bean_type}",
        f"Bean Module: {bean_module}",
        f"Bean ID: {id(bean)}"
    ]

    # 添加生命周期信息
    lifecycle_info = BeanUtils.has_lifecycle_methods(type(bean))
    if any(lifecycle_info.values()):
        lifecycle_parts = []
        if lifecycle_info['implements_initializing_bean']:
            lifecycle_parts.append("InitializingBean")
        if lifecycle_info['implements_disposable_bean']:
            lifecycle_parts.append("DisposableBean")
        if lifecycle_parts:
            info_parts.append(f"Lifecycle Interfaces: {', '.join(lifecycle_parts)}")

    return "\n".join(info_parts)

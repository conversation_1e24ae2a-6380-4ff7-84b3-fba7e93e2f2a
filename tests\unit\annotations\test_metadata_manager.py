#!/usr/bin/env python
"""
* @author: cz
* @description: 元数据管理器测试

测试元数据管理器的功能，包括存储、查询、验证和序列化。
"""

import json
import unittest
from dataclasses import dataclass

from miniboot.annotations import (
    AutowiredMetadata,
    BeanMetadata,
    ComponentMetadata,
    MetadataRegistry,
    MetadataSerializer,
    MetadataValidator,
    Scope,
    deserialize_metadata,
    get_global_registry,
    serialize_metadata,
    validate_metadata,
)


# 测试用的类
class TestService:
    pass


class TestRepository:
    pass


@dataclass
class CustomMetadata:
    """自定义元数据类"""

    name: str
    value: int = 0


class TestMetadataRegistry(unittest.TestCase):
    """元数据注册表测试类"""

    def setUp(self):
        """测试前准备"""
        self.registry = MetadataRegistry()

    def test_register_class_metadata(self):
        """测试注册类元数据"""
        metadata = ComponentMetadata(name="testService", scope=Scope.SINGLETON)

        self.registry.register_class_metadata(TestService, "Component", metadata)

        # 验证注册成功
        retrieved = self.registry.get_class_metadata(TestService, "Component")
        self.assertEqual(retrieved.name, "testService")
        self.assertEqual(retrieved.scope, Scope.SINGLETON)

    def test_register_method_metadata(self):
        """测试注册方法元数据"""
        metadata = BeanMetadata(name="testBean", scope=Scope.PROTOTYPE)

        self.registry.register_method_metadata(TestService, "create_bean", "Bean", metadata)

        # 验证注册成功
        retrieved = self.registry.get_method_metadata(TestService, "create_bean", "Bean")
        self.assertEqual(retrieved.name, "testBean")
        self.assertEqual(retrieved.scope, Scope.PROTOTYPE)

    def test_register_field_metadata(self):
        """测试注册字段元数据"""
        metadata = AutowiredMetadata(required=True, name="testDep")

        self.registry.register_field_metadata(TestService, "dependency", "Autowired", metadata)

        # 验证注册成功
        retrieved = self.registry.get_field_metadata(TestService, "dependency", "Autowired")
        self.assertEqual(retrieved.required, True)
        self.assertEqual(retrieved.name, "testDep")

    def test_get_all_metadata(self):
        """测试获取所有元数据"""
        # 注册多个元数据
        comp_metadata = ComponentMetadata(name="testService")
        bean_metadata = BeanMetadata(name="testBean")

        self.registry.register_class_metadata(TestService, "Component", comp_metadata)
        self.registry.register_method_metadata(TestService, "create_bean", "Bean", bean_metadata)

        # 获取类的所有元数据
        all_class_metadata = self.registry.get_class_metadata(TestService)
        self.assertIn("Component", all_class_metadata)
        self.assertEqual(all_class_metadata["Component"].name, "testService")

        # 获取方法的所有元数据
        all_method_metadata = self.registry.get_method_metadata(TestService, "create_bean")
        self.assertIn("Bean", all_method_metadata)
        self.assertEqual(all_method_metadata["Bean"].name, "testBean")

    def test_find_by_annotation(self):
        """测试按注解类型查找"""
        metadata1 = ComponentMetadata(name="service1")
        metadata2 = ComponentMetadata(name="service2")

        self.registry.register_class_metadata(TestService, "Component", metadata1)
        self.registry.register_class_metadata(TestRepository, "Component", metadata2)

        # 查找所有Component注解
        components = self.registry.find_by_annotation("Component")
        self.assertEqual(len(components), 2)

        # 验证包含正确的类名
        class_names = [name.split(".")[-1] for name in components]
        self.assertIn("TestService", class_names)
        self.assertIn("TestRepository", class_names)

    def test_statistics(self):
        """测试统计信息"""
        # 初始统计
        stats = self.registry.get_statistics()
        self.assertEqual(stats["total_classes"], 0)
        self.assertEqual(stats["total_methods"], 0)
        self.assertEqual(stats["total_fields"], 0)

        # 注册一些元数据
        self.registry.register_class_metadata(TestService, "Component", ComponentMetadata())
        self.registry.register_method_metadata(TestService, "method1", "Bean", BeanMetadata())
        self.registry.register_field_metadata(TestService, "field1", "Autowired", AutowiredMetadata())

        # 检查更新后的统计
        stats = self.registry.get_statistics()
        self.assertEqual(stats["total_classes"], 1)
        self.assertEqual(stats["total_methods"], 1)
        self.assertEqual(stats["total_fields"], 1)
        self.assertEqual(stats["annotation_counts"]["Component"], 1)
        self.assertEqual(stats["annotation_counts"]["Bean"], 1)
        self.assertEqual(stats["annotation_counts"]["Autowired"], 1)

    def test_clear(self):
        """测试清空注册表"""
        # 注册一些元数据
        self.registry.register_class_metadata(TestService, "Component", ComponentMetadata())

        # 验证有数据
        self.assertIsNotNone(self.registry.get_class_metadata(TestService, "Component"))

        # 清空
        self.registry.clear()

        # 验证已清空
        self.assertIsNone(self.registry.get_class_metadata(TestService, "Component"))
        stats = self.registry.get_statistics()
        self.assertEqual(stats["total_classes"], 0)


class TestMetadataValidator(unittest.TestCase):
    """元数据验证器测试类"""

    def test_validate_component_metadata(self):
        """测试组件元数据验证"""
        # 有效的元数据
        valid_metadata = ComponentMetadata(name="testService", depends_on=["dep1", "dep2"])
        errors = MetadataValidator.validate_component_metadata(valid_metadata)
        self.assertEqual(len(errors), 0)

        # 无效的名称
        invalid_metadata = ComponentMetadata(name="")
        errors = MetadataValidator.validate_component_metadata(invalid_metadata)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any("empty" in error for error in errors))

        # 无效的依赖
        invalid_metadata = ComponentMetadata(depends_on=["valid", ""])
        errors = MetadataValidator.validate_component_metadata(invalid_metadata)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any("dependency" in error for error in errors))

    def test_validate_bean_metadata(self):
        """测试Bean元数据验证"""
        # 有效的元数据
        valid_metadata = BeanMetadata(name="testBean", init_method="init", destroy_method="destroy")
        errors = MetadataValidator.validate_bean_metadata(valid_metadata)
        self.assertEqual(len(errors), 0)

        # 无效的方法名
        invalid_metadata = BeanMetadata(init_method="")
        errors = MetadataValidator.validate_bean_metadata(invalid_metadata)
        self.assertGreater(len(errors), 0)

    def test_validate_autowired_metadata(self):
        """测试自动装配元数据验证"""
        # 有效的元数据
        valid_metadata = AutowiredMetadata(required=True, name="testDep")
        errors = MetadataValidator.validate_autowired_metadata(valid_metadata)
        self.assertEqual(len(errors), 0)

        # 无效的名称
        invalid_metadata = AutowiredMetadata(name="")
        errors = MetadataValidator.validate_autowired_metadata(invalid_metadata)
        self.assertGreater(len(errors), 0)

    def test_validate_metadata_generic(self):
        """测试通用元数据验证"""
        # 测试已知类型
        component_metadata = ComponentMetadata(name="test")
        errors = validate_metadata(component_metadata)
        self.assertEqual(len(errors), 0)

        # 测试自定义类型
        custom_metadata = CustomMetadata(name="test", value=42)
        errors = validate_metadata(custom_metadata)
        self.assertEqual(len(errors), 0)

        # 测试非dataclass
        invalid_metadata = "not a dataclass"
        errors = validate_metadata(invalid_metadata)
        self.assertGreater(len(errors), 0)


class TestMetadataSerializer(unittest.TestCase):
    """元数据序列化器测试类"""

    def test_to_dict(self):
        """测试转换为字典"""
        metadata = ComponentMetadata(name="testService", scope=Scope.SINGLETON)

        result = MetadataSerializer.to_dict(metadata)

        self.assertIsInstance(result, dict)
        self.assertEqual(result["name"], "testService")
        # Enum被序列化为特殊格式
        self.assertIsInstance(result["scope"], dict)
        self.assertEqual(result["scope"]["__enum__"], "Scope")
        self.assertEqual(result["scope"]["value"], "singleton")
        self.assertEqual(result["__type__"], "ComponentMetadata")

    def test_from_dict(self):
        """测试从字典创建"""
        data = {
            "name": "testService",
            "scope": {"__enum__": "Scope", "value": "singleton"},
            "lazy": False,
            "primary": False,
            "depends_on": [],
            "__type__": "ComponentMetadata",
        }

        metadata = MetadataSerializer.from_dict(data)

        self.assertIsInstance(metadata, ComponentMetadata)
        self.assertEqual(metadata.name, "testService")
        self.assertEqual(metadata.scope, Scope.SINGLETON)

    def test_json_serialization(self):
        """测试JSON序列化"""
        metadata = ComponentMetadata(name="testService")

        # 序列化
        json_str = serialize_metadata(metadata, "json")
        self.assertIsInstance(json_str, str)

        # 验证JSON格式
        data = json.loads(json_str)
        self.assertEqual(data["name"], "testService")
        self.assertEqual(data["__type__"], "ComponentMetadata")

        # 反序列化
        restored = deserialize_metadata(json_str, "json")
        self.assertIsInstance(restored, ComponentMetadata)
        self.assertEqual(restored.name, "testService")

    def test_pickle_serialization(self):
        """测试Pickle序列化"""
        metadata = ComponentMetadata(name="testService", scope=Scope.PROTOTYPE)

        # 序列化
        pickle_data = serialize_metadata(metadata, "pickle")
        self.assertIsInstance(pickle_data, bytes)

        # 反序列化
        restored = deserialize_metadata(pickle_data, "pickle")
        self.assertIsInstance(restored, ComponentMetadata)
        self.assertEqual(restored.name, "testService")
        self.assertEqual(restored.scope, Scope.PROTOTYPE)

    def test_dict_serialization(self):
        """测试字典序列化"""
        metadata = BeanMetadata(name="testBean", scope=Scope.SINGLETON)

        # 序列化
        data = serialize_metadata(metadata, "dict")
        self.assertIsInstance(data, dict)
        self.assertEqual(data["name"], "testBean")

        # 反序列化
        restored = deserialize_metadata(data, "dict")
        self.assertIsInstance(restored, BeanMetadata)
        self.assertEqual(restored.name, "testBean")


class TestGlobalRegistry(unittest.TestCase):
    """全局注册表测试类"""

    def test_global_registry_singleton(self):
        """测试全局注册表单例"""
        registry1 = get_global_registry()
        registry2 = get_global_registry()

        # 应该是同一个实例
        self.assertIs(registry1, registry2)

    def test_global_registry_persistence(self):
        """测试全局注册表数据持久性"""
        registry = get_global_registry()

        # 清空以确保干净状态
        registry.clear()

        # 注册数据
        metadata = ComponentMetadata(name="globalTest")
        registry.register_class_metadata(TestService, "Component", metadata)

        # 从另一个引用获取数据
        registry2 = get_global_registry()
        retrieved = registry2.get_class_metadata(TestService, "Component")

        self.assertIsNotNone(retrieved)
        self.assertEqual(retrieved.name, "globalTest")

        # 清理
        registry.clear()

    def test_memory_optimization_features(self):
        """测试内存优化功能"""
        # 测试启用弱引用的注册表
        registry = MetadataRegistry(enable_weak_refs=True, max_entries=100)

        # 测试内存使用情况
        memory_usage = registry.get_memory_usage()
        self.assertIn("class_metadata_count", memory_usage)
        self.assertIn("estimated_memory_bytes", memory_usage)

        # 测试缓存清理
        for i in range(150):  # 超过max_entries
            metadata = ComponentMetadata(name=f"component_{i}")
            cls = type(f"TestClass_{i}", (), {})
            registry.register_class_metadata(cls, "Component", metadata)

        # 应该触发清理
        registry._cleanup_if_needed()

        # 验证缓存大小被限制
        self.assertLessEqual(len(registry._class_metadata), 150)

    def test_cleanup_functionality(self):
        """测试清理功能"""
        registry = MetadataRegistry(max_entries=10)

        # 添加超过限制的条目
        for i in range(15):
            metadata = ComponentMetadata(name=f"component_{i}")
            cls = type(f"TestClass_{i}", (), {})
            registry.register_class_metadata(cls, "Component", metadata)

        # 验证清理被触发
        total_entries = len(registry._class_metadata) + len(registry._method_metadata) + len(registry._field_metadata)
        self.assertLessEqual(total_entries, 15)

    def test_remove_entry_functionality(self):
        """测试移除条目功能"""
        registry = MetadataRegistry()

        # 添加一些访问计数
        test_key = "test.key.for.removal"
        registry._access_count[test_key] = 5

        # 验证访问计数存在
        self.assertEqual(registry._access_count[test_key], 5)

        # 手动移除条目
        registry._remove_entry(test_key)

        # 验证访问计数已被移除
        self.assertNotIn(test_key, registry._access_count)


if __name__ == "__main__":
    unittest.main()

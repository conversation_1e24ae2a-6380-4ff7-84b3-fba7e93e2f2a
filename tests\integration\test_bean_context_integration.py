#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean模块与Context模块集成测试
"""

import asyncio
import unittest
from typing import Any, Dict, List
from unittest.mock import Mock, patch

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import Default<PERSON>eanFactory
from miniboot.context.application import DefaultApplicationContext


class TestService:
    """测试服务类"""

    def __init__(self, name: str = "test-service"):
        self.name = name
        self.initialized = False
        self.context = None

    def init(self):
        """初始化方法"""
        self.initialized = True

    def get_info(self) -> Dict[str, Any]:
        return {
            "name": self.name,
            "initialized": self.initialized,
            "has_context": self.context is not None
        }


class TestRepository:
    """测试仓储类"""

    def __init__(self, database_url: str = "memory://test"):
        self.database_url = database_url
        self.connected = False
        self.data = []

    def connect(self):
        """连接数据库"""
        self.connected = True
        self.data = ["item1", "item2", "item3"]

    def find_all(self) -> List[str]:
        if not self.connected:
            raise RuntimeError("Database not connected")
        return self.data.copy()

    def save(self, item: str):
        if not self.connected:
            raise RuntimeError("Database not connected")
        self.data.append(item)


class TestController:
    """测试控制器类"""

    def __init__(self):
        self.service = None
        self.repository = None
        self.context = None

    def set_service(self, service: TestService):
        self.service = service

    def set_repository(self, repository: TestRepository):
        self.repository = repository

    def set_context(self, context: DefaultApplicationContext):
        self.context = context

    def handle_request(self, action: str, data: str = None) -> Dict[str, Any]:
        """处理请求"""
        if not self.service or not self.repository:
            raise ValueError("Dependencies not injected")

        if action == "get_info":
            return {
                "service_info": self.service.get_info(),
                "repository_connected": self.repository.connected,
                "context_available": self.context is not None
            }
        elif action == "save_data":
            if data:
                self.repository.save(data)
                return {"status": "saved", "data": data}
            else:
                raise ValueError("No data provided")
        elif action == "get_all":
            return {"data": self.repository.find_all()}
        else:
            raise ValueError(f"Unknown action: {action}")


class TestBeanContextIntegration(unittest.TestCase):
    """Bean模块与Context模块集成测试"""

    def setUp(self):
        """测试前置设置"""
        self.context = DefaultApplicationContext()

    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.context, 'close'):
            self.context.close()

    def test_context_bean_registration(self):
        """测试通过Context注册Bean"""
        # 创建Bean定义
        service_def = BeanDefinition(
            bean_name="testService",
            bean_class=TestService,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )

        # 通过Context注册Bean
        self.context._bean_registry.register_bean_definition("testService", service_def)

        # 验证Bean已注册
        self.assertTrue(self.context.contains_bean("testService"))

        # 获取Bean
        service = self.context.get_bean("testService")
        self.assertIsInstance(service, TestService)
        self.assertTrue(service.initialized)

    def test_context_dependency_injection(self):
        """测试Context中的依赖注入"""
        # 注册服务Bean
        service_def = BeanDefinition(
            bean_name="testService",
            bean_class=TestService,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        self.context.register_bean_definition("testService", service_def)

        # 注册仓储Bean
        repository_def = BeanDefinition(
            bean_name="testRepository",
            bean_class=TestRepository,
            scope=BeanScope.SINGLETON,
            init_method_name="connect"
        )
        self.context.register_bean_definition("testRepository", repository_def)

        # 注册控制器Bean，注入依赖
        controller_def = BeanDefinition(
            bean_name="testController",
            bean_class=TestController,
            scope=BeanScope.SINGLETON
        )
        controller_def.add_property_value("service", ref="testService")
        controller_def.add_property_value("repository", ref="testRepository")
        controller_def.add_property_value("context", ref="applicationContext")

        self.context.register_bean_definition("testController", controller_def)

        # 获取控制器Bean
        controller = self.context.get_bean("testController")
        self.assertIsInstance(controller, TestController)

        # 验证依赖注入
        self.assertIsNotNone(controller.service)
        self.assertIsNotNone(controller.repository)
        self.assertIsInstance(controller.service, TestService)
        self.assertIsInstance(controller.repository, TestRepository)

        # 验证服务和仓储已初始化
        self.assertTrue(controller.service.initialized)
        self.assertTrue(controller.repository.connected)

        # 测试业务功能
        info = controller.handle_request("get_info")
        self.assertTrue(info["service_info"]["initialized"])
        self.assertTrue(info["repository_connected"])

    def test_context_lifecycle_management(self):
        """测试Context的生命周期管理"""
        # 注册带生命周期的Bean
        service_def = BeanDefinition(
            bean_name="lifecycleService",
            bean_class=TestService,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )

        self.context.register_bean_definition("lifecycleService", service_def)

        # 启动Context
        self.context.refresh()

        # 验证Bean已创建并初始化
        service = self.context.get_bean("lifecycleService")
        self.assertTrue(service.initialized)

        # 关闭Context
        self.context.close()

        # 验证Context状态
        self.assertFalse(self.context.is_active())

    def test_context_bean_scopes(self):
        """测试Context中的Bean作用域"""
        # 注册单例Bean
        singleton_def = BeanDefinition(
            bean_name="singletonService",
            bean_class=TestService,
            scope=BeanScope.SINGLETON
        )
        self.context.register_bean_definition("singletonService", singleton_def)

        # 注册原型Bean
        prototype_def = BeanDefinition(
            bean_name="prototypeService",
            bean_class=TestService,
            scope=BeanScope.PROTOTYPE
        )
        self.context.register_bean_definition("prototypeService", prototype_def)

        # 测试单例作用域
        singleton1 = self.context.get_bean("singletonService")
        singleton2 = self.context.get_bean("singletonService")
        self.assertIs(singleton1, singleton2)

        # 测试原型作用域
        prototype1 = self.context.get_bean("prototypeService")
        prototype2 = self.context.get_bean("prototypeService")
        self.assertIsNot(prototype1, prototype2)

    def test_context_error_handling(self):
        """测试Context的错误处理"""
        # 测试获取不存在的Bean
        with self.assertRaises(Exception):
            self.context.get_bean("nonexistentBean")

        # 测试注册无效的Bean定义
        invalid_def = BeanDefinition(
            bean_name="",  # 空名称
            bean_class=TestService,
            scope=BeanScope.SINGLETON
        )

        with self.assertRaises(Exception):
            self.context.register_bean_definition("", invalid_def)

    def test_context_bean_post_processing(self):
        """测试Context中的Bean后处理"""
        # 创建Bean后处理器
        class TestBeanPostProcessor:
            def __init__(self):
                self.processed_beans = []

            def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
                if hasattr(bean, 'name'):
                    bean.name = f"processed_{bean.name}"
                return bean

            def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
                self.processed_beans.append(bean_name)
                return bean

        # 注册Bean后处理器
        processor = TestBeanPostProcessor()
        self.context.add_bean_post_processor(processor)

        # 注册测试Bean
        service_def = BeanDefinition(
            bean_name="processedService",
            bean_class=TestService,
            scope=BeanScope.SINGLETON
        )
        self.context.register_bean_definition("processedService", service_def)

        # 获取Bean并验证后处理
        service = self.context.get_bean("processedService")
        self.assertTrue(service.name.startswith("processed_"))
        self.assertIn("processedService", processor.processed_beans)

    def test_context_configuration_integration(self):
        """测试Context与配置的集成"""
        # 模拟配置数据
        config_data = {
            "database.url": "postgresql://localhost:5432/test",
            "service.name": "integration-test-service",
            "app.debug": True
        }

        # 注册配置Bean
        for key, value in config_data.items():
            self.context.register_singleton(key, value)

        # 注册使用配置的Bean
        repository_def = BeanDefinition(
            bean_name="configuredRepository",
            bean_class=TestRepository,
            scope=BeanScope.SINGLETON
        )
        repository_def.add_constructor_arg(0, ref="database.url")

        self.context.register_bean_definition("configuredRepository", repository_def)

        # 验证配置注入
        repository = self.context.get_bean("configuredRepository")
        self.assertEqual(repository.database_url, "postgresql://localhost:5432/test")

    def test_context_event_handling(self):
        """测试Context的事件处理"""
        events_received = []

        # 创建事件监听器
        def on_context_refreshed(event):
            events_received.append("context_refreshed")

        def on_context_closed(event):
            events_received.append("context_closed")

        # 注册事件监听器
        self.context.add_application_listener(on_context_refreshed)
        self.context.add_application_listener(on_context_closed)

        # 触发事件
        self.context.refresh()
        self.context.close()

        # 验证事件处理
        self.assertIn("context_refreshed", events_received)
        self.assertIn("context_closed", events_received)


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 音频播放 Starter 使用示例
"""

import asyncio
from miniboot.context import DefaultApplicationContext
from miniboot.annotations import Component, Autowired
from miniboot.starters.audio import AudioService, AudioItem


@Component
class NotificationService:
    """通知服务示例"""

    @Autowired
    def set_audio_service(self, audio_service: AudioService):
        self.audio_service = audio_service

    async def play_notification_sound(self, sound_file: str):
        """播放通知音效"""
        print(f"🎵 播放通知音效: {sound_file}")
        success = await self.audio_service.play_file(sound_file)
        if success:
            print("✅ 音效播放成功")
        else:
            print("❌ 音效播放失败")

    async def speak_notification(self, message: str):
        """语音播报通知"""
        print(f"🗣️ 语音播报: {message}")
        success = await self.audio_service.speak(message)
        if success:
            print("✅ 语音播报成功")
        else:
            print("❌ 语音播报失败")

    async def play_completion_sequence(self):
        """播放完成序列"""
        print("🎯 播放任务完成序列...")

        # 方式1:分别调用
        print("方式1:分别调用")
        await self.speak_notification("任务即将完成")
        await self.speak_notification("请稍等...")

        # 方式2:混合序列播放(自动识别类型)
        print("\n方式2:混合序列播放")
        sequence = [
            "任务已完成",  # 自动识别为文本
            "请查看结果",
            "感谢您的使用",
        ]
        success = await self.audio_service.play_sequence(sequence, interval=0.5)
        if success:
            print("✅ 完成序列播放成功")
        else:
            print("❌ 完成序列播放失败")

        # 方式3:使用AudioItem明确指定类型
        print("\n方式3:使用AudioItem明确指定")
        sequence = [AudioItem.text("系统提示"), AudioItem.text("所有任务已完成"), AudioItem.text("祝您工作愉快")]
        success = await self.audio_service.play_sequence(sequence, interval=0.3)
        if success:
            print("✅ AudioItem序列播放成功")
        else:
            print("❌ AudioItem序列播放失败")

    async def play_multiple_notifications(self):
        """播放多个通知"""
        print("📢 播放多个通知...")

        # 连续播放多段文本
        messages = ["第一条通知:系统更新完成", "第二条通知:数据备份成功", "第三条通知:所有服务正常运行"]
        success = await self.audio_service.speak_texts(messages, interval=0.8)
        if success:
            print("✅ 多条通知播放成功")
        else:
            print("❌ 多条通知播放失败")

    async def test_audio_controls(self):
        """测试音频控制功能"""
        print("🎛️ 测试音频控制...")

        # 检查功能状态
        print(f"音频播放启用: {self.audio_service.is_audio_enabled()}")
        print(f"TTS启用: {self.audio_service.is_tts_enabled()}")

        # 调整音量和语速
        print("调整TTS参数...")
        self.audio_service.set_tts_properties(rate=120, volume=0.8)
        await self.audio_service.speak("这是调整参数后的语音测试")

        # 恢复默认参数
        print("恢复默认参数...")
        self.audio_service.set_tts_properties(rate=150, volume=1.0)
        await self.audio_service.speak("参数已恢复默认值")


async def main():
    """主函数"""
    print("🚀 Mini-Boot 音频播放 Starter 示例")
    print("=" * 50)

    # 创建应用上下文
    context = DefaultApplicationContext()

    # 启动应用上下文
    await context.start()

    try:
        # 获取通知服务
        notification_service = context.get_bean(NotificationService)

        # 演示各种功能
        print("\n1. 基本语音播报测试")
        await notification_service.speak_notification("欢迎使用Mini-Boot音频播放功能")

        print("\n2. 完成序列播放测试")
        await notification_service.play_completion_sequence()

        print("\n3. 多条通知播放测试")
        await notification_service.play_multiple_notifications()

        print("\n4. 音频控制功能测试")
        await notification_service.test_audio_controls()

        print("\n✅ 所有测试完成!")

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback

        traceback.print_exc()

    finally:
        # 关闭应用上下文
        await context.stop()


if __name__ == "__main__":
    # 运行示例
    asyncio.run(main())

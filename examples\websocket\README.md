# WebSocket Starter 使用示例

本目录包含了 Mini-Boot WebSocket Starter 的各种使用示例，帮助您快速上手 WebSocket 实时通信功能。

## 📁 示例文件

### 1. simple_echo.py - 简单回显服务
最基础的 WebSocket 示例，演示如何：
- 处理连接建立和断开事件
- 接收和发送文本、JSON、二进制消息
- 处理错误事件

**连接地址**: `ws://localhost:8080/echo`

### 2. chat_example.py - 聊天室应用
完整的聊天室应用示例，演示如何：
- 管理在线用户和会话
- 实现多房间聊天功能
- 处理私聊消息
- 实现命令系统
- 广播消息到指定房间

**连接地址**: `ws://localhost:8080/chat`

## 🚀 快速开始

### 1. 配置 WebSocket Starter

在您的 `application.yml` 中添加配置：

```yaml
miniboot:
  starters:
    websocket:
      enabled: true
      path: "/ws"
      max_message_size: 524288  # 512KB
      
      # 服务器配置
      server:
        host: "0.0.0.0"
        port: 8080
        read_timeout: 10
        write_timeout: 10
        idle_timeout: 60
      
      # 安全配置
      security:
        auth:
          enabled: false  # 开发环境可以关闭认证
        allowed_origins:
          - "*"  # 开发环境允许所有来源
      
      # 连接限制
      connection_limit:
        enabled: true
        max_connections_per_user: 5
        max_total_connections: 1000
```

### 2. 创建 WebSocket 控制器

```python
from miniboot.annotations import Component
from miniboot.starters.websocket.annotations import (
    WebSocketController,
    WebSocketOnConnect,
    WebSocketOnMessage
)
from miniboot.starters.websocket.session import WebSocketSession

@Component
@WebSocketController("/my-websocket")
class MyWebSocketController:
    
    @WebSocketOnConnect()
    async def on_connect(self, session: WebSocketSession):
        await session.send_text("连接成功！")
    
    @WebSocketOnMessage("text")
    async def on_message(self, session: WebSocketSession, message: str):
        await session.send_text(f"收到消息: {message}")
```

### 3. 启动应用

```python
from miniboot import MiniBootApplication

app = MiniBootApplication()
app.run()
```

### 4. 测试连接

使用 WebSocket 客户端连接到 `ws://localhost:8080/my-websocket`

## 📚 注解说明

### 控制器注解

- `@WebSocketController(path)`: 标记 WebSocket 控制器类
- `@Component`: 将控制器注册为 Spring Bean

### 事件处理注解

- `@WebSocketOnConnect()`: 处理连接建立事件
- `@WebSocketOnDisconnect()`: 处理连接断开事件
- `@WebSocketOnError()`: 处理错误事件

### 消息处理注解

- `@WebSocketOnMessage("text")`: 处理文本消息
- `@WebSocketOnMessage("json")`: 处理 JSON 消息
- `@WebSocketOnMessage("binary")`: 处理二进制消息
- `@WebSocketOnMessage("any")`: 处理任意类型消息

### 消息发送注解

- `@WebSocketSendTo("all")`: 广播给所有用户
- `@WebSocketSendTo("sender")`: 发送给发送者
- `@WebSocketSendTo("user")`: 发送给指定用户
- `@WebSocketSendTo("room")`: 发送给房间内用户

## 🔧 高级功能

### 1. 认证和授权

```yaml
miniboot:
  starters:
    websocket:
      security:
        auth:
          enabled: true
          token_header: "Authorization"
          token_query_param: "token"
        jwt:
          enabled: true
          secret_key: "your-secret-key"
          issuer: "your-app"
          expiration_time: 3600
```

### 2. 消息压缩

```yaml
miniboot:
  starters:
    websocket:
      compression:
        enabled: true
        level: 6  # 1-9, 数字越大压缩率越高
```

### 3. 超时配置

```yaml
miniboot:
  starters:
    websocket:
      timeout:
        enabled: true
        duration: 5
        ping_interval: 54
        pong_timeout: 60
```

## 🧪 测试

运行测试用例：

```bash
python -m pytest tests/starters/websocket/ -v
```

## 📖 更多文档

- [WebSocket Starter 设计文档](../../docs/23.websocket-starter.md)
- [API 参考文档](../../docs/api/websocket-starter.md)
- [配置参考](../../docs/configuration/websocket-starter.md)

## 🤝 贡献

欢迎提交 Issue 和 Pull Request 来改进这些示例！

## 📄 许可证

本项目采用 MIT 许可证。

#!/usr/bin/env python
"""
* @author: cz
* @description: 依赖注入注解测试

测试依赖注入注解的功能，包括@Autowired、@Inject、@Qualifier、@Primary、@Lazy、@DependsOn等。
"""

import unittest
from typing import Protocol

from miniboot.annotations import (Autowired, Component, DependsOn, Inject,
                                  Lazy, Primary, Qualifier, autowired_metadata,
                                  depends_on_beans, is_autowired, is_lazy,
                                  is_primary, is_qualified, qualifier_value)


# 测试用的接口和类
class UserRepository(Protocol):
    """用户仓储接口"""

    def find_by_id(self, user_id: int) -> dict: ...


class DatabaseUserRepository:
    """数据库用户仓储实现"""

    def find_by_id(self, user_id: int) -> dict:
        return {"id": user_id, "name": "test_user"}


class CacheUserRepository:
    """缓存用户仓储实现"""

    def find_by_id(self, user_id: int) -> dict:
        return {"id": user_id, "name": "cached_user"}


class TestInjectionAnnotations(unittest.TestCase):
    """依赖注入注解测试类"""

    def test_autowired_annotation_without_params(self):
        """测试@Autowired注解无参数使用"""

        class UserService:
            @Autowired
            def set_user_repository(self, user_repository: UserRepository):
                self.user_repository = user_repository

        method = UserService.set_user_repository

        # 验证注解属性
        self.assertTrue(hasattr(method, "__is_autowired__"))
        self.assertTrue(method.__is_autowired__)
        self.assertTrue(method.__autowired_required__)
        self.assertIsNone(method.__autowired_name__)
        self.assertTrue(method.__is_setter_injection__)

        # 验证元数据
        self.assertTrue(hasattr(method, "__autowired_metadata__"))
        metadata = method.__autowired_metadata__
        self.assertTrue(metadata.required)
        self.assertIsNone(metadata.name)
        self.assertIsNone(metadata.qualifier)

    def test_autowired_annotation_with_params(self):
        """测试@Autowired注解带参数使用"""

        class UserService:
            @Autowired(required=False, name="primaryUserRepository")
            def set_user_repository(self, user_repository: UserRepository):
                self.user_repository = user_repository

        method = UserService.set_user_repository

        # 验证注解属性
        self.assertTrue(method.__is_autowired__)
        self.assertFalse(method.__autowired_required__)
        self.assertEqual(method.__autowired_name__, "primaryUserRepository")

        # 验证元数据
        metadata = method.__autowired_metadata__
        self.assertFalse(metadata.required)
        self.assertEqual(metadata.name, "primaryUserRepository")

    def test_inject_annotation(self):
        """测试@Inject注解"""

        class UserService:
            @Inject
            def set_user_repository(self, user_repository: UserRepository):
                self.user_repository = user_repository

        method = UserService.set_user_repository

        # 验证是Autowired的实现
        self.assertTrue(method.__is_autowired__)
        self.assertTrue(method.__autowired_required__)  # JSR-330默认required=True

        # 验证JSR-330标记
        self.assertTrue(hasattr(method, "__is_jsr330_inject__"))
        self.assertTrue(method.__is_jsr330_inject__)

    def test_inject_annotation_with_name(self):
        """测试@Inject注解带名称"""

        class UserService:
            @Inject(name="primaryUserRepository")
            def set_user_repository(self, user_repository: UserRepository):
                self.user_repository = user_repository

        method = UserService.set_user_repository

        self.assertTrue(method.__is_autowired__)
        self.assertEqual(method.__autowired_name__, "primaryUserRepository")
        self.assertTrue(method.__is_jsr330_inject__)

    def test_qualifier_annotation(self):
        """测试@Qualifier注解"""

        class UserService:
            @Autowired
            @Qualifier("primaryUserRepository")
            def set_user_repository(self, user_repository: UserRepository):
                self.user_repository = user_repository

        method = UserService.set_user_repository

        # 验证Qualifier属性
        self.assertTrue(hasattr(method, "__is_qualified__"))
        self.assertTrue(method.__is_qualified__)
        self.assertEqual(method.__qualifier_value__, "primaryUserRepository")

        # 验证元数据
        self.assertTrue(hasattr(method, "__qualifier_metadata__"))
        qualifier_metadata = method.__qualifier_metadata__
        self.assertEqual(qualifier_metadata.value, "primaryUserRepository")

        # 验证Autowired元数据中的qualifier被更新
        autowired_metadata = method.__autowired_metadata__
        self.assertEqual(autowired_metadata.qualifier, "primaryUserRepository")

    def test_primary_annotation_on_class(self):
        """测试@Primary注解在类上"""

        @Component
        @Primary
        class PrimaryUserRepository:
            pass

        # 验证Primary属性
        self.assertTrue(hasattr(PrimaryUserRepository, "__is_primary__"))
        self.assertTrue(PrimaryUserRepository.__is_primary__)
        self.assertTrue(PrimaryUserRepository.__primary__)

        # 验证元数据
        self.assertTrue(hasattr(PrimaryUserRepository, "__primary_metadata__"))

        # 验证组件元数据中的primary被更新
        component_metadata = PrimaryUserRepository.__component_metadata__
        self.assertTrue(component_metadata.primary)

    def test_primary_annotation_on_method(self):
        """测试@Primary注解在方法上"""

        class DatabaseConfig:
            @Primary
            def primary_data_source(self):
                return "PrimaryDataSource"

        method = DatabaseConfig.primary_data_source

        self.assertTrue(method.__is_primary__)
        self.assertTrue(method.__primary__)
        self.assertTrue(hasattr(method, "__primary_metadata__"))

    def test_lazy_annotation_without_params(self):
        """测试@Lazy注解无参数使用"""

        @Component
        @Lazy
        class LazyService:
            pass

        # 验证Lazy属性
        self.assertTrue(hasattr(LazyService, "__is_lazy__"))
        self.assertTrue(LazyService.__is_lazy__)
        self.assertTrue(LazyService.__lazy_value__)

        # 验证元数据
        self.assertTrue(hasattr(LazyService, "__lazy_metadata__"))
        lazy_metadata = LazyService.__lazy_metadata__
        self.assertTrue(lazy_metadata.value)

        # 验证组件元数据中的lazy被更新
        component_metadata = LazyService.__component_metadata__
        self.assertTrue(component_metadata.lazy)

    def test_lazy_annotation_with_false_value(self):
        """测试@Lazy注解显式设置为False"""

        @Component
        @Lazy(value=False)
        class EagerService:
            pass

        self.assertTrue(EagerService.__is_lazy__)
        self.assertFalse(EagerService.__lazy_value__)

        lazy_metadata = EagerService.__lazy_metadata__
        self.assertFalse(lazy_metadata.value)

        component_metadata = EagerService.__component_metadata__
        self.assertFalse(component_metadata.lazy)

    def test_depends_on_annotation(self):
        """测试@DependsOn注解"""

        @Component
        @DependsOn("databaseService", "cacheService")
        class UserService:
            pass

        # 验证DependsOn属性
        self.assertTrue(hasattr(UserService, "__is_depends_on__"))
        self.assertTrue(UserService.__is_depends_on__)
        self.assertEqual(UserService.__depends_on_beans__, ["databaseService", "cacheService"])

        # 验证元数据
        self.assertTrue(hasattr(UserService, "__depends_on_metadata__"))
        depends_on_metadata = UserService.__depends_on_metadata__
        self.assertEqual(depends_on_metadata.bean_names, ["databaseService", "cacheService"])

        # 验证组件元数据中的depends_on被更新
        component_metadata = UserService.__component_metadata__
        self.assertIn("databaseService", component_metadata.depends_on)
        self.assertIn("cacheService", component_metadata.depends_on)

    def test_combined_annotations(self):
        """测试组合使用多个注解"""

        @Component
        @Primary
        @Lazy
        @DependsOn("configService")
        class ComplexService:
            @Autowired
            @Qualifier("primaryRepository")
            def set_repository(self, repository: UserRepository):
                self.repository = repository

        # 验证类级别注解
        self.assertTrue(ComplexService.__is_primary__)
        self.assertTrue(ComplexService.__is_lazy__)
        self.assertTrue(ComplexService.__is_depends_on__)
        self.assertEqual(ComplexService.__depends_on_beans__, ["configService"])

        # 验证方法级别注解
        method = ComplexService.set_repository
        self.assertTrue(method.__is_autowired__)
        self.assertTrue(method.__is_qualified__)
        self.assertEqual(method.__qualifier_value__, "primaryRepository")

    def test_utility_functions(self):
        """测试工具函数"""

        @Component
        @Primary
        @Lazy
        @DependsOn("service1", "service2")
        class TestService:
            @Autowired
            @Qualifier("testRepository")
            def set_repository(self, repository: UserRepository):
                self.repository = repository

        # 测试类级别检查函数
        self.assertTrue(is_primary(TestService))
        self.assertTrue(is_lazy(TestService))
        self.assertEqual(depends_on_beans(TestService), ["service1", "service2"])

        # 测试方法级别检查函数
        method = TestService.set_repository
        self.assertTrue(is_autowired(method))
        self.assertTrue(is_qualified(method))
        self.assertEqual(qualifier_value(method), "testRepository")

        # 测试获取元数据
        autowired_metadata_value = autowired_metadata(method)
        self.assertIsNotNone(autowired_metadata_value)
        self.assertTrue(autowired_metadata_value.required)
        self.assertEqual(autowired_metadata_value.qualifier, "testRepository")

    def test_utility_functions_with_no_annotations(self):
        """测试工具函数在没有注解的情况下"""

        class PlainService:
            def plain_method(self):
                pass

        # 测试类级别检查函数
        self.assertFalse(is_primary(PlainService))
        self.assertFalse(is_lazy(PlainService))
        self.assertEqual(depends_on_beans(PlainService), [])

        # 测试方法级别检查函数
        method = PlainService.plain_method
        self.assertFalse(is_autowired(method))
        self.assertFalse(is_qualified(method))
        self.assertIsNone(qualifier_value(method))
        self.assertIsNone(autowired_metadata(method))

    def test_autowired_field_injection(self):
        """测试@Autowired字段注入"""

        # 定义测试用的服务类型
        class UserService:
            pass

        class OrderService:
            pass

        @Autowired
        class ServiceWithFieldInjection:
            user_service: UserService
            order_service: OrderService

        # 验证字段注入元数据
        self.assertTrue(hasattr(ServiceWithFieldInjection, "__autowired_fields__"))
        fields = ServiceWithFieldInjection.__autowired_fields__

        self.assertIn("user_service", fields)
        self.assertIn("order_service", fields)

        # 验证字段元数据
        user_metadata = fields["user_service"]
        self.assertTrue(user_metadata.required)
        self.assertEqual(user_metadata.name, "user_service")

    def test_autowired_with_custom_name(self):
        """测试@Autowired带自定义名称"""

        # 定义测试用的服务类型
        class SomeService:
            pass

        @Autowired(name="customService")
        class ServiceWithCustomName:
            service: SomeService

        fields = ServiceWithCustomName.__autowired_fields__
        metadata = fields["service"]
        self.assertEqual(metadata.name, "customService")


if __name__ == "__main__":
    unittest.main()

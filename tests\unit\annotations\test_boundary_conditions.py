#!/usr/bin/env python
"""
* @author: cz
* @description: 注解系统边界条件测试

测试注解系统的边界条件，包括异常处理、参数验证、性能测试等。
"""

import time
import unittest

from miniboot.annotations import (Autowired, Bean, Component, ComponentScanner,
                                  Configuration, MetadataRegistry, Service,
                                  validate_metadata)
from miniboot.annotations.metadata import ComponentMetadata, Scope


# 在模块级别定义测试组件，以便扫描器能够找到它们
@Component
class ModuleLevelComponent:
    pass


@Service
class ModuleLevelService:
    @Autowired
    def set_dependency(self, dep):
        self.dep = dep


@Component
class ServiceA:
    @Autowired
    def set_service_b(self, service_b):
        self.service_b = service_b


@Component
class ServiceB:
    @Autowired
    def set_service_a(self, service_a):
        self.service_a = service_a


@Component
class ServiceWithMissingDep:
    @Autowired
    def set_missing_service(self, missing_service):
        self.missing_service = missing_service


@Component
class EmptyComponent:
    pass


@Component
class OuterComponent:
    @Component
    class InnerComponent:
        pass


@Component
class BaseComponent:
    @Autowired
    def set_base_dependency(self, dep):
        self.base_dep = dep


class DerivedComponent(BaseComponent):
    @Autowired
    def set_derived_dependency(self, dep):
        self.derived_dep = dep


class TestExceptionHandling(unittest.TestCase):
    """异常处理测试类"""

    def test_invalid_annotation_parameters(self):
        """测试无效的注解参数"""

        # 测试Component注解的无效参数
        with self.assertRaises(TypeError):

            @Component(invalid_param="test")
            class InvalidComponent:
                pass

        # 测试Bean注解的无效参数
        with self.assertRaises(TypeError):

            @Configuration
            class TestConfig:
                @Bean(invalid_param="test")
                def test_bean(self):
                    return "test"

    def test_autowired_on_invalid_targets(self):
        """测试在无效目标上使用@Autowired"""

        # 测试在类上使用@Autowired（应该只能用在方法和字段上）
        try:

            @Autowired
            class InvalidAutowiredClass:
                pass

            # 如果没有抛出异常，验证注解是否被正确忽略
            self.assertFalse(hasattr(InvalidAutowiredClass, "__autowired__"))
        except Exception:
            # 如果抛出异常，这也是可以接受的
            pass

    def test_circular_dependencies(self):
        """测试循环依赖检测"""

        # 使用模块级别定义的ServiceA和ServiceB
        # 扫描应该成功，但在实际依赖注入时可能会检测到循环依赖
        scanner = ComponentScanner()
        result = scanner.scan("tests.unit.annotations.test_boundary_conditions")

        # 验证组件被正确扫描
        self.assertGreater(len(result.components), 0)

        # 验证ServiceA和ServiceB都被扫描到
        component_names = [name.split(".")[-1] for name in result.components]
        self.assertIn("ServiceA", component_names)
        self.assertIn("ServiceB", component_names)

    def test_missing_dependencies(self):
        """测试缺失依赖的处理"""

        # 使用模块级别定义的ServiceWithMissingDep
        # 扫描应该成功，但会记录缺失的依赖
        scanner = ComponentScanner()
        result = scanner.scan("tests.unit.annotations.test_boundary_conditions")

        # 验证扫描结果
        self.assertGreater(len(result.autowired_methods), 0)

        # 验证ServiceWithMissingDep被扫描到
        self.assertIn("ServiceWithMissingDep", [name.split(".")[-1] for name in result.autowired_methods])

    def test_scanner_error_handling(self):
        """测试扫描器错误处理"""
        scanner = ComponentScanner()

        # 测试扫描不存在的包
        result = scanner.scan(["nonexistent.package.that.does.not.exist"])

        # 应该有错误记录
        self.assertGreater(len(result.scan_errors), 0)

        # 验证错误信息包含包名
        error_found = any("nonexistent.package" in error for error in result.scan_errors)
        self.assertTrue(error_found)

    def test_metadata_registry_error_handling(self):
        """测试元数据注册表错误处理"""
        registry = MetadataRegistry()

        # 测试获取不存在的元数据
        result = registry.get_class_metadata(str, "NonexistentAnnotation")
        self.assertIsNone(result)

        # 测试查找不存在的注解类型
        result = registry.find_by_annotation("NonexistentAnnotation")
        self.assertEqual(len(result), 0)


class TestParameterValidation(unittest.TestCase):
    """参数验证测试类"""

    def test_component_name_validation(self):
        """测试组件名称验证"""

        # 测试空名称
        metadata = ComponentMetadata(name="")
        errors = validate_metadata(metadata)
        self.assertGreater(len(errors), 0)
        self.assertTrue(any("empty" in error.lower() for error in errors))

        # 测试None名称（应该是有效的，使用默认名称）
        metadata = ComponentMetadata(name=None)
        errors = validate_metadata(metadata)
        self.assertEqual(len(errors), 0)

        # 测试有效名称
        metadata = ComponentMetadata(name="validName")
        errors = validate_metadata(metadata)
        self.assertEqual(len(errors), 0)

    def test_scope_validation(self):
        """测试作用域验证"""

        # 测试有效的作用域
        for scope in [Scope.SINGLETON, Scope.PROTOTYPE]:
            metadata = ComponentMetadata(scope=scope)
            errors = validate_metadata(metadata)
            self.assertEqual(len(errors), 0)

    def test_depends_on_validation(self):
        """测试依赖关系验证"""

        # 测试有效的依赖
        metadata = ComponentMetadata(depends_on=["dep1", "dep2"])
        errors = validate_metadata(metadata)
        self.assertEqual(len(errors), 0)

        # 测试无效的依赖（空字符串）
        metadata = ComponentMetadata(depends_on=["valid", "", "another"])
        errors = validate_metadata(metadata)
        self.assertGreater(len(errors), 0)

        # 测试无效的依赖类型
        try:
            metadata = ComponentMetadata(depends_on="not_a_list")
            errors = validate_metadata(metadata)
            self.assertGreater(len(errors), 0)
        except TypeError:
            # 如果在创建时就抛出异常，这也是可以接受的
            pass

    def test_autowired_parameter_validation(self):
        """测试自动装配参数验证"""
        from miniboot.annotations.metadata import AutowiredMetadata

        # 测试有效参数
        metadata = AutowiredMetadata(required=True, name="validName")
        errors = validate_metadata(metadata)
        self.assertEqual(len(errors), 0)

        # 测试无效的名称
        metadata = AutowiredMetadata(name="")
        errors = validate_metadata(metadata)
        self.assertGreater(len(errors), 0)

    def test_bean_parameter_validation(self):
        """测试Bean参数验证"""
        from miniboot.annotations.metadata import BeanMetadata

        # 测试有效参数
        metadata = BeanMetadata(name="validBean", init_method="init", destroy_method="destroy")
        errors = validate_metadata(metadata)
        self.assertEqual(len(errors), 0)

        # 测试无效的方法名
        metadata = BeanMetadata(init_method="")
        errors = validate_metadata(metadata)
        self.assertGreater(len(errors), 0)


class TestPerformance(unittest.TestCase):
    """性能测试类"""

    def test_annotation_creation_performance(self):
        """测试注解创建性能"""

        def create_components(count):
            components = []
            for i in range(count):

                @Component(name=f"component_{i}")
                class DynamicComponent:
                    pass

                components.append(DynamicComponent)
            return components

        # 测试创建1000个组件的性能
        start_time = time.time()
        components = create_components(1000)
        creation_time = time.time() - start_time

        print(f"创建1000个组件耗时: {creation_time:.4f}秒")

        # 验证性能合理（应该在1秒内完成）
        self.assertLess(creation_time, 1.0)
        self.assertEqual(len(components), 1000)

    def test_scanning_performance(self):
        """测试扫描性能"""
        scanner = ComponentScanner()

        # 测试扫描miniboot.annotations包的性能
        start_time = time.time()
        result = scanner.scan("miniboot.annotations")
        scan_time = time.time() - start_time

        print(f"扫描miniboot.annotations包耗时: {scan_time:.4f}秒")
        print(f"扫描到{len(result.scanned_modules)}个模块，{result.get_total_components()}个组件")

        # 验证性能合理（应该在2秒内完成）
        self.assertLess(scan_time, 2.0)
        self.assertGreater(len(result.scanned_modules), 0)

    def test_metadata_registry_performance(self):
        """测试元数据注册表性能"""
        registry = MetadataRegistry()

        # 测试大量注册的性能
        num_registrations = 10000

        start_time = time.time()
        for i in range(num_registrations):
            # 创建虚拟类
            cls = type(f"TestClass_{i}", (), {})
            metadata = ComponentMetadata(name=f"component_{i}")
            registry.register_class_metadata(cls, "Component", metadata)

        register_time = time.time() - start_time

        # 测试查询性能
        start_time = time.time()
        for i in range(0, num_registrations, 100):  # 每100个查询一次
            cls = type(f"TestClass_{i}", (), {})
            metadata = registry.get_class_metadata(cls, "Component")
            if metadata:
                self.assertEqual(metadata.name, f"component_{i}")

        query_time = time.time() - start_time

        # 测试按类型查找性能
        start_time = time.time()
        components = registry.find_by_annotation("Component")
        find_time = time.time() - start_time

        print("性能测试结果:")
        print(f"  注册{num_registrations}个元数据: {register_time:.4f}秒")
        print(f"  查询{num_registrations // 100}次: {query_time:.4f}秒")
        print(f"  按类型查找{len(components)}个组件: {find_time:.4f}秒")

        # 验证性能合理
        self.assertLess(register_time, 2.0)  # 注册应该在2秒内完成
        self.assertLess(query_time, 0.5)  # 查询应该在0.5秒内完成
        self.assertLess(find_time, 0.1)  # 查找应该在0.1秒内完成

        self.assertEqual(len(components), num_registrations)

    def test_auto_scan_performance(self):
        """测试自动扫描性能"""

        # 创建一个测试应用类
        from miniboot.annotations import MiniBootApplication

        @MiniBootApplication
        class TestApplication:
            pass

        # 测试自动扫描性能
        start_time = time.time()
        scanner = ComponentScanner()
        result = scanner.auto_scan(TestApplication)
        auto_scan_time = time.time() - start_time

        print(f"自动扫描耗时: {auto_scan_time:.4f}秒")
        print(f"扫描到{len(result.scanned_modules)}个模块")

        # 验证性能合理（应该在3秒内完成）
        self.assertLess(auto_scan_time, 3.0)

    def test_memory_usage(self):
        """测试内存使用情况"""
        import gc

        # 获取初始内存使用
        gc.collect()
        initial_objects = len(gc.get_objects())

        # 创建大量组件
        components = []
        for i in range(1000):

            @Component(name=f"memory_test_component_{i}")
            class MemoryTestComponent:
                @Autowired
                def set_dependency(self, dep):
                    self.dep = dep

            components.append(MemoryTestComponent)

        # 扫描组件
        scanner = ComponentScanner()
        scanner.scan("tests.unit.annotations.test_boundary_conditions")

        # 获取最终内存使用
        gc.collect()
        final_objects = len(gc.get_objects())

        objects_created = final_objects - initial_objects
        print(f"创建了{objects_created}个对象")

        # 验证内存使用合理（不应该创建过多对象）
        # 这个阈值可能需要根据实际情况调整
        self.assertLess(objects_created, 50000)


class TestEdgeCases(unittest.TestCase):
    """边缘情况测试类"""

    def test_empty_class_scanning(self):
        """测试扫描空类"""

        # 使用模块级别定义的EmptyComponent
        scanner = ComponentScanner()
        result = scanner.scan("tests.unit.annotations.test_boundary_conditions")

        # 应该能正确扫描到空组件
        self.assertGreater(len(result.components), 0)

        # 验证EmptyComponent被扫描到
        component_names = [name.split(".")[-1] for name in result.components]
        self.assertIn("EmptyComponent", component_names)

    def test_nested_class_scanning(self):
        """测试扫描嵌套类"""

        # 使用模块级别定义的OuterComponent
        scanner = ComponentScanner()
        result = scanner.scan("tests.unit.annotations.test_boundary_conditions")

        # 验证扫描结果
        self.assertGreater(len(result.components), 0)

        # 验证OuterComponent被扫描到
        component_names = [name.split(".")[-1] for name in result.components]
        self.assertIn("OuterComponent", component_names)

    def test_multiple_annotations_on_same_class(self):
        """测试同一个类上的多个注解"""

        @Component
        @Service  # 这可能会导致冲突
        class MultiAnnotatedClass:
            pass

        # 验证类仍然可以被正确处理
        self.assertTrue(hasattr(MultiAnnotatedClass, "__is_component__"))
        self.assertTrue(hasattr(MultiAnnotatedClass, "__is_service__"))

    def test_annotation_inheritance(self):
        """测试注解继承"""

        # 使用模块级别定义的BaseComponent和DerivedComponent
        scanner = ComponentScanner()
        result = scanner.scan("tests.unit.annotations.test_boundary_conditions")

        # 验证继承的注解行为
        self.assertGreater(len(result.components), 0)

        # 验证BaseComponent被扫描到（有@Component注解）
        component_names = [name.split(".")[-1] for name in result.components]
        self.assertIn("BaseComponent", component_names)

        # DerivedComponent没有@Component注解，所以不会被扫描为组件
        # 但它的@Autowired方法可能会被扫描到

    def test_unicode_names(self):
        """测试Unicode名称"""

        @Component(name="测试组件")
        class UnicodeNameComponent:
            pass

        # 验证Unicode名称被正确处理
        self.assertTrue(hasattr(UnicodeNameComponent, "__component_name__"))

    def test_very_long_names(self):
        """测试很长的名称"""

        long_name = "a" * 1000  # 1000个字符的名称

        @Component(name=long_name)
        class LongNameComponent:
            pass

        # 验证长名称被正确处理
        self.assertTrue(hasattr(LongNameComponent, "__component_name__"))
        self.assertEqual(LongNameComponent.__component_name__, long_name)


if __name__ == "__main__":
    unittest.main(verbosity=2)

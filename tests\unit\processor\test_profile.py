#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Profile 处理器单元测试

测试 Profile Bean 后置处理器的功能，包括：
- Profile 条件评估
- Bean 创建控制
- 多环境支持
- 处理器集成
"""

import unittest
from unittest.mock import Mock

from miniboot.annotations.profile import Profile
from miniboot.errors.bean import BeanCreationError
from miniboot.processor.profile import (
    ProfileBeanPostProcessor,
    ProfileConditionEvaluator,
    create_profile_condition_evaluator,
    create_profile_processor,
)


class TestProfileBeanPostProcessor(unittest.TestCase):
    """Profile Bean 后置处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.mock_environment = Mock()
        self.processor = ProfileBeanPostProcessor(self.mock_environment)

    def test_processor_creation(self):
        """测试处理器创建"""
        processor = create_profile_processor(self.mock_environment)
        self.assertIsInstance(processor, ProfileBeanPostProcessor)

    def test_supports_profile_bean(self):
        """测试支持有 Profile 注解的 Bean"""

        @Profile("dev")
        class DevService:
            pass

        bean = DevService()
        self.assertTrue(self.processor.supports(bean, "devService"))

    def test_not_supports_regular_bean(self):
        """测试不支持没有 Profile 注解的 Bean"""

        class RegularService:
            pass

        bean = RegularService()
        self.assertFalse(self.processor.supports(bean, "regularService"))

    def test_profile_condition_satisfied(self):
        """测试 Profile 条件满足时允许 Bean 创建"""

        @Profile("dev")
        class DevService:
            pass

        # 设置环境返回匹配的 Profile
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean = DevService()

        # 处理应该成功
        result = self.processor.post_process_before_initialization(bean, "devService")
        self.assertEqual(result, bean)

    def test_profile_condition_not_satisfied(self):
        """测试 Profile 条件不满足时阻止 Bean 创建"""

        @Profile("prod")
        class ProdService:
            pass

        # 设置环境返回不匹配的 Profile
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean = ProdService()

        # 处理应该抛出异常
        with self.assertRaises(BeanCreationError) as context:
            self.processor.post_process_before_initialization(bean, "prodService")

        self.assertIn("Profile condition", str(context.exception))

    def test_multiple_profiles_match(self):
        """测试多个 Profile 中有一个匹配"""

        @Profile("dev", "test")
        class DevTestService:
            pass

        # 设置环境返回其中一个匹配的 Profile
        self.mock_environment.get_active_profiles.return_value = ["test"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean = DevTestService()

        # 处理应该成功
        result = self.processor.post_process_before_initialization(bean, "devTestService")
        self.assertEqual(result, bean)

    def test_profile_expression_satisfied(self):
        """测试 Profile 表达式满足"""

        @Profile(expression="dev & !prod")
        class DevNotProdService:
            pass

        # 设置环境满足表达式
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean = DevNotProdService()

        # 处理应该成功
        result = self.processor.post_process_before_initialization(bean, "devNotProdService")
        self.assertEqual(result, bean)

    def test_profile_expression_not_satisfied(self):
        """测试 Profile 表达式不满足"""

        @Profile(expression="dev & prod")
        class DevAndProdService:
            pass

        # 设置环境不满足表达式（只有 dev，没有 prod）
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean = DevAndProdService()

        # 处理应该抛出异常
        with self.assertRaises(BeanCreationError):
            self.processor.post_process_before_initialization(bean, "devAndProdService")

    def test_no_profile_annotation(self):
        """测试没有 Profile 注解的 Bean"""

        class RegularService:
            pass

        bean = RegularService()

        # 处理应该成功（没有 Profile 限制）
        result = self.processor.post_process_before_initialization(bean, "regularService")
        self.assertEqual(result, bean)

    def test_default_profile_fallback(self):
        """测试默认 Profile 回退"""

        @Profile("default")
        class DefaultService:
            pass

        # 设置环境没有激活的 Profile，使用默认 Profile
        self.mock_environment.get_active_profiles.return_value = []
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean = DefaultService()

        # 处理应该成功
        result = self.processor.post_process_before_initialization(bean, "defaultService")
        self.assertEqual(result, bean)

    def test_processor_order(self):
        """测试处理器执行顺序"""
        from miniboot.processor.base import ProcessorOrder

        order = self.processor.get_order()
        self.assertEqual(order, ProcessorOrder.VALIDATION_PROCESSOR - 10)

    def test_post_process_after_initialization(self):
        """测试初始化后处理（应该直接返回 Bean）"""

        class TestService:
            pass

        bean = TestService()
        result = self.processor.post_process_after_initialization(bean, "testService")
        self.assertEqual(result, bean)

    def test_processed_beans_cache(self):
        """测试已处理 Bean 的缓存"""

        @Profile("dev")
        class DevService:
            pass

        # 设置环境
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean = DevService()

        # 第一次处理
        result1 = self.processor.post_process_before_initialization(bean, "devService")
        self.assertEqual(result1, bean)

        # 第二次处理相同的 Bean（应该直接返回）
        result2 = self.processor.post_process_before_initialization(bean, "devService")
        self.assertEqual(result2, bean)

    def test_set_environment(self):
        """测试设置环境配置"""
        new_env = Mock()
        new_env.get_active_profiles.return_value = ["prod"]

        self.processor.set_environment(new_env)

        # 验证环境已更新
        self.assertEqual(self.processor._environment, new_env)
        self.assertEqual(self.processor.get_active_profiles(), ["prod"])

    def test_evaluator_methods(self):
        """测试评估器方法"""
        self.mock_environment.get_active_profiles.return_value = ["dev", "test"]
        self.mock_environment.get_default_profiles.return_value = ["default"]
        self.mock_environment.accepts_profiles.return_value = True

        # 测试方法
        self.assertEqual(self.processor.get_active_profiles(), ["dev", "test"])
        self.assertEqual(self.processor.get_default_profiles(), ["default"])
        self.assertTrue(self.processor.accepts_profiles("dev"))


class TestProfileConditionEvaluator(unittest.TestCase):
    """Profile 条件评估器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.mock_environment = Mock()
        self.evaluator = ProfileConditionEvaluator(self.mock_environment)

    def test_evaluator_creation(self):
        """测试评估器创建"""
        evaluator = create_profile_condition_evaluator(self.mock_environment)
        self.assertIsInstance(evaluator, ProfileConditionEvaluator)

    def test_should_create_bean_with_matching_profile(self):
        """测试应该创建匹配 Profile 的 Bean"""

        @Profile("dev")
        class DevService:
            pass

        # 设置环境
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        # 应该创建 Bean
        self.assertTrue(self.evaluator.should_create_bean(DevService))

    def test_should_not_create_bean_with_non_matching_profile(self):
        """测试不应该创建不匹配 Profile 的 Bean"""

        @Profile("prod")
        class ProdService:
            pass

        # 设置环境
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        # 不应该创建 Bean
        self.assertFalse(self.evaluator.should_create_bean(ProdService))

    def test_should_create_bean_without_profile(self):
        """测试应该创建没有 Profile 注解的 Bean"""

        class RegularService:
            pass

        # 应该创建 Bean
        self.assertTrue(self.evaluator.should_create_bean(RegularService))

    def test_filter_bean_classes(self):
        """测试过滤 Bean 类列表"""

        @Profile("dev")
        class DevService:
            pass

        @Profile("prod")
        class ProdService:
            pass

        class RegularService:
            pass

        # 设置环境只激活 dev
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean_classes = [DevService, ProdService, RegularService]
        filtered_classes = self.evaluator.filter_bean_classes(bean_classes)

        # 应该只保留 DevService 和 RegularService
        self.assertIn(DevService, filtered_classes)
        self.assertNotIn(ProdService, filtered_classes)
        self.assertIn(RegularService, filtered_classes)

    def test_filter_empty_list(self):
        """测试过滤空列表"""
        filtered_classes = self.evaluator.filter_bean_classes([])
        self.assertEqual(filtered_classes, [])

    def test_filter_all_matching(self):
        """测试过滤全部匹配的列表"""

        @Profile("dev")
        class DevService1:
            pass

        @Profile("dev")
        class DevService2:
            pass

        # 设置环境
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean_classes = [DevService1, DevService2]
        filtered_classes = self.evaluator.filter_bean_classes(bean_classes)

        # 应该保留所有类
        self.assertEqual(len(filtered_classes), 2)
        self.assertIn(DevService1, filtered_classes)
        self.assertIn(DevService2, filtered_classes)

    def test_filter_none_matching(self):
        """测试过滤全部不匹配的列表"""

        @Profile("prod")
        class ProdService1:
            pass

        @Profile("staging")
        class StagingService:
            pass

        # 设置环境
        self.mock_environment.get_active_profiles.return_value = ["dev"]
        self.mock_environment.get_default_profiles.return_value = ["default"]

        bean_classes = [ProdService1, StagingService]
        filtered_classes = self.evaluator.filter_bean_classes(bean_classes)

        # 应该过滤掉所有类
        self.assertEqual(len(filtered_classes), 0)


if __name__ == "__main__":
    unittest.main()

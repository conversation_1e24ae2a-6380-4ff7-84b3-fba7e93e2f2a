"""
Mini-Boot Web 指标功能演示

展示完整的指标收集和分析功能:
- WebMetricsCollector: Web 应用性能指标收集
- BackpressureMetrics: 背压控制效果监控
- SchedulingMetrics: 智能调度性能监控
- HealthIndicator: Web 应用健康检查
- Diagnostics: 运行时诊断信息
- PerformanceAnalyzer: 性能分析报告
"""

import asyncio
import time
import json
from typing import Dict, Any

from miniboot.web.metrics import WebMetricsCollector, BackpressureMetrics, SchedulingMetrics, HealthIndicator, DiagnosticEndpoint, PerformanceAnalyzer
from miniboot.web.metrics.integration import MonitoringIntegration, initialize_monitoring, get_monitoring_summary
from miniboot.web.metrics.web import RequestMetrics
from miniboot.web.metrics.scheduling import SchedulingEvent


async def demo_web_metrics():
    """演示 Web 指标收集"""
    print("🌐 Web 指标收集演示")
    print("=" * 50)

    collector = WebMetricsCollector()

    # 模拟一些 Web 请求
    print("📊 模拟 Web 请求...")
    for i in range(10):
        metrics = RequestMetrics(
            timestamp=time.time(),
            method="GET" if i % 3 == 0 else "POST",
            path=f"/api/endpoint{i % 3}",
            status_code=200 if i < 9 else 500,  # 10% 错误率
            response_time=50.0 + (i % 5) * 30,  # 50-170ms 响应时间
            request_size=1024 + i * 100,
            response_size=2048 + i * 200,
        )
        collector.record_request(metrics)

    # 获取指标汇总
    summary = collector.get_metrics_summary()
    print(f"📈 指标汇总:")
    print(f"   总请求数: {summary.total_requests}")
    print(f"   成功请求: {summary.successful_requests}")
    print(f"   失败请求: {summary.failed_requests}")
    print(f"   平均响应时间: {summary.avg_response_time:.2f}ms")
    print(f"   错误率: {summary.error_rate:.1f}%")
    print(f"   请求速率: {summary.requests_per_second:.2f} QPS")

    print()


async def demo_backpressure_metrics():
    """演示背压控制指标"""
    print("🛡️ 背压控制指标演示")
    print("=" * 50)

    metrics = BackpressureMetrics()

    # 模拟背压控制事件
    print("⚡ 模拟背压控制事件...")

    # 熔断器事件
    metrics.record_circuit_breaker_event("api-breaker", "closed")
    metrics.record_circuit_breaker_event("db-breaker", "open", {"reason": "high_error_rate"})

    # 负载脱落事件
    metrics.record_load_shedding_event("load-balancer", "triggered", {"load_level": 0.95})

    # 记录负载水平
    for i in range(5):
        metrics.record_load_level(0.6 + (i * 0.08))  # 逐渐增加的负载

    # 获取指标汇总
    summary = metrics.get_metrics_summary()
    print(f"📊 背压指标汇总:")
    print(f"   熔断器触发次数: {summary.circuit_breaker_triggers}")
    print(f"   负载脱落事件: {summary.load_shedding_events}")
    print(f"   平均负载水平: {summary.avg_load_level:.2f}")
    print(f"   系统稳定性评分: {summary.system_stability_score:.1f}")
    print(f"   保护效果: {summary.protection_effectiveness:.1f}%")

    print()


async def demo_scheduling_metrics():
    """演示智能调度指标"""
    print("🧠 智能调度指标演示")
    print("=" * 50)

    metrics = SchedulingMetrics()

    # 模拟任务执行事件
    print("⚙️ 模拟任务执行...")

    task_types = ["api_request", "data_processing", "file_upload"]
    classifications = ["io_intensive", "cpu_intensive", "mixed"]
    strategies = ["async", "sync", "thread_pool"]

    for i in range(15):
        task_type = task_types[i % len(task_types)]
        classification = classifications[i % len(classifications)]
        strategy = strategies[i % len(strategies)]

        # 模拟执行时间(异步任务通常更快)
        base_time = 0.1
        if strategy == "async":
            execution_time = base_time + (i % 3) * 0.02
        elif strategy == "thread_pool":
            execution_time = base_time + (i % 3) * 0.04
        else:  # sync
            execution_time = base_time + (i % 3) * 0.06

        event = SchedulingEvent(
            timestamp=time.time(),
            task_id=f"task-{i}",
            task_type=task_type,
            classification=classification,
            strategy=strategy,
            execution_time=execution_time,
            predicted_time=execution_time * (0.9 + (i % 2) * 0.2),  # 模拟预测时间
            success=i < 14,  # 93% 成功率
        )

        metrics.record_task_execution(event)

    # 获取指标汇总
    summary = metrics.get_metrics_summary()
    print(f"📈 调度指标汇总:")
    print(f"   总任务数: {summary.total_tasks}")
    print(f"   成功任务: {summary.successful_tasks}")
    print(f"   失败任务: {summary.failed_tasks}")
    print(f"   分类准确性: {summary.classification_accuracy:.1f}%")
    print(f"   策略有效性: {summary.strategy_effectiveness:.1f}%")
    print(f"   平均执行时间: {summary.avg_execution_time:.3f}s")
    print(f"   异步采用率: {summary.async_adoption_rate:.1f}%")

    print()


async def demo_health_indicator():
    """演示健康检查"""
    print("💚 健康检查演示")
    print("=" * 50)

    health_indicator = HealthIndicator()

    # 执行健康检查
    print("🔍 执行健康检查...")
    health_results = await health_indicator.check_health()

    overall_status = health_indicator.get_overall_status()
    print(f"🏥 整体健康状态: {overall_status.value}")

    print(f"\n📋 组件健康状态:")
    for name, result in health_results.items():
        status_emoji = "✅" if result.status.value == "UP" else "❌"
        print(f"   {status_emoji} {name}: {result.status.value}")
        print(f"      消息: {result.message}")

    print()


async def demo_diagnostics():
    """演示诊断功能"""
    print("🔧 诊断功能演示")
    print("=" * 50)

    diagnostic = DiagnosticEndpoint()

    # 获取应用信息
    app_info = diagnostic.get_application_info()
    print(f"📱 应用信息:")
    print(f"   名称: {app_info['name']}")
    print(f"   版本: {app_info['version']}")
    print(f"   运行时间: {app_info['uptime_formatted']}")
    print(f"   环境: {app_info['environment']}")

    # 获取系统信息
    system_info = diagnostic.get_system_info()
    print(f"\n💻 系统信息:")
    print(f"   操作系统: {system_info['platform']['system']} {system_info['platform']['release']}")
    print(f"   Python版本: {system_info['python']['version'].split()[0]}")
    print(f"   CPU核心数: {system_info['system_resources']['cpu_count']}")

    print()


async def demo_performance_analyzer():
    """演示性能分析器"""
    print("📊 性能分析器演示")
    print("=" * 50)

    # 创建带有数据的监控组件
    web_metrics = WebMetricsCollector()
    backpressure_metrics = BackpressureMetrics()
    scheduling_metrics = SchedulingMetrics()

    # 添加一些测试数据
    for i in range(5):
        # Web 指标
        request = RequestMetrics(
            timestamp=time.time(),
            method="GET",
            path="/api/test",
            status_code=200 if i < 4 else 500,
            response_time=100 + i * 100,  # 逐渐增加的响应时间
        )
        web_metrics.record_request(request)

        # 背压指标
        backpressure_metrics.record_load_level(0.5 + i * 0.1)

        # 调度指标
        task_event = SchedulingEvent(
            timestamp=time.time(),
            task_id=f"task-{i}",
            task_type="api_request",
            classification="io_intensive",
            strategy="async",
            execution_time=0.1 + i * 0.02,
            success=True,
        )
        scheduling_metrics.record_task_execution(task_event)

    # 创建性能分析器
    analyzer = PerformanceAnalyzer(web_metrics, backpressure_metrics, scheduling_metrics)

    # 生成性能报告
    print("📈 生成性能报告...")
    report_dict = analyzer.to_dict(period_seconds=300)

    print(f"🎯 性能报告:")
    print(f"   总体评分: {report_dict['overall_score']:.1f}/100")

    # Web 性能
    web_summary = report_dict["summary"].get("web", {})
    if web_summary:
        print(f"\n🌐 Web 性能:")
        print(f"   平均响应时间: {web_summary.get('avg_response_time', 0):.1f}ms")
        print(f"   错误率: {web_summary.get('error_rate', 0):.1f}%")
        print(f"   总请求数: {web_summary.get('total_requests', 0)}")

    # 问题和建议
    issues = report_dict.get("issues", [])
    if issues:
        print(f"\n⚠️ 发现的问题:")
        for issue in issues[:2]:  # 只显示前2个问题
            severity_emoji = {"critical": "🚨", "high": "⚠️", "medium": "📝", "low": "💡"}.get(issue["severity"], "📝")
            print(f"   {severity_emoji} {issue['title']}: {issue['description']}")

    recommendations = report_dict.get("recommendations", [])
    if recommendations:
        print(f"\n💡 优化建议:")
        for rec in recommendations[:2]:  # 只显示前2个建议
            print(f"   • {rec}")

    print()


async def demo_integration():
    """演示指标集成"""
    print("🔗 指标集成演示")
    print("=" * 50)

    # 初始化监控集成
    integration = initialize_monitoring()

    # 添加一些测试数据
    web_metrics = integration.get_web_metrics_collector()
    for i in range(3):
        request = RequestMetrics(timestamp=time.time(), method="GET", path="/api/integration", status_code=200, response_time=80 + i * 20)
        web_metrics.record_request(request)

    # 获取监控摘要
    print("📋 获取监控摘要...")
    summary = await get_monitoring_summary()

    print(f"🎯 监控集成状态:")
    print(f"   整体健康状态: {summary.get('overall_health', 'UNKNOWN')}")

    web_summary = summary["summary"]["web"]
    print(f"🌐 Web 摘要: {web_summary['total_requests']}个请求, 平均{web_summary['avg_response_time']:.1f}ms")

    print()


async def main():
    """主演示函数"""
    print("🚀 Mini-Boot Web 指标功能演示")
    print("=" * 60)
    print()

    # 运行各个演示
    await demo_web_metrics()
    await demo_backpressure_metrics()
    await demo_scheduling_metrics()
    await demo_health_indicator()
    await demo_diagnostics()
    await demo_performance_analyzer()
    await demo_integration()

    print("🎉 指标功能演示完成!")
    print("\n📝 功能总结:")
    print("   ✅ WebMetrics - Web 应用性能指标收集")
    print("   ✅ BackpressureMetrics - 背压控制效果监控")
    print("   ✅ SchedulingMetrics - 智能调度性能监控")
    print("   ✅ HealthIndicator - Web 应用健康检查")
    print("   ✅ Diagnostics - 运行时诊断信息")
    print("   ✅ PerformanceAnalyzer - 性能分析报告")
    print("   ✅ Integration - 指标功能集成")


if __name__ == "__main__":
    asyncio.run(main())

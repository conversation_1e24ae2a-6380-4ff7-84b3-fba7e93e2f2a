#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 启用 WebSocket 功能示例
"""

from miniboot.context import ApplicationContext


def main():
    """启用 WebSocket 功能示例"""
    print("🔧 启用 WebSocket 功能示例")
    print("=" * 50)

    print("📋 WebSocket Starter 默认配置:")
    print("  - WebSocket 功能默认是 禁用 状态")
    print("  - 这是为了避免不必要的资源占用")
    print("  - 用户需要根据项目需求主动启用")

    print("\n🛠️ 启用方式:")

    print("\n1️⃣ 方式一:在 application.yml 中启用")
    print("""
miniboot:
    starters:
        websocket:
            enabled: true  # 启用 WebSocket 功能
            path: "/ws"    # 设置端点路径

            # 其他配置...
            server:
                port: 8080
            compression:
                enabled: true
    """)

    print("2️⃣ 方式二:在环境特定配置中启用")
    print("""
# application-dev.yml (开发环境)
miniboot:
    starters:
        websocket:
            enabled: true
            server:
                host: localhost
                port: 8080
            security:
                allowed-origins: ["http://localhost:3000"]
    """)

    print("3️⃣ 方式三:通过环境变量启用")
    print("""
# 设置环境变量
export MINIBOOT_STARTERS_WEBSOCKET_ENABLED=true
export MINIBOOT_STARTERS_WEBSOCKET_PATH=/ws
export MINIBOOT_STARTERS_WEBSOCKET_SERVER_PORT=8080
    """)

    print("4️⃣ 方式四:通过代码配置启用")
    print("""
from miniboot.context import ApplicationContext
from miniboot.starters.websocket import WebSocketProperties

# 创建应用上下文
context = ApplicationContext()

# 获取并修改配置
properties = context.get_bean(WebSocketProperties)
properties.enabled = True
properties.path = "/ws"
properties.server.port = 8080

# 启动应用
context.run()
    """)

    print("\n✅ 推荐的启用流程:")
    print("  1. 在 application.yml 中设置基础配置")
    print("  2. 在环境特定配置中覆盖环境相关设置")
    print("  3. 通过环境变量覆盖敏感配置(如密钥)")
    print("  4. 在代码中进行最终的动态调整")

    print("\n🎯 最佳实践:")
    print("  - 开发环境:启用详细日志和宽松的安全设置")
    print("  - 测试环境:启用完整功能进行集成测试")
    print("  - 生产环境:启用安全认证和性能优化")

    # 演示检查 WebSocket 是否启用
    print("\n🔍 检查 WebSocket 是否启用:")
    try:
        context = ApplicationContext()
        from miniboot.starters.websocket import WebSocketProperties

        properties = context.get_bean(WebSocketProperties)

        if properties.enabled:
            print("  ✅ WebSocket 功能已启用")
            print(f"  🌐 端点路径: {properties.path}")
            print(f"  🖥️ 服务器端口: {properties.server.port}")
        else:
            print("  ❌ WebSocket 功能未启用")
            print("  💡 请在配置文件中设置 miniboot.starters.websocket.enabled=true")

        context.close()

    except Exception as e:
        print(f"  ⚠️ 检查失败: {e}")


def show_configuration_examples():
    """展示不同场景的配置示例"""
    print("\n📝 不同场景的配置示例:")
    print("=" * 50)

    print("🏠 个人项目/原型开发:")
    print("""
miniboot:
    starters:
        websocket:
            enabled: true
            path: "/ws"
            security:
                auth:
                    enabled: false  # 简化开发
            compression:
                enabled: false      # 减少复杂性
    """)

    print("🏢 企业级应用:")
    print("""
miniboot:
    starters:
        websocket:
            enabled: true
            path: "/api/ws"
            max-message-size: 1048576  # 1MB

            security:
                allowed-origins: ["https://yourdomain.com"]
                auth:
                    enabled: true
                jwt:
                    enabled: true
                    secret-key: "${JWT_SECRET}"

            compression:
                enabled: true
                level: 6

            connection-limit:
                max-total-connections: 10000
                max-connections-per-ip: 100

            pool:
                max-connections: 200
                enable-validation: true

            metrics:
                enabled: true
                export-format: "prometheus"

            health:
                enabled: true
                check-interval: 30
    """)

    print("🎮 实时游戏服务:")
    print("""
miniboot:
    starters:
        websocket:
            enabled: true
            path: "/game/ws"
            max-message-size: 65536    # 64KB,小消息快速传输

            server:
                idle-timeout: 300      # 5分钟空闲超时

            timeout:
                heartbeat: 10          # 10秒心跳,快速检测断线

            compression:
                enabled: true
                level: 1               # 低延迟优先
                min-size: 256          # 小消息也压缩

            connection-limit:
                max-connections-per-user: 1  # 每用户单连接
                max-total-connections: 50000
    """)

    print("📊 数据监控面板:")
    print("""
miniboot:
    starters:
        websocket:
            enabled: true
            path: "/monitor/ws"

            timeout:
                heartbeat: 5           # 频繁心跳保持连接

            compression:
                enabled: true
                level: 9               # 高压缩比,节省带宽

            metrics:
                enabled: true
                collection-interval: 10  # 频繁收集指标

            health:
                enabled: true
                check-interval: 5      # 频繁健康检查
    """)


if __name__ == "__main__":
    main()
    show_configuration_examples()

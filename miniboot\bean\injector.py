#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 统一依赖注入器
"""

import inspect
import threading
from typing import Any, Dict, List, Optional, Tuple, get_type_hints

from loguru import logger

from .definition import BeanDefinition, ConstructorArgument, PropertyValue


class DependencyInjector:
    """统一依赖注入器

    负责实现Bean的所有依赖注入功能，包括：
    1. 构造函数参数注入
    2. 属性值注入（直接值和Bean引用）
    3. 字段依赖注入（@Autowired注解）
    4. Setter方法注入

    与Bean工厂协同工作，提供完整的依赖解析和注入能力。
    """

    def __init__(self, bean_factory: "BeanFactory"):
        """初始化依赖注入器

        Args:
            bean_factory: Bean工厂实例，用于解析Bean依赖
        """
        self._bean_factory = bean_factory
        self._lock = threading.RLock()

        # 缓存已解析的依赖信息，提高性能
        self._field_dependencies_cache: Dict[type, List[Tuple[str, type, bool]]] = {}
        self._constructor_dependencies_cache: Dict[type, List[Tuple[str, type, bool]]] = {}

        # 注入统计信息
        self._injection_stats = {
            'property_injections': 0,
            'field_injections': 0,
            'constructor_injections': 0,
            'failed_injections': 0
        }

        # Bean创建优化器（延迟初始化）
        self._optimizer = None

    def _get_optimizer(self):
        """获取Bean创建优化器（延迟初始化）"""
        if self._optimizer is None:
            try:
                from .performance import BeanCreationOptimizer
                self._optimizer = BeanCreationOptimizer()
                logger.debug("Dependency injection optimizer initialized successfully")
            except Exception as e:
                # 如果优化器初始化失败，使用空对象模式
                logger.warning(f"Failed to initialize dependency injection optimizer: {e}")
                self._optimizer = None
        return self._optimizer

    def get_injection_stats(self) -> dict:
        """获取依赖注入的性能统计信息"""
        stats = self._injection_stats.copy()

        # 计算总注入次数
        total_injections = (
            stats['property_injections'] +
            stats['field_injections'] +
            stats['constructor_injections']
        )
        stats['total_injections'] = total_injections

        # 计算成功率
        if total_injections > 0:
            stats['success_rate'] = (total_injections - stats['failed_injections']) / total_injections
        else:
            stats['success_rate'] = 1.0

        # 添加优化器统计信息
        optimizer = self._get_optimizer()
        if optimizer:
            stats['optimizer_enabled'] = True
            optimizer_stats = optimizer.get_performance_stats()
            stats['optimizer_cache_hit_rates'] = {
                'constructor': optimizer_stats.get('constructor_cache_hit_rate', 0.0),
                'type_check': optimizer_stats.get('type_check_cache_hit_rate', 0.0),
                'dependency': optimizer_stats.get('dependency_cache_hit_rate', 0.0)
            }
        else:
            stats['optimizer_enabled'] = False

        return stats

    def inject(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """注入Bean的所有依赖

        这是依赖注入的主入口，会按顺序执行：
        1. 属性值注入（property_values）
        2. 字段依赖注入（@Autowired字段）
        3. Setter方法注入（@Autowired方法）

        Args:
            bean: Bean实例
            bean_definition: Bean定义

        Raises:
            BeanCreationError: 如果依赖注入失败
        """
        try:
            logger.debug(f"Starting dependency injection for bean: {bean_definition.bean_name}")

            # 1. 注入属性值（包括直接值和Bean引用）
            self._inject_property_values(bean, bean_definition)

            # 2. 注入字段依赖（@Autowired注解的字段）
            self._inject_field_dependencies(bean, bean_definition)

            # 3. 注入Setter方法依赖（@Autowired注解的方法）
            self._inject_setter_dependencies(bean, bean_definition)

            logger.debug(f"Successfully completed dependency injection for bean: {bean_definition.bean_name}")

        except Exception as e:
            self._injection_stats['failed_injections'] += 1
            logger.error(f"Failed to inject dependencies for bean '{bean_definition.bean_name}': {e}")
            raise RuntimeError(
                f"Failed to inject dependencies for bean '{bean_definition.bean_name}': {str(e)}"
            ) from e

    def resolve_constructor(self, bean_definition: BeanDefinition) -> List[Any]:
        """解析构造函数依赖

        根据Bean定义中的构造函数参数或类型注解解析依赖。

        Args:
            bean_definition: Bean定义

        Returns:
            List[Any]: 构造函数参数列表

        Raises:
            BeanCreationError: 如果依赖解析失败
        """
        bean_class = bean_definition.bean_class

        # 如果有显式的构造函数参数，直接使用
        if bean_definition.has_args():
            return self._resolve_explicit_constructor_args(bean_definition)

        # 否则通过类型注解自动解析
        return self._resolve_auto_constructor_args(bean_class, bean_definition.bean_name)

    def _resolve_explicit_constructor_args(self, bean_definition: BeanDefinition) -> List[Any]:
        """解析显式构造函数参数

        Args:
            bean_definition: Bean定义

        Returns:
            List[Any]: 构造函数参数列表
        """
        args = []

        for arg in bean_definition.constructor_args:
            if arg.is_reference():
                # Bean引用
                dependency = self._bean_factory.get_bean(arg.ref)
                args.append(dependency)
                self._injection_stats['constructor_injections'] += 1
            else:
                # 直接值
                args.append(arg.value)

        return args

    def _resolve_auto_constructor_args(self, bean_class: type, bean_name: str) -> List[Any]:
        """自动解析构造函数参数

        通过类型注解自动解析构造函数依赖。

        Args:
            bean_class: Bean类
            bean_name: Bean名称

        Returns:
            List[Any]: 构造函数参数列表
        """
        try:
            # 使用优化器缓存构造函数信息
            optimizer = self._get_optimizer()
            if optimizer and optimizer.config.enable_constructor_caching:
                constructor_info = optimizer.get_constructor_info(bean_class)
                signature = constructor_info.get('signature')
                type_hints = constructor_info.get('type_hints', {})

                if signature is None:
                    # 回退到直接调用
                    constructor = bean_class.__init__
                    signature = inspect.signature(constructor)
                    type_hints = get_type_hints(constructor)
            else:
                # 回退到直接调用
                constructor = bean_class.__init__
                signature = inspect.signature(constructor)
                type_hints = get_type_hints(constructor)

            args = []
            for param_name, param in signature.parameters.items():
                if param_name == "self":
                    continue

                # 获取参数类型
                param_type = type_hints.get(param_name, param.annotation)

                if param_type == inspect.Parameter.empty:
                    # 没有类型注解
                    if param.default != inspect.Parameter.empty:
                        # 使用默认值
                        args.append(param.default)
                    else:
                        raise ValueError(
                            f"Constructor parameter '{param_name}' has no type annotation and no default value"
                        )
                    continue

                # 解析依赖
                dependency = self._resolve_dependency(param_type, param_name, bean_name)
                args.append(dependency)
                self._injection_stats['constructor_injections'] += 1

            return args

        except Exception as e:
            logger.error(f"Failed to resolve constructor dependencies for bean '{bean_name}': {e}")
            raise RuntimeError(
                f"Failed to resolve constructor dependencies for bean '{bean_name}': {str(e)}"
            ) from e

    def _inject_property_values(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """注入属性值

        注入Bean定义中配置的属性值，包括直接值和Bean引用。

        Args:
            bean: Bean实例
            bean_definition: Bean定义
        """
        import time
        start_time = time.time()

        for prop in bean_definition.property_values:
            try:
                if prop.is_reference():
                    # Bean引用
                    dependency = self._bean_factory.get_bean(prop.ref)
                    setattr(bean, prop.name, dependency)
                else:
                    # 直接值
                    setattr(bean, prop.name, prop.value)

                self._injection_stats['property_injections'] += 1

            except Exception as e:
                logger.error(f"Failed to inject property '{prop.name}' for bean '{bean_definition.bean_name}': {e}")
                raise RuntimeError(
                    f"Failed to inject property '{prop.name}' for bean '{bean_definition.bean_name}': {str(e)}"
                ) from e

        # 记录属性注入性能
        injection_time = time.time() - start_time
        optimizer = self._get_optimizer()
        if optimizer and optimizer.config.enable_performance_monitoring:
            # 可以在这里记录属性注入的性能数据
            pass

    def _inject_field_dependencies(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """注入字段依赖

        注入@Autowired注解的字段依赖。

        Args:
            bean: Bean实例
            bean_definition: Bean定义
        """
        import time
        start_time = time.time()

        bean_class = bean_definition.bean_class

        # 使用优化器缓存字段依赖信息
        optimizer = self._get_optimizer()
        if optimizer and optimizer.config.enable_dependency_resolution_caching:
            # 尝试从优化器获取依赖信息
            cached_dependencies = optimizer.get_dependency_info(bean_class)
            if cached_dependencies:
                # 使用缓存的依赖信息（这里简化处理，实际可以更复杂）
                field_dependencies = self._get_field_dependencies(bean_class)
            else:
                field_dependencies = self._get_field_dependencies(bean_class)
        else:
            field_dependencies = self._get_field_dependencies(bean_class)

        for field_name, field_type, required in field_dependencies:
            try:
                # 解析依赖
                dependency = self._resolve_dependency(field_type, field_name, bean_definition.bean_name, required)
                if dependency is not None:
                    setattr(bean, field_name, dependency)
                    self._injection_stats['field_injections'] += 1

            except Exception as e:
                if required:
                    logger.error(f"Failed to inject required field '{field_name}' for bean '{bean_definition.bean_name}': {e}")
                    raise RuntimeError(
                        f"Failed to inject required field '{field_name}' for bean '{bean_definition.bean_name}': {str(e)}"
                    ) from e
                else:
                    # 非必需的依赖注入失败，设置为None
                    logger.warning(f"Failed to inject optional field '{field_name}' for bean '{bean_definition.bean_name}': {e}")
                    setattr(bean, field_name, None)

        # 记录字段注入性能
        injection_time = time.time() - start_time
        if optimizer and optimizer.config.enable_performance_monitoring:
            # 可以在这里记录字段注入的性能数据
            pass

    def _inject_setter_dependencies(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """注入Setter方法依赖

        调用@Autowired注解的setter方法进行依赖注入。

        Args:
            bean: Bean实例
            bean_definition: Bean定义
        """
        bean_class = bean_definition.bean_class
        setter_dependencies = self._get_setter_dependencies(bean_class)

        for method_name, param_type, required in setter_dependencies:
            try:
                # 解析依赖
                dependency = self._resolve_dependency(param_type, method_name, bean_definition.bean_name, required)
                if dependency is not None:
                    # 调用setter方法
                    setter_method = getattr(bean, method_name)
                    setter_method(dependency)
                    self._injection_stats['field_injections'] += 1

            except Exception as e:
                if required:
                    logger.error(f"Failed to inject required setter method '{method_name}' for bean '{bean_definition.bean_name}': {e}")
                    raise RuntimeError(
                        f"Failed to inject setter method '{method_name}' for bean '{bean_definition.bean_name}': {str(e)}"
                    ) from e
                else:
                    logger.warning(f"Failed to inject optional setter method '{method_name}' for bean '{bean_definition.bean_name}': {e}")

    def _get_field_dependencies(self, bean_class: type) -> List[Tuple[str, type, bool]]:
        """获取类的字段依赖信息

        分析类中@Autowired注解的字段，返回依赖信息。

        Args:
            bean_class: Bean类

        Returns:
            List[Tuple[str, type, bool]]: 字段依赖信息列表 (字段名, 类型, 是否必需)
        """
        with self._lock:
            if bean_class in self._field_dependencies_cache:
                return self._field_dependencies_cache[bean_class]

        field_dependencies = []

        try:
            # 获取类型注解
            type_hints = get_type_hints(bean_class)

            # 检查类是否有__autowired_fields__属性（类级别的@Autowired）
            if hasattr(bean_class, "__autowired_fields__"):
                autowired_fields = bean_class.__autowired_fields__

                for field_name, metadata in autowired_fields.items():
                    field_type = type_hints.get(field_name)
                    if field_type:
                        required = metadata.required if metadata else True
                        field_dependencies.append((field_name, field_type, required))

            # 缓存结果
            with self._lock:
                self._field_dependencies_cache[bean_class] = field_dependencies

        except Exception:
            # 如果分析失败，返回空列表
            field_dependencies = []

        return field_dependencies

    def _get_setter_dependencies(self, bean_class: type) -> List[Tuple[str, type, bool]]:
        """获取类的setter方法依赖信息

        分析类中@Autowired注解的setter方法，返回依赖信息。

        Args:
            bean_class: Bean类

        Returns:
            List[Tuple[str, type, bool]]: setter依赖信息列表 (方法名, 参数类型, 是否必需)
        """
        setter_dependencies = []

        try:
            for attr_name in dir(bean_class):
                if attr_name.startswith("_"):
                    continue

                attr = getattr(bean_class, attr_name, None)

                # 检查是否是方法且有@Autowired注解
                if (inspect.isfunction(attr) or inspect.ismethod(attr)) and self._is_autowired_method(attr):
                    # 获取方法参数类型
                    signature = inspect.signature(attr)
                    type_hints = get_type_hints(attr)

                    # 假设setter方法只有一个参数（除了self）
                    params = list(signature.parameters.values())
                    if len(params) == 2:  # self + 一个参数
                        param = params[1]
                        param_type = type_hints.get(param.name, param.annotation)

                        if param_type != inspect.Parameter.empty:
                            required = self._get_autowired_required(attr)
                            setter_dependencies.append((attr_name, param_type, required))

        except Exception:
            # 如果分析失败，返回空列表
            setter_dependencies = []

        return setter_dependencies

    def _resolve_dependency(self, dependency_type: type, field_name: str, bean_name: str,
                          required: bool = True) -> Optional[Any]:
        """解析单个依赖

        Args:
            dependency_type: 依赖类型
            field_name: 字段名称
            bean_name: Bean名称
            required: 是否必需

        Returns:
            Optional[Any]: 解析的依赖对象，如果非必需且解析失败则返回None
        """
        try:
            return self._bean_factory.get_bean_by_type(dependency_type)
        except Exception as e:
            if required:
                raise RuntimeError(
                    f"Failed to resolve dependency of type {dependency_type.__name__} "
                    f"for field '{field_name}' in bean '{bean_name}': {str(e)}"
                ) from e
            else:
                return None

    def _is_autowired_method(self, method: Any) -> bool:
        """检查方法是否有@Autowired注解

        Args:
            method: 方法对象

        Returns:
            bool: 如果有@Autowired注解返回True
        """
        # TODO: 实现@Autowired注解检查
        # 这里需要与annotations模块集成
        return hasattr(method, '__autowired__')

    def _get_autowired_required(self, method: Any) -> bool:
        """获取@Autowired注解的required属性

        Args:
            method: 方法对象

        Returns:
            bool: required属性值，默认为True
        """
        # TODO: 实现@Autowired元数据获取
        # 这里需要与annotations模块集成
        autowired_metadata = getattr(method, '__autowired__', None)
        if autowired_metadata and hasattr(autowired_metadata, 'required'):
            return autowired_metadata.required
        return True

    def stats(self) -> Dict[str, int]:
        """获取注入统计信息

        Returns:
            Dict[str, int]: 注入统计信息
        """
        return self._injection_stats.copy()

    def clear(self) -> None:
        """清空依赖缓存"""
        with self._lock:
            self._field_dependencies_cache.clear()
            self._constructor_dependencies_cache.clear()

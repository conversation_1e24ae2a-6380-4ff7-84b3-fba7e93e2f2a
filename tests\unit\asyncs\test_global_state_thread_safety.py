#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 全局状态管理线程安全测试 - 验证FIX-1.1.2修复的线程安全问题
"""

import threading
import time
import unittest
from concurrent.futures import ThreadPoolExecutor, as_completed

from miniboot.asyncs.pool import ThreadPoolManager
from miniboot.utils.singleton import SingletonMeta


class GlobalStateThreadSafetyTestCase(unittest.TestCase):
    """全局状态管理线程安全测试"""

    def setUp(self):
        """设置测试环境"""
        # 确保每个测试开始时都有干净的状态
        SingletonMeta.reset_instance(ThreadPoolManager)

    def tearDown(self):
        """清理测试环境"""
        # 确保每个测试结束后都清理状态
        SingletonMeta.reset_instance(ThreadPoolManager)

    def test_concurrent_singleton_creation(self):
        """测试并发单例创建的线程安全性"""
        results = []
        errors = []

        def get_manager():
            """并发获取管理器的函数"""
            try:
                manager = ThreadPoolManager()
                results.append(manager)
                return manager
            except Exception as e:
                errors.append(e)
                raise

        # 使用多线程并发获取管理器
        with ThreadPoolExecutor(max_workers=10) as executor:
            futures = [executor.submit(get_manager) for _ in range(20)]

            # 等待所有任务完成
            for future in as_completed(futures):
                future.result()

        # 验证结果
        self.assertEqual(len(errors), 0, f"获取过程中出现错误: {errors}")
        self.assertEqual(len(results), 20, "应该获取20个管理器实例")

        # 验证所有管理器实例都是同一个对象（单例）
        first_manager = results[0]
        for manager in results[1:]:
            self.assertIs(manager, first_manager, "所有管理器实例应该是同一个对象")

    def test_singleton_thread_safety_under_load(self):
        """测试高负载下的单例线程安全性"""
        managers = []
        lock = threading.Lock()

        def stress_test():
            """压力测试函数"""
            for _ in range(100):
                manager = ThreadPoolManager()
                with lock:
                    managers.append(manager)
                # 模拟一些工作
                time.sleep(0.001)

        # 创建多个线程进行压力测试
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=stress_test)
            threads.append(thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证所有管理器都是同一个实例
        self.assertEqual(len(managers), 500, "应该有500个管理器引用")
        first_manager = managers[0]
        for manager in managers[1:]:
            self.assertIs(manager, first_manager, "所有管理器实例应该是同一个对象")

    def test_singleton_reset_thread_safety(self):
        """测试单例重置的线程安全性"""
        # 先获取一个管理器实例
        original_manager = ThreadPoolManager()

        # 重置单例
        SingletonMeta.reset_instance(ThreadPoolManager)

        # 再次获取管理器实例
        new_manager = ThreadPoolManager()

        # 验证是不同的实例
        self.assertIsNot(original_manager, new_manager, "重置后应该是不同的实例")
        self.assertIsInstance(new_manager, ThreadPoolManager, "新实例应该是ThreadPoolManager类型")

    def test_concurrent_reset_and_access(self):
        """测试并发重置和访问的线程安全性"""
        results = []
        errors = []

        def access_manager():
            """访问管理器的函数"""
            try:
                manager = ThreadPoolManager()
                results.append(manager)
                time.sleep(0.01)  # 模拟一些工作
                return manager
            except Exception as e:
                errors.append(e)
                return e

        def reset_manager():
            """重置管理器的函数"""
            try:
                time.sleep(0.005)  # 稍微延迟
                SingletonMeta.reset_instance(ThreadPoolManager)
                return True
            except Exception as e:
                errors.append(e)
                return e

        # 并发执行访问和重置操作
        with ThreadPoolExecutor(max_workers=6) as executor:
            # 提交访问任务
            access_futures = [executor.submit(access_manager) for _ in range(10)]
            # 提交重置任务
            reset_futures = [executor.submit(reset_manager) for _ in range(2)]

            # 等待所有任务完成
            all_futures = access_futures + reset_futures
            for future in as_completed(all_futures):
                future.result()

        # 验证没有异常
        self.assertEqual(len(errors), 0, f"操作过程中出现错误: {errors}")

        # 验证所有访问都成功获取了管理器实例
        self.assertEqual(len(results), 10, "应该有10个管理器访问结果")
        for manager in results:
            self.assertIsInstance(manager, ThreadPoolManager, "所有结果都应该是ThreadPoolManager实例")

    def test_singleton_memory_consistency(self):
        """测试单例的内存一致性"""
        managers_from_threads = []

        def get_and_store_manager(thread_id):
            """获取并存储管理器的函数"""
            manager = ThreadPoolManager()
            # 存储线程ID和管理器ID的映射
            managers_from_threads.append((thread_id, id(manager), manager))

        # 创建多个线程
        threads = []
        for i in range(10):
            thread = threading.Thread(target=get_and_store_manager, args=(i,))
            threads.append(thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证所有线程看到的都是同一个对象
        self.assertEqual(len(managers_from_threads), 10, "应该有10个线程的结果")

        first_manager_id = managers_from_threads[0][1]
        first_manager = managers_from_threads[0][2]

        for thread_id, manager_id, manager in managers_from_threads:
            self.assertEqual(manager_id, first_manager_id, f"线程{thread_id}看到的管理器ID应该相同")
            self.assertIs(manager, first_manager, f"线程{thread_id}看到的管理器应该是同一个对象")

    def test_class_level_singleton_behavior(self):
        """测试类级别单例行为"""
        # 直接创建ThreadPoolManager实例
        manager1 = ThreadPoolManager()
        manager2 = ThreadPoolManager()

        # 验证类级别单例
        self.assertIs(manager1, manager2, "ThreadPoolManager应该是类级别单例")

        # 通过直接创建获取的实例应该与之前创建的相同
        manager3 = ThreadPoolManager()
        self.assertIs(manager1, manager3, "直接创建的实例应该与之前创建的相同")


if __name__ == "__main__":
    unittest.main()

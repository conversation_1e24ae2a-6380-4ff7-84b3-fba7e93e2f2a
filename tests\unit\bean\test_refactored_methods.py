#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean模块重构后方法测试
"""

import unittest
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.bean.cache import ThreeLevelCache


class TestBean:
    """测试Bean类"""
    
    def __init__(self, name: str = "test"):
        self.name = name
        self.initialized = False
        self.destroyed = False
    
    def init(self):
        """初始化方法"""
        self.initialized = True
    
    def destroy(self):
        """销毁方法"""
        self.destroyed = True


class TestRefactoredMethods(unittest.TestCase):
    """测试重构后的方法"""
    
    def setUp(self):
        """测试前置设置"""
        self.factory = DefaultBeanFactory()
        self.registry = DefaultBeanDefinitionRegistry()
        self.cache = ThreeLevelCache()
    
    def test_factory_refactored_methods(self):
        """测试BeanFactory重构后的方法"""
        # 创建Bean定义
        definition = BeanDefinition(
            bean_name="testBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        definition.add_arg(0, value="refactor-test")
        
        # 注册Bean
        self.factory._registry.register("testBean", definition)
        
        # 测试contains方法
        self.assertTrue(self.factory.contains("testBean"))
        
        # 测试get方法
        bean = self.factory.get("testBean")
        self.assertIsInstance(bean, TestBean)
        self.assertEqual(bean.name, "refactor-test")
        self.assertTrue(bean.initialized)
        
        # 测试names方法
        names = self.factory.names()
        self.assertIn("testBean", names)
        
        # 测试singleton方法
        self.assertTrue(self.factory.singleton("testBean"))
        
        # 测试type方法
        bean_type = self.factory.type("testBean")
        self.assertEqual(bean_type, TestBean)
    
    def test_registry_refactored_methods(self):
        """测试BeanDefinitionRegistry重构后的方法"""
        definition = BeanDefinition(
            bean_name="registryBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        
        # 测试register方法
        self.registry.register("registryBean", definition)
        
        # 测试has_definition方法
        self.assertTrue(self.registry.has_definition("registryBean"))
        
        # 测试get_definition方法
        retrieved_def = self.registry.get_definition("registryBean")
        self.assertEqual(retrieved_def.bean_name, "registryBean")
        
        # 测试names方法
        names = self.registry.names()
        self.assertIn("registryBean", names)
        
        # 测试count方法
        count = self.registry.count()
        self.assertGreaterEqual(count, 1)
        
        # 测试remove方法
        self.registry.remove("registryBean")
        self.assertFalse(self.registry.has_definition("registryBean"))
    
    def test_definition_refactored_methods(self):
        """测试BeanDefinition重构后的方法"""
        definition = BeanDefinition(
            bean_name="defBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        
        # 测试add_property方法
        definition.add_property("test_prop", value="test_value")
        
        # 测试property方法
        prop = definition.property("test_prop")
        self.assertIsNotNone(prop)
        self.assertEqual(prop.value, "test_value")
        
        # 测试has_property方法
        self.assertTrue(definition.has_property("test_prop"))
        
        # 测试add_arg方法
        definition.add_arg(0, value="arg_value")
        
        # 测试arg方法
        arg = definition.arg(0)
        self.assertIsNotNone(arg)
        self.assertEqual(arg.value, "arg_value")
        
        # 测试has_args方法
        self.assertTrue(definition.has_args())
        
        # 测试singleton方法
        self.assertTrue(definition.singleton())
        
        # 测试prototype方法
        self.assertFalse(definition.prototype())
        
        # 测试has_init方法
        self.assertTrue(definition.has_init())
        
        # 测试lazy方法
        self.assertFalse(definition.lazy())
        
        # 测试is_primary方法
        self.assertFalse(definition.is_primary())
    
    def test_cache_refactored_methods(self):
        """测试BeanCache重构后的方法"""
        test_bean = TestBean("cache-test")
        
        # 测试put方法
        self.cache.put("cacheBean", test_bean)
        
        # 测试get方法
        cached_bean = self.cache.get("cacheBean")
        self.assertIsNotNone(cached_bean)
        self.assertEqual(cached_bean.name, "cache-test")
        
        # 测试contains方法
        self.assertTrue(self.cache.contains("cacheBean"))
        
        # 测试has方法
        self.assertTrue(self.cache.has("cacheBean"))
        
        # 测试names方法
        names = self.cache.names()
        self.assertIn("cacheBean", names)
        
        # 测试stats方法
        stats = self.cache.stats()
        self.assertIsInstance(stats, dict)
        
        # 测试info方法
        info = self.cache.info()
        self.assertIsInstance(info, dict)
        
        # 测试remove方法
        removed_bean = self.cache.remove("cacheBean")
        self.assertIsNotNone(removed_bean)
        self.assertIsNone(self.cache.get("cacheBean"))
    
    def test_extended_factory_methods(self):
        """测试扩展的工厂方法"""
        # 注册TestBean
        definition = BeanDefinition(
            bean_name="extendedBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register("extendedBean", definition)
        
        # 测试by_type方法
        bean_by_type = self.factory.by_type(TestBean)
        self.assertIsInstance(bean_by_type, TestBean)
        
        # 测试all_by_type方法
        beans_by_type = self.factory.all_by_type(TestBean)
        self.assertIsInstance(beans_by_type, dict)
        self.assertGreaterEqual(len(beans_by_type), 1)
        
        # 测试processors方法
        processors = self.factory.processors()
        self.assertIsInstance(processors, list)
    
    def test_method_chaining_compatibility(self):
        """测试方法链兼容性"""
        definition = BeanDefinition(
            bean_name="chainBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        
        # 测试方法链式调用
        definition.add_property("prop1", value="value1").add_property("prop2", value="value2")
        
        # 验证属性都已添加
        self.assertTrue(definition.has_property("prop1"))
        self.assertTrue(definition.has_property("prop2"))
        
        # 验证属性值
        self.assertEqual(definition.property("prop1").value, "value1")
        self.assertEqual(definition.property("prop2").value, "value2")


if __name__ == '__main__':
    unittest.main()

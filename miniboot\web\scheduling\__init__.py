"""
智能调度模块

提供可配置的异步粒度优化功能,包括智能任务调度、性能分析和执行策略选择.

主要功能:
- TaskScheduler - 智能任务调度器,根据任务特性选择最优执行策略
- TaskClassifier - 任务分类器,基于规则和机器学习的任务分类
- PerformanceProfiler - 性能分析器,收集和分析任务执行性能
- BenefitEvaluator - 异步收益评估器,评估异步执行的性能收益
- StrategySelector - 执行策略选择器,智能选择同步/异步/线程池执行
- LearningEngine - 自适应学习引擎,基于历史数据优化调度策略
"""

# 自适应学习
from .learning import LearningEngine, LearningModel, PredictionResult

# 异步收益评估
from .evaluator import BenefitEvaluator, BenefitAnalysis, ExecutionMode

# 执行策略选择
from .selector import ExecutionStrategy, StrategySelector, StrategyDecision

# 性能分析
from .profiler import PerformanceMetrics, PerformanceProfiler, ProfileResult

# 核心调度器
from .scheduler import SchedulingDecision, SchedulingMetrics, TaskScheduler, TaskRequest

# 任务分类
from .classifier import TaskCharacteristics, TaskClassifier, TaskType

# 版本信息
__version__ = "0.1.0"

# 公开API
__all__ = [
    # 核心调度器
    "TaskScheduler",
    # 任务分类
    "TaskClassifier",
    "TaskType",
    "TaskCharacteristics",
    # 性能分析
    "PerformanceProfiler",
    "PerformanceMetrics",
    "ProfileResult",
    # 异步收益评估
    "BenefitEvaluator",
    "BenefitAnalysis",
    "ExecutionMode",
    # 执行策略选择
    "StrategySelector",
    "ExecutionStrategy",
    "StrategyDecision",
    # 自适应学习
    "LearningEngine",
    "LearningModel",
    "PredictionResult",
    # 版本信息
    "__version__",
]

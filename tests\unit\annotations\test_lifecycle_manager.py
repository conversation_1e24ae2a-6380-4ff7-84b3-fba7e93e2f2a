#!/usr/bin/env python
"""
* @author: cz
* @description: 生命周期管理器测试

测试生命周期管理器的功能，包括Bean实例的注册、初始化和销毁。
"""

import unittest
from unittest.mock import patch

from miniboot.annotations import (
    LifecycleManager,
    PostConstruct,
    PreDestroy,
    destroy_bean_instance,
    get_bean_lifecycle_info,
    get_lifecycle_manager,
    initialize_bean_instance,
    process_bean_instance,
    register_bean_instance,
)


class TestLifecycleManager(unittest.TestCase):
    """生命周期管理器测试类"""

    def setUp(self):
        """设置测试环境"""
        self.manager = LifecycleManager()

    def test_lifecycle_manager_creation(self):
        """测试生命周期管理器创建"""
        manager = LifecycleManager()
        self.assertIsNotNone(manager)
        self.assertEqual(manager.get_managed_instances_count(), 0)

    def test_register_instance_with_lifecycle_methods(self):
        """测试注册有生命周期方法的实例"""

        class TestService:
            @PostConstruct
            def init(self):
                pass

            @PreDestroy
            def cleanup(self):
                pass

        instance = TestService()
        self.manager.register_instance(instance)

        # 验证实例被注册
        self.assertEqual(self.manager.get_managed_instances_count(), 1)

    def test_register_instance_without_lifecycle_methods(self):
        """测试注册没有生命周期方法的实例"""

        class PlainService:
            def normal_method(self):
                pass

        instance = PlainService()
        self.manager.register_instance(instance)

        # 验证实例没有被注册（因为没有生命周期方法）
        self.assertEqual(self.manager.get_managed_instances_count(), 0)

    def test_register_none_instance(self):
        """测试注册None实例"""
        self.manager.register_instance(None)
        self.assertEqual(self.manager.get_managed_instances_count(), 0)

    def test_initialize_instance(self):
        """测试初始化实例"""

        class TestService:
            def __init__(self):
                self.initialized = False

            @PostConstruct
            def init(self):
                self.initialized = True

        instance = TestService()
        self.assertFalse(instance.initialized)

        self.manager.initialize_instance(instance)
        self.assertTrue(instance.initialized)

    def test_initialize_instance_multiple_times(self):
        """测试多次初始化同一实例"""

        class TestService:
            def __init__(self):
                self.init_count = 0

            @PostConstruct
            def init(self):
                self.init_count += 1

        instance = TestService()

        # 多次初始化
        self.manager.initialize_instance(instance)
        self.manager.initialize_instance(instance)
        self.manager.initialize_instance(instance)

        # 验证只初始化了一次
        self.assertEqual(instance.init_count, 1)

    def test_initialize_none_instance(self):
        """测试初始化None实例"""
        # 应该不会抛出异常
        self.manager.initialize_instance(None)

    def test_destroy_instance(self):
        """测试销毁实例"""

        class TestService:
            def __init__(self):
                self.destroyed = False

            @PreDestroy
            def cleanup(self):
                self.destroyed = True

        instance = TestService()
        self.assertFalse(instance.destroyed)

        self.manager.destroy_instance(instance)
        self.assertTrue(instance.destroyed)

    def test_destroy_instance_multiple_times(self):
        """测试多次销毁同一实例"""

        class TestService:
            def __init__(self):
                self.destroy_count = 0

            @PreDestroy
            def cleanup(self):
                self.destroy_count += 1

        instance = TestService()

        # 多次销毁
        self.manager.destroy_instance(instance)
        self.manager.destroy_instance(instance)
        self.manager.destroy_instance(instance)

        # 验证只销毁了一次
        self.assertEqual(instance.destroy_count, 1)

    def test_destroy_none_instance(self):
        """测试销毁None实例"""
        # 应该不会抛出异常
        self.manager.destroy_instance(None)

    def test_process_instance(self):
        """测试处理实例的完整生命周期"""

        class TestService:
            def __init__(self):
                self.initialized = False

            @PostConstruct
            def init(self):
                self.initialized = True

        instance = TestService()
        self.assertFalse(instance.initialized)

        self.manager.process_instance(instance)

        # 验证实例被注册和初始化
        self.assertTrue(instance.initialized)
        self.assertEqual(self.manager.get_managed_instances_count(), 1)

    def test_get_lifecycle_info(self):
        """测试获取生命周期信息"""

        class TestService:
            @PostConstruct
            def init(self):
                pass

            @PreDestroy
            def cleanup(self):
                pass

        instance = TestService()

        # 获取初始信息
        info = self.manager.get_lifecycle_info(instance)
        self.assertEqual(info["class_name"], "TestService")
        self.assertTrue(info["has_lifecycle_methods"])
        self.assertIn("init", info["post_construct_methods"])
        self.assertIn("cleanup", info["pre_destroy_methods"])
        self.assertFalse(info["is_managed"])
        self.assertFalse(info["is_initialized"])
        self.assertFalse(info["is_destroyed"])

        # 注册和初始化后
        self.manager.register_instance(instance)
        self.manager.initialize_instance(instance)

        info = self.manager.get_lifecycle_info(instance)
        self.assertTrue(info["is_managed"])
        self.assertTrue(info["is_initialized"])
        self.assertFalse(info["is_destroyed"])

        # 销毁后
        self.manager.destroy_instance(instance)

        info = self.manager.get_lifecycle_info(instance)
        self.assertTrue(info["is_destroyed"])

    def test_get_lifecycle_info_none_instance(self):
        """测试获取None实例的生命周期信息"""
        info = self.manager.get_lifecycle_info(None)
        self.assertEqual(info, {})

    def test_shutdown(self):
        """测试关闭生命周期管理器"""

        class TestService:
            def __init__(self):
                self.destroyed = False

            @PostConstruct
            def init(self):
                pass

            @PreDestroy
            def cleanup(self):
                self.destroyed = True

        # 创建多个实例
        instances = [TestService() for _ in range(3)]

        # 注册所有实例
        for instance in instances:
            self.manager.register_instance(instance)

        self.assertEqual(self.manager.get_managed_instances_count(), 3)

        # 关闭管理器
        self.manager.shutdown()

        # 验证所有实例都被销毁
        for instance in instances:
            self.assertTrue(instance.destroyed)

    def test_lifecycle_methods_with_exceptions(self):
        """测试生命周期方法抛出异常的情况"""

        class TestService:
            def __init__(self):
                self.init_called = False
                self.destroy_called = False

            @PostConstruct
            def init_with_error(self):
                raise RuntimeError("Init error")

            @PostConstruct
            def init_normal(self):
                self.init_called = True

            @PreDestroy
            def cleanup_with_error(self):
                raise RuntimeError("Cleanup error")

            @PreDestroy
            def cleanup_normal(self):
                self.destroy_called = True

        instance = TestService()

        # 初始化应该处理异常并继续
        with patch("builtins.print") as mock_print:
            self.manager.initialize_instance(instance)
            mock_print.assert_called()  # 应该打印错误信息

        self.assertTrue(instance.init_called)  # 正常方法应该被调用

        # 销毁应该处理异常并继续
        with patch("builtins.print") as mock_print:
            self.manager.destroy_instance(instance)
            mock_print.assert_called()  # 应该打印错误信息

        self.assertTrue(instance.destroy_called)  # 正常方法应该被调用

    def test_global_functions(self):
        """测试全局函数"""

        class TestService:
            def __init__(self):
                self.initialized = False
                self.destroyed = False

            @PostConstruct
            def init(self):
                self.initialized = True

            @PreDestroy
            def cleanup(self):
                self.destroyed = True

        instance = TestService()

        # 测试全局函数
        register_bean_instance(instance)
        initialize_bean_instance(instance)

        self.assertTrue(instance.initialized)

        # 获取生命周期信息
        info = get_bean_lifecycle_info(instance)
        self.assertTrue(info["is_initialized"])

        # 销毁实例
        destroy_bean_instance(instance)
        self.assertTrue(instance.destroyed)

    def test_process_bean_instance_global_function(self):
        """测试全局process_bean_instance函数"""

        class TestService:
            def __init__(self):
                self.initialized = False

            @PostConstruct
            def init(self):
                self.initialized = True

        instance = TestService()

        # 使用全局函数处理实例
        process_bean_instance(instance)

        self.assertTrue(instance.initialized)

        # 验证实例被管理
        info = get_bean_lifecycle_info(instance)
        self.assertTrue(info["is_managed"])
        self.assertTrue(info["is_initialized"])

    def test_get_lifecycle_manager_global_function(self):
        """测试获取全局生命周期管理器"""
        manager = get_lifecycle_manager()
        self.assertIsInstance(manager, LifecycleManager)

        # 验证是同一个实例
        manager2 = get_lifecycle_manager()
        self.assertIs(manager, manager2)


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
"""
* @author: cz
* @description: 事件系统集成测试

测试事件基类系统与注解系统的集成，验证事件监听器注解能够正确识别事件类型。
"""

import unittest
from unittest.mock import Mock

from miniboot.annotations.event import EventListener, get_event_type
from miniboot.events import (
    ApplicationEvent,
    ApplicationStartedEvent,
    BeanCreatedEvent,
    Event,
)


class UserEvent(ApplicationEvent):
    """用户事件示例"""

    def __init__(self, user_id: str, action: str):
        super().__init__(data={"user_id": user_id, "action": action})
        self.user_id = user_id
        self.action = action


class OrderEvent(ApplicationEvent):
    """订单事件示例"""

    def __init__(self, order_id: str, status: str, amount: float):
        super().__init__(data={"order_id": order_id, "status": status, "amount": amount})
        self.order_id = order_id
        self.status = status
        self.amount = amount


class EventListenerService:
    """事件监听器服务示例"""

    def __init__(self):
        self.handled_events = []

    @EventListener
    def handle_user_event(self, event: UserEvent):
        """处理用户事件"""
        self.handled_events.append(("user", event))

    @EventListener(order=1)
    def handle_application_started(self, event: ApplicationStartedEvent):
        """处理应用启动事件"""
        self.handled_events.append(("app_started", event))

    @EventListener(condition="event.get_data('amount') > 1000")
    def handle_large_order(self, event: OrderEvent):
        """处理大额订单事件"""
        self.handled_events.append(("large_order", event))

    @EventListener(async_exec=True)
    async def handle_bean_created(self, event: BeanCreatedEvent):
        """异步处理Bean创建事件"""
        self.handled_events.append(("bean_created", event))


class TestEventIntegration(unittest.TestCase):
    """事件系统集成测试"""

    def setUp(self):
        """设置测试"""
        self.service = EventListenerService()

    def test_event_listener_type_inference(self):
        """测试事件监听器类型推断"""
        # 测试用户事件监听器
        user_handler = self.service.handle_user_event
        event_type = get_event_type(user_handler)
        self.assertEqual(event_type, UserEvent)

        # 测试应用启动事件监听器
        app_handler = self.service.handle_application_started
        event_type = get_event_type(app_handler)
        self.assertEqual(event_type, ApplicationStartedEvent)

        # 测试订单事件监听器
        order_handler = self.service.handle_large_order
        event_type = get_event_type(order_handler)
        self.assertEqual(event_type, OrderEvent)

        # 测试Bean创建事件监听器
        bean_handler = self.service.handle_bean_created
        event_type = get_event_type(bean_handler)
        self.assertEqual(event_type, BeanCreatedEvent)

    def test_custom_event_creation(self):
        """测试自定义事件创建"""
        # 创建用户事件
        user_event = UserEvent("user123", "login")

        self.assertIsInstance(user_event, ApplicationEvent)
        self.assertIsInstance(user_event, Event)
        self.assertEqual(user_event.user_id, "user123")
        self.assertEqual(user_event.action, "login")
        self.assertEqual(user_event.get_data("user_id"), "user123")
        self.assertEqual(user_event.get_data("action"), "login")
        self.assertEqual(user_event.event_type, "UserEvent")

    def test_order_event_creation(self):
        """测试订单事件创建"""
        order_event = OrderEvent("ORDER-001", "created", 1500.0)

        self.assertIsInstance(order_event, ApplicationEvent)
        self.assertEqual(order_event.order_id, "ORDER-001")
        self.assertEqual(order_event.status, "created")
        self.assertEqual(order_event.amount, 1500.0)
        self.assertEqual(order_event.get_data("order_id"), "ORDER-001")
        self.assertEqual(order_event.get_data("status"), "created")
        self.assertEqual(order_event.get_data("amount"), 1500.0)

    def test_builtin_events_with_listeners(self):
        """测试内置事件与监听器的兼容性"""
        # 创建应用启动事件
        app = Mock()
        app_event = ApplicationStartedEvent(app, startup_time=2.5)

        self.assertIsInstance(app_event, ApplicationEvent)
        self.assertEqual(app_event.source, app)
        self.assertEqual(app_event.get_data("startup_time"), 2.5)

        # 创建Bean创建事件
        bean_instance = Mock()
        bean_event = BeanCreatedEvent(bean_instance, "testBean", Mock)

        self.assertIsInstance(bean_event, ApplicationEvent)
        self.assertEqual(bean_event.source, bean_instance)
        self.assertEqual(bean_event.get_data("bean_name"), "testBean")
        self.assertEqual(bean_event.get_data("bean_type"), "Mock")

    def test_event_inheritance_hierarchy(self):
        """测试事件继承层次结构"""
        # 创建各种事件
        user_event = UserEvent("user123", "register")
        order_event = OrderEvent("ORDER-001", "paid", 500.0)
        app_event = ApplicationStartedEvent(Mock())

        # 验证继承关系
        events = [user_event, order_event, app_event]

        for event in events:
            self.assertIsInstance(event, Event)
            self.assertIsInstance(event, ApplicationEvent)
            self.assertIsNotNone(event.event_id)
            self.assertIsInstance(event.timestamp, type(event.timestamp))
            self.assertFalse(event.processed)

    def test_event_data_manipulation(self):
        """测试事件数据操作"""
        event = UserEvent("user123", "login")

        # 添加额外数据
        event.set_data("ip_address", "***********")
        event.set_data("user_agent", "Mozilla/5.0")

        # 验证数据
        self.assertEqual(event.get_data("user_id"), "user123")
        self.assertEqual(event.get_data("action"), "login")
        self.assertEqual(event.get_data("ip_address"), "***********")
        self.assertEqual(event.get_data("user_agent"), "Mozilla/5.0")

        # 更新数据
        event.update_data({"session_id": "sess_123", "login_time": "2024-01-01T10:00:00"})

        self.assertEqual(event.get_data("session_id"), "sess_123")
        self.assertEqual(event.get_data("login_time"), "2024-01-01T10:00:00")

    def test_event_processing_lifecycle(self):
        """测试事件处理生命周期"""
        event = UserEvent("user123", "logout")

        # 初始状态
        self.assertFalse(event.is_processed())

        # 模拟事件处理
        event.mark_processed()
        self.assertTrue(event.is_processed())

        # 验证事件状态在字符串表示中
        str_repr = str(event)
        self.assertIn("processed=True", str_repr)


class TestEventSystemArchitecture(unittest.TestCase):
    """事件系统架构测试"""

    def test_event_base_class_design(self):
        """测试事件基类设计"""
        # Event是抽象基类
        self.assertTrue(hasattr(Event, "__abstractmethods__"))

        # ApplicationEvent继承自Event
        self.assertTrue(issubclass(ApplicationEvent, Event))

        # 内置事件类型继承自ApplicationEvent
        self.assertTrue(issubclass(ApplicationStartedEvent, ApplicationEvent))
        self.assertTrue(issubclass(BeanCreatedEvent, ApplicationEvent))

    def test_event_type_system(self):
        """测试事件类型系统"""
        # 创建不同类型的事件
        user_event = UserEvent("user123", "action")
        order_event = OrderEvent("order123", "status", 100.0)

        # 验证事件类型
        self.assertEqual(user_event.event_type, "UserEvent")
        self.assertEqual(order_event.event_type, "OrderEvent")

        # 验证类型检查
        self.assertIsInstance(user_event, UserEvent)
        self.assertIsInstance(order_event, OrderEvent)
        self.assertNotIsInstance(user_event, OrderEvent)
        self.assertNotIsInstance(order_event, UserEvent)

    def test_event_immutability_design(self):
        """测试事件不可变性设计"""
        event = UserEvent("user123", "test")

        # 基本属性应该是只读的
        with self.assertRaises(AttributeError):
            event.event_id = "new_id"

        with self.assertRaises(AttributeError):
            event.timestamp = "new_timestamp"

        # 但是可以标记为已处理
        event.mark_processed()
        self.assertTrue(event.processed)


if __name__ == "__main__":
    unittest.main()

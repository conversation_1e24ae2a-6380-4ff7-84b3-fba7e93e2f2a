#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 注解缓存机制测试 - 验证FIX-2.1.1修复的性能优化
"""

import gc
import time
import unittest
import weakref

from miniboot.annotations.cache import AnnotationCache
from miniboot.annotations.core import Component, Service
from miniboot.annotations.injection import Autowired


class TestService:
    """测试服务类"""

    pass


@Component
class TestComponent:
    """测试组件类"""

    @Autowired
    def set_service(self, service: TestService):
        self.service = service


@Service
class TestServiceImpl:
    """测试服务实现类"""

    def __init__(self):
        self.service = None


class AnnotationCacheTestCase(unittest.TestCase):
    """注解缓存机制测试"""

    def setUp(self):
        """设置测试环境"""
        # 清理缓存
        cache = AnnotationCache()
        cache.clear_cache()

    def tearDown(self):
        """清理测试环境"""
        # 清理缓存
        cache = AnnotationCache()
        cache.clear_cache()

    def test_annotation_cache_initialization(self):
        """测试注解缓存初始化"""
        cache = AnnotationCache()

        # 验证初始状态
        stats = cache.get_cache_stats()
        self.assertEqual(stats["hits"], 0)
        self.assertEqual(stats["misses"], 0)
        self.assertEqual(stats["cache_size"], 0)

    def test_class_annotation_caching(self):
        """测试类注解缓存"""
        cache = AnnotationCache()

        # 第一次获取注解
        annotations1 = cache.get_class_annotations(TestComponent)

        # 第二次获取注解（应该从缓存返回）
        annotations2 = cache.get_class_annotations(TestComponent)

        # 验证结果一致
        self.assertEqual(annotations1, annotations2)

        # 验证缓存统计
        stats = cache.get_cache_stats()
        self.assertEqual(stats["hits"], 1)
        self.assertEqual(stats["misses"], 1)
        self.assertGreater(stats["cache_size"], 0)

    def test_method_annotation_caching(self):
        """测试方法注解缓存"""
        cache = AnnotationCache()

        # 第一次获取方法注解
        methods1 = cache.get_method_annotations(TestComponent)

        # 第二次获取方法注解（应该从缓存返回）
        methods2 = cache.get_method_annotations(TestComponent)

        # 验证结果一致
        self.assertEqual(methods1, methods2)

        # 验证有@Autowired方法
        self.assertIn("set_service", methods1)
        self.assertTrue(methods1["set_service"]["__is_autowired__"])

    def test_fast_annotation_check(self):
        """测试快速注解检查"""
        cache = AnnotationCache()

        # 检查类注解
        self.assertTrue(cache.has_annotation(TestComponent, "__is_component__"))
        self.assertTrue(cache.has_annotation(TestServiceImpl, "__is_service__"))
        self.assertFalse(cache.has_annotation(TestService, "__is_component__"))

        # 验证缓存被使用
        stats = cache.get_cache_stats()
        self.assertGreater(stats["hits"] + stats["misses"], 0)

    def test_fast_class_annotations(self):
        """测试快速类注解获取"""
        cache = AnnotationCache()
        annotations = cache.get_class_annotations(TestComponent)

        # 验证注解信息
        self.assertTrue(annotations.get("__is_component__", False))
        self.assertIn("__component_metadata__", annotations)

    def test_fast_method_annotations(self):
        """测试快速方法注解获取"""
        cache = AnnotationCache()
        methods = cache.get_method_annotations(TestComponent)

        # 验证方法注解
        self.assertIn("set_service", methods)
        self.assertTrue(methods["set_service"]["__is_autowired__"])

    def test_fast_annotated_methods(self):
        """测试快速获取有注解的方法"""
        cache = AnnotationCache()
        autowired_methods = cache.get_annotated_methods(TestComponent, "__is_autowired__")

        # 验证结果
        self.assertIn("set_service", autowired_methods)

    def test_cache_performance(self):
        """测试缓存性能"""
        cache = AnnotationCache()

        # 创建多个测试类
        test_classes = []
        for i in range(100):

            @Component
            class DynamicTestClass:
                @Autowired
                def test_method(self):
                    pass

            # 重命名类以避免冲突
            DynamicTestClass.__name__ = f"DynamicTestClass{i}"
            test_classes.append(DynamicTestClass)

        # 测量第一次访问时间（缓存未命中）
        start_time = time.time()
        for cls in test_classes:
            cache.get_class_annotations(cls)
        first_access_time = time.time() - start_time

        # 测量第二次访问时间（缓存命中）
        start_time = time.time()
        for cls in test_classes:
            cache.get_class_annotations(cls)
        second_access_time = time.time() - start_time

        # 验证缓存提升性能（允许时间相等，因为操作很快）
        self.assertLessEqual(second_access_time, first_access_time)

        # 验证缓存统计
        stats = cache.get_cache_stats()
        self.assertEqual(stats["hits"], 100)
        self.assertEqual(stats["misses"], 100)
        self.assertGreater(stats["hit_ratio"], 0.4)

    def test_weak_reference_cleanup(self):
        """测试弱引用清理"""
        cache = AnnotationCache()

        # 创建临时类
        def create_temp_class():
            @Component
            class TempClass:
                pass

            return TempClass

        temp_class = create_temp_class()
        weakref.ref(temp_class)

        # 获取注解信息
        cache.get_class_annotations(temp_class)

        # 验证缓存中有数据
        self.assertGreater(cache.get_cache_stats()["cache_size"], 0)

        # 删除类引用
        del temp_class
        gc.collect()

        # 弱引用缓存应该自动清理
        # 注意：WeakKeyDictionary的清理可能不是立即的

    def test_cache_clear(self):
        """测试缓存清理"""
        cache = AnnotationCache()

        # 添加一些数据到缓存
        cache.get_class_annotations(TestComponent)
        cache.get_method_annotations(TestComponent)

        # 验证缓存有数据
        stats_before = cache.get_cache_stats()
        self.assertGreater(stats_before["cache_size"], 0)

        # 清理缓存
        cache.clear_cache()

        # 验证缓存已清理
        stats_after = cache.get_cache_stats()
        self.assertEqual(stats_after["cache_size"], 0)
        self.assertEqual(stats_after["hits"], 0)
        self.assertEqual(stats_after["misses"], 0)

    def test_global_cache_instance(self):
        """测试全局缓存实例"""
        # 获取全局缓存实例
        cache1 = AnnotationCache()
        cache2 = AnnotationCache()

        # 验证是同一个实例
        self.assertIs(cache1, cache2)

        # 测试全局缓存清理
        cache1.get_class_annotations(TestComponent)
        self.assertGreater(cache1.get_cache_stats()["cache_size"], 0)

        cache1.clear_cache()
        self.assertEqual(cache1.get_cache_stats()["cache_size"], 0)

    def test_thread_safety(self):
        """测试线程安全"""
        import threading

        cache = AnnotationCache()
        results = []
        errors = []

        def access_cache():
            """并发访问缓存的函数"""
            try:
                for _ in range(10):
                    annotations = cache.get_class_annotations(TestComponent)
                    results.append(len(annotations))
            except Exception as e:
                errors.append(e)

        # 创建多个线程并发访问缓存
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=access_cache)
            threads.append(thread)

        # 启动所有线程
        for thread in threads:
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证没有错误
        self.assertEqual(len(errors), 0, f"并发访问出现错误: {errors}")

        # 验证所有结果一致
        if results:
            expected_length = results[0]
            for result in results:
                self.assertEqual(result, expected_length)

    def test_cache_statistics(self):
        """测试缓存统计"""
        cache = AnnotationCache()

        # 执行一些缓存操作
        cache.get_class_annotations(TestComponent)
        cache.get_class_annotations(TestServiceImpl)
        cache.get_class_annotations(TestComponent)  # 缓存命中

        # 获取统计信息
        stats = cache.get_cache_stats()

        # 验证统计信息
        self.assertEqual(stats["hits"], 1)
        self.assertEqual(stats["misses"], 2)
        self.assertAlmostEqual(stats["hit_ratio"], 1 / 3, places=2)
        self.assertGreater(stats["cache_size"], 0)
        self.assertIn("lru_cache_info", stats)

    def test_memory_efficiency(self):
        """测试内存效率"""
        cache = AnnotationCache(max_size=10)

        # 创建大量类
        classes = []
        for i in range(20):

            @Component
            class TestClass:
                pass

            TestClass.__name__ = f"TestClass{i}"
            classes.append(TestClass)

        # 访问所有类
        for cls in classes:
            cache.get_class_annotations(cls)

        # 验证弱引用缓存可能包含所有类，但LRU缓存应该有限制
        # 注意：LRU缓存的大小限制是在内部方法级别，这里我们验证缓存工作正常
        stats = cache.get_cache_stats()
        self.assertGreater(stats["cache_size"], 0)
        self.assertGreater(stats["misses"], 0)


if __name__ == "__main__":
    unittest.main()

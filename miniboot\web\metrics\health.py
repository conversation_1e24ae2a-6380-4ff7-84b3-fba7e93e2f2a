"""
Web 应用健康检查指示器

检查 Web 应用的健康状态:
- 应用状态检查
- 数据库连接检查
- 外部服务检查
- 系统资源检查
- 背压控制状态检查
- 智能调度状态检查
"""

import asyncio
import time
import psutil
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum

from loguru import logger


class HealthStatus(Enum):
    """健康状态枚举"""

    UP = "UP"
    DOWN = "DOWN"
    OUT_OF_SERVICE = "OUT_OF_SERVICE"
    UNKNOWN = "UNKNOWN"


@dataclass
class HealthDetail:
    """健康检查详情"""

    status: HealthStatus
    message: str
    details: Dict[str, Any]
    timestamp: float
    check_duration: float


class HealthCheck:
    """健康检查基类"""

    def __init__(self, name: str, timeout: float = 5.0):
        self.name = name
        self.timeout = timeout

    async def check(self) -> HealthDetail:
        """执行健康检查"""
        start_time = time.time()

        try:
            result = await asyncio.wait_for(self._do_check(), timeout=self.timeout)
            duration = time.time() - start_time

            return HealthDetail(
                status=result.get("status", HealthStatus.UNKNOWN),
                message=result.get("message", "Health check completed"),
                details=result.get("details", {}),
                timestamp=start_time,
                check_duration=duration,
            )

        except asyncio.TimeoutError:
            duration = time.time() - start_time
            return HealthDetail(
                status=HealthStatus.DOWN,
                message=f"Health check timed out after {self.timeout}s",
                details={"timeout": self.timeout},
                timestamp=start_time,
                check_duration=duration,
            )
        except Exception as e:
            duration = time.time() - start_time
            return HealthDetail(
                status=HealthStatus.DOWN,
                message=f"Health check failed: {str(e)}",
                details={"error": str(e), "error_type": type(e).__name__},
                timestamp=start_time,
                check_duration=duration,
            )

    async def _do_check(self) -> Dict[str, Any]:
        """子类需要实现的具体检查逻辑"""
        raise NotImplementedError


class SystemResourcesHealthCheck(HealthCheck):
    """系统资源健康检查"""

    def __init__(self, cpu_threshold: float = 90.0, memory_threshold: float = 90.0, disk_threshold: float = 90.0):
        super().__init__("system_resources")
        self.cpu_threshold = cpu_threshold
        self.memory_threshold = memory_threshold
        self.disk_threshold = disk_threshold

    async def _do_check(self) -> Dict[str, Any]:
        """检查系统资源使用情况"""
        # CPU 使用率
        cpu_percent = psutil.cpu_percent(interval=1)

        # 内存使用率
        memory = psutil.virtual_memory()
        memory_percent = memory.percent

        # 磁盘使用率
        disk = psutil.disk_usage("/")
        disk_percent = disk.percent

        # 判断健康状态
        issues = []
        if cpu_percent > self.cpu_threshold:
            issues.append(f"High CPU usage: {cpu_percent:.1f}%")

        if memory_percent > self.memory_threshold:
            issues.append(f"High memory usage: {memory_percent:.1f}%")

        if disk_percent > self.disk_threshold:
            issues.append(f"High disk usage: {disk_percent:.1f}%")

        status = HealthStatus.DOWN if issues else HealthStatus.UP
        message = "; ".join(issues) if issues else "System resources are healthy"

        return {
            "status": status,
            "message": message,
            "details": {
                "cpu": {"percent": cpu_percent, "threshold": self.cpu_threshold, "healthy": cpu_percent <= self.cpu_threshold},
                "memory": {
                    "percent": memory_percent,
                    "threshold": self.memory_threshold,
                    "healthy": memory_percent <= self.memory_threshold,
                    "total": memory.total,
                    "available": memory.available,
                },
                "disk": {
                    "percent": disk_percent,
                    "threshold": self.disk_threshold,
                    "healthy": disk_percent <= self.disk_threshold,
                    "total": disk.total,
                    "free": disk.free,
                },
            },
        }


class ApplicationHealthCheck(HealthCheck):
    """应用程序健康检查"""

    def __init__(self, app_instance=None):
        super().__init__("application")
        self.app_instance = app_instance

    async def _do_check(self) -> Dict[str, Any]:
        """检查应用程序状态"""
        details = {
            "startup_time": time.time(),  # 简化实现
            "version": "1.0.0",  # 可以从配置或环境变量获取
            "environment": "development",  # 可以从配置获取
        }

        # 检查应用实例状态
        if self.app_instance:
            try:
                # 这里可以添加更多应用特定的检查
                details["app_state"] = "running"
                details["routes_count"] = len(getattr(self.app_instance, "routes", []))
            except Exception as e:
                return {"status": HealthStatus.DOWN, "message": f"Application instance check failed: {str(e)}", "details": details}

        return {"status": HealthStatus.UP, "message": "Application is running normally", "details": details}


class BackpressureHealthCheck(HealthCheck):
    """背压控制健康检查"""

    def __init__(self, backpressure_metrics=None):
        super().__init__("backpressure")
        self.backpressure_metrics = backpressure_metrics

    async def _do_check(self) -> Dict[str, Any]:
        """检查背压控制状态"""
        if not self.backpressure_metrics:
            return {"status": HealthStatus.UNKNOWN, "message": "Backpressure metrics not available", "details": {}}

        try:
            summary = self.backpressure_metrics.get_metrics_summary()

            # 评估健康状态
            issues = []

            # 检查系统稳定性评分
            if summary.system_stability_score < 70:
                issues.append(f"Low stability score: {summary.system_stability_score:.1f}")

            # 检查熔断器状态
            circuit_breakers = summary.circuit_breaker_status
            open_breakers = [name for name, state in circuit_breakers.items() if state == "open"]
            if open_breakers:
                issues.append(f"Circuit breakers open: {', '.join(open_breakers)}")

            # 检查负载水平
            if summary.avg_load_level > 0.9:
                issues.append(f"High load level: {summary.avg_load_level:.2f}")

            status = HealthStatus.DOWN if issues else HealthStatus.UP
            message = "; ".join(issues) if issues else "Backpressure control is healthy"

            return {
                "status": status,
                "message": message,
                "details": {
                    "stability_score": summary.system_stability_score,
                    "load_level": summary.avg_load_level,
                    "circuit_breakers": circuit_breakers,
                    "protection_effectiveness": summary.protection_effectiveness,
                },
            }

        except Exception as e:
            return {"status": HealthStatus.DOWN, "message": f"Backpressure health check failed: {str(e)}", "details": {"error": str(e)}}


class SchedulingHealthCheck(HealthCheck):
    """智能调度健康检查"""

    def __init__(self, scheduling_metrics=None):
        super().__init__("scheduling")
        self.scheduling_metrics = scheduling_metrics

    async def _do_check(self) -> Dict[str, Any]:
        """检查智能调度状态"""
        if not self.scheduling_metrics:
            return {"status": HealthStatus.UNKNOWN, "message": "Scheduling metrics not available", "details": {}}

        try:
            summary = self.scheduling_metrics.get_metrics_summary()

            # 评估健康状态
            issues = []

            # 检查分类准确性
            if summary.classification_accuracy < 70:
                issues.append(f"Low classification accuracy: {summary.classification_accuracy:.1f}%")

            # 检查策略有效性
            if summary.strategy_effectiveness < 50:
                issues.append(f"Low strategy effectiveness: {summary.strategy_effectiveness:.1f}%")

            # 检查失败率
            if summary.total_tasks > 0:
                failure_rate = (summary.failed_tasks / summary.total_tasks) * 100
                if failure_rate > 10:
                    issues.append(f"High failure rate: {failure_rate:.1f}%")

            status = HealthStatus.DOWN if issues else HealthStatus.UP
            message = "; ".join(issues) if issues else "Intelligent scheduling is healthy"

            return {
                "status": status,
                "message": message,
                "details": {
                    "classification_accuracy": summary.classification_accuracy,
                    "strategy_effectiveness": summary.strategy_effectiveness,
                    "total_tasks": summary.total_tasks,
                    "success_rate": (summary.successful_tasks / summary.total_tasks * 100) if summary.total_tasks > 0 else 0,
                    "async_adoption_rate": summary.async_adoption_rate,
                },
            }

        except Exception as e:
            return {"status": HealthStatus.DOWN, "message": f"Scheduling health check failed: {str(e)}", "details": {"error": str(e)}}


class HealthIndicator:
    """Web 应用健康指示器"""

    def __init__(self):
        self.health_checks: Dict[str, HealthCheck] = {}
        self.last_check_results: Dict[str, HealthDetail] = {}

        # 注册默认健康检查
        self.register_health_check(SystemResourcesHealthCheck())
        self.register_health_check(ApplicationHealthCheck())

        logger.info("HealthIndicator initialized")

    def register_health_check(self, health_check: HealthCheck) -> None:
        """注册健康检查"""
        self.health_checks[health_check.name] = health_check
        logger.debug(f"Registered health check: {health_check.name}")

    def unregister_health_check(self, name: str) -> None:
        """注销健康检查"""
        if name in self.health_checks:
            del self.health_checks[name]
            if name in self.last_check_results:
                del self.last_check_results[name]
            logger.debug(f"Unregistered health check: {name}")

    async def check_health(self, check_name: str = None) -> Dict[str, HealthDetail]:
        """执行健康检查"""
        if check_name:
            # 检查特定的健康检查
            if check_name not in self.health_checks:
                raise ValueError(f"Health check '{check_name}' not found")

            result = await self.health_checks[check_name].check()
            self.last_check_results[check_name] = result
            return {check_name: result}

        # 检查所有健康检查
        results = {}
        tasks = []

        for name, health_check in self.health_checks.items():
            task = asyncio.create_task(health_check.check())
            tasks.append((name, task))

        for name, task in tasks:
            try:
                result = await task
                results[name] = result
                self.last_check_results[name] = result
            except Exception as e:
                logger.error(f"Health check {name} failed: {e}")
                results[name] = HealthDetail(
                    status=HealthStatus.DOWN,
                    message=f"Health check failed: {str(e)}",
                    details={"error": str(e)},
                    timestamp=time.time(),
                    check_duration=0.0,
                )

        return results

    def get_overall_status(self) -> HealthStatus:
        """获取整体健康状态"""
        if not self.last_check_results:
            return HealthStatus.UNKNOWN

        statuses = [result.status for result in self.last_check_results.values()]

        if any(status == HealthStatus.DOWN for status in statuses):
            return HealthStatus.DOWN
        elif any(status == HealthStatus.OUT_OF_SERVICE for status in statuses):
            return HealthStatus.OUT_OF_SERVICE
        elif any(status == HealthStatus.UNKNOWN for status in statuses):
            return HealthStatus.UNKNOWN
        else:
            return HealthStatus.UP

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        overall_status = self.get_overall_status()

        components = {}
        for name, result in self.last_check_results.items():
            components[name] = {
                "status": result.status.value,
                "message": result.message,
                "details": result.details,
                "timestamp": result.timestamp,
                "check_duration": result.check_duration,
            }

        return {"status": overall_status.value, "components": components, "timestamp": time.time()}

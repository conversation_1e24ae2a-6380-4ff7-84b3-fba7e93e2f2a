"""
Actuator Starter 配置属性类

负责从 application.yml 中读取 starters.actuator 配置，
并提供类型安全的配置访问接口。

配置映射关系：
- application.yml 中的 starters.actuator.* 配置
- 自动绑定到 ActuatorProperties 及其嵌套属性类
- 支持类型转换和默认值处理
- 支持配置验证和错误处理

使用示例：
    # 在配置类中注入
    @Bean
    @ConfigurationProperties(prefix="starters.actuator")
    def actuator_properties() -> ActuatorProperties:
        return ActuatorProperties()
"""

from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional, Union

from miniboot.annotations.config import ConfigurationProperties
from miniboot.env.environment import Environment


@dataclass
@ConfigurationProperties(prefix="miniboot.starters.actuator")
class ActuatorProperties:
    """Actuator Starter 配置属性"""

    # 基础配置
    enabled: bool = True
    auto_start: bool = True

    # 指标配置
    metrics: 'MetricsProperties' = field(default_factory=lambda: MetricsProperties())

    # Web 集成配置
    web: 'WebProperties' = field(default_factory=lambda: WebProperties())

    # 安全配置
    security: 'SecurityProperties' = field(default_factory=lambda: SecurityProperties())

    # 性能配置
    performance: 'PerformanceProperties' = field(default_factory=lambda: PerformanceProperties())

    # 全局配置
    graceful_shutdown: bool = True
    shutdown_timeout: int = 30
    debug: bool = False
    performance_monitoring: bool = True

    def validate(self) -> List[str]:
        """验证配置属性

        Returns:
            List[str]: 验证错误列表，空列表表示验证通过
        """
        errors = []

        # 验证基础配置
        if self.shutdown_timeout <= 0:
            errors.append("shutdown_timeout 必须大于 0")

        # 验证指标配置
        if self.metrics:
            # collection_interval 的格式验证由框架的转换系统自动处理
            if self.metrics.batch_size <= 0:
                errors.append("metrics.batch_size 必须大于 0")

        # 验证 Web 配置
        if self.web and self.web.enabled:
            if not ActuatorConfigUtils.validate_endpoint_path(self.web.base_path):
                errors.append(f"web.base_path 格式错误: {self.web.base_path}")

            if self.web.port is not None and (self.web.port < 1 or self.web.port > 65535):
                errors.append("web.port 必须在 1-65535 范围内")

        # 验证安全配置
        if self.security:
            errors.extend(self.security.validate())

        return errors

    def is_metrics_enabled(self) -> bool:
        """检查指标功能是否启用"""
        return self.enabled and self.metrics and self.metrics.enabled

    def is_web_enabled(self) -> bool:
        """检查 Web 功能是否启用"""
        return self.enabled and self.web and self.web.enabled

    def is_security_enabled(self) -> bool:
        """检查安全功能是否启用"""
        return self.enabled and self.security and self.security.enabled

    def get_enabled_core_modules(self) -> List[str]:
        """获取启用的核心模块列表"""
        if not self.is_metrics_enabled():
            return []

        enabled_modules = []
        core_modules = self.metrics.core_modules

        if core_modules.bean:
            enabled_modules.append("bean")
        if core_modules.scheduler:
            enabled_modules.append("scheduler")
        if core_modules.context:
            enabled_modules.append("context")
        if core_modules.env:
            enabled_modules.append("env")

        return enabled_modules

    def get_enabled_endpoints(self) -> List[str]:
        """获取启用的端点列表"""
        if not self.is_web_enabled():
            return []

        enabled_endpoints = []
        endpoints = self.web.endpoints

        if endpoints.health:
            enabled_endpoints.append("health")
        if endpoints.info:
            enabled_endpoints.append("info")
        if endpoints.metrics:
            enabled_endpoints.append("metrics")
        if endpoints.beans:
            enabled_endpoints.append("beans")
        if endpoints.env:
            enabled_endpoints.append("env")
        if endpoints.loggers:
            enabled_endpoints.append("loggers")
        if endpoints.threaddump:
            enabled_endpoints.append("threaddump")

        # 添加自定义端点
        enabled_endpoints.extend(endpoints.custom)

        return enabled_endpoints

    def get_metrics_collection_interval_seconds(self) -> float:
        """获取指标收集间隔（秒）"""
        if not self.is_metrics_enabled():
            return 0.0

        # 如果 collection_interval 是字符串，需要转换为秒数
        interval = self.metrics.collection_interval
        if isinstance(interval, str):
            # 简单的时间转换逻辑
            if interval.endswith('s'):
                return float(interval[:-1])
            elif interval.endswith('m'):
                return float(interval[:-1]) * 60
            elif interval.endswith('h'):
                return float(interval[:-1]) * 3600
            else:
                return 30.0  # 默认值
        return float(interval)

    def get_merged_common_tags(self) -> Dict[str, str]:
        """获取合并后的公共标签"""
        if not self.is_metrics_enabled() or not self.metrics.custom:
            return {}

        # 基础标签
        base_tags = {
            "application": "miniboot-app",
            "version": "1.0.0"
        }

        # 合并用户配置的公共标签
        return ActuatorConfigUtils.merge_tags(base_tags, self.metrics.custom.common_tags)

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式（用于调试和日志）"""
        return {
            "enabled": self.enabled,
            "auto_start": self.auto_start,
            "graceful_shutdown": self.graceful_shutdown,
            "shutdown_timeout": self.shutdown_timeout,
            "debug": self.debug,
            "performance_monitoring": self.performance_monitoring,
            "metrics_enabled": self.is_metrics_enabled(),
            "web_enabled": self.is_web_enabled(),
            "security_enabled": self.is_security_enabled(),
            "enabled_core_modules": self.get_enabled_core_modules(),
            "enabled_endpoints": self.get_enabled_endpoints()
        }


@dataclass
class MetricsProperties:
    """指标配置属性"""

    enabled: bool = True
    collection_interval: str = "30s"
    cache_enabled: bool = True
    async_collection: bool = True
    batch_size: int = 100

    # 核心模块指标配置
    core_modules: 'CoreModulesProperties' = field(default_factory=lambda: CoreModulesProperties())

    # 自定义指标配置
    custom: 'CustomMetricsProperties' = field(default_factory=lambda: CustomMetricsProperties())

    # 自定义收集器
    custom_collectors: List[str] = field(default_factory=list)


@dataclass
class CoreModulesProperties:
    """核心模块指标配置"""

    bean: bool = True
    scheduler: bool = True
    context: bool = True
    env: bool = True


@dataclass
class CustomMetricsProperties:
    """自定义指标配置"""

    enabled: bool = True
    auto_time_requests: bool = True
    auto_count_requests: bool = True

    # 命名配置
    naming: 'NamingProperties' = field(default_factory=lambda: NamingProperties())

    # 标签配置
    common_tags: Dict[str, str] = field(default_factory=dict)

    # 导出配置
    export: 'ExportProperties' = field(default_factory=lambda: ExportProperties())

    # 性能配置
    performance: 'PerformanceProperties' = field(default_factory=lambda: PerformanceProperties())


@dataclass
class NamingProperties:
    """指标命名配置"""

    prefix: str = "miniboot"
    separator: str = "."


@dataclass
class ExportProperties:
    """指标导出配置"""

    prometheus: 'PrometheusExportProperties' = field(default_factory=lambda: PrometheusExportProperties())
    json: 'JsonExportProperties' = field(default_factory=lambda: JsonExportProperties())


@dataclass
class PrometheusExportProperties:
    """Prometheus 导出配置"""

    enabled: bool = True
    endpoint: str = "/actuator/prometheus"


@dataclass
class JsonExportProperties:
    """JSON 导出配置"""

    enabled: bool = True
    pretty_print: bool = True


@dataclass
class PerformanceProperties:
    """性能配置"""

    collection_interval: float = 60.0
    cache_ttl: float = 30.0
    max_metrics: int = 10000
    cleanup_interval: str = "300s"


@dataclass
class WebProperties:
    """Web 集成配置"""

    enabled: bool = True
    base_path: str = "/actuator"
    port: Optional[int] = None
    cors_enabled: bool = True
    cors_origins: List[str] = field(default_factory=lambda: ["*"])

    # 端点配置
    endpoints: 'EndpointsProperties' = field(default_factory=lambda: EndpointsProperties())

    # 安全配置
    security_enabled: bool = False
    allowed_ips: List[str] = field(default_factory=list)

    # 兼容性属性
    @property
    def cors_allowed_origins(self) -> List[str]:
        """CORS 允许的来源（兼容性属性）"""
        return self.cors_origins

    @property
    def expose_all_endpoints(self) -> bool:
        """是否暴露所有端点（兼容性属性）"""
        return True  # 默认暴露所有端点


@dataclass
class EndpointsProperties:
    """端点配置"""

    health: bool = True
    info: bool = True
    metrics: bool = True
    beans: bool = True
    env: bool = True
    loggers: bool = True
    threaddump: bool = True
    custom: List[str] = field(default_factory=list)


@dataclass
class SecurityProperties:
    """安全配置"""

    enabled: bool = True
    username: str = "admin"
    password: str = "admin"
    cors_enabled: bool = False
    authentication_required: bool = False
    api_key: Optional[str] = None
    allowed_roles: List[str] = field(default_factory=list)
    allowed_origins: List[str] = field(default_factory=lambda: ["*"])
    allowed_methods: List[str] = field(default_factory=lambda: ["GET", "POST"])
    allowed_headers: List[str] = field(default_factory=lambda: ["*"])
    rate_limiting: bool = True
    max_requests_per_minute: int = 100

    # 生产环境安全配置
    require_https: bool = False
    session_timeout: int = 3600  # 会话超时时间(秒)
    max_failed_attempts: int = 5  # 最大失败尝试次数
    lockout_duration: int = 900  # 锁定持续时间(秒)

    # 敏感信息保护
    show_sensitive: bool = False  # 是否显示敏感信息
    max_sensitivity: str = "INTERNAL"  # 最大敏感级别

    def validate(self) -> List[str]:
        """验证安全配置"""
        errors = []

        if self.authentication_required and not self.api_key and not self.allowed_roles:
            errors.append("authentication_required=true 时必须配置 api_key 或 allowed_roles")

        if self.max_requests_per_minute <= 0:
            errors.append("max_requests_per_minute 必须大于 0")

        return errors


# 配置工具类
class ActuatorConfigUtils:
    """Actuator 配置工具类"""

    @staticmethod
    def validate_endpoint_path(path: str) -> bool:
        """验证端点路径格式

        Args:
            path: 端点路径

        Returns:
            bool: 是否有效
        """
        if not path:
            return False

        # 必须以 / 开头
        if not path.startswith('/'):
            return False

        # 不能包含特殊字符
        invalid_chars = ['?', '#', '&', ' ', '\t', '\n']
        return not any(char in path for char in invalid_chars)

    @staticmethod
    def merge_tags(common_tags: Dict[str, str], specific_tags: Dict[str, str]) -> Dict[str, str]:
        """合并公共标签和特定标签

        Args:
            common_tags: 公共标签
            specific_tags: 特定标签

        Returns:
            Dict[str, str]: 合并后的标签
        """
        merged = common_tags.copy()
        merged.update(specific_tags)
        return merged

    @staticmethod
    def create_from_environment(environment: Environment) -> 'ActuatorProperties':
        """从环境配置创建 ActuatorProperties

        Args:
            environment: 环境配置对象

        Returns:
            ActuatorProperties: 配置实例
        """
        # 基础配置
        enabled = environment.get_property("miniboot.starters.actuator.enabled", True)
        if enabled is None:
            enabled = True
        auto_start = environment.get_property("miniboot.starters.actuator.auto-start", True)
        if auto_start is None:
            auto_start = True

        # 指标配置
        metrics_enabled = environment.get_property("miniboot.starters.actuator.metrics.enabled", True)
        if metrics_enabled is None:
            metrics_enabled = True

        collection_interval = environment.get_property("miniboot.starters.actuator.metrics.collection-interval", "30s")
        if collection_interval is None:
            collection_interval = "30s"

        cache_enabled = environment.get_property("miniboot.starters.actuator.metrics.cache-enabled", True)
        if cache_enabled is None:
            cache_enabled = True

        async_collection = environment.get_property("miniboot.starters.actuator.metrics.async-collection", True)
        if async_collection is None:
            async_collection = True

        batch_size = environment.get_property("miniboot.starters.actuator.metrics.batch-size", 100)
        if batch_size is None:
            batch_size = 100

        metrics = MetricsProperties(
            enabled=metrics_enabled,
            collection_interval=collection_interval,
            cache_enabled=cache_enabled,
            async_collection=async_collection,
            batch_size=batch_size,

            # 核心模块配置
            core_modules=CoreModulesProperties(
                bean=environment.get_property("miniboot.starters.actuator.metrics.core-modules.bean", True),
                scheduler=environment.get_property("miniboot.starters.actuator.metrics.core-modules.scheduler", True),
                context=environment.get_property("miniboot.starters.actuator.metrics.core-modules.context", True),
                env=environment.get_property("miniboot.starters.actuator.metrics.core-modules.env", True)
            ),

            # 自定义指标配置
            custom=CustomMetricsProperties(
                enabled=environment.get_property("miniboot.starters.actuator.metrics.custom.enabled", True),
                auto_time_requests=environment.get_property("miniboot.starters.actuator.metrics.custom.auto-time-requests", True),
                auto_count_requests=environment.get_property("miniboot.starters.actuator.metrics.custom.auto-count-requests", True),

                naming=NamingProperties(
                    prefix=environment.get_property("miniboot.starters.actuator.metrics.custom.naming.prefix", "miniboot"),
                    separator=environment.get_property("miniboot.starters.actuator.metrics.custom.naming.separator", ".")
                ),

                common_tags=environment.get_property("miniboot.starters.actuator.metrics.custom.common-tags", {}),

                export=ExportProperties(
                    prometheus=PrometheusExportProperties(
                        enabled=environment.get_property("miniboot.starters.actuator.metrics.custom.export.prometheus.enabled", True),
                        endpoint=environment.get_property("miniboot.starters.actuator.metrics.custom.export.prometheus.endpoint", "/actuator/prometheus")
                    ),
                    json=JsonExportProperties(
                        enabled=environment.get_property("miniboot.starters.actuator.metrics.custom.export.json.enabled", True),
                        pretty_print=environment.get_property("miniboot.starters.actuator.metrics.custom.export.json.pretty-print", True)
                    )
                ),

                performance=PerformanceProperties(
                    collection_interval=environment.get_property("miniboot.starters.actuator.metrics.custom.performance.collection-interval", "10s"),
                    max_metrics=environment.get_property("miniboot.starters.actuator.metrics.custom.performance.max-metrics", 10000),
                    cleanup_interval=environment.get_property("miniboot.starters.actuator.metrics.custom.performance.cleanup-interval", "300s")
                )
            ),

            custom_collectors=environment.get_property("miniboot.starters.actuator.metrics.custom-collectors", [])
        )

        # Web 配置
        web_enabled = environment.get_property("miniboot.starters.actuator.web.enabled", True)
        if web_enabled is None:
            web_enabled = True

        web_base_path = environment.get_property("miniboot.starters.actuator.web.base-path", "/actuator")
        if web_base_path is None:
            web_base_path = "/actuator"

        web_port = environment.get_property("miniboot.starters.actuator.web.port", None)

        cors_enabled = environment.get_property("miniboot.starters.actuator.web.cors-enabled", True)
        if cors_enabled is None:
            cors_enabled = True

        cors_origins = environment.get_property("miniboot.starters.actuator.web.cors-origins", ["*"])
        if cors_origins is None:
            cors_origins = ["*"]

        web = WebProperties(
            enabled=web_enabled,
            base_path=web_base_path,
            port=web_port,
            cors_enabled=cors_enabled,
            cors_origins=cors_origins,

            endpoints=EndpointsProperties(
                health=environment.get_property("miniboot.starters.actuator.web.endpoints.health", True),
                info=environment.get_property("miniboot.starters.actuator.web.endpoints.info", True),
                metrics=environment.get_property("miniboot.starters.actuator.web.endpoints.metrics", True),
                beans=environment.get_property("miniboot.starters.actuator.web.endpoints.beans", False),
                env=environment.get_property("miniboot.starters.actuator.web.endpoints.env", False),
                loggers=environment.get_property("miniboot.starters.actuator.web.endpoints.loggers", False),
                threaddump=environment.get_property("miniboot.starters.actuator.web.endpoints.threaddump", False),
                custom=environment.get_property("miniboot.starters.actuator.web.endpoints.custom", [])
            ),

            security_enabled=environment.get_property("miniboot.starters.actuator.web.security-enabled", False),
            allowed_ips=environment.get_property("miniboot.starters.actuator.web.allowed-ips", [])
        )

        # 安全配置
        security = SecurityProperties(
            enabled=environment.get_property("miniboot.starters.actuator.security.enabled", False),
            authentication_required=environment.get_property("miniboot.starters.actuator.security.authentication-required", False),
            api_key=environment.get_property("miniboot.starters.actuator.security.api-key", None),
            allowed_roles=environment.get_property("miniboot.starters.actuator.security.allowed-roles", []),
            rate_limiting=environment.get_property("miniboot.starters.actuator.security.rate-limiting", True),
            max_requests_per_minute=environment.get_property("miniboot.starters.actuator.security.max-requests-per-minute", 100)
        )

        # 性能配置
        cache_ttl = environment.get_property("miniboot.starters.actuator.performance.cache-ttl", 30.0)
        collection_interval = environment.get_property("miniboot.starters.actuator.performance.collection-interval", 60.0)

        # 确保类型转换
        if isinstance(cache_ttl, str):
            cache_ttl = float(cache_ttl)
        if isinstance(collection_interval, str):
            collection_interval = float(collection_interval)

        performance = PerformanceProperties(
            cache_ttl=cache_ttl,
            collection_interval=collection_interval
        )

        # 全局配置
        graceful_shutdown = environment.get_property("miniboot.starters.actuator.graceful-shutdown", True)
        shutdown_timeout = environment.get_property("miniboot.starters.actuator.shutdown-timeout", 30)
        debug = environment.get_property("miniboot.starters.actuator.debug", False)
        performance_monitoring = environment.get_property("miniboot.starters.actuator.performance-monitoring", True)

        return ActuatorProperties(
            enabled=enabled,
            auto_start=auto_start,
            metrics=metrics,
            web=web,
            security=security,
            performance=performance,
            graceful_shutdown=graceful_shutdown,
            shutdown_timeout=shutdown_timeout,
            debug=debug,
            performance_monitoring=performance_monitoring
        )

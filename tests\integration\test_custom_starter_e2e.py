#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 用户自定义Starter端到端集成测试
"""

import os
import shutil
import tempfile
from dataclasses import dataclass
from unittest.mock import Mock

import pytest

from miniboot.annotations import Bean, ConfigurationProperties
from miniboot.autoconfigure.discovery import AutoConfigurationLoader
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.autoconfigure.properties import StarterProperties
from miniboot.autoconfigure.starter import StarterAutoConfiguration
from miniboot.context import ApplicationContext


@ConfigurationProperties(prefix="custom.email")
@dataclass
class EmailProperties(StarterProperties):
    """邮件服务配置属性"""

    smtp_host: str = "localhost"
    smtp_port: int = 587
    username: str = ""
    password: str = ""
    use_tls: bool = True
    timeout: int = 30


class EmailService:
    """邮件服务"""

    def __init__(self, properties: EmailProperties):
        self.properties = properties
        self.connected = False

    def connect(self):
        """连接邮件服务器"""
        # 模拟连接逻辑
        if self.properties.smtp_host and self.properties.smtp_port:
            self.connected = True
            return True
        return False

    def send_email(self, to: str, subject: str, body: str):
        """发送邮件"""
        if not self.connected:
            raise RuntimeError("Email service not connected")

        return {
            "to": to,
            "subject": subject,
            "body": body,
            "smtp_host": self.properties.smtp_host,
            "smtp_port": self.properties.smtp_port,
            "sent": True,
        }

    def get_status(self):
        """获取服务状态"""
        return {
            "connected": self.connected,
            "smtp_host": self.properties.smtp_host,
            "smtp_port": self.properties.smtp_port,
            "use_tls": self.properties.use_tls,
            "timeout": self.properties.timeout,
        }


class EmailAutoConfiguration(StarterAutoConfiguration):
    """邮件服务自动配置"""

    def get_metadata(self) -> AutoConfigurationMetadata:
        return AutoConfigurationMetadata(
            name="email-auto-configuration", description="Email service auto configuration", priority=100, conditions=["custom.email.enabled=true"]
        )

    def get_starter_name(self) -> str:
        return "custom-email-starter"

    def get_starter_version(self) -> str:
        return "1.0.0"

    def get_starter_description(self) -> str:
        return "Custom email service starter for sending emails"

    def get_configuration_properties_classes(self) -> list[type]:
        return [EmailProperties]

    @Bean
    def email_service(self, email_properties: EmailProperties) -> EmailService:
        """创建邮件服务Bean"""
        service = EmailService(email_properties)
        if email_properties.enabled:
            service.connect()
        return service

    @Bean
    def email_template_engine(self, email_properties: EmailProperties) -> dict:
        """创建邮件模板引擎Bean"""
        return {"type": "simple_template", "enabled": email_properties.enabled, "default_sender": email_properties.username}


@ConfigurationProperties(prefix="custom.cache")
@dataclass
class CacheProperties(StarterProperties):
    """缓存服务配置属性"""

    cache_type: str = "memory"
    max_size: int = 1000
    ttl_seconds: int = 3600
    cleanup_interval: int = 300


class CacheService:
    """缓存服务"""

    def __init__(self, properties: CacheProperties):
        self.properties = properties
        self._cache = {}
        self._initialized = False

    def initialize(self):
        """初始化缓存服务"""
        self._initialized = True
        return f"Cache service initialized with type: {self.properties.cache_type}"

    def put(self, key: str, value: any):
        """存储缓存"""
        if not self._initialized:
            raise RuntimeError("Cache service not initialized")

        if len(self._cache) >= self.properties.max_size:
            # 简单的LRU清理
            oldest_key = next(iter(self._cache))
            del self._cache[oldest_key]

        self._cache[key] = value

    def get(self, key: str):
        """获取缓存"""
        if not self._initialized:
            raise RuntimeError("Cache service not initialized")

        return self._cache.get(key)

    def clear(self):
        """清空缓存"""
        self._cache.clear()

    def get_stats(self):
        """获取缓存统计"""
        return {
            "cache_type": self.properties.cache_type,
            "max_size": self.properties.max_size,
            "current_size": len(self._cache),
            "ttl_seconds": self.properties.ttl_seconds,
            "initialized": self._initialized,
        }


class CacheAutoConfiguration(StarterAutoConfiguration):
    """缓存服务自动配置"""

    def get_metadata(self) -> AutoConfigurationMetadata:
        return AutoConfigurationMetadata(
            name="cache-auto-configuration",
            description="Cache service auto configuration",
            priority=90,  # 比邮件服务优先级高
            conditions=["custom.cache.enabled=true"],
        )

    def get_starter_name(self) -> str:
        return "custom-cache-starter"

    def get_starter_version(self) -> str:
        return "1.0.0"

    def get_starter_description(self) -> str:
        return "Custom cache service starter for data caching"

    def get_configuration_properties_classes(self) -> list[type]:
        return [CacheProperties]

    @Bean
    def cache_service(self, cache_properties: CacheProperties) -> CacheService:
        """创建缓存服务Bean"""
        service = CacheService(cache_properties)
        if cache_properties.enabled:
            service.initialize()
        return service

    @Bean
    def cache_manager(self, cache_service: CacheService) -> dict:
        """创建缓存管理器Bean"""
        return {"service": cache_service, "type": "simple_manager", "auto_cleanup": True}


class TestCustomStarterE2E:
    """用户自定义Starter端到端测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)
        self.context.get_environment.return_value = Mock()
        self.context.get_bean_factory.return_value = Mock()
        self.context.contains_bean.return_value = False
        self.context.is_running.return_value = True

        # 创建临时目录用于测试
        self.temp_dir = tempfile.mkdtemp()

    def teardown_method(self):
        """测试后置清理"""
        if os.path.exists(self.temp_dir):
            shutil.rmtree(self.temp_dir)

    def test_email_starter_development_flow(self):
        """测试邮件Starter开发流程"""
        # 1. 创建配置属性
        email_props = EmailProperties()
        email_props.enabled = True
        email_props.smtp_host = "smtp.example.com"
        email_props.smtp_port = 587
        email_props.username = "<EMAIL>"
        email_props.password = "password"

        # 验证配置属性
        assert email_props.smtp_host == "smtp.example.com"
        assert email_props.smtp_port == 587
        assert email_props.enabled is True

        # 2. 创建服务类
        email_service = EmailService(email_props)

        # 验证服务功能
        assert email_service.connect() is True
        assert email_service.connected is True

        email_result = email_service.send_email("<EMAIL>", "Test Subject", "Test Body")

        assert email_result["sent"] is True
        assert email_result["to"] == "<EMAIL>"
        assert email_result["subject"] == "Test Subject"

        # 3. 创建自动配置
        email_config = EmailAutoConfiguration()

        # 验证配置元数据
        metadata = email_config.get_metadata()
        assert metadata.name == "email-auto-configuration"
        assert metadata.description == "Email service auto configuration"
        assert metadata.priority == 100

        # 验证Starter信息
        assert email_config.get_starter_name() == "custom-email-starter"
        assert email_config.get_starter_version() == "1.0.0"
        assert EmailProperties in email_config.get_configuration_properties_classes()

    def test_cache_starter_development_flow(self):
        """测试缓存Starter开发流程"""
        # 1. 创建配置属性
        cache_props = CacheProperties()
        cache_props.enabled = True
        cache_props.cache_type = "redis"
        cache_props.max_size = 5000
        cache_props.ttl_seconds = 7200

        # 验证配置属性
        assert cache_props.cache_type == "redis"
        assert cache_props.max_size == 5000
        assert cache_props.ttl_seconds == 7200

        # 2. 创建服务类
        cache_service = CacheService(cache_props)

        # 验证服务功能
        init_result = cache_service.initialize()
        assert "redis" in init_result

        cache_service.put("key1", "value1")
        cache_service.put("key2", "value2")

        assert cache_service.get("key1") == "value1"
        assert cache_service.get("key2") == "value2"
        assert cache_service.get("nonexistent") is None

        stats = cache_service.get_stats()
        assert stats["current_size"] == 2
        assert stats["cache_type"] == "redis"
        assert stats["initialized"] is True

        # 3. 创建自动配置
        cache_config = CacheAutoConfiguration()

        # 验证配置元数据
        metadata = cache_config.get_metadata()
        assert metadata.name == "cache-auto-configuration"
        assert metadata.description == "Cache service auto configuration"
        assert metadata.priority == 90

        # 验证Starter信息
        assert cache_config.get_starter_name() == "custom-cache-starter"
        assert cache_config.get_starter_version() == "1.0.0"
        assert CacheProperties in cache_config.get_configuration_properties_classes()

    def test_multiple_custom_starters_integration(self):
        """测试多个自定义Starter集成"""
        # 模拟环境配置
        env_mock = Mock()
        env_configs = {
            "custom.email": {"enabled": "true", "smtp_host": "smtp.test.com", "smtp_port": "587", "username": "<EMAIL>"},
            "custom.cache": {"enabled": "true", "cache_type": "memory", "max_size": "2000", "ttl_seconds": "1800"},
        }

        def get_properties_with_prefix(prefix):
            return env_configs.get(prefix, {})

        env_mock.get_properties_with_prefix.side_effect = get_properties_with_prefix
        self.context.get_environment.return_value = env_mock

        # 模拟Bean工厂
        bean_factory_mock = Mock()
        registered_beans = {}

        def register_singleton(name, instance):
            registered_beans[name] = instance

        bean_factory_mock.register_singleton.side_effect = register_singleton
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 创建并配置多个Starter
        email_config = EmailAutoConfiguration()
        cache_config = CacheAutoConfiguration()

        # 执行配置
        email_config.configure(self.context)
        cache_config.configure(self.context)

        # 验证Bean注册
        assert len(registered_beans) >= 2  # 至少2个配置属性

        # 验证邮件配置属性
        email_props = None
        cache_props = None

        for _name, instance in registered_beans.items():
            if isinstance(instance, EmailProperties):
                email_props = instance
            elif isinstance(instance, CacheProperties):
                cache_props = instance

        assert email_props is not None
        assert email_props.smtp_host == "smtp.test.com"
        assert email_props.smtp_port == 587
        assert email_props.username == "<EMAIL>"

        assert cache_props is not None
        assert cache_props.cache_type == "memory"
        assert cache_props.max_size == 2000
        assert cache_props.ttl_seconds == 1800

    def test_custom_starter_factories_file_creation(self):
        """测试自定义Starter的factories文件创建"""
        # 创建临时factories文件
        factories_content = """
# Custom Email Starter
miniboot.autoconfigure.EnableAutoConfiguration=\\
tests.integration.test_custom_starter_e2e.EmailAutoConfiguration

# Custom Cache Starter
miniboot.autoconfigure.EnableAutoConfiguration=\\
tests.integration.test_custom_starter_e2e.CacheAutoConfiguration
        """

        factories_file = os.path.join(self.temp_dir, "mini.factories")
        with open(factories_file, "w", encoding="utf-8") as f:
            f.write(factories_content)

        # 使用AutoConfigurationLoader加载
        loader = AutoConfigurationLoader()

        # 从factories文件加载配置
        count = loader.load_from_config_file(factories_file)

        # 验证加载的配置数量
        assert count >= 0  # 至少不会出错

        # 验证注册表中的配置
        registry = loader.get_registry()
        configs = registry.get_all_configurations()

        # 如果成功加载，验证配置名称
        if count > 0:
            assert len(configs) >= 1

    def test_custom_starter_error_handling(self):
        """测试自定义Starter错误处理"""

        # 创建会产生错误的配置
        class ErrorEmailConfig(EmailAutoConfiguration):
            @Bean
            def email_service(self, email_properties: EmailProperties) -> EmailService:
                raise RuntimeError("Failed to create email service")

        error_config = ErrorEmailConfig()

        # 模拟环境
        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {"enabled": "true"}
        self.context.get_environment.return_value = env_mock

        # 模拟Bean工厂
        bean_factory_mock = Mock()
        bean_factory_mock.register_singleton.side_effect = RuntimeError("Bean creation failed")
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 验证错误处理
        with pytest.raises(RuntimeError):
            error_config.configure(self.context)

    def test_custom_starter_conditional_loading(self):
        """测试自定义Starter条件加载"""
        # 测试启用条件
        email_config = EmailAutoConfiguration()
        metadata = email_config.get_metadata()

        # 验证条件配置
        assert "custom.email.enabled=true" in metadata.conditions

        # 模拟禁用环境
        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {"enabled": "false"}
        self.context.get_environment.return_value = env_mock

        bean_factory_mock = Mock()
        registered_beans = {}

        def register_singleton(name, instance):
            registered_beans[name] = instance

        bean_factory_mock.register_singleton.side_effect = register_singleton
        self.context.get_bean_factory.return_value = bean_factory_mock

        # 执行配置
        email_config.configure(self.context)

        # 验证禁用状态下的行为
        email_props = None
        for _name, instance in registered_beans.items():
            if isinstance(instance, EmailProperties):
                email_props = instance
                break

        if email_props:
            # 注意：字符串 "false" 需要转换为布尔值
            assert email_props.enabled is False or email_props.enabled == "false"


if __name__ == "__main__":
    pytest.main([__file__, "-v"])

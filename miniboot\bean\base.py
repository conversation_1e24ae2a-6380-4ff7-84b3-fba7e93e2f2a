#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean模块基础接口和抽象类定义
"""

from abc import ABC, abstractmethod
from typing import Any, Optional, TypeVar

T = TypeVar('T')


class BeanFactory(ABC):
    """Bean工厂基础接口

    定义Bean工厂的核心功能，所有Bean工厂实现都应该继承此接口。
    提供Bean的获取、查询和管理功能。
    """

    @abstractmethod
    def get(self, name: str, required_type: Optional[type] = None) -> Any:
        """获取Bean实例

        根据Bean名称获取Bean实例，支持类型检查。
        框架会自动处理Bean的创建、依赖注入和生命周期管理。

        Args:
            name: Bean名称，必须是已注册的Bean名称
            required_type: 期望的Bean类型，用于类型检查（可选）

        Returns:
            Bean实例或代理对象，具体类型取决于Bean定义和环境

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
            BeanCreationError: 如果Bean创建失败
            TypeError: 如果Bean类型不匹配required_type
        """
        pass

    @abstractmethod
    def contains(self, name: str) -> bool:
        """检查Bean是否存在

        检查指定名称的Bean是否已注册并可用。

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean存在返回True，否则返回False
        """
        pass

    def names(self) -> list[str]:
        """获取所有Bean名称

        返回当前工厂中所有已注册的Bean名称列表。

        Returns:
            list[str]: Bean名称列表，如果没有Bean则返回空列表
        """
        return []

    def singleton(self, name: str) -> bool:
        """检查Bean是否为单例

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean是单例返回True，否则返回False

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
        """
        return True

    def type(self, name: str) -> Optional[type]:
        """获取Bean的类型

        Args:
            name: Bean名称

        Returns:
            Optional[type]: Bean的类型，如果无法确定则返回None

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
        """
        return None


class BeanDefinitionRegistry(ABC):
    """Bean定义注册表接口

    管理Bean定义的注册、获取和查询功能。
    Bean定义包含了创建Bean实例所需的所有信息。
    """

    @abstractmethod
    def register(self, name: str, bean_definition: "BeanDefinition") -> None:
        """注册Bean定义

        将Bean定义注册到注册表中，如果名称已存在则覆盖原有定义。

        Args:
            name: Bean名称，必须唯一
            bean_definition: Bean定义对象，包含Bean的创建信息

        Raises:
            BeanDefinitionError: 如果Bean定义无效
            IllegalArgumentError: 如果参数无效
        """
        pass

    @abstractmethod
    def get_definition(self, name: str) -> "BeanDefinition":
        """获取Bean定义

        根据Bean名称获取对应的Bean定义。

        Args:
            name: Bean名称

        Returns:
            BeanDefinition: Bean定义对象

        Raises:
            NoSuchBeanDefinitionError: 如果Bean定义不存在
        """
        pass

    @abstractmethod
    def has_definition(self, name: str) -> bool:
        """检查Bean定义是否存在

        检查指定名称的Bean定义是否已注册。

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean定义存在返回True，否则返回False
        """
        pass

    @abstractmethod
    def names(self) -> list[str]:
        """获取所有Bean定义名称

        返回注册表中所有Bean定义的名称列表。

        Returns:
            list[str]: Bean定义名称列表，如果没有定义则返回空列表
        """
        pass

    def remove(self, name: str) -> None:
        """移除Bean定义

        从注册表中移除指定的Bean定义。

        Args:
            name: Bean名称

        Raises:
            NoSuchBeanDefinitionError: 如果Bean定义不存在
        """
        pass

    def count(self) -> int:
        """获取Bean定义数量

        Returns:
            int: Bean定义的总数量
        """
        return len(self.names())


class BeanPostProcessor(ABC):
    """Bean后置处理器接口

    在Bean初始化前后执行自定义处理逻辑。
    可用于实现AOP、属性注入、代理创建等功能。
    """

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """Bean初始化前处理

        在Bean的初始化方法（如@PostConstruct）调用之前执行。
        可以修改Bean实例或返回代理对象。

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            Any: 处理后的Bean实例，可以是原实例或代理对象

        Raises:
            BeanCreationError: 如果处理过程中发生错误
        """
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """Bean初始化后处理

        在Bean的初始化方法（如@PostConstruct）调用之后执行。
        这是创建代理对象的最佳时机。

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            Any: 处理后的Bean实例，可以是原实例或代理对象

        Raises:
            BeanCreationError: 如果处理过程中发生错误
        """
        return bean


class InitializingBean(ABC):
    """初始化Bean接口

    实现此接口的Bean会在属性设置完成后自动调用after_properties_set方法。
    这是Bean自定义初始化逻辑的标准方式。
    """

    @abstractmethod
    def after_properties_set(self) -> None:
        """属性设置完成后的初始化方法

        在所有属性注入完成后调用，用于执行Bean的初始化逻辑。
        此方法在@PostConstruct注解的方法之前调用。

        Raises:
            BeanInitializationError: 如果初始化过程中发生错误
        """
        pass


class DisposableBean(ABC):
    """可销毁Bean接口

    实现此接口的Bean会在容器关闭时自动调用destroy方法。
    用于释放资源、关闭连接等清理工作。
    """

    @abstractmethod
    def destroy(self) -> None:
        """销毁方法

        在Bean销毁时调用，用于执行清理工作。
        此方法在@PreDestroy注解的方法之后调用。

        Raises:
            Exception: 销毁过程中可能抛出的异常
        """
        pass


class BeanNameAware(ABC):
    """Bean名称感知接口

    实现此接口的Bean会在初始化时自动注入Bean名称。
    Bean可以通过此接口获知自己在容器中的名称。
    """

    @abstractmethod
    def set_bean_name(self, name: str) -> None:
        """设置Bean名称

        在Bean初始化过程中由容器调用，传入Bean的名称。

        Args:
            name: Bean在容器中的名称
        """
        pass


class BeanFactoryAware(ABC):
    """Bean工厂感知接口

    实现此接口的Bean会在初始化时自动注入Bean工厂。
    Bean可以通过此接口获取其他Bean或执行高级操作。
    """

    @abstractmethod
    def set_bean_factory(self, bean_factory: BeanFactory) -> None:
        """设置Bean工厂

        在Bean初始化过程中由容器调用，传入Bean工厂实例。

        Args:
            bean_factory: Bean工厂实例
        """
        pass


class ApplicationContextAware(ABC):
    """应用上下文感知接口

    实现此接口的Bean会在初始化时自动注入应用上下文。
    Bean可以通过此接口访问应用上下文的所有功能。
    """

    @abstractmethod
    def set_application_context(self, application_context: "ApplicationContext") -> None:
        """设置应用上下文

        在Bean初始化过程中由容器调用，传入应用上下文实例。

        Args:
            application_context: 应用上下文实例
        """
        pass


class Lifecycle(ABC):
    """生命周期接口

    定义Bean的启动和停止方法。
    实现此接口的Bean可以参与容器的生命周期管理。
    """

    @abstractmethod
    def start(self) -> None:
        """启动方法

        在容器启动时调用，用于启动Bean的相关服务。

        Raises:
            Exception: 启动过程中可能抛出的异常
        """
        pass

    @abstractmethod
    def stop(self) -> None:
        """停止方法

        在容器停止时调用，用于停止Bean的相关服务。

        Raises:
            Exception: 停止过程中可能抛出的异常
        """
        pass

    @abstractmethod
    def is_running(self) -> bool:
        """检查是否正在运行

        返回Bean的运行状态。

        Returns:
            bool: 如果正在运行返回True，否则返回False
        """
        pass


class SmartLifecycle(Lifecycle):
    """智能生命周期接口

    扩展Lifecycle接口，提供更精细的生命周期控制。
    支持自动启动和阶段性启动。
    """

    def is_auto_startup(self) -> bool:
        """是否自动启动

        Returns:
            bool: 如果需要自动启动返回True，否则返回False
        """
        return True

    def get_phase(self) -> int:
        """获取启动阶段

        返回Bean的启动阶段，数值越小越早启动。

        Returns:
            int: 启动阶段，默认为0
        """
        return 0

    def stop(self, callback: Optional[callable] = None) -> None:
        """异步停止方法

        支持异步停止，完成后调用回调函数。

        Args:
            callback: 停止完成后的回调函数（可选）
        """
        super().stop()
        if callback:
            callback()

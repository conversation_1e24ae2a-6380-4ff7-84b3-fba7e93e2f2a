#!/usr/bin/env python
"""
熔断器模块

提供故障隔离和快速失败机制,防止级联故障和系统雪崩.

主要功能:
- 多状态熔断器 (CLOSED、OPEN、HALF_OPEN)
- 可配置的失败阈值和恢复机制
- 自动故障检测和恢复
- 实时状态监控和指标收集
- 支持自定义失败判断逻辑
"""

import asyncio
import time
from dataclasses import dataclass, field
from enum import Enum
from threading import Lock
from typing import Any, Callable, Dict, List, Optional

from loguru import logger

from ..properties import BackpressureConfig


class CircuitBreakerState(Enum):
    """熔断器状态"""

    CLOSED = "closed"  # 关闭状态,正常处理请求
    OPEN = "open"  # 开启状态,拒绝所有请求
    HALF_OPEN = "half_open"  # 半开状态,允许少量请求测试服务恢复


@dataclass
class CircuitBreakerMetrics:
    """熔断器指标"""

    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    rejected_requests: int = 0
    success_rate: float = 100.0
    failure_rate: float = 0.0
    avg_response_time: float = 0.0
    last_failure_time: Optional[float] = None
    last_success_time: Optional[float] = None
    state_change_count: int = 0
    time_in_open_state: float = 0.0


@dataclass
class CircuitBreakerConfig:
    """熔断器配置"""

    failure_threshold: int = 5  # 失败阈值
    success_threshold: int = 3  # 恢复阈值(半开状态下)
    timeout: float = 60.0  # 超时时间(秒)
    recovery_timeout: float = 30.0  # 恢复超时时间(秒)
    failure_rate_threshold: float = 50.0  # 失败率阈值(百分比)
    min_requests: int = 10  # 最小请求数(用于计算失败率)
    half_open_max_requests: int = 5  # 半开状态最大请求数


class CircuitBreakerError(Exception):
    """熔断器异常"""

    def __init__(self, message: str, state: CircuitBreakerState):
        super().__init__(message)
        self.state = state


class CircuitBreaker:
    """熔断器
    实现三状态熔断器模式,提供故障隔离和快速失败机制.
    支持可配置的失败阈值、恢复机制和自定义失败判断逻辑."""

    def __init__(self, name: str, config: Optional[CircuitBreakerConfig] = None, backpressure_config: Optional[BackpressureConfig] = None):
        """初始化熔断器

        Args:
            name: 熔断器名称
            config: 熔断器配置
            backpressure_config: 背压控制配置
        """
        self.name = name
        self.config = config or CircuitBreakerConfig()
        self.backpressure_config = backpressure_config

        # 状态管理
        self._state = CircuitBreakerState.CLOSED
        self._last_failure_time: Optional[float] = None
        self._last_state_change_time = time.time()

        # 计数器
        self._failure_count = 0
        self._success_count = 0
        self._half_open_requests = 0

        # 指标收集
        self._metrics = CircuitBreakerMetrics()
        self._response_times: List[float] = []

        # 线程安全锁
        self._lock = Lock()

        # 回调函数
        self._state_change_callbacks: List[Callable[[CircuitBreakerState, CircuitBreakerState], None]] = []

        logger.info(f"CircuitBreaker '{name}' initialized with config: {self.config}")

    async def call(self, func: Callable, *args, **kwargs) -> Any:
        """执行函数调用,带熔断保护

        Args:
            func: 要执行的函数
            *args: 函数参数
            **kwargs: 函数关键字参数

        Returns:
            函数执行结果

        Raises:
            CircuitBreakerError: 熔断器开启时抛出
        """
        # 检查熔断器状态
        if not self._can_execute():
            self._record_rejected_request()
            raise CircuitBreakerError(f"Circuit breaker '{self.name}' is {self._state.value}", self._state)

        start_time = time.time()

        try:
            # 执行函数
            if asyncio.iscoroutinefunction(func):
                result = await func(*args, **kwargs)
            else:
                result = func(*args, **kwargs)

            # 记录成功
            response_time = time.time() - start_time
            self._record_success(response_time)

            return result

        except Exception as e:
            # 记录失败
            response_time = time.time() - start_time
            self._record_failure(response_time, e)
            raise

    def _can_execute(self) -> bool:
        """检查是否可以执行请求"""
        with self._lock:
            current_time = time.time()

            if self._state == CircuitBreakerState.CLOSED:
                return True

            elif self._state == CircuitBreakerState.OPEN:
                # 检查是否可以转换到半开状态
                if self._last_failure_time and current_time - self._last_failure_time >= self.config.recovery_timeout:
                    self._transition_to_half_open()
                    return True
                return False

            elif self._state == CircuitBreakerState.HALF_OPEN:
                # 半开状态下限制请求数量
                if self._half_open_requests < self.config.half_open_max_requests:
                    self._half_open_requests += 1
                    return True
                return False

            return False

    def _record_success(self, response_time: float) -> None:
        """记录成功请求"""
        with self._lock:
            self._success_count += 1
            self._metrics.total_requests += 1
            self._metrics.successful_requests += 1
            self._metrics.last_success_time = time.time()

            # 记录响应时间
            self._response_times.append(response_time)
            if len(self._response_times) > 100:  # 保留最近100个响应时间
                self._response_times.pop(0)

            # 更新平均响应时间
            self._metrics.avg_response_time = sum(self._response_times) / len(self._response_times)

            # 更新成功率
            self._update_rates()

            # 状态转换逻辑
            if self._state == CircuitBreakerState.HALF_OPEN:
                if self._success_count >= self.config.success_threshold:
                    self._transition_to_closed()

            logger.debug(f"CircuitBreaker '{self.name}' recorded success, response_time: {response_time:.3f}s")

    def _record_failure(self, response_time: float, error: Exception) -> None:
        """记录失败请求"""
        with self._lock:
            self._failure_count += 1
            self._metrics.total_requests += 1
            self._metrics.failed_requests += 1
            self._metrics.last_failure_time = time.time()
            self._last_failure_time = self._metrics.last_failure_time

            # 记录响应时间
            self._response_times.append(response_time)
            if len(self._response_times) > 100:
                self._response_times.pop(0)

            # 更新平均响应时间
            self._metrics.avg_response_time = sum(self._response_times) / len(self._response_times)

            # 更新失败率
            self._update_rates()

            # 状态转换逻辑
            if self._state == CircuitBreakerState.CLOSED:
                if self._should_open():
                    self._transition_to_open()
            elif self._state == CircuitBreakerState.HALF_OPEN:
                # 半开状态下任何失败都会回到开启状态
                self._transition_to_open()

            logger.warning(f"CircuitBreaker '{self.name}' recorded failure: {error}, response_time: {response_time:.3f}s")

    def _record_rejected_request(self) -> None:
        """记录被拒绝的请求"""
        with self._lock:
            self._metrics.rejected_requests += 1
            logger.debug(f"CircuitBreaker '{self.name}' rejected request, state: {self._state.value}")

    def _should_open(self) -> bool:
        """判断是否应该开启熔断器"""
        # 基于失败次数的判断
        if self._failure_count >= self.config.failure_threshold:
            return True

        # 基于失败率的判断
        if self._metrics.total_requests >= self.config.min_requests and self._metrics.failure_rate >= self.config.failure_rate_threshold:
            return True

        return False

    def _transition_to_open(self) -> None:
        """转换到开启状态"""
        old_state = self._state
        self._state = CircuitBreakerState.OPEN
        self._last_state_change_time = time.time()
        self._metrics.state_change_count += 1

        logger.warning(f"CircuitBreaker '{self.name}' transitioned from {old_state.value} to {self._state.value}")
        self._notify_state_change(old_state, self._state)

    def _transition_to_half_open(self) -> None:
        """转换到半开状态"""
        old_state = self._state
        self._state = CircuitBreakerState.HALF_OPEN
        self._last_state_change_time = time.time()
        self._half_open_requests = 0
        self._success_count = 0
        self._failure_count = 0
        self._metrics.state_change_count += 1

        logger.info(f"CircuitBreaker '{self.name}' transitioned from {old_state.value} to {self._state.value}")
        self._notify_state_change(old_state, self._state)

    def _transition_to_closed(self) -> None:
        """转换到关闭状态"""
        old_state = self._state
        self._state = CircuitBreakerState.CLOSED
        self._last_state_change_time = time.time()
        self._failure_count = 0
        self._success_count = 0
        self._half_open_requests = 0
        self._metrics.state_change_count += 1

        logger.info(f"CircuitBreaker '{self.name}' transitioned from {old_state.value} to {self._state.value}")
        self._notify_state_change(old_state, self._state)

    def _update_rates(self) -> None:
        """更新成功率和失败率"""
        if self._metrics.total_requests > 0:
            self._metrics.success_rate = (self._metrics.successful_requests / self._metrics.total_requests) * 100
            self._metrics.failure_rate = (self._metrics.failed_requests / self._metrics.total_requests) * 100

    def _notify_state_change(self, old_state: CircuitBreakerState, new_state: CircuitBreakerState) -> None:
        """通知状态变化"""
        for callback in self._state_change_callbacks:
            try:
                callback(old_state, new_state)
            except Exception as e:
                logger.error(f"Error in state change callback: {e}")

    def get_state(self) -> CircuitBreakerState:
        """获取当前状态"""
        return self._state

    def get_metrics(self) -> CircuitBreakerMetrics:
        """获取指标"""
        with self._lock:
            # 更新开启状态时间
            if self._state == CircuitBreakerState.OPEN:
                self._metrics.time_in_open_state = time.time() - self._last_state_change_time

            return CircuitBreakerMetrics(**self._metrics.__dict__)

    def add_state_change_callback(self, callback: Callable[[CircuitBreakerState, CircuitBreakerState], None]) -> None:
        """添加状态变化回调"""
        self._state_change_callbacks.append(callback)

    def remove_state_change_callback(self, callback: Callable[[CircuitBreakerState, CircuitBreakerState], None]) -> None:
        """移除状态变化回调"""
        if callback in self._state_change_callbacks:
            self._state_change_callbacks.remove(callback)

    def reset(self) -> None:
        """重置熔断器"""
        with self._lock:
            old_state = self._state
            self._state = CircuitBreakerState.CLOSED
            self._failure_count = 0
            self._success_count = 0
            self._half_open_requests = 0
            self._last_failure_time = None
            self._last_state_change_time = time.time()

            # 重置指标
            self._metrics = CircuitBreakerMetrics()
            self._response_times.clear()

            logger.info(f"CircuitBreaker '{self.name}' reset from {old_state.value} to {self._state.value}")

            if old_state != self._state:
                self._notify_state_change(old_state, self._state)

    def force_open(self) -> None:
        """强制开启熔断器"""
        with self._lock:
            if self._state != CircuitBreakerState.OPEN:
                self._transition_to_open()

    def force_close(self) -> None:
        """强制关闭熔断器"""
        with self._lock:
            if self._state != CircuitBreakerState.CLOSED:
                self._transition_to_closed()

    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        metrics = self.get_metrics()
        return {
            "name": self.name,
            "state": self._state.value,
            "total_requests": metrics.total_requests,
            "success_rate": round(metrics.success_rate, 2),
            "failure_rate": round(metrics.failure_rate, 2),
            "rejected_requests": metrics.rejected_requests,
            "avg_response_time": round(metrics.avg_response_time, 3),
            "state_changes": metrics.state_change_count,
            "time_since_last_change": round(time.time() - self._last_state_change_time, 1),
            "is_healthy": self._state == CircuitBreakerState.CLOSED and metrics.failure_rate < 10,
        }

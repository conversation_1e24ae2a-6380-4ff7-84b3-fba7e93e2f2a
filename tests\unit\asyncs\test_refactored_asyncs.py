#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 重构后的 asyncs 模块测试案例
"""

import asyncio
import threading
import time
import unittest
from unittest.mock import Mock, patch

from miniboot.asyncs import AsyncComponentBase, AsyncSingletonComponentBase
from miniboot.asyncs.metrics import AsyncTaskMetricsCollector
from miniboot.asyncs.properties import AsyncProperties


class TestAsyncComponentBase(unittest.TestCase):
    """测试异步组件基类"""

    def setUp(self):
        """设置测试"""
        self.component = None

    def tearDown(self):
        """清理测试"""
        if self.component:
            self.component._cleanup()

    def test_component_initialization(self):
        """测试组件初始化"""

        class TestComponent(AsyncComponentBase):
            def __init__(self):
                super().__init__()
                self.init_called = False

            def _do_initialize(self):
                self.init_called = True

        self.component = TestComponent()

        # 初始状态
        self.assertFalse(self.component.is_initialized())
        self.assertFalse(self.component.init_called)

        # 触发初始化
        self.component._ensure_initialized()
        self.assertTrue(self.component.is_initialized())
        self.assertTrue(self.component.init_called)

        # 重复初始化不会再次调用
        self.component.init_called = False
        self.component._ensure_initialized()
        self.assertFalse(self.component.init_called)

    def test_component_cleanup(self):
        """测试组件清理"""

        class TestComponent(AsyncComponentBase):
            def __init__(self):
                super().__init__()
                self.cleanup_called = False

            def _do_initialize(self):
                pass

            def _do_cleanup(self):
                self.cleanup_called = True

        self.component = TestComponent()
        self.component._ensure_initialized()

        # 执行清理
        self.component._cleanup()
        self.assertFalse(self.component.is_initialized())
        self.assertTrue(self.component.cleanup_called)

    def test_thread_safety(self):
        """测试线程安全性"""

        class TestComponent(AsyncComponentBase):
            def __init__(self):
                super().__init__()
                self.init_count = 0

            def _do_initialize(self):
                time.sleep(0.01)  # 模拟初始化耗时
                self.init_count += 1

        self.component = TestComponent()

        # 多线程同时初始化
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=self.component._ensure_initialized)
            threads.append(thread)
            thread.start()

        for thread in threads:
            thread.join()

        # 只应该初始化一次
        self.assertEqual(self.component.init_count, 1)
        self.assertTrue(self.component.is_initialized())


class TestAsyncSingletonComponentBase(unittest.TestCase):
    """测试异步单例组件基类"""

    def tearDown(self):
        """清理测试"""
        # 重置所有单例实例
        from miniboot.asyncs.base import AsyncSingletonMeta

        # 清理所有单例实例
        AsyncSingletonMeta._instances.clear()

    def test_singleton_behavior(self):
        """测试单例行为"""

        class TestSingletonComponent(AsyncSingletonComponentBase):
            def __init__(self):
                super().__init__()
                self.instance_id = id(self)

            def _do_initialize(self):
                pass

        # 创建多个实例，应该是同一个对象
        instance1 = TestSingletonComponent()
        instance2 = TestSingletonComponent()

        self.assertIs(instance1, instance2)
        self.assertEqual(instance1.instance_id, instance2.instance_id)

    def test_singleton_reset(self):
        """测试单例重置"""

        class TestSingletonComponent(AsyncSingletonComponentBase):
            def __init__(self):
                super().__init__()
                self.instance_id = id(self)

            def _do_initialize(self):
                pass

        # 创建实例
        instance1 = TestSingletonComponent()
        instance1_id = instance1.instance_id

        # 重置单例
        from miniboot.asyncs.base import AsyncSingletonMeta
        AsyncSingletonMeta.reset_instance(TestSingletonComponent)

        # 创建新实例，应该是不同的对象
        instance2 = TestSingletonComponent()
        instance2_id = instance2.instance_id

        self.assertNotEqual(instance1_id, instance2_id)


class TestSingletonRefactoring(unittest.TestCase):
    """测试单例重构"""

    def tearDown(self):
        """清理测试"""
        from miniboot.asyncs.base import AsyncSingletonMeta
        from miniboot.asyncs.metrics import AsyncTaskMetricsCollector
        from miniboot.utils.singleton import SingletonMeta

        # 重置所有单例
        SingletonMeta.reset_instance(AsyncTaskMetricsCollector)

    def test_async_properties_creation(self):
        """测试异步配置属性创建"""
        # AsyncConfigManager 已简化为直接使用 AsyncProperties
        properties1 = AsyncProperties()
        properties2 = AsyncProperties()

        # 验证配置属性的基本功能
        self.assertTrue(properties1.enabled)
        self.assertEqual(properties1.executor.thread_pool.core_size, properties2.executor.thread_pool.core_size)
        self.assertIsInstance(properties1.executor.thread_pool.thread_name_prefix, str)

    def test_metrics_collector_singleton(self):
        """测试指标收集器单例"""
        # 获取多个实例，应该是同一个对象
        collector1 = AsyncTaskMetricsCollector()
        collector2 = AsyncTaskMetricsCollector()

        self.assertIs(collector1, collector2)


class TestFunctionAliasRemoval(unittest.TestCase):
    """测试函数别名删除"""

    def test_aliases_removed(self):
        """测试函数别名已被删除"""
        from miniboot.asyncs import tools

        # 这些别名应该不存在
        with self.assertRaises(AttributeError):
            _ = tools.run_sync_in_thread

        with self.assertRaises(AttributeError):
            _ = tools.concurrent_gather

        with self.assertRaises(AttributeError):
            _ = tools.with_timeout

        with self.assertRaises(AttributeError):
            _ = tools.async_retry

    def test_original_functions_exist(self):
        """测试原始函数仍然存在"""
        from miniboot.asyncs import tools

        # 原始函数应该存在
        self.assertTrue(hasattr(tools, 'run_sync'))
        self.assertTrue(hasattr(tools, 'gather'))
        self.assertTrue(hasattr(tools, 'timeout'))
        self.assertTrue(hasattr(tools, 'retry'))


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 配置元数据测试
"""

import pytest

from miniboot.autoconfigure.metadata import AutoConfigurationMetadata


class TestAutoConfigurationMetadata:
    """配置元数据测试"""

    def test_basic_metadata_creation(self):
        """测试基本元数据创建"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration")

        assert metadata.name == "test-config"
        assert metadata.description == "Test configuration"
        assert metadata.priority == 100  # 默认值
        assert metadata.depends_on == []
        assert metadata.conflicts_with == []
        assert metadata.conditions == []
        assert metadata.properties_prefix is None
        assert metadata.auto_configure_before == []
        assert metadata.auto_configure_after == []

    def test_metadata_with_all_fields(self):
        """测试包含所有字段的元数据"""
        metadata = AutoConfigurationMetadata(
            name="web-config",
            description="Web configuration",
            priority=50,
            depends_on=["database-config"],
            conflicts_with=["legacy-web-config"],
            conditions=["property:web.enabled", "class:fastapi.FastAPI"],
            properties_prefix="app.web",
            auto_configure_before=["security-config"],
            auto_configure_after=["logging-config"],
        )

        assert metadata.name == "web-config"
        assert metadata.description == "Web configuration"
        assert metadata.priority == 50
        assert metadata.depends_on == ["database-config"]
        assert metadata.conflicts_with == ["legacy-web-config"]
        assert metadata.conditions == ["property:web.enabled", "class:fastapi.FastAPI"]
        assert metadata.properties_prefix == "app.web"
        assert metadata.auto_configure_before == ["security-config"]
        assert metadata.auto_configure_after == ["logging-config"]

    def test_validate_success(self):
        """测试验证成功"""
        metadata = AutoConfigurationMetadata(name="valid-config", description="Valid configuration", priority=10)

        # 应该不抛出异常
        metadata.validate()

    def test_validate_empty_name(self):
        """测试空名称验证失败"""
        metadata = AutoConfigurationMetadata(name="", description="Test configuration")

        with pytest.raises(ValueError, match="Configuration name is required"):
            metadata.validate()

    def test_validate_empty_description(self):
        """测试空描述验证失败"""
        metadata = AutoConfigurationMetadata(name="test-config", description="")

        with pytest.raises(ValueError, match="Configuration description is required"):
            metadata.validate()

    def test_validate_negative_priority(self):
        """测试负优先级验证失败"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration", priority=-1)

        with pytest.raises(ValueError, match="Priority must be non-negative"):
            metadata.validate()

    def test_validate_overlapping_dependencies(self):
        """测试重叠依赖验证失败"""
        metadata = AutoConfigurationMetadata(
            name="test-config", description="Test configuration", depends_on=["config-a", "config-b"], auto_configure_after=["config-b", "config-c"]
        )

        with pytest.raises(ValueError, match="Configuration cannot both depend on and configure after"):
            metadata.validate()

    def test_get_all_dependencies(self):
        """测试获取所有依赖配置"""
        metadata = AutoConfigurationMetadata(
            name="test-config",
            description="Test configuration",
            depends_on=["config-a", "config-b"],
            auto_configure_after=["config-c", "config-b"],  # config-b重复
        )

        dependencies = metadata.get_all_dependencies()

        # 应该去重
        assert set(dependencies) == {"config-a", "config-b", "config-c"}
        assert len(dependencies) == 3

    def test_get_all_dependencies_empty(self):
        """测试无依赖时获取所有依赖"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration")

        dependencies = metadata.get_all_dependencies()
        assert dependencies == []

    def test_has_dependency_on_true(self):
        """测试存在依赖关系"""
        metadata = AutoConfigurationMetadata(
            name="test-config", description="Test configuration", depends_on=["config-a"], auto_configure_after=["config-b"]
        )

        assert metadata.has_dependency_on("config-a") is True
        assert metadata.has_dependency_on("config-b") is True

    def test_has_dependency_on_false(self):
        """测试不存在依赖关系"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration", depends_on=["config-a"])

        assert metadata.has_dependency_on("config-c") is False

    def test_conflicts_with_config_true(self):
        """测试存在冲突关系"""
        metadata = AutoConfigurationMetadata(
            name="test-config", description="Test configuration", conflicts_with=["legacy-config", "alternative-config"]
        )

        assert metadata.conflicts_with_config("legacy-config") is True
        assert metadata.conflicts_with_config("alternative-config") is True

    def test_conflicts_with_config_false(self):
        """测试不存在冲突关系"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration", conflicts_with=["legacy-config"])

        assert metadata.conflicts_with_config("other-config") is False

    def test_should_configure_before_true(self):
        """测试应该在指定配置之前配置"""
        metadata = AutoConfigurationMetadata(
            name="test-config", description="Test configuration", auto_configure_before=["security-config", "web-config"]
        )

        assert metadata.should_configure_before("security-config") is True
        assert metadata.should_configure_before("web-config") is True

    def test_should_configure_before_false(self):
        """测试不应该在指定配置之前配置"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration", auto_configure_before=["security-config"])

        assert metadata.should_configure_before("other-config") is False

    def test_should_configure_after_with_depends_on(self):
        """测试通过depends_on应该在指定配置之后配置"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration", depends_on=["database-config"])

        assert metadata.should_configure_after("database-config") is True

    def test_should_configure_after_with_auto_configure_after(self):
        """测试通过auto_configure_after应该在指定配置之后配置"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration", auto_configure_after=["logging-config"])

        assert metadata.should_configure_after("logging-config") is True

    def test_should_configure_after_false(self):
        """测试不应该在指定配置之后配置"""
        metadata = AutoConfigurationMetadata(
            name="test-config", description="Test configuration", depends_on=["database-config"], auto_configure_after=["logging-config"]
        )

        assert metadata.should_configure_after("other-config") is False

    def test_metadata_immutability_through_dataclass(self):
        """测试元数据通过dataclass的不可变性"""
        metadata = AutoConfigurationMetadata(name="test-config", description="Test configuration")

        # 可以修改列表内容（这是dataclass的默认行为）
        metadata.depends_on.append("new-dependency")
        assert "new-dependency" in metadata.depends_on

        # 但不能重新赋值字段（除非使用frozen=True）
        metadata.name = "new-name"
        assert metadata.name == "new-name"

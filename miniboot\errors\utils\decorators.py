#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mini-Boot框架异常处理装饰器

提供统一的异常处理装饰器，包括：
- exception_handler: 通用异常处理装饰器
- timeout_handler: 超时处理装饰器
- performance_monitor: 性能监控装饰器
- circuit_breaker: 熔断器装饰器
- retry_with_backoff: 重试装饰器
- validate_arguments: 参数验证装饰器
"""

import asyncio
import functools
import time
from typing import Any, Callable, Dict, Optional, Union

from loguru import logger

from .strategies import ExceptionAction


def handle_context_exceptions(func: Callable) -> Callable:
    """上下文异常处理装饰器

    专门用于处理上下文相关的异常，提供统一的异常处理逻辑。

    Args:
        func: 被装饰的函数

    Returns:
        装饰后的函数
    """
    @functools.wraps(func)
    async def async_wrapper(*args, **kwargs):
        try:
            return await func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Context exception in {func.__name__}: {e}")
            raise

    @functools.wraps(func)
    def sync_wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except Exception as e:
            logger.error(f"Context exception in {func.__name__}: {e}")
            raise

    # 根据函数类型返回对应的包装器
    if asyncio.iscoroutinefunction(func):
        return async_wrapper
    else:
        return sync_wrapper


def exception_handler(
    operation_name: str = "",
    action: ExceptionAction = ExceptionAction.PROPAGATE,
    log_level: str = "ERROR",
    module_name: str = "",
    component_name: str = ""
):
    """通用异常处理装饰器

    Args:
        operation_name: 操作名称
        action: 异常处理动作
        log_level: 日志级别
        module_name: 模块名称
        component_name: 组件名称
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                return func(*args, **kwargs)
            except Exception as e:
                # 记录日志
                op_name = operation_name or func.__name__
                if log_level.upper() == "ERROR":
                    logger.error(f"Exception in {op_name}: {e}")
                elif log_level.upper() == "WARNING":
                    logger.warning(f"Exception in {op_name}: {e}")

                # 根据动作处理异常
                if action == ExceptionAction.PROPAGATE:
                    raise
                elif action == ExceptionAction.SUPPRESS:
                    return None
                elif action == ExceptionAction.LOG_AND_CONTINUE:
                    return None
                else:
                    raise
        return wrapper
    return decorator


def timeout_handler(timeout_seconds: float = 30.0, timeout_message: str = ""):
    """超时处理装饰器

    Args:
        timeout_seconds: 超时时间（秒）
        timeout_message: 自定义超时消息
    """
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            if asyncio.iscoroutinefunction(func):
                async def async_wrapper():
                    try:
                        return await asyncio.wait_for(func(*args, **kwargs), timeout=timeout_seconds)
                    except asyncio.TimeoutError:
                        error_msg = timeout_message or f"Function {func.__name__} timed out after {timeout_seconds} seconds"
                        logger.error(error_msg)
                        raise TimeoutError(error_msg)
                return async_wrapper()
            else:
                # 同步函数的超时处理
                start_time = time.time()
                result = func(*args, **kwargs)
                elapsed = time.time() - start_time
                if elapsed > timeout_seconds:
                    warning_msg = timeout_message or f"Function {func.__name__} took {elapsed:.2f}s (timeout: {timeout_seconds}s)"
                    logger.warning(warning_msg)
                return result
        return wrapper
    return decorator


def performance_monitor(slow_threshold: float = 1.0):
    """性能监控装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                elapsed = time.time() - start_time
                if elapsed > slow_threshold:
                    logger.warning(f"Slow operation: {func.__name__} took {elapsed:.2f}s")
                return result
            except Exception as e:
                elapsed = time.time() - start_time
                logger.error(f"Failed operation: {func.__name__} failed after {elapsed:.2f}s: {e}")
                raise
        return wrapper
    return decorator


def circuit_breaker(failure_threshold: int = 5, recovery_timeout: float = 60.0):
    """熔断器装饰器"""
    def decorator(func: Callable) -> Callable:
        failure_count = 0
        last_failure_time = 0

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            nonlocal failure_count, last_failure_time

            # 检查是否在恢复期
            if failure_count >= failure_threshold:
                if time.time() - last_failure_time < recovery_timeout:
                    raise Exception(f"Circuit breaker is open for {func.__name__}")
                else:
                    # 重置计数器，尝试恢复
                    failure_count = 0

            try:
                result = func(*args, **kwargs)
                failure_count = 0  # 成功时重置计数器
                return result
            except Exception as e:
                failure_count += 1
                last_failure_time = time.time()
                logger.error(f"Circuit breaker failure {failure_count}/{failure_threshold} for {func.__name__}: {e}")
                raise
        return wrapper
    return decorator


def retry_with_backoff(
    max_attempts: int = 3,
    base_delay: float = 1.0,
    backoff_factor: float = 2.0,
    strategy: str = "exponential",
    exceptions: Union[type, tuple] = Exception
):
    """重试装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            last_exception = None
            for attempt in range(max_attempts):
                try:
                    return func(*args, **kwargs)
                except exceptions as e:
                    last_exception = e
                    if attempt < max_attempts - 1:
                        if strategy == "exponential":
                            delay = base_delay * (backoff_factor ** attempt)
                        elif strategy == "linear":
                            delay = base_delay * (attempt + 1)
                        else:  # fixed
                            delay = base_delay
                        logger.warning(f"Retry {attempt + 1}/{max_attempts} for {func.__name__} after {delay}s: {e}")
                        time.sleep(delay)
                    else:
                        logger.error(f"All {max_attempts} attempts failed for {func.__name__}")
                except Exception as e:
                    # 不在重试异常列表中的异常直接抛出
                    raise
            raise last_exception
        return wrapper
    return decorator


def exponential_backoff_retry(max_attempts: int = 3, base_delay: float = 1.0):
    """指数退避重试装饰器"""
    return retry_with_backoff(max_attempts, base_delay, 2.0)


def validate_arguments(**validators):
    """参数验证装饰器"""
    def decorator(func: Callable) -> Callable:
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 获取函数参数名
            import inspect
            sig = inspect.signature(func)
            bound_args = sig.bind(*args, **kwargs)
            bound_args.apply_defaults()

            # 验证参数
            for param_name, validator in validators.items():
                if param_name in bound_args.arguments:
                    value = bound_args.arguments[param_name]
                    if not validator(value):
                        raise ValueError(f"Validation failed for parameter '{param_name}' with value: {value}")

            return func(*args, **kwargs)
        return wrapper
    return decorator


# 全局指标收集器
_decorator_metrics = {
    "exception_count": 0,
    "timeout_count": 0,
    "slow_operations": 0,
    "circuit_breaker_trips": 0,
    "retry_attempts": 0,
    "function_calls": {},
    "exceptions": {},
    "execution_times": {},
    "performance_stats": {}
}


def get_decorator_metrics() -> Dict[str, Any]:
    """获取装饰器指标"""
    return _decorator_metrics.copy()

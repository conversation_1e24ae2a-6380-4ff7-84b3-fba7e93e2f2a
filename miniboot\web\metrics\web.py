"""
Web 应用性能指标收集器

收集和分析 Web 应用的各种性能指标:
- 请求响应时间
- 请求吞吐量
- 错误率统计
- 资源使用情况
- 并发连接数
"""

import time
import asyncio
import psutil
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, field
from collections import defaultdict, deque
from threading import Lock
import statistics

from loguru import logger


@dataclass
class RequestMetrics:
    """请求指标数据"""

    timestamp: float
    method: str
    path: str
    status_code: int
    response_time: float
    request_size: int = 0
    response_size: int = 0
    user_agent: Optional[str] = None
    ip_address: Optional[str] = None


@dataclass
class WebMetricsSummary:
    """Web 指标汇总"""

    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    avg_response_time: float = 0.0
    p95_response_time: float = 0.0
    p99_response_time: float = 0.0
    requests_per_second: float = 0.0
    error_rate: float = 0.0
    active_connections: int = 0
    cpu_usage: float = 0.0
    memory_usage: float = 0.0
    disk_usage: float = 0.0
    network_io: Dict[str, float] = field(default_factory=dict)


class MetricsCollector:
    """Web 应用性能指标收集器"""

    def __init__(self, max_history: int = 10000, window_size: int = 300):
        """
        初始化指标收集器

        Args:
            max_history: 最大历史记录数
            window_size: 时间窗口大小(秒)
        """
        self.max_history = max_history
        self.window_size = window_size

        # 请求历史记录
        self.request_history: deque = deque(maxlen=max_history)
        self.lock = Lock()

        # 实时统计
        self.active_requests = 0
        self.active_connections = 0

        # 系统资源监控
        self.process = psutil.Process()
        self.last_network_io = None

        # 路径统计
        self.path_metrics: Dict[str, List[float]] = defaultdict(list)
        self.status_code_counts: Dict[int, int] = defaultdict(int)

        logger.info("WebMetricsCollector initialized")

    def record_request(self, metrics: RequestMetrics) -> None:
        """记录请求指标"""
        with self.lock:
            self.request_history.append(metrics)

            # 更新路径统计
            self.path_metrics[f"{metrics.method} {metrics.path}"].append(metrics.response_time)

            # 更新状态码统计
            self.status_code_counts[metrics.status_code] += 1

            # 限制路径指标历史长度
            if len(self.path_metrics[f"{metrics.method} {metrics.path}"]) > 1000:
                self.path_metrics[f"{metrics.method} {metrics.path}"] = self.path_metrics[f"{metrics.method} {metrics.path}"][-500:]

    def start_request(self) -> None:
        """开始请求计数"""
        self.active_requests += 1

    def end_request(self) -> None:
        """结束请求计数"""
        self.active_requests = max(0, self.active_requests - 1)

    def add_connection(self) -> None:
        """增加连接计数"""
        self.active_connections += 1

    def remove_connection(self) -> None:
        """减少连接计数"""
        self.active_connections = max(0, self.active_connections - 1)

    def get_recent_requests(self, seconds: int = None) -> List[RequestMetrics]:
        """获取最近的请求记录"""
        if seconds is None:
            seconds = self.window_size

        cutoff_time = time.time() - seconds

        with self.lock:
            return [req for req in self.request_history if req.timestamp >= cutoff_time]

    def get_system_metrics(self) -> Dict[str, Any]:
        """获取系统资源指标"""
        try:
            # CPU 使用率
            cpu_percent = self.process.cpu_percent()

            # 内存使用情况
            memory_info = self.process.memory_info()
            memory_percent = self.process.memory_percent()

            # 磁盘使用情况
            disk_usage = psutil.disk_usage("/")

            # 网络 I/O
            network_io = psutil.net_io_counters()
            network_stats = {}

            if self.last_network_io:
                bytes_sent_per_sec = network_io.bytes_sent - self.last_network_io.bytes_sent
                bytes_recv_per_sec = network_io.bytes_recv - self.last_network_io.bytes_recv
                network_stats = {"bytes_sent_per_sec": bytes_sent_per_sec, "bytes_recv_per_sec": bytes_recv_per_sec}

            self.last_network_io = network_io

            return {
                "cpu": {"percent": cpu_percent, "count": psutil.cpu_count()},
                "memory": {"rss": memory_info.rss, "vms": memory_info.vms, "percent": memory_percent, "available": psutil.virtual_memory().available},
                "disk": {"total": disk_usage.total, "used": disk_usage.used, "free": disk_usage.free, "percent": disk_usage.percent},
                "network": network_stats,
            }

        except Exception as e:
            logger.warning(f"Failed to collect system metrics: {e}")
            return {}

    def get_metrics_summary(self, seconds: int = None) -> WebMetricsSummary:
        """获取指标汇总"""
        recent_requests = self.get_recent_requests(seconds)

        if not recent_requests:
            return WebMetricsSummary(active_connections=self.active_connections, **self._get_system_summary())

        # 计算基本统计
        total_requests = len(recent_requests)
        successful_requests = sum(1 for req in recent_requests if 200 <= req.status_code < 400)
        failed_requests = total_requests - successful_requests

        # 响应时间统计
        response_times = [req.response_time for req in recent_requests]
        avg_response_time = statistics.mean(response_times)

        # 百分位数
        p95_response_time = statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max(response_times)
        p99_response_time = statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else max(response_times)

        # 请求速率
        time_span = seconds or self.window_size
        requests_per_second = total_requests / time_span if time_span > 0 else 0

        # 错误率
        error_rate = (failed_requests / total_requests) * 100 if total_requests > 0 else 0

        return WebMetricsSummary(
            total_requests=total_requests,
            successful_requests=successful_requests,
            failed_requests=failed_requests,
            avg_response_time=avg_response_time,
            p95_response_time=p95_response_time,
            p99_response_time=p99_response_time,
            requests_per_second=requests_per_second,
            error_rate=error_rate,
            active_connections=self.active_connections,
            **self._get_system_summary(),
        )

    def _get_system_summary(self) -> Dict[str, Any]:
        """获取系统指标汇总"""
        system_metrics = self.get_system_metrics()

        return {
            "cpu_usage": system_metrics.get("cpu", {}).get("percent", 0.0),
            "memory_usage": system_metrics.get("memory", {}).get("percent", 0.0),
            "disk_usage": system_metrics.get("disk", {}).get("percent", 0.0),
            "network_io": system_metrics.get("network", {}),
        }

    def get_path_statistics(self) -> Dict[str, Dict[str, Any]]:
        """获取路径统计信息"""
        path_stats = {}

        with self.lock:
            for path, response_times in self.path_metrics.items():
                if response_times:
                    path_stats[path] = {
                        "count": len(response_times),
                        "avg_response_time": statistics.mean(response_times),
                        "min_response_time": min(response_times),
                        "max_response_time": max(response_times),
                        "p95_response_time": statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max(response_times),
                    }

        return path_stats

    def get_status_code_distribution(self) -> Dict[int, int]:
        """获取状态码分布"""
        with self.lock:
            return dict(self.status_code_counts)

    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self.lock:
            self.request_history.clear()
            self.path_metrics.clear()
            self.status_code_counts.clear()
            self.active_requests = 0
            self.active_connections = 0

        logger.info("WebMetricsCollector metrics reset")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        summary = self.get_metrics_summary()

        return {
            "summary": {
                "total_requests": summary.total_requests,
                "successful_requests": summary.successful_requests,
                "failed_requests": summary.failed_requests,
                "avg_response_time": summary.avg_response_time,
                "p95_response_time": summary.p95_response_time,
                "p99_response_time": summary.p99_response_time,
                "requests_per_second": summary.requests_per_second,
                "error_rate": summary.error_rate,
                "active_connections": summary.active_connections,
                "active_requests": self.active_requests,
            },
            "system": {
                "cpu_usage": summary.cpu_usage,
                "memory_usage": summary.memory_usage,
                "disk_usage": summary.disk_usage,
                "network_io": summary.network_io,
            },
            "paths": self.get_path_statistics(),
            "status_codes": self.get_status_code_distribution(),
        }

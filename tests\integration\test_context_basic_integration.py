#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Context模块基础集成测试

测试ApplicationContext的基础集成功能，验证与核心模块的基本协同工作。
"""

import asyncio
import tempfile
import unittest
from pathlib import Path

from miniboot.context import DefaultApplicationContext
from miniboot.env import StandardEnvironment


class SimpleTestService:
    """简单测试服务"""

    def __init__(self):
        self.initialized = False
        self.call_count = 0

    def init(self):
        """初始化方法"""
        self.initialized = True

    def process(self, data: str) -> str:
        """处理数据"""
        self.call_count += 1
        return f"processed_{data}_{self.call_count}"


class ContextBasicIntegrationTestCase(unittest.IsolatedAsyncioTestCase):
    """Context模块基础集成测试类"""

    async def asyncSetUp(self):
        """异步设置测试环境"""
        # 创建临时配置文件
        self.temp_dir = tempfile.mkdtemp()
        self.config_file = Path(self.temp_dir) / "basic_test_config.yml"

        # 写入基础测试配置
        config_content = """
test:
  basic:
    value: basic_test_value
    number: 42

miniboot:
  application:
    name: basic-test-app
    version: 1.0.0

  # 禁用Web模块
  web:
    enabled: false

  # 禁用调度器模块
  scheduler:
    enabled: false

  # 禁用监控模块
  actuators:
    endpoints:
      web:
        enabled: false

  # 异步配置
  async:
    enabled: true

  # 日志配置 - 最小化输出
  logging:
    level: ERROR
    console:
      enabled: false
      level: ERROR
    file:
      enabled: false
"""
        self.config_file.write_text(config_content, encoding="utf-8")

        # 创建应用上下文
        self.context = DefaultApplicationContext(config_path=str(self.config_file))

    async def asyncTearDown(self):
        """异步清理测试环境"""
        if self.context and self.context.is_running():
            await self.context.stop()

        # 清理临时文件
        if self.config_file.exists():
            self.config_file.unlink()
        Path(self.temp_dir).rmdir()

    async def test_context_startup_and_shutdown(self):
        """测试上下文启动和关闭"""
        # 验证初始状态
        self.assertFalse(self.context.is_running())

        # 启动上下文
        await self.context.start()
        self.assertTrue(self.context.is_running())

        # 关闭上下文
        await self.context.stop()
        self.assertFalse(self.context.is_running())

    async def test_environment_integration(self):
        """测试环境集成"""
        await self.context.start()

        # 获取环境
        environment = self.context.get_environment()
        self.assertIsInstance(environment, StandardEnvironment)

        # 验证配置加载
        basic_value = environment.get_property("test.basic.value")
        self.assertEqual(basic_value, "basic_test_value")

        # 验证类型转换
        number_value = environment.get_property_as("test.basic.number", int)
        self.assertEqual(number_value, 42)

        # 验证默认值
        default_value = environment.get_property("nonexistent.key", "default")
        self.assertEqual(default_value, "default")

    async def test_bean_factory_integration(self):
        """测试Bean工厂集成"""
        await self.context.start()

        # 获取Bean工厂
        bean_factory = self.context.get_bean_factory()
        self.assertIsNotNone(bean_factory)

        # 手动注册Bean
        self.context.register_type(SimpleTestService, "simpleTestService")

        # 验证Bean注册
        self.assertTrue(self.context.contains_bean("simpleTestService"))

        # 获取Bean实例
        service = self.context.get_bean("simpleTestService")
        self.assertIsInstance(service, SimpleTestService)

        # 验证Bean是单例
        service2 = self.context.get_bean("simpleTestService")
        self.assertIs(service, service2)

    async def test_bean_lifecycle_integration(self):
        """测试Bean生命周期集成"""
        await self.context.start()

        # 注册Bean
        self.context.register_type(SimpleTestService, "lifecycleTestService")

        # 获取Bean并验证初始状态
        service = self.context.get_bean("lifecycleTestService")
        self.assertFalse(service.initialized)

        # 手动调用初始化
        service.init()
        self.assertTrue(service.initialized)

        # 测试业务方法
        result = service.process("test_data")
        self.assertEqual(result, "processed_test_data_1")
        self.assertEqual(service.call_count, 1)

    async def test_property_resolution_integration(self):
        """测试属性解析集成"""
        await self.context.start()

        # 测试属性获取
        app_name = self.context.get_property("miniboot.application.name")
        # 验证应用名称存在（可能来自不同的配置源）
        self.assertIsNotNone(app_name)
        self.assertTrue(len(app_name) > 0)

        app_version = self.context.get_property("miniboot.application.version")
        self.assertEqual(app_version, "1.0.0")

        # 测试带默认值的属性获取
        unknown_prop = self.context.get_property("unknown.property", "default_value")
        self.assertEqual(unknown_prop, "default_value")

    async def test_error_handling_integration(self):
        """测试错误处理集成"""
        await self.context.start()

        # 测试获取不存在的Bean
        from miniboot.errors.context import ContextError as BeanNotFoundError

        with self.assertRaises(BeanNotFoundError):
            self.context.get_bean("nonexistent_bean")

        # 测试Bean存在性检查
        self.assertFalse(self.context.contains_bean("nonexistent_bean"))

        # 注册Bean后再次检查
        self.context.register_type(SimpleTestService, "errorTestService")
        self.assertTrue(self.context.contains_bean("errorTestService"))

    async def test_concurrent_bean_access(self):
        """测试并发Bean访问"""
        await self.context.start()

        # 注册Bean
        self.context.register_type(SimpleTestService, "concurrentTestService")

        async def access_bean(worker_id: int) -> str:
            """并发访问Bean"""
            service = self.context.get_bean("concurrentTestService")
            return service.process(f"worker_{worker_id}")

        # 创建并发任务
        tasks = [access_bean(i) for i in range(10)]
        results = await asyncio.gather(*tasks)

        # 验证结果
        self.assertEqual(len(results), 10)

        # 验证所有结果都不同（因为call_count递增）
        unique_results = set(results)
        self.assertEqual(len(unique_results), 10)

        # 验证服务状态
        service = self.context.get_bean("concurrentTestService")
        self.assertEqual(service.call_count, 10)

    async def test_multiple_contexts_isolation(self):
        """测试多个上下文的隔离性"""
        # 创建第二个上下文，使用相同的配置文件
        context2 = DefaultApplicationContext(config_path=str(self.config_file))

        try:
            # 启动两个上下文
            await self.context.start()
            await context2.start()

            # 在第一个上下文中注册Bean
            self.context.register_type(SimpleTestService, "isolationTest1")

            # 在第二个上下文中注册Bean
            context2.register_type(SimpleTestService, "isolationTest2")

            # 验证隔离性
            self.assertTrue(self.context.contains_bean("isolationTest1"))
            self.assertFalse(self.context.contains_bean("isolationTest2"))

            self.assertTrue(context2.contains_bean("isolationTest2"))
            self.assertFalse(context2.contains_bean("isolationTest1"))

            # 验证Bean实例不同
            service1 = self.context.get_bean("isolationTest1")
            service2 = context2.get_bean("isolationTest2")

            self.assertIsNot(service1, service2)

        finally:
            # 清理第二个上下文
            if context2.is_running():
                await context2.stop()

    async def test_context_restart(self):
        """测试上下文重启"""
        # 第一次启动
        await self.context.start()
        self.assertTrue(self.context.is_running())

        # 注册Bean
        self.context.register_type(SimpleTestService, "restartTestService")
        service1 = self.context.get_bean("restartTestService")
        service1.process("test1")

        # 关闭上下文
        await self.context.stop()
        self.assertFalse(self.context.is_running())

        # 重新启动
        await self.context.start()
        self.assertTrue(self.context.is_running())

        # 重新注册Bean（因为上下文重启后Bean定义会丢失）
        self.context.register_type(SimpleTestService, "restartTestService")
        service2 = self.context.get_bean("restartTestService")

        # 验证是新的实例
        self.assertIsNot(service1, service2)
        self.assertEqual(service2.call_count, 0)  # 新实例的计数器应该是0


if __name__ == "__main__":
    unittest.main()

"""
背压控制指标监控

监控背压控制系统的效果和状态:
- 熔断器状态和触发次数
- 负载监控指标
- 并发限制效果
- 降级策略执行情况
- 负载脱落统计
"""

import statistics
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from threading import Lock
from typing import Any, Dict, List, Optional

from loguru import logger


@dataclass
class BackpressureEvent:
    """背压事件记录"""

    timestamp: float
    event_type: str  # 'circuit_break', 'load_shed', 'concurrency_limit', 'degradation'
    component: str
    action: str  # 'triggered', 'recovered', 'executed'
    details: Dict[str, Any] = field(default_factory=dict)


@dataclass
class BackpressureMetricsSummary:
    """背压指标汇总"""

    circuit_breaker_status: Dict[str, str] = field(default_factory=dict)
    circuit_breaker_triggers: int = 0
    load_shedding_events: int = 0
    concurrency_limits_hit: int = 0
    degradation_activations: int = 0
    avg_load_level: float = 0.0
    max_load_level: float = 0.0
    protection_effectiveness: float = 0.0  # 保护效果百分比
    system_stability_score: float = 0.0  # 系统稳定性评分


class PressureMetrics:
    """背压控制指标监控器"""

    def __init__(self, max_history: int = 5000, window_size: int = 300):
        """
        初始化背压指标监控器

        Args:
            max_history: 最大历史记录数
            window_size: 时间窗口大小(秒)
        """
        self.max_history = max_history
        self.window_size = window_size

        # 事件历史记录
        self.event_history: deque = deque(maxlen=max_history)
        self.lock = Lock()

        # 组件状态跟踪
        self.circuit_breaker_states: Dict[str, str] = {}
        self.load_levels: deque = deque(maxlen=1000)
        self.concurrency_usage: deque = deque(maxlen=1000)

        # 统计计数器
        self.event_counters: Dict[str, int] = defaultdict(int)
        self.component_counters: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))

        logger.info("BackpressureMetrics initialized")

    def record_circuit_breaker_event(self, breaker_name: str, state: str, details: Dict[str, Any] = None) -> None:
        """记录熔断器事件"""
        event = BackpressureEvent(timestamp=time.time(), event_type="circuit_break", component=breaker_name, action=state, details=details or {})

        with self.lock:
            self.event_history.append(event)
            self.circuit_breaker_states[breaker_name] = state
            self.event_counters["circuit_break"] += 1
            self.component_counters[breaker_name][state] += 1

        logger.debug(f"Circuit breaker {breaker_name} state changed to {state}")

    def record_load_shedding_event(self, component: str, action: str, details: Dict[str, Any] = None) -> None:
        """记录负载脱落事件"""
        event = BackpressureEvent(timestamp=time.time(), event_type="load_shed", component=component, action=action, details=details or {})

        with self.lock:
            self.event_history.append(event)
            self.event_counters["load_shed"] += 1
            self.component_counters[component][action] += 1

        logger.debug(f"Load shedding {action} by {component}")

    def record_concurrency_limit_event(self, limiter_name: str, action: str, details: Dict[str, Any] = None) -> None:
        """记录并发限制事件"""
        event = BackpressureEvent(timestamp=time.time(), event_type="concurrency_limit", component=limiter_name, action=action, details=details or {})

        with self.lock:
            self.event_history.append(event)
            self.event_counters["concurrency_limit"] += 1
            self.component_counters[limiter_name][action] += 1

        logger.debug(f"Concurrency limit {action} by {limiter_name}")

    def record_degradation_event(self, component: str, action: str, level: str = None, details: Dict[str, Any] = None) -> None:
        """记录降级事件"""
        event_details = details or {}
        if level:
            event_details["level"] = level

        event = BackpressureEvent(timestamp=time.time(), event_type="degradation", component=component, action=action, details=event_details)

        with self.lock:
            self.event_history.append(event)
            self.event_counters["degradation"] += 1
            self.component_counters[component][action] += 1

        logger.debug(f"Degradation {action} by {component} at level {level}")

    def record_load_level(self, load_level: float) -> None:
        """记录负载水平"""
        with self.lock:
            self.load_levels.append({"timestamp": time.time(), "level": load_level})

    def record_concurrency_usage(self, current: int, limit: int, component: str = "default") -> None:
        """记录并发使用情况"""
        usage_ratio = current / limit if limit > 0 else 0

        with self.lock:
            self.concurrency_usage.append(
                {"timestamp": time.time(), "current": current, "limit": limit, "usage_ratio": usage_ratio, "component": component}
            )

    def get_recent_events(self, seconds: int = None) -> List[BackpressureEvent]:
        """获取最近的事件记录"""
        if seconds is None:
            seconds = self.window_size

        cutoff_time = time.time() - seconds

        with self.lock:
            return [event for event in self.event_history if event.timestamp >= cutoff_time]

    def get_circuit_breaker_status(self) -> Dict[str, str]:
        """获取熔断器状态"""
        with self.lock:
            return dict(self.circuit_breaker_states)

    def get_load_statistics(self, seconds: int = None) -> Dict[str, float]:
        """获取负载统计"""
        if seconds is None:
            seconds = self.window_size

        cutoff_time = time.time() - seconds

        with self.lock:
            recent_loads = [entry["level"] for entry in self.load_levels if entry["timestamp"] >= cutoff_time]

        if not recent_loads:
            return {"avg": 0.0, "max": 0.0, "min": 0.0, "current": 0.0}

        return {
            "avg": statistics.mean(recent_loads),
            "max": max(recent_loads),
            "min": min(recent_loads),
            "current": recent_loads[-1] if recent_loads else 0.0,
        }

    def get_concurrency_statistics(self, seconds: int = None) -> Dict[str, Any]:
        """获取并发统计"""
        if seconds is None:
            seconds = self.window_size

        cutoff_time = time.time() - seconds

        with self.lock:
            recent_usage = [entry for entry in self.concurrency_usage if entry["timestamp"] >= cutoff_time]

        if not recent_usage:
            return {"avg_usage_ratio": 0.0, "max_usage_ratio": 0.0, "components": {}}

        usage_ratios = [entry["usage_ratio"] for entry in recent_usage]

        # 按组件分组统计
        component_stats = defaultdict(list)
        for entry in recent_usage:
            component_stats[entry["component"]].append(entry["usage_ratio"])

        component_summary = {}
        for component, ratios in component_stats.items():
            component_summary[component] = {
                "avg_usage_ratio": statistics.mean(ratios),
                "max_usage_ratio": max(ratios),
                "current_usage_ratio": ratios[-1] if ratios else 0.0,
            }

        return {"avg_usage_ratio": statistics.mean(usage_ratios), "max_usage_ratio": max(usage_ratios), "components": component_summary}

    def calculate_protection_effectiveness(self, seconds: int = None) -> float:
        """计算保护效果"""
        recent_events = self.get_recent_events(seconds)

        if not recent_events:
            return 100.0  # 没有事件说明系统稳定

        # 计算各种保护措施的触发比例
        total_events = len(recent_events)
        protection_events = sum(1 for event in recent_events if event.action in ["triggered", "executed", "activated"])

        # 保护效果 = (保护措施触发次数 / 总事件数) * 100
        effectiveness = (protection_events / total_events) * 100 if total_events > 0 else 100.0

        return min(effectiveness, 100.0)

    def calculate_stability_score(self, seconds: int = None) -> float:
        """计算系统稳定性评分"""
        load_stats = self.get_load_statistics(seconds)
        concurrency_stats = self.get_concurrency_statistics(seconds)
        recent_events = self.get_recent_events(seconds)

        # 基础分数
        score = 100.0

        # 负载水平影响 (负载越高,分数越低)
        avg_load = load_stats.get("avg", 0.0)
        if avg_load > 0.8:
            score -= (avg_load - 0.8) * 100  # 超过80%负载开始扣分

        # 并发使用率影响
        avg_concurrency = concurrency_stats.get("avg_usage_ratio", 0.0)
        if avg_concurrency > 0.9:
            score -= (avg_concurrency - 0.9) * 200  # 超过90%并发开始扣分

        # 事件频率影响
        event_frequency = len(recent_events) / (seconds or self.window_size)
        if event_frequency > 0.1:  # 每秒超过0.1个事件
            score -= event_frequency * 50

        return max(score, 0.0)

    def get_metrics_summary(self, seconds: int = None) -> BackpressureMetricsSummary:
        """获取指标汇总"""
        recent_events = self.get_recent_events(seconds)
        load_stats = self.get_load_statistics(seconds)

        # 统计各类事件
        circuit_breaker_triggers = sum(1 for event in recent_events if event.event_type == "circuit_break" and event.action == "open")
        load_shedding_events = sum(1 for event in recent_events if event.event_type == "load_shed")
        concurrency_limits_hit = sum(1 for event in recent_events if event.event_type == "concurrency_limit")
        degradation_activations = sum(1 for event in recent_events if event.event_type == "degradation")

        return BackpressureMetricsSummary(
            circuit_breaker_status=self.get_circuit_breaker_status(),
            circuit_breaker_triggers=circuit_breaker_triggers,
            load_shedding_events=load_shedding_events,
            concurrency_limits_hit=concurrency_limits_hit,
            degradation_activations=degradation_activations,
            avg_load_level=load_stats.get("avg", 0.0),
            max_load_level=load_stats.get("max", 0.0),
            protection_effectiveness=self.calculate_protection_effectiveness(seconds),
            system_stability_score=self.calculate_stability_score(seconds),
        )

    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self.lock:
            self.event_history.clear()
            self.circuit_breaker_states.clear()
            self.load_levels.clear()
            self.concurrency_usage.clear()
            self.event_counters.clear()
            self.component_counters.clear()

        logger.info("BackpressureMetrics reset")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        summary = self.get_metrics_summary()

        return {
            "summary": {
                "circuit_breaker_triggers": summary.circuit_breaker_triggers,
                "load_shedding_events": summary.load_shedding_events,
                "concurrency_limits_hit": summary.concurrency_limits_hit,
                "degradation_activations": summary.degradation_activations,
                "avg_load_level": summary.avg_load_level,
                "max_load_level": summary.max_load_level,
                "protection_effectiveness": summary.protection_effectiveness,
                "system_stability_score": summary.system_stability_score,
            },
            "circuit_breakers": summary.circuit_breaker_status,
            "load_statistics": self.get_load_statistics(),
            "concurrency_statistics": self.get_concurrency_statistics(),
            "event_counters": dict(self.event_counters),
            "component_counters": {k: dict(v) for k, v in self.component_counters.items()},
        }

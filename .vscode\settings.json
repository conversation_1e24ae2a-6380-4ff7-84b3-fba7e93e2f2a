{
    "python.defaultInterpreterPath": "./.venv/Scripts/python.exe",
    "python.linting.enabled": true,
    "python.linting.ruffEnabled": true,
    "python.linting.pylintEnabled": false,
    "python.linting.flake8Enabled": false,
    "python.linting.mypyEnabled": false,
    "python.formatting.provider": "ruff",
    "ruff.configurationPreference": "filesystemFirst",
    "python.testing.pytestEnabled": false,
    "python.testing.unittestEnabled": true,
    "python.testing.unittestArgs": [
        "-v",
        "-s",
        "./tests",
        "-p",
        "*_test.py"
    ],
    "python.testing.autoTestDiscoverOnSaveEnabled": true,
    "files.exclude": {
        "**/__pycache__": true,
        "**/*.pyc": true,
        ".venv": false,
        "*.egg-info": true,
        ".pytest_cache": true,
        ".coverage": true,
        "htmlcov": true
    },
    "files.watcherExclude": {
        "**/.venv/**": true,
        "**/node_modules/**": true,
        "**/__pycache__/**": true
    },
    "editor.formatOnSave": true,
    "editor.formatOnPaste": true,
    "editor.codeActionsOnSave": {
        "source.organizeImports": "explicit",
        "source.fixAll.ruff": "explicit"
    },

    // 代码质量设置
    "files.trimTrailingWhitespace": true,
    "files.insertFinalNewline": true,
    "files.trimFinalNewlines": true,
    "editor.tabSize": 4,
    "editor.insertSpaces": true,
    "editor.detectIndentation": false,
    "editor.rulers": [150],
    "editor.renderWhitespace": "trailing",
    "editor.trimAutoWhitespace": true,
    "python.analysis.typeCheckingMode": "basic",
    "python.analysis.autoImportCompletions": true,
    "python.analysis.autoSearchPaths": true,
    "python.analysis.extraPaths": ["./miniboot"],
    "python.analysis.diagnosticSeverityOverrides": {
        "reportUnnecessaryTypeIgnoreComment": "none",
        "reportUnnecessaryComparison": "none",
        "reportUnnecessaryContains": "none",
        "reportUnnecessaryIsInstance": "none",
        "reportUnnecessaryPass": "none"
    },
    "[python]": {
        "editor.defaultFormatter": "charliermarsh.ruff",
        "editor.tabSize": 4,
        "editor.insertSpaces": true,
        "editor.formatOnSave": true,
        "editor.codeActionsOnSave": {
            "source.fixAll": "explicit",
            "source.organizeImports": "explicit"
        }
    },


    // Terminal environment configuration for Windows
    "terminal.integrated.env.windows": {
        "PYTHONPATH": "${workspaceRoot}"
    },

    // Code Runner configuration
    "code-runner.runInTerminal": true,
    "code-runner.respectShebang": false,
    "code-runner.saveFileBeforeRun": true,
    "code-runner.fileDirectoryAsCwd": true,
    "code-runner.clearPreviousOutput": true,
    "code-runner.executorMap": {
        "python": "$env:PYTHONIOENCODING='utf8'; $env:PYTHONPATH='${workspaceRoot}'; & '${workspaceFolder}\\.venv\\Scripts\\python.exe' -u $fullFileName"
    }
}

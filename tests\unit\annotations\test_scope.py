#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 作用域注解单元测试

测试作用域注解的功能，包括：
- 基本作用域注解
- Web 相关作用域
- 作用域元数据
- 便利函数
"""

import unittest

from miniboot.annotations.metadata import Scope as ScopeEnum
from miniboot.annotations.scope import (
    ApplicationScope,
    PrototypeScope,
    RequestScope,
    Scope,
    SessionScope,
    SingletonScope,
    WebSocketScope,
    get_proxy_mode,
    get_scope_metadata,
    get_scope_value,
    has_application_scope,
    has_prototype_scope,
    has_request_scope,
    has_session_scope,
    has_singleton_scope,
    has_web_scope,
    has_websocket_scope,
    is_scoped,
)


class TestScopeAnnotation(unittest.TestCase):
    """作用域注解测试类"""

    def test_default_scope(self):
        """测试默认作用域（单例）"""

        @Scope()
        class DefaultService:
            pass

        # 检查注解标记
        self.assertTrue(is_scoped(DefaultService))

        # 检查元数据
        metadata = get_scope_metadata(DefaultService)
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.scope, ScopeEnum.SINGLETON)
        self.assertEqual(metadata.proxy_mode, "default")

        # 检查便利函数
        self.assertEqual(get_scope_value(DefaultService), ScopeEnum.SINGLETON)
        self.assertEqual(get_proxy_mode(DefaultService), "default")
        self.assertTrue(has_singleton_scope(DefaultService))

    def test_singleton_scope(self):
        """测试单例作用域"""

        @Scope("singleton")
        class SingletonService:
            pass

        # 检查作用域
        self.assertTrue(has_singleton_scope(SingletonService))
        self.assertFalse(has_prototype_scope(SingletonService))
        self.assertFalse(has_web_scope(SingletonService))

    def test_prototype_scope(self):
        """测试原型作用域"""

        @Scope("prototype")
        class PrototypeService:
            pass

        # 检查作用域
        self.assertTrue(has_prototype_scope(PrototypeService))
        self.assertFalse(has_singleton_scope(PrototypeService))
        self.assertFalse(has_web_scope(PrototypeService))

    def test_request_scope(self):
        """测试请求作用域"""

        @Scope("request")
        class RequestService:
            pass

        # 检查作用域
        self.assertTrue(has_request_scope(RequestService))
        self.assertTrue(has_web_scope(RequestService))
        self.assertFalse(has_singleton_scope(RequestService))

    def test_session_scope(self):
        """测试会话作用域"""

        @Scope("session")
        class SessionService:
            pass

        # 检查作用域
        self.assertTrue(has_session_scope(SessionService))
        self.assertTrue(has_web_scope(SessionService))
        self.assertFalse(has_singleton_scope(SessionService))

    def test_application_scope(self):
        """测试应用作用域"""

        @Scope("application")
        class ApplicationService:
            pass

        # 检查作用域
        self.assertTrue(has_application_scope(ApplicationService))
        self.assertTrue(has_web_scope(ApplicationService))
        self.assertFalse(has_singleton_scope(ApplicationService))

    def test_websocket_scope(self):
        """测试 WebSocket 作用域"""

        @Scope("websocket")
        class WebSocketService:
            pass

        # 检查作用域
        self.assertTrue(has_websocket_scope(WebSocketService))
        self.assertTrue(has_web_scope(WebSocketService))
        self.assertFalse(has_singleton_scope(WebSocketService))

    def test_scope_with_enum(self):
        """测试使用枚举值的作用域"""

        @Scope(ScopeEnum.REQUEST)
        class EnumScopeService:
            pass

        # 检查作用域
        self.assertEqual(get_scope_value(EnumScopeService), ScopeEnum.REQUEST)
        self.assertTrue(has_request_scope(EnumScopeService))

    def test_proxy_mode(self):
        """测试代理模式"""

        @Scope("request", proxy_mode="interfaces")
        class ProxyService:
            pass

        # 检查代理模式
        self.assertEqual(get_proxy_mode(ProxyService), "interfaces")

    def test_convenience_annotations(self):
        """测试便利注解"""

        @RequestScope()
        class RequestService:
            pass

        @SessionScope()
        class SessionService:
            pass

        @ApplicationScope()
        class ApplicationService:
            pass

        @WebSocketScope()
        class WebSocketService:
            pass

        @PrototypeScope()
        class PrototypeService:
            pass

        @SingletonScope()
        class SingletonService:
            pass

        # 检查所有便利注解
        self.assertTrue(has_request_scope(RequestService))
        self.assertTrue(has_session_scope(SessionService))
        self.assertTrue(has_application_scope(ApplicationService))
        self.assertTrue(has_websocket_scope(WebSocketService))
        self.assertTrue(has_prototype_scope(PrototypeService))
        self.assertTrue(has_singleton_scope(SingletonService))

    def test_convenience_annotations_with_proxy_mode(self):
        """测试带代理模式的便利注解"""

        @RequestScope(proxy_mode="target_class")
        class ProxyRequestService:
            pass

        # 检查代理模式
        self.assertEqual(get_proxy_mode(ProxyRequestService), "target_class")
        self.assertTrue(has_request_scope(ProxyRequestService))

    def test_no_scope_annotation(self):
        """测试没有作用域注解的类"""

        class RegularService:
            pass

        # 检查注解标记
        self.assertFalse(is_scoped(RegularService))

        # 检查元数据
        self.assertIsNone(get_scope_metadata(RegularService))
        self.assertIsNone(get_scope_value(RegularService))
        self.assertIsNone(get_proxy_mode(RegularService))

        # 检查便利函数
        self.assertFalse(has_singleton_scope(RegularService))
        self.assertFalse(has_prototype_scope(RegularService))
        self.assertFalse(has_web_scope(RegularService))

    def test_method_scope(self):
        """测试方法级别的作用域注解"""

        class ConfigClass:
            @Scope("request")
            def request_bean(self):
                return "request_bean"

            @Scope("session")
            def session_bean(self):
                return "session_bean"

        # 检查方法注解
        self.assertTrue(is_scoped(ConfigClass.request_bean))
        self.assertTrue(is_scoped(ConfigClass.session_bean))

        # 检查作用域
        self.assertTrue(has_request_scope(ConfigClass.request_bean))
        self.assertTrue(has_session_scope(ConfigClass.session_bean))

    def test_web_scope_detection(self):
        """测试 Web 作用域检测"""

        @RequestScope()
        class RequestService:
            pass

        @SessionScope()
        class SessionService:
            pass

        @ApplicationScope()
        class ApplicationService:
            pass

        @WebSocketScope()
        class WebSocketService:
            pass

        @SingletonScope()
        class SingletonService:
            pass

        @PrototypeScope()
        class PrototypeService:
            pass

        # Web 作用域应该被检测到
        self.assertTrue(has_web_scope(RequestService))
        self.assertTrue(has_web_scope(SessionService))
        self.assertTrue(has_web_scope(ApplicationService))
        self.assertTrue(has_web_scope(WebSocketService))

        # 非 Web 作用域不应该被检测到
        self.assertFalse(has_web_scope(SingletonService))
        self.assertFalse(has_web_scope(PrototypeService))

    def test_invalid_scope_string(self):
        """测试无效的作用域字符串"""
        with self.assertRaises(ValueError):

            @Scope("invalid_scope")
            class InvalidService:
                pass

    def test_scope_metadata_structure(self):
        """测试作用域元数据结构"""

        @Scope("request", proxy_mode="interfaces")
        class TestService:
            pass

        metadata = get_scope_metadata(TestService)

        # 检查元数据结构
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.scope, ScopeEnum.REQUEST)
        self.assertEqual(metadata.proxy_mode, "interfaces")

    def test_multiple_scope_checks(self):
        """测试多个作用域检查"""

        @RequestScope()
        class RequestService:
            pass

        # 一个服务只能有一个作用域
        self.assertTrue(has_request_scope(RequestService))
        self.assertFalse(has_session_scope(RequestService))
        self.assertFalse(has_application_scope(RequestService))
        self.assertFalse(has_websocket_scope(RequestService))
        self.assertFalse(has_singleton_scope(RequestService))
        self.assertFalse(has_prototype_scope(RequestService))


if __name__ == "__main__":
    unittest.main()

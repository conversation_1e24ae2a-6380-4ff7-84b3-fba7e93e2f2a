#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean定义单元测试
"""

import unittest
from typing import Any
from unittest.mock import Mock

from miniboot.bean.definition import (BeanDefinition, BeanScope,
                                      ConstructorArgument, PropertyValue)


class TestBeanScope(unittest.TestCase):
    """BeanScope枚举测试"""

    def test_bean_scope_values(self):
        """测试BeanScope枚举值"""
        self.assertEqual(BeanScope.SINGLETON.value, "singleton")
        self.assertEqual(BeanScope.PROTOTYPE.value, "prototype")
        self.assertEqual(BeanScope.REQUEST.value, "request")
        self.assertEqual(BeanScope.SESSION.value, "session")
        self.assertEqual(BeanScope.APPLICATION.value, "application")
        self.assertEqual(BeanScope.WEBSOCKET.value, "websocket")

    def test_bean_scope_comparison(self):
        """测试BeanScope比较"""
        self.assertEqual(BeanScope.SINGLETON, BeanScope.SINGLETON)
        self.assertNotEqual(BeanScope.SINGLETON, BeanScope.PROTOTYPE)

    def test_bean_scope_string_representation(self):
        """测试BeanScope字符串表示"""
        self.assertEqual(str(BeanScope.SINGLETON), "singleton")
        self.assertEqual(repr(BeanScope.SINGLETON), "BeanScope.SINGLETON")


class TestPropertyValue(unittest.TestCase):
    """PropertyValue测试"""

    def test_property_value_creation(self):
        """测试PropertyValue创建"""
        # 测试值属性
        prop_value = PropertyValue("name", "testValue")
        self.assertEqual(prop_value.name, "name")
        self.assertEqual(prop_value.value, "testValue")
        self.assertIsNone(prop_value.ref)

        # 测试引用属性
        prop_ref = PropertyValue("service", ref="userService")
        self.assertEqual(prop_ref.name, "service")
        self.assertIsNone(prop_ref.value)
        self.assertEqual(prop_ref.ref, "userService")

    def test_property_value_validation(self):
        """测试PropertyValue验证"""
        # 正常情况
        prop = PropertyValue("test", "value")
        self.assertEqual(prop.name, "test")
        self.assertEqual(prop.value, "value")

        prop = PropertyValue("test", ref="ref")
        self.assertEqual(prop.name, "test")
        self.assertEqual(prop.ref, "ref")

        # 异常情况：既没有value也没有ref
        with self.assertRaises(ValueError):
            PropertyValue("test")

        # 异常情况：既有value又有ref
        with self.assertRaises(ValueError):
            PropertyValue("test", "value", "ref")

    def test_property_value_string_representation(self):
        """测试PropertyValue字符串表示"""
        prop_value = PropertyValue("name", "testValue")
        self.assertIn("name", str(prop_value))
        self.assertIn("testValue", str(prop_value))

        prop_ref = PropertyValue("service", ref="userService")
        self.assertIn("service", str(prop_ref))
        self.assertIn("userService", str(prop_ref))


class TestConstructorArgument(unittest.TestCase):
    """ConstructorArgument测试"""

    def test_constructor_argument_creation(self):
        """测试ConstructorArgument创建"""
        # 测试值参数
        arg_value = ConstructorArgument(0, "testValue")
        self.assertEqual(arg_value.index, 0)
        self.assertEqual(arg_value.value, "testValue")
        self.assertIsNone(arg_value.ref)
        self.assertIsNone(arg_value.type_hint)

        # 测试引用参数
        arg_ref = ConstructorArgument(1, ref="userService", type_hint=str)
        self.assertEqual(arg_ref.index, 1)
        self.assertIsNone(arg_ref.value)
        self.assertEqual(arg_ref.ref, "userService")
        self.assertEqual(arg_ref.type_hint, str)

    def test_constructor_argument_validation(self):
        """测试ConstructorArgument验证"""
        # 正常情况
        arg = ConstructorArgument(0, "value")
        self.assertEqual(arg.index, 0)
        self.assertEqual(arg.value, "value")

        arg = ConstructorArgument(0, ref="ref")
        self.assertEqual(arg.index, 0)
        self.assertEqual(arg.ref, "ref")

        # 异常情况：既没有value也没有ref
        with self.assertRaises(ValueError):
            ConstructorArgument(0)

        # 异常情况：既有value又有ref
        with self.assertRaises(ValueError):
            ConstructorArgument(0, "value", "ref")

    def test_constructor_argument_string_representation(self):
        """测试ConstructorArgument字符串表示"""
        arg_value = ConstructorArgument(0, "testValue")
        self.assertIn("index=0", str(arg_value))
        self.assertIn("testValue", str(arg_value))

        arg_ref = ConstructorArgument(1, ref="userService")
        self.assertIn("index=1", str(arg_ref))
        self.assertIn("userService", str(arg_ref))


class TestBeanDefinition(unittest.TestCase):
    """BeanDefinition测试"""

    def setUp(self):
        """测试前置设置"""
        self.test_class = Mock
        self.bean_name = "testBean"

    def test_bean_definition_creation(self):
        """测试BeanDefinition创建"""
        bean_def = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class,
            scope=BeanScope.SINGLETON
        )

        self.assertEqual(bean_def.bean_name, self.bean_name)
        self.assertEqual(bean_def.bean_class, self.test_class)
        self.assertEqual(bean_def.scope, BeanScope.SINGLETON)
        self.assertFalse(bean_def.lazy_init)  # 默认为False
        self.assertIsNone(bean_def.init_method_name)
        self.assertIsNone(bean_def.destroy_method_name)
        self.assertEqual(len(bean_def.property_values), 0)
        self.assertEqual(len(bean_def.constructor_args), 0)

    def test_bean_definition_with_properties(self):
        """测试带属性的BeanDefinition"""
        properties = [
            PropertyValue("name", "testName"),
            PropertyValue("service", ref="userService")
        ]

        bean_def = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class,
            scope=BeanScope.PROTOTYPE,
            property_values=properties
        )

        self.assertEqual(bean_def.scope, BeanScope.PROTOTYPE)
        self.assertEqual(len(bean_def.property_values), 2)
        self.assertEqual(bean_def.property_values[0].name, "name")
        self.assertEqual(bean_def.property_values[1].ref, "userService")

    def test_bean_definition_with_constructor_args(self):
        """测试带构造函数参数的BeanDefinition"""
        constructor_args = [
            ConstructorArgument(0, "arg1"),
            ConstructorArgument(1, ref="dependency")
        ]

        bean_def = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class,
            constructor_args=constructor_args
        )

        self.assertEqual(len(bean_def.constructor_args), 2)
        self.assertEqual(bean_def.constructor_args[0].value, "arg1")
        self.assertEqual(bean_def.constructor_args[1].ref, "dependency")

    def test_bean_definition_lifecycle_methods(self):
        """测试BeanDefinition生命周期方法"""
        bean_def = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class,
            init_method_name="init",
            destroy_method_name="cleanup"
        )

        self.assertEqual(bean_def.init_method_name, "init")
        self.assertEqual(bean_def.destroy_method_name, "cleanup")

    def test_bean_definition_validation(self):
        """测试BeanDefinition验证"""
        # 正常的Bean定义
        bean_def = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class
        )
        self.assertEqual(bean_def.bean_name, self.bean_name)
        self.assertEqual(bean_def.bean_class, self.test_class)

        # 缺少bean_name
        with self.assertRaises(ValueError):
            BeanDefinition(
                bean_name="",
                bean_class=self.test_class
            )

        # 缺少bean_class
        with self.assertRaises(ValueError):
            BeanDefinition(
                bean_name=self.bean_name,
                bean_class=None
            )

    def test_bean_definition_property_operations(self):
        """测试BeanDefinition属性操作"""
        bean_def = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class
        )

        # 添加属性
        bean_def.add_property_value("name", value="testName")
        self.assertEqual(len(bean_def.property_values), 1)
        self.assertEqual(bean_def.property_values[0].name, "name")

        # 添加引用属性
        bean_def.add_property_value("service", ref="userService")
        self.assertEqual(len(bean_def.property_values), 2)
        self.assertEqual(bean_def.property_values[1].ref, "userService")

        # 获取属性值
        prop_value = bean_def.get_property_value("name")
        self.assertIsNotNone(prop_value)
        self.assertEqual(prop_value.value, "testName")

        # 获取不存在的属性
        prop_value = bean_def.get_property_value("nonexistent")
        self.assertIsNone(prop_value)

    def test_bean_definition_constructor_operations(self):
        """测试BeanDefinition构造函数参数操作"""
        bean_def = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class
        )

        # 添加构造函数参数
        bean_def.add_constructor_arg(0, value="arg1")
        self.assertEqual(len(bean_def.constructor_args), 1)
        self.assertEqual(bean_def.constructor_args[0].value, "arg1")

        # 添加引用参数
        bean_def.add_constructor_arg(1, ref="dependency")
        self.assertEqual(len(bean_def.constructor_args), 2)
        self.assertEqual(bean_def.constructor_args[1].ref, "dependency")

        # 检查是否有构造函数参数
        self.assertTrue(len(bean_def.constructor_args) > 0)

    def test_bean_definition_string_representation(self):
        """测试BeanDefinition字符串表示"""
        bean_def = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class,
            scope=BeanScope.SINGLETON
        )

        str_repr = str(bean_def)
        self.assertIn(self.bean_name, str_repr)
        self.assertIn("singleton", str_repr)  # BeanScope的字符串表示是小写

    def test_bean_definition_equality(self):
        """测试BeanDefinition相等性"""
        bean_def1 = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class
        )

        bean_def2 = BeanDefinition(
            bean_name=self.bean_name,
            bean_class=self.test_class
        )

        # 相同的Bean定义应该相等
        self.assertEqual(bean_def1, bean_def2)

        # 不同名称的Bean定义不相等
        bean_def3 = BeanDefinition(
            bean_name="differentName",
            bean_class=self.test_class
        )

        self.assertNotEqual(bean_def1, bean_def3)


if __name__ == '__main__':
    unittest.main()

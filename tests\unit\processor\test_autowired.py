#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 自动装配处理器单元测试 - 简化版本
"""

import unittest
from unittest.mock import Mock

from miniboot.errors.processor import BeanProcessingError
from miniboot.processor.autowired import AutowiredAnnotationProcessor
from miniboot.processor.base import ProcessorOrder


class MockRepository:
    """模拟仓库类"""

    def __init__(self, name="mock_repo"):
        self.name = name


class MockService:
    """模拟服务类"""

    def __init__(self, name="mock_service"):
        self.name = name


class TestBeanWithAutowired:
    """测试Bean - 有@Autowired字段"""

    def __init__(self):
        self.repository = None

    # 模拟@Autowired字段元数据
    __autowired_fields__ = {"repository": type("AutowiredMetadata", (), {"required": True, "name": None, "qualifier": None})()}
    __annotations__ = {"repository": MockRepository}


class TestBeanWithoutAutowired:
    """测试Bean - 无@Autowired注解"""

    def __init__(self):
        self.value = "test"


class TestAutowiredProcessor(unittest.TestCase):
    """自动装配处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.processor = AutowiredAnnotationProcessor()
        self.mock_bean_factory = Mock()
        self.processor.set_bean_factory(self.mock_bean_factory)

        # 设置模拟Bean工厂的返回值
        self.mock_repository = MockRepository("test_repo")
        self.mock_bean_factory.get_bean.return_value = self.mock_repository
        self.mock_bean_factory.get_bean_by_type.return_value = self.mock_repository

    def test_processor_order(self):
        """测试处理器执行顺序"""
        self.assertEqual(self.processor.get_order(), ProcessorOrder.DEPENDENCY_INJECTION_PROCESSOR)

    def test_supports_bean_with_autowired(self):
        """测试支持有@Autowired注解的Bean"""
        bean = TestBeanWithAutowired()
        self.assertTrue(self.processor.supports(bean, "testBean"))

    def test_supports_bean_without_autowired(self):
        """测试不支持无@Autowired注解的Bean"""
        bean = TestBeanWithoutAutowired()
        self.assertFalse(self.processor.supports(bean, "testBean"))

    def test_supports_none_bean(self):
        """测试不支持None Bean"""
        self.assertFalse(self.processor.supports(None, "testBean"))

    def test_field_injection_success(self):
        """测试字段注入成功"""
        bean = TestBeanWithAutowired()

        # 执行处理
        result = self.processor.post_process_before_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)
        self.assertIs(bean.repository, self.mock_repository)

        # 验证Bean工厂调用
        self.mock_bean_factory.get_bean_by_type.assert_called_once()
        # 检查调用参数是MockRepository类型（可能是类或字符串形式）
        call_args = self.mock_bean_factory.get_bean_by_type.call_args[0][0]
        self.assertTrue(call_args == MockRepository or str(call_args) == str(MockRepository) or call_args == str(MockRepository))

    def test_dependency_not_found(self):
        """测试依赖未找到"""
        bean = TestBeanWithAutowired()

        # 设置Bean工厂抛出异常
        self.mock_bean_factory.get_bean_by_type.side_effect = Exception("Bean not found")

        # 验证抛出异常
        with self.assertRaises(BeanProcessingError) as context:
            self.processor.post_process_before_initialization(bean, "testBean")

        self.assertIn("Failed to process @Autowired annotations", str(context.exception))

    def test_no_bean_factory(self):
        """测试没有Bean工厂的情况"""
        processor = AutowiredAnnotationProcessor()  # 没有设置Bean工厂
        bean = TestBeanWithAutowired()

        # 验证抛出异常
        with self.assertRaises(BeanProcessingError):
            processor.post_process_before_initialization(bean, "testBean")

    def test_already_processed_bean(self):
        """测试已处理的Bean不会重复处理"""
        bean = TestBeanWithAutowired()

        # 第一次处理
        self.processor.post_process_before_initialization(bean, "testBean")

        # 重置mock调用记录
        self.mock_bean_factory.reset_mock()

        # 第二次处理
        result = self.processor.post_process_before_initialization(bean, "testBean")

        # 验证没有再次调用Bean工厂
        self.mock_bean_factory.get_bean.assert_not_called()
        self.assertIs(result, bean)

    def test_field_already_has_value(self):
        """测试字段已有值的情况"""
        bean = TestBeanWithAutowired()
        existing_repo = MockRepository("existing")
        bean.repository = existing_repo

        # 执行处理
        self.processor.post_process_before_initialization(bean, "testBean")

        # 验证不会覆盖已有值
        self.assertIs(bean.repository, existing_repo)
        self.assertNotEqual(bean.repository.name, "test_repo")

    def test_post_process_after_initialization(self):
        """测试初始化后处理（应该直接返回原Bean）"""
        bean = TestBeanWithAutowired()

        result = self.processor.post_process_after_initialization(bean, "testBean")

        self.assertIs(result, bean)

    def test_none_bean_handling(self):
        """测试None Bean的处理"""
        result = self.processor.post_process_before_initialization(None, "testBean")
        self.assertIsNone(result)


if __name__ == "__main__":
    unittest.main()

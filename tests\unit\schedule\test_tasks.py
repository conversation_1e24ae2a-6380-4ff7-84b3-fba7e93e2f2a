#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 任务接口和类型系统单元测试
"""

import unittest

from miniboot.schedule import (
    LambdaTask,
    MethodTask,
    Scheduled,
    ScheduledConfig,
    ScheduledTask,
    TaskFactory,
    TaskIdGenerator,
    TaskRegistrationError,
    TaskRegistry,
    TaskStatus,
    TaskType,
)


class TestTaskType(unittest.TestCase):
    """测试TaskType枚举"""

    def test_task_type_values(self):
        """测试任务类型值"""
        self.assertEqual(TaskType.CRON.value, "cron")
        self.assertEqual(TaskType.FIXED_RATE.value, "fixed_rate")
        self.assertEqual(TaskType.FIXED_DELAY.value, "fixed_delay")
        self.assertEqual(TaskType.ONE_TIME.value, "one_time")


class TestTaskStatus(unittest.TestCase):
    """测试TaskStatus枚举"""

    def test_task_status_values(self):
        """测试任务状态值"""
        self.assertEqual(TaskStatus.PENDING.value, "pending")
        self.assertEqual(TaskStatus.RUNNING.value, "running")
        self.assertEqual(TaskStatus.COMPLETED.value, "completed")
        self.assertEqual(TaskStatus.FAILED.value, "failed")
        self.assertEqual(TaskStatus.CANCELLED.value, "cancelled")


class TestTaskIdGenerator(unittest.TestCase):
    """测试TaskIdGenerator"""

    def test_singleton_pattern(self):
        """测试单例模式"""
        gen1 = TaskIdGenerator()
        gen2 = TaskIdGenerator()
        self.assertIs(gen1, gen2)

    def test_generate_id(self):
        """测试ID生成"""
        gen = TaskIdGenerator()
        id1 = gen.generate_id()
        id2 = gen.generate_id()

        self.assertNotEqual(id1, id2)
        self.assertTrue(id1.startswith("task_"))
        self.assertTrue(id2.startswith("task_"))

    def test_generate_uuid(self):
        """测试UUID生成"""
        gen = TaskIdGenerator()
        uuid1 = gen.generate_uuid()
        uuid2 = gen.generate_uuid()

        self.assertNotEqual(uuid1, uuid2)
        self.assertTrue(uuid1.startswith("task_"))
        self.assertTrue(uuid2.startswith("task_"))


class ConcreteTask(ScheduledTask):
    """具体任务类，用于测试抽象基类"""

    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.execution_result = "test_result"

    async def execute(self):
        return self.execution_result


class TestScheduledTask(unittest.TestCase):
    """测试ScheduledTask抽象基类"""

    def test_task_initialization(self):
        """测试任务初始化"""
        config = ScheduledConfig(cron="0 */5 * * * *")
        task = ConcreteTask(name="test_task", config=config, description="测试任务")

        self.assertEqual(task.name, "test_task")
        self.assertEqual(task.config, config)
        self.assertEqual(task.description, "测试任务")
        self.assertEqual(task.status, TaskStatus.CREATED)
        self.assertEqual(task.task_type, TaskType.CRON)
        self.assertEqual(task.execution_count, 0)
        self.assertEqual(task.failure_count, 0)

    def test_task_type_determination(self):
        """测试任务类型确定"""
        # Cron任务
        cron_task = ConcreteTask(config=ScheduledConfig(cron="0 */5 * * * *"))
        self.assertEqual(cron_task.task_type, TaskType.CRON)

        # 固定频率任务
        rate_task = ConcreteTask(config=ScheduledConfig(fixed_rate="30s"))
        self.assertEqual(rate_task.task_type, TaskType.FIXED_RATE)

        # 固定延迟任务
        delay_task = ConcreteTask(config=ScheduledConfig(fixed_delay="1m"))
        self.assertEqual(delay_task.task_type, TaskType.FIXED_DELAY)

        # 一次性任务
        one_time_task = ConcreteTask()
        self.assertEqual(one_time_task.task_type, TaskType.ONE_TIME)

    def test_task_execution_lifecycle(self):
        """测试任务执行生命周期"""
        task = ConcreteTask()

        # 初始状态
        self.assertEqual(task.status, TaskStatus.CREATED)
        self.assertEqual(task.execution_count, 0)

        # 开始执行
        task.mark_execution_start()
        self.assertEqual(task.status, TaskStatus.RUNNING)
        self.assertEqual(task.execution_count, 1)
        self.assertIsNotNone(task.last_execution)

        # 执行成功
        task.mark_execution_success()
        self.assertEqual(task.status, TaskStatus.COMPLETED)
        self.assertIsNone(task.last_error)

        # 重置状态
        task.reset_status()
        self.assertEqual(task.status, TaskStatus.PENDING)

    def test_task_execution_failure(self):
        """测试任务执行失败"""
        task = ConcreteTask()
        error = Exception("测试错误")

        task.mark_execution_start()
        task.mark_execution_failure(error)

        self.assertEqual(task.status, TaskStatus.FAILED)
        self.assertEqual(task.failure_count, 1)
        self.assertEqual(task.last_error, error)

    def test_task_cancellation(self):
        """测试任务取消"""
        task = ConcreteTask()
        task.mark_cancelled()
        self.assertEqual(task.status, TaskStatus.CANCELLED)

    def test_task_execution(self):
        """测试任务执行"""
        import asyncio

        task = ConcreteTask()
        result = asyncio.run(task.execute())
        self.assertEqual(result, "test_result")


class TestMethodTask(unittest.TestCase):
    """测试MethodTask"""

    def setUp(self):
        """设置测试环境"""
        self.test_instance = TestService()

    def test_sync_method_task(self):
        """测试同步方法任务"""
        config = ScheduledConfig(fixed_rate="30s")
        task = MethodTask(method=self.test_instance.sync_method, instance=self.test_instance, config=config)

        self.assertEqual(task.name, "TestService.sync_method")
        self.assertFalse(task.is_async)
        self.assertTrue(task.is_bound_method)
        self.assertEqual(task.task_type, TaskType.FIXED_RATE)

    def test_async_method_task(self):
        """测试异步方法任务"""
        config = ScheduledConfig(cron="0 */5 * * * *")
        task = MethodTask(method=self.test_instance.async_method, instance=self.test_instance, config=config)

        self.assertEqual(task.name, "TestService.async_method")
        self.assertTrue(task.is_async)
        self.assertTrue(task.is_bound_method)
        self.assertEqual(task.task_type, TaskType.CRON)

    def test_method_execution(self):
        """测试方法执行"""
        import asyncio

        task = MethodTask(method=self.test_instance.sync_method, instance=self.test_instance)

        result = asyncio.run(task.execute())
        self.assertEqual(result, "sync_result")

    def test_async_method_execution(self):
        """测试异步方法执行"""
        import asyncio

        task = MethodTask(method=self.test_instance.async_method, instance=self.test_instance)

        result = asyncio.run(task.execute())
        self.assertEqual(result, "async_result")

    def test_invalid_method_validation(self):
        """测试无效方法验证"""
        with self.assertRaises(TaskRegistrationError):
            MethodTask(method="not_callable")

    def test_method_with_required_params(self):
        """测试带必需参数的方法"""

        def method_with_params(x, y):
            return x + y

        with self.assertRaises(TaskRegistrationError):
            MethodTask(method=method_with_params)


class TestService:
    """测试服务类"""

    def sync_method(self):
        """同步方法"""
        return "sync_result"

    async def async_method(self):
        """异步方法"""
        return "async_result"

    @Scheduled(fixed_rate="30s")
    def scheduled_method(self):
        """带@Scheduled装饰的方法"""
        return "scheduled_result"


class TestLambdaTask(unittest.TestCase):
    """测试LambdaTask"""

    def test_lambda_task_creation(self):
        """测试Lambda任务创建"""

        def func():
            return "lambda_result"

        config = ScheduledConfig(fixed_delay="1m")

        task = LambdaTask(func=func, config=config, name="test_lambda")

        self.assertEqual(task.name, "test_lambda")
        self.assertFalse(task.is_async)
        self.assertEqual(task.task_type, TaskType.FIXED_DELAY)

    def test_lambda_execution(self):
        """测试Lambda执行"""
        import asyncio

        def func():
            return "lambda_result"

        task = LambdaTask(func=func)

        result = asyncio.run(task.execute())
        self.assertEqual(result, "lambda_result")

    def test_function_with_args(self):
        """测试带参数的函数"""
        import asyncio

        def add_func(x, y):
            return x + y

        task = LambdaTask(func=add_func, args=(1, 2))

        result = asyncio.run(task.execute())
        self.assertEqual(result, 3)


class TestTaskFactory(unittest.TestCase):
    """测试TaskFactory"""

    def setUp(self):
        """设置测试环境"""
        self.test_service = TestService()

    def test_create_method_task(self):
        """测试创建方法任务"""
        config = ScheduledConfig(fixed_rate="30s")
        task = TaskFactory.create_method_task(method=self.test_service.sync_method, instance=self.test_service, config=config)

        self.assertIsInstance(task, MethodTask)
        self.assertEqual(task.config, config)

    def test_create_lambda_task(self):
        """测试创建Lambda任务"""

        def func():
            return "result"

        config = ScheduledConfig(cron="0 */5 * * * *")

        task = TaskFactory.create_lambda_task(func=func, config=config)

        self.assertIsInstance(task, LambdaTask)
        self.assertEqual(task.config, config)

    def test_create_from_scheduled_method(self):
        """测试从@Scheduled方法创建任务"""
        task = TaskFactory.create_from_scheduled_method(method=self.test_service.scheduled_method, instance=self.test_service)

        self.assertIsInstance(task, MethodTask)
        self.assertIsNotNone(task.config)
        self.assertEqual(task.config.fixed_rate, "30s")

    def test_create_from_non_scheduled_method(self):
        """测试从非@Scheduled方法创建任务"""
        task = TaskFactory.create_from_scheduled_method(method=self.test_service.sync_method, instance=self.test_service)

        self.assertIsNone(task)


class TestTaskRegistry(unittest.TestCase):
    """测试TaskRegistry"""

    def setUp(self):
        """设置测试环境"""
        self.registry = TaskRegistry()
        self.task1 = ConcreteTask(name="task1", config=ScheduledConfig(cron="0 */5 * * * *"))
        self.task2 = ConcreteTask(name="task2", config=ScheduledConfig(fixed_rate="30s"))

    def test_register_task(self):
        """测试注册任务"""
        task_id = self.registry.register_task(self.task1)
        self.assertEqual(task_id, self.task1.task_id)

        # 获取任务
        retrieved_task = self.registry.get_task(task_id)
        self.assertEqual(retrieved_task, self.task1)

    def test_register_duplicate_task(self):
        """测试注册重复任务"""
        self.registry.register_task(self.task1)

        # 尝试注册相同ID的任务
        duplicate_task = ConcreteTask(task_id=self.task1.task_id)
        with self.assertRaises(TaskRegistrationError):
            self.registry.register_task(duplicate_task)

    def test_unregister_task(self):
        """测试注销任务"""
        task_id = self.registry.register_task(self.task1)

        # 注销任务
        result = self.registry.unregister_task(task_id)
        self.assertTrue(result)

        # 验证任务已被移除
        retrieved_task = self.registry.get_task(task_id)
        self.assertIsNone(retrieved_task)

        # 再次注销应该返回False
        result = self.registry.unregister_task(task_id)
        self.assertFalse(result)

    def test_get_all_tasks(self):
        """测试获取所有任务"""
        self.registry.register_task(self.task1)
        self.registry.register_task(self.task2)

        all_tasks = self.registry.get_all_tasks()
        self.assertEqual(len(all_tasks), 2)
        self.assertIn(self.task1.task_id, all_tasks)
        self.assertIn(self.task2.task_id, all_tasks)

    def test_get_tasks_by_type(self):
        """测试按类型获取任务"""
        self.registry.register_task(self.task1)  # CRON
        self.registry.register_task(self.task2)  # FIXED_RATE

        cron_tasks = self.registry.get_tasks_by_type(TaskType.CRON)
        self.assertEqual(len(cron_tasks), 1)
        self.assertIn(self.task1.task_id, cron_tasks)

        rate_tasks = self.registry.get_tasks_by_type(TaskType.FIXED_RATE)
        self.assertEqual(len(rate_tasks), 1)
        self.assertIn(self.task2.task_id, rate_tasks)

    def test_get_tasks_by_status(self):
        """测试按状态获取任务"""
        self.registry.register_task(self.task1)
        self.registry.register_task(self.task2)

        # 修改任务状态
        self.task1.mark_execution_start()

        created_tasks = self.registry.get_tasks_by_status(TaskStatus.CREATED)
        self.assertEqual(len(created_tasks), 1)
        self.assertIn(self.task2.task_id, created_tasks)

        running_tasks = self.registry.get_tasks_by_status(TaskStatus.RUNNING)
        self.assertEqual(len(running_tasks), 1)
        self.assertIn(self.task1.task_id, running_tasks)

    def test_get_stats(self):
        """测试获取统计信息"""
        self.registry.register_task(self.task1)
        self.registry.register_task(self.task2)

        stats = self.registry.get_stats()

        self.assertEqual(stats["total_tasks"], 2)
        self.assertEqual(stats["by_type"]["cron"], 1)
        self.assertEqual(stats["by_type"]["fixed_rate"], 1)
        self.assertEqual(stats["by_status"]["created"], 2)

    def test_clear_registry(self):
        """测试清空注册表"""
        self.registry.register_task(self.task1)
        self.registry.register_task(self.task2)

        self.registry.clear()

        all_tasks = self.registry.get_all_tasks()
        self.assertEqual(len(all_tasks), 0)


if __name__ == "__main__":
    unittest.main()

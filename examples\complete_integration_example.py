#!/usr/bin/env python
# encoding: utf-8
"""
Mini-Boot框架完整集成示例

展示Bean、Context、Events、Schedule模块与新异常处理架构的集成使用
"""

import asyncio
import time
from typing import Optional

# 导入Mini-Boot核心模块
from miniboot.bean import BeanDefinition, BeanScope, create_bean_factory
from miniboot.context import DefaultApplicationContext, auto_context
# 导入异常处理架构
from miniboot.errors import BusinessError  # 异常处理装饰器; 重试和回退机制; 指标收集; 异常类
from miniboot.errors import (ExceptionAction, TimeoutError, ValidationError,
                             circuit_breaker, exception_handler,
                             exponential_backoff_retry, get_decorator_metrics,
                             performance_monitor, retry_with_backoff,
                             timeout_handler, validate_arguments)
from miniboot.events import ApplicationEvent, Event, EventPublisher
from miniboot.schedule import LambdaTask, MiniBootScheduler

# ============================================================================
# 业务实体和事件定义
# ============================================================================


class User:
    """用户实体"""

    def __init__(self, user_id: str, name: str, email: str):
        self.user_id = user_id
        self.name = name
        self.email = email

    def __str__(self):
        return f"User(id={self.user_id}, name={self.name}, email={self.email})"


class UserRegisteredEvent(ApplicationEvent):
    """用户注册事件"""

    def __init__(self, user: User):
        super().__init__()
        self.user = user

    def get_event_name(self) -> str:
        return "UserRegistered"


class UserNotificationEvent(Event):
    """用户通知事件"""

    def __init__(self, user_id: str, message: str):
        super().__init__()
        self.user_id = user_id
        self.message = message

    def get_event_name(self) -> str:
        return "UserNotification"


# ============================================================================
# 业务服务层(展示Bean模块集成)
# ============================================================================


class UserRepository:
    """用户仓储服务"""

    def __init__(self):
        self.users = {}

    @exception_handler(operation_name="用户查询", action=ExceptionAction.PROPAGATE)
    @performance_monitor(slow_threshold=0.1)
    def find_by_id(self, user_id: str) -> Optional[User]:
        """根据ID查找用户"""
        if not user_id:
            error = ValidationError("Validation failed for field 'user_id': 用户ID不能为空")
            error.context.update({
                "field_name": "user_id",
                "field_value": user_id,
                "validation_rule": "用户ID不能为空"
            })
            raise error

        # 模拟数据库查询延迟
        time.sleep(0.05)
        return self.users.get(user_id)

    @exception_handler(operation_name="用户保存")
    @retry_with_backoff(max_attempts=3, base_delay=0.1, strategy="exponential", exceptions=(ConnectionError,))
    def save(self, user: User) -> None:
        """保存用户"""
        if not user or not user.user_id:
            error = ValidationError("Validation failed for field 'user': 用户对象不能为空且必须有ID")
            error.context.update({
                "field_name": "user",
                "field_value": str(user),
                "validation_rule": "用户对象不能为空且必须有ID"
            })
            raise error

        # 模拟可能的网络异常
        import random

        if random.random() < 0.2:  # 20%概率失败
            raise ConnectionError("数据库连接失败")

        self.users[user.user_id] = user
        print(f"✅ 用户已保存: {user}")


class UserService:
    """用户业务服务"""

    def __init__(self, user_repository: UserRepository, event_publisher: EventPublisher):
        self.user_repository = user_repository
        self.event_publisher = event_publisher

    @validate_arguments(name=lambda x: isinstance(x, str) and len(x.strip()) > 0, email=lambda x: isinstance(x, str) and "@" in x)
    @exception_handler(operation_name="用户注册", action=ExceptionAction.PROPAGATE)
    @timeout_handler(timeout_seconds=5.0)
    def register_user(self, name: str, email: str) -> User:
        """注册新用户"""
        # 生成用户ID
        import uuid

        user_id = str(uuid.uuid4())[:8]

        # 检查邮箱是否已存在
        existing_users = [u for u in self.user_repository.users.values() if u.email == email]
        if existing_users:
            raise BusinessError(f"验证失败:邮箱 {email} 已被注册")

        # 创建用户
        user = User(user_id, name.strip(), email.lower())

        try:
            # 保存用户
            self.user_repository.save(user)

            # 发布用户注册事件
            event = UserRegisteredEvent(user)
            self.event_publisher.publish(event)

            return user

        except Exception as e:
            # 创建链式异常
            chained_error = BusinessError(f"用户注册失败: {name} ({email})")
            chained_error.__cause__ = e
            raise chained_error from e

    @circuit_breaker(failure_threshold=3, recovery_timeout=10.0)
    def get_user(self, user_id: str) -> Optional[User]:
        """获取用户信息"""
        return self.user_repository.find_by_id(user_id)


# ============================================================================
# 事件处理器(展示Events模块集成)
# ============================================================================


class NotificationService:
    """通知服务"""

    @exception_handler(operation_name="发送通知")
    @exponential_backoff_retry(max_attempts=3, base_delay=0.5)
    def send_notification(self, user_id: str, message: str) -> None:
        """发送通知"""
        print(f"📧 发送通知给用户 {user_id}: {message}")

        # 模拟可能的发送失败
        import random

        if random.random() < 0.3:  # 30%概率失败
            raise ConnectionError("通知服务暂时不可用")


def setup_event_handlers(event_publisher: EventPublisher, notification_service: NotificationService):
    """设置事件处理器"""

    @exception_handler(operation_name="用户注册事件处理")
    def handle_user_registered(event: UserRegisteredEvent):
        """处理用户注册事件"""
        user = event.user
        print(f"🎉 处理用户注册事件: {user.name}")

        # 发送欢迎通知
        welcome_message = f"欢迎加入,{user.name}!您的账户已成功创建."
        notification_service.send_notification(user.user_id, welcome_message)

        # 发布通知事件
        notification_event = UserNotificationEvent(user.user_id, welcome_message)
        event_publisher.publish(notification_event)

    @exception_handler(operation_name="用户通知事件处理")
    def handle_user_notification(event: UserNotificationEvent):
        """处理用户通知事件"""
        print(f"📱 通知事件已处理: 用户 {event.user_id} 收到消息")

    # 注册事件处理器
    event_publisher.subscribe(UserRegisteredEvent, handle_user_registered)
    event_publisher.subscribe(UserNotificationEvent, handle_user_notification)


# ============================================================================
# 定时任务(展示Schedule模块集成)
# ============================================================================


@exception_handler(operation_name="用户统计任务")
@performance_monitor(slow_threshold=1.0)
def user_statistics_task(user_repository: UserRepository):
    """用户统计定时任务"""
    total_users = len(user_repository.users)
    print(f"📊 用户统计报告: 当前总用户数 = {total_users}")

    # 模拟统计计算
    time.sleep(0.1)

    return f"统计完成,共 {total_users} 个用户"


# ============================================================================
# 应用主程序(展示Context模块集成)
# ============================================================================


@auto_context(auto_start=False)
async def main_application(context=None):
    """主应用程序"""
    print("🚀 启动Mini-Boot完整集成示例应用")

    # 1. 创建Bean工厂并注册服务
    print("\n📦 初始化Bean容器...")
    bean_factory = create_bean_factory()

    # 注册用户仓储
    user_repo_def = BeanDefinition(bean_name="userRepository", bean_class=UserRepository, scope=BeanScope.SINGLETON)
    bean_factory._registry.register("userRepository", user_repo_def)

    # 注册事件发布器
    event_publisher_def = BeanDefinition(bean_name="eventPublisher", bean_class=EventPublisher, scope=BeanScope.SINGLETON)
    bean_factory._registry.register("eventPublisher", event_publisher_def)

    # 注册通知服务
    notification_service_def = BeanDefinition(bean_name="notificationService", bean_class=NotificationService, scope=BeanScope.SINGLETON)
    bean_factory._registry.register("notificationService", notification_service_def)

    # 获取Bean实例
    user_repository = bean_factory.get_bean("userRepository")
    event_publisher = bean_factory.get_bean("eventPublisher")
    notification_service = bean_factory.get_bean("notificationService")

    # 创建用户服务
    user_service = UserService(user_repository, event_publisher)

    # 2. 设置事件处理器
    print("📡 设置事件处理器...")
    setup_event_handlers(event_publisher, notification_service)

    # 3. 启动调度器并添加定时任务
    print("⏰ 启动任务调度器...")
    scheduler = MiniBootScheduler(use_asyncio=False)
    scheduler.start()

    # 创建定时任务
    stats_task = LambdaTask(func=lambda: user_statistics_task(user_repository), name="user_statistics")

    try:
        scheduler.schedule_task(stats_task)
        print("✅ 定时任务已调度")
    except Exception as e:
        print(f"⚠️ 定时任务调度失败: {e}")

    # 4. 模拟业务操作
    print("\n👥 开始用户注册演示...")

    test_users = [
        ("张三", "<EMAIL>"),
        ("李四", "<EMAIL>"),
        ("王五", "<EMAIL>"),
        ("", "<EMAIL>"),  # 无效用户名
        ("赵六", "<EMAIL>"),  # 重复邮箱
    ]

    for name, email in test_users:
        try:
            print(f"\n🔄 注册用户: {name} ({email})")
            user = user_service.register_user(name, email)
            print(f"✅ 用户注册成功: {user}")

            # 查询用户
            found_user = user_service.get_user(user.user_id)
            if found_user:
                print(f"🔍 用户查询成功: {found_user}")

        except ValidationError as e:
            print(f"❌ 验证错误: {e}")
        except Exception as e:
            print(f"❌ 注册失败: {e}")

    # 5. 等待事件处理完成
    print("\n⏳ 等待事件处理完成...")
    await asyncio.sleep(1)

    # 6. 显示指标统计
    print("\n📈 异常处理指标统计:")
    metrics = get_decorator_metrics()

    print("函数调用统计:")
    for func_name, count in metrics["function_calls"].items():
        print(f"  - {func_name}: {count} 次")

    print("异常统计:")
    for exception_type, count in metrics["exceptions"].items():
        print(f"  - {exception_type}: {count} 次")

    print("执行时间统计:")
    for func_name, times in metrics["execution_times"].items():
        if times:
            avg_time = sum(times) / len(times)
            print(f"  - {func_name}: 平均 {avg_time:.3f}s")

    # 7. 清理资源
    print("\n🧹 清理资源...")
    scheduler.shutdown()

    print("\n🎉 Mini-Boot完整集成示例应用运行完成!")
    return "应用执行成功"


if __name__ == "__main__":
    # 运行应用
    result = asyncio.run(main_application())
    print(f"\n✨ 最终结果: {result}")

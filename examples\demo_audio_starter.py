#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Mini-Boot 音频播放 Starter 功能演示
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from miniboot.starters.audio import AudioService, AudioItem, AudioProperties


async def demo_basic_tts():
    """演示基本TTS功能"""
    print("🎤 === 基本TTS功能演示 ===")

    # 创建音频服务
    props = AudioProperties()
    audio_service = AudioService(props)
    await audio_service.initialize()

    # 基本语音播放
    await audio_service.speak("欢迎使用Mini-Boot音频播放功能!")
    await asyncio.sleep(0.5)

    await audio_service.speak("这是一个功能完整的音频播放Starter")
    await asyncio.sleep(0.5)

    await audio_service.speak("支持音频文件播放和文本转语音功能")

    print("✅ 基本TTS功能演示完成\n")


async def demo_multiple_texts():
    """演示多文本连续播放"""
    print("📢 === 多文本连续播放演示 ===")

    props = AudioProperties()
    audio_service = AudioService(props)
    await audio_service.initialize()

    messages = ["系统启动中...", "正在加载配置文件...", "初始化数据库连接...", "启动Web服务器...", "系统启动完成!"]

    await audio_service.speak_texts(messages, interval=0.8)

    print("✅ 多文本连续播放演示完成\n")


async def demo_mixed_sequence():
    """演示混合序列播放"""
    print("🎵 === 混合序列播放演示 ===")

    props = AudioProperties()
    audio_service = AudioService(props)
    await audio_service.initialize()

    # 使用字符串(自动识别类型)
    sequence1 = ["开始执行任务序列", "第一步:数据预处理", "第二步:模型训练", "第三步:结果验证", "任务序列执行完成"]

    print("方式1:使用字符串自动识别")
    await audio_service.play_sequence(sequence1, interval=0.6)

    await asyncio.sleep(1)

    # 使用AudioItem明确指定类型
    sequence2 = [
        AudioItem.text("现在使用AudioItem方式"),
        AudioItem.text("这样可以明确指定内容类型"),
        AudioItem.text("并且可以传递额外的参数"),
        AudioItem.text("演示完成,谢谢观看"),
    ]

    print("方式2:使用AudioItem明确指定")
    await audio_service.play_sequence(sequence2, interval=0.5)

    print("✅ 混合序列播放演示完成\n")


async def demo_parameter_adjustment():
    """演示参数调整功能"""
    print("⚙️ === 参数调整功能演示 ===")

    props = AudioProperties()
    audio_service = AudioService(props)
    await audio_service.initialize()

    # 默认参数
    await audio_service.speak("这是默认参数的语音效果")
    await asyncio.sleep(0.5)

    # 慢速语音
    print("调整为慢速语音...")
    audio_service.set_tts_properties(rate=100)
    await audio_service.speak("这是慢速语音的效果,语速为每分钟100个词")
    await asyncio.sleep(0.5)

    # 快速语音
    print("调整为快速语音...")
    audio_service.set_tts_properties(rate=200)
    await audio_service.speak("这是快速语音的效果,语速为每分钟200个词")
    await asyncio.sleep(0.5)

    # 低音量
    print("调整为低音量...")
    audio_service.set_tts_properties(rate=150, volume=0.3)
    await audio_service.speak("这是低音量的效果,音量设置为30%")
    await asyncio.sleep(0.5)

    # 恢复默认
    print("恢复默认参数...")
    audio_service.set_tts_properties(rate=150, volume=1.0)
    await audio_service.speak("参数已恢复为默认值")

    print("✅ 参数调整功能演示完成\n")


async def demo_notification_service():
    """演示通知服务场景"""
    print("📱 === 通知服务场景演示 ===")

    props = AudioProperties()
    audio_service = AudioService(props)
    await audio_service.initialize()

    # 模拟各种通知场景
    notifications = [
        ("系统通知", "您有新的邮件消息"),
        ("任务提醒", "定时任务执行完成"),
        ("安全警告", "检测到异常登录行为"),
        ("更新通知", "系统更新已安装完成"),
        ("备份提醒", "数据备份已成功完成"),
    ]

    for title, message in notifications:
        # 播放通知标题和内容
        await audio_service.speak(f"{title}:{message}")
        await asyncio.sleep(1.2)

    # 播放总结
    await audio_service.speak("所有通知播报完成")

    print("✅ 通知服务场景演示完成\n")


async def demo_status_check():
    """演示状态检查功能"""
    print("🔍 === 状态检查功能演示 ===")

    props = AudioProperties()
    audio_service = AudioService(props)
    await audio_service.initialize()

    # 检查功能状态
    audio_enabled = audio_service.is_audio_enabled()
    tts_enabled = audio_service.is_tts_enabled()

    print(f"音频播放功能启用: {audio_enabled}")
    print(f"TTS功能启用: {tts_enabled}")

    # 语音播报状态
    if audio_enabled:
        await audio_service.speak("音频播放功能已启用")

    if tts_enabled:
        await audio_service.speak("文本转语音功能已启用")

    await audio_service.speak("系统状态检查完成,所有功能正常")

    print("✅ 状态检查功能演示完成\n")


async def main():
    """主演示函数"""
    print("🚀 Mini-Boot 音频播放 Starter 功能演示")
    print("=" * 60)
    print("本演示将展示音频播放 Starter 的各种功能")
    print("请确保您的音响设备已开启")
    print("=" * 60)

    demos = [
        ("基本TTS功能", demo_basic_tts),
        ("多文本连续播放", demo_multiple_texts),
        ("混合序列播放", demo_mixed_sequence),
        ("参数调整功能", demo_parameter_adjustment),
        ("通知服务场景", demo_notification_service),
        ("状态检查功能", demo_status_check),
    ]

    for i, (name, demo_func) in enumerate(demos, 1):
        print(f"\n🎬 演示 {i}/{len(demos)}: {name}")
        print("-" * 40)

        try:
            await demo_func()
        except Exception as e:
            print(f"❌ 演示 {name} 出错: {e}")

        if i < len(demos):
            print("⏳ 准备下一个演示...")
            await asyncio.sleep(2)

    print("\n" + "=" * 60)
    print("🎉 所有功能演示完成!")
    print("📋 演示总结:")
    print("   ✅ 基本TTS语音播放")
    print("   ✅ 多文本连续播放")
    print("   ✅ 混合序列播放")
    print("   ✅ 语音参数动态调整")
    print("   ✅ 实际应用场景模拟")
    print("   ✅ 系统状态检查")
    print("\n🔊 如果您听到了所有语音播放,说明音频播放 Starter 功能完全正常!")
    print("=" * 60)


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n\n⏹️ 演示被用户中断")
    except Exception as e:
        print(f"\n❌ 演示出错: {e}")
        import traceback

        traceback.print_exc()

    print("\n👋 感谢使用 Mini-Boot 音频播放 Starter!")

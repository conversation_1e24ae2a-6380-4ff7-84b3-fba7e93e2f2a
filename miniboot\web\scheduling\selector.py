#!/usr/bin/env python
"""
执行策略选择器

基于任务特性和历史性能数据,智能选择最优的执行策略.

主要功能:
- 多维度策略评估
- 动态策略调整
- 性能反馈学习
- 策略优化建议
"""

import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger

from ..properties import AsyncOptimizationConfig


class ExecutionStrategy(Enum):
    """执行策略"""

    SYNC = "sync"  # 同步执行
    ASYNC = "async"  # 异步执行
    THREAD_POOL = "thread_pool"  # 线程池执行
    PROCESS_POOL = "process_pool"  # 进程池执行
    ADAPTIVE = "adaptive"  # 自适应执行


@dataclass
class StrategyDecision:
    """策略决策"""

    strategy: ExecutionStrategy
    executor: str
    estimated_duration: float
    confidence: float
    reason: str
    factors: Dict[str, Any] = field(default_factory=dict)
    alternatives: List[ExecutionStrategy] = field(default_factory=list)


@dataclass
class StrategyRule:
    """策略规则"""

    name: str
    condition: Callable[[Dict[str, Any]], bool]
    strategy: ExecutionStrategy
    priority: int
    confidence: float
    description: str


class StrategySelector:
    """执行策略选择器

    基于任务特性、系统状态和历史性能数据,
    智能选择最优的执行策略.
    """

    def __init__(self, config: Optional[AsyncOptimizationConfig] = None):
        """初始化执行策略选择器

        Args:
            config: 异步优化配置
        """
        self.config = config or AsyncOptimizationConfig()

        # 策略规则
        self._rules: List[StrategyRule] = []
        self._init_default_rules()

        # 性能阈值
        self._thresholds = {
            "cpu_bound_threshold": 0.05,  # CPU密集型阈值
            "io_bound_threshold": 0.01,  # I/O密集型阈值
            "lightweight_threshold": 0.001,  # 轻量级任务阈值
            "high_concurrency_threshold": 10,  # 高并发阈值
        }

        # 策略性能历史
        self._strategy_performance: Dict[str, List[float]] = {}

        # 系统状态缓存
        self._system_state_cache = {"cpu_usage": 0.0, "memory_usage": 0.0, "active_threads": 0, "last_update": 0.0}

        logger.info("StrategySelector initialized")

    def _init_default_rules(self) -> None:
        """初始化默认策略规则"""

        # 轻量级任务 -> 同步执行
        self._rules.append(
            StrategyRule(
                name="lightweight_sync",
                condition=lambda ctx: (
                    ctx.get("task_type") == "lightweight" and ctx.get("estimated_duration", 0) < self._thresholds["lightweight_threshold"]
                ),
                strategy=ExecutionStrategy.SYNC,
                priority=10,
                confidence=0.9,
                description="Lightweight tasks are best executed synchronously",
            )
        )

        # I/O密集型任务 -> 异步执行
        self._rules.append(
            StrategyRule(
                name="io_bound_async",
                condition=lambda ctx: (
                    ctx.get("task_type") == "io_bound" or (ctx.get("io_operations", False) and ctx.get("async_compatible", False))
                ),
                strategy=ExecutionStrategy.ASYNC,
                priority=8,
                confidence=0.8,
                description="I/O bound tasks benefit from async execution",
            )
        )

        # CPU密集型任务 -> 线程池执行
        self._rules.append(
            StrategyRule(
                name="cpu_bound_thread_pool",
                condition=lambda ctx: (
                    ctx.get("task_type") == "cpu_bound"
                    and ctx.get("estimated_duration", 0) > self._thresholds["cpu_bound_threshold"]
                    and ctx.get("thread_safe", True)
                ),
                strategy=ExecutionStrategy.THREAD_POOL,
                priority=7,
                confidence=0.7,
                description="CPU bound tasks can benefit from thread pool execution",
            )
        )

        # 高并发场景 -> 异步执行
        self._rules.append(
            StrategyRule(
                name="high_concurrency_async",
                condition=lambda ctx: (
                    ctx.get("concurrent_requests", 0) > self._thresholds["high_concurrency_threshold"] and ctx.get("async_compatible", False)
                ),
                strategy=ExecutionStrategy.ASYNC,
                priority=6,
                confidence=0.6,
                description="High concurrency scenarios benefit from async execution",
            )
        )

        # 混合型任务 -> 自适应执行
        self._rules.append(
            StrategyRule(
                name="mixed_adaptive",
                condition=lambda ctx: ctx.get("task_type") == "mixed",
                strategy=ExecutionStrategy.ADAPTIVE,
                priority=5,
                confidence=0.5,
                description="Mixed tasks require adaptive execution strategy",
            )
        )

    async def select_strategy(self, task_request, characteristics: Dict[str, Any]) -> StrategyDecision:
        """选择执行策略

        Args:
            task_request: 任务请求
            characteristics: 任务特性

        Returns:
            策略决策
        """
        try:
            # 1. 更新系统状态
            await self._update_system_state()

            # 2. 构建决策上下文
            context = self._build_decision_context(task_request, characteristics)

            # 3. 应用策略规则
            rule_decisions = self._apply_strategy_rules(context)

            # 4. 考虑历史性能
            performance_adjusted = self._adjust_for_performance_history(rule_decisions, context)

            # 5. 选择最佳策略
            final_decision = self._select_best_strategy(performance_adjusted, context)

            # 6. 生成决策对象
            decision = self._create_strategy_decision(final_decision, context)

            logger.debug(f"Selected strategy {decision.strategy.value} for {task_request.function.__name__}")

            return decision

        except Exception as e:
            logger.error(f"Strategy selection failed: {e}")
            return self._get_fallback_decision(task_request)

    async def _update_system_state(self) -> None:
        """更新系统状态"""
        current_time = time.time()

        # 缓存有效期5秒
        if current_time - self._system_state_cache["last_update"] < 5.0:
            return

        try:
            import psutil
            import threading

            self._system_state_cache.update(
                {
                    "cpu_usage": psutil.cpu_percent(interval=0.1),
                    "memory_usage": psutil.virtual_memory().percent,
                    "active_threads": threading.active_count(),
                    "last_update": current_time,
                }
            )
        except ImportError:
            # psutil不可用时使用默认值
            self._system_state_cache["last_update"] = current_time

    def _build_decision_context(self, task_request, characteristics: Dict[str, Any]) -> Dict[str, Any]:
        """构建决策上下文"""
        context = characteristics.copy()

        # 添加任务请求信息
        context.update(
            {
                "function_name": task_request.function.__name__,
                "arg_count": len(task_request.args),
                "kwarg_count": len(task_request.kwargs),
                "priority": task_request.priority,
                "has_timeout": task_request.timeout is not None,
            }
        )

        # 添加系统状态
        context.update(self._system_state_cache)

        # 添加历史性能信息
        function_name = task_request.function.__name__
        if function_name in self._strategy_performance:
            context["historical_performance"] = self._strategy_performance[function_name]

        return context

    def _apply_strategy_rules(self, context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """应用策略规则"""
        matching_rules = []

        for rule in self._rules:
            try:
                if rule.condition(context):
                    matching_rules.append(
                        {
                            "rule": rule,
                            "strategy": rule.strategy,
                            "priority": rule.priority,
                            "confidence": rule.confidence,
                            "reason": rule.description,
                        }
                    )
            except Exception as e:
                logger.debug(f"Rule {rule.name} evaluation failed: {e}")

        # 按优先级排序
        matching_rules.sort(key=lambda x: x["priority"], reverse=True)

        return matching_rules

    def _adjust_for_performance_history(self, rule_decisions: List[Dict[str, Any]], context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """根据历史性能调整策略"""
        function_name = context.get("function_name", "unknown")

        if function_name not in self._strategy_performance:
            return rule_decisions

        performance_history = self._strategy_performance[function_name]

        # 根据历史性能调整置信度
        for decision in rule_decisions:
            strategy_name = decision["strategy"].value

            # 查找该策略的历史性能
            strategy_performances = [p for p in performance_history if p > 0]

            if strategy_performances:
                avg_performance = sum(strategy_performances) / len(strategy_performances)

                # 性能好的策略提升置信度
                if avg_performance < 0.1:  # 快速执行
                    decision["confidence"] = min(1.0, decision["confidence"] * 1.2)
                elif avg_performance > 1.0:  # 慢速执行
                    decision["confidence"] = max(0.1, decision["confidence"] * 0.8)

        return rule_decisions

    def _select_best_strategy(self, decisions: List[Dict[str, Any]], context: Dict[str, Any]) -> Dict[str, Any]:
        """选择最佳策略"""
        if not decisions:
            return {"strategy": ExecutionStrategy.SYNC, "confidence": 0.1, "reason": "No matching rules, using default sync strategy"}

        # 计算综合评分
        for decision in decisions:
            # 基础评分 = 优先级 * 置信度
            base_score = decision["priority"] * decision["confidence"]

            # 系统负载调整
            cpu_usage = context.get("cpu_usage", 0)
            memory_usage = context.get("memory_usage", 0)

            if decision["strategy"] == ExecutionStrategy.THREAD_POOL and cpu_usage > 80:
                base_score *= 0.7  # 高CPU使用时降低线程池策略评分

            if memory_usage > 85:
                base_score *= 0.8  # 高内存使用时降低所有策略评分

            decision["final_score"] = base_score

        # 选择评分最高的策略
        best_decision = max(decisions, key=lambda d: d["final_score"])

        return best_decision

    def _create_strategy_decision(self, decision_data: Dict[str, Any], context: Dict[str, Any]) -> StrategyDecision:
        """创建策略决策对象"""
        strategy = decision_data["strategy"]

        # 确定执行器
        executor_mapping = {
            ExecutionStrategy.SYNC: "sync",
            ExecutionStrategy.ASYNC: "async",
            ExecutionStrategy.THREAD_POOL: "thread_pool",
            ExecutionStrategy.PROCESS_POOL: "process_pool",
            ExecutionStrategy.ADAPTIVE: "adaptive",
        }

        executor = executor_mapping.get(strategy, "sync")

        # 估算执行时间
        estimated_duration = context.get("estimated_duration", 0.01)

        # 调整估算时间
        if strategy == ExecutionStrategy.ASYNC and context.get("io_operations", False):
            estimated_duration *= 0.7  # 异步I/O通常更快
        elif strategy == ExecutionStrategy.THREAD_POOL:
            estimated_duration *= 1.1  # 线程池有少量开销

        # 生成替代策略
        alternatives = [d["strategy"] for d in context.get("all_decisions", []) if d["strategy"] != strategy][:3]

        return StrategyDecision(
            strategy=strategy,
            executor=executor,
            estimated_duration=estimated_duration,
            confidence=decision_data.get("confidence", 0.5),
            reason=decision_data.get("reason", "Strategy selected by rules"),
            factors={
                "system_cpu": context.get("cpu_usage", 0),
                "system_memory": context.get("memory_usage", 0),
                "task_type": context.get("task_type", "unknown"),
                "io_operations": context.get("io_operations", False),
                "async_compatible": context.get("async_compatible", False),
            },
            alternatives=alternatives,
        )

    def _get_fallback_decision(self, task_request) -> StrategyDecision:
        """获取回退决策"""
        import asyncio

        # 根据函数类型选择回退策略
        if asyncio.iscoroutinefunction(task_request.function):
            strategy = ExecutionStrategy.ASYNC
            executor = "async"
        else:
            strategy = ExecutionStrategy.SYNC
            executor = "sync"

        return StrategyDecision(
            strategy=strategy,
            executor=executor,
            estimated_duration=0.01,
            confidence=0.1,
            reason="Fallback strategy due to selection error",
            factors={"error": True},
            alternatives=[],
        )

    def record_execution_result(self, function_name: str, strategy: ExecutionStrategy, execution_time: float, success: bool) -> None:
        """记录执行结果

        Args:
            function_name: 函数名称
            strategy: 使用的策略
            execution_time: 执行时间
            success: 是否成功
        """
        if function_name not in self._strategy_performance:
            self._strategy_performance[function_name] = []

        # 只记录成功的执行时间
        if success:
            self._strategy_performance[function_name].append(execution_time)

            # 限制历史记录大小
            if len(self._strategy_performance[function_name]) > 100:
                self._strategy_performance[function_name].pop(0)

    def add_custom_rule(self, rule: StrategyRule) -> None:
        """添加自定义策略规则

        Args:
            rule: 策略规则
        """
        self._rules.append(rule)
        # 重新排序规则
        self._rules.sort(key=lambda r: r.priority, reverse=True)

        logger.info(f"Added custom strategy rule: {rule.name}")

    def get_strategy_statistics(self) -> Dict[str, Any]:
        """获取策略统计信息"""
        total_functions = len(self._strategy_performance)

        if total_functions == 0:
            return {"total_functions": 0, "total_executions": 0}

        total_executions = sum(len(performances) for performances in self._strategy_performance.values())

        # 计算平均性能
        all_performances = []
        for performances in self._strategy_performance.values():
            all_performances.extend(performances)

        avg_execution_time = sum(all_performances) / len(all_performances) if all_performances else 0

        return {
            "total_functions": total_functions,
            "total_executions": total_executions,
            "avg_execution_time": round(avg_execution_time, 4),
            "functions_with_history": list(self._strategy_performance.keys()),
            "rule_count": len(self._rules),
            "system_state": self._system_state_cache,
        }

    def optimize_thresholds(self) -> None:
        """优化策略阈值"""
        # 基于历史性能数据优化阈值
        # 这里可以实现更复杂的优化算法

        if not self._strategy_performance:
            return

        all_times = []
        for performances in self._strategy_performance.values():
            all_times.extend(performances)

        if len(all_times) < 10:
            return

        # 根据执行时间分布调整阈值
        all_times.sort()

        # 更新轻量级任务阈值(10%分位数)
        lightweight_threshold = all_times[len(all_times) // 10]
        self._thresholds["lightweight_threshold"] = min(0.001, lightweight_threshold)

        # 更新CPU密集型阈值(90%分位数)
        cpu_bound_threshold = all_times[len(all_times) * 9 // 10]
        self._thresholds["cpu_bound_threshold"] = max(0.05, cpu_bound_threshold)

        logger.info("Strategy thresholds optimized based on historical performance")

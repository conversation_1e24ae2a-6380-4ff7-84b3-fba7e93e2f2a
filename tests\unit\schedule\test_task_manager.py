#!/usr/bin/env python
# encoding: utf-8
"""
@author: cz
@description: 任务管理功能单元测试
"""

import asyncio
import unittest

from miniboot.schedule import TaskExecutionMetrics, TaskFactory, TaskType, TaskWrapper


class ServiceTest:
    """测试服务类"""

    def __init__(self):
        self.execution_count = 0
        self.results = []

    def sync_method(self):
        """同步方法"""
        self.execution_count += 1
        result = f"sync_result_{self.execution_count}"
        self.results.append(result)
        return result

    async def async_method(self):
        """异步方法"""
        self.execution_count += 1
        result = f"async_result_{self.execution_count}"
        self.results.append(result)
        return result

    def error_method(self):
        """错误方法"""
        raise ValueError("Test error")

    def reset(self):
        """重置状态"""
        self.execution_count = 0
        self.results.clear()


class TestTaskFactory(unittest.TestCase):
    """任务工厂测试"""

    def setUp(self):
        """设置测试环境"""
        self.service = ServiceTest()

    def tearDown(self):
        """清理测试环境"""
        self.service.reset()

    def test_create_sync_task(self):
        """测试创建同步任务"""
        from miniboot.schedule import ScheduledConfig

        config = ScheduledConfig(fixed_rate="1s")
        task = TaskFactory.create_lambda_task(func=self.service.sync_method, config=config, name="sync_task")

        self.assertIsNotNone(task)
        self.assertEqual(task.name, "sync_task")
        self.assertEqual(task.task_type, TaskType.FIXED_RATE)

    def test_create_async_task(self):
        """测试创建异步任务"""
        from miniboot.schedule import ScheduledConfig

        config = ScheduledConfig(cron="0 */5 * * * *")
        task = TaskFactory.create_lambda_task(func=self.service.async_method, config=config, name="async_task")

        self.assertIsNotNone(task)
        self.assertEqual(task.name, "async_task")
        self.assertEqual(task.task_type, TaskType.CRON)

    def test_task_execution(self):
        """测试任务执行"""
        import asyncio

        from miniboot.schedule import ScheduledConfig

        config = ScheduledConfig(fixed_rate="1s")
        task = TaskFactory.create_lambda_task(func=self.service.sync_method, config=config, name="exec_task")

        # 执行任务（异步）
        result = asyncio.run(task.execute())

        self.assertEqual(self.service.execution_count, 1)
        self.assertEqual(result, "sync_result_1")

    def test_async_task_execution(self):
        """测试异步任务执行"""

        async def test():
            task = TaskFactory.create_task(task_id="async_exec_task", func=self.service.async_method, task_type=TaskType.ASYNC)

            # 执行异步任务
            result = await task.execute_async()

            self.assertEqual(self.service.execution_count, 1)
            self.assertEqual(result, "async_result_1")

        # 运行异步测试
        asyncio.run(test())

    def test_task_error_handling(self):
        """测试任务错误处理"""
        task = TaskFactory.create_task(task_id="error_task", func=self.service.error_method, task_type=TaskType.SYNC)

        # 执行会出错的任务
        with self.assertRaises(ValueError):
            task.execute()


class TestTaskWrapper(unittest.TestCase):
    """任务包装器测试"""

    def setUp(self):
        """设置测试环境"""
        self.service = ServiceTest()

    def tearDown(self):
        """清理测试环境"""
        self.service.reset()

    def test_task_wrapper_creation(self):
        """测试任务包装器创建"""
        # 创建一个模拟的ScheduledTask
        from miniboot.schedule import ScheduledTask, TaskType

        task = ScheduledTask(task_id="wrapper_task", func=self.service.sync_method, task_type=TaskType.SYNC)

        # 创建TaskWrapper需要task和scheduler_ref
        wrapper = TaskWrapper(task, scheduler_ref=None)

        self.assertIsNotNone(wrapper)
        self.assertEqual(wrapper.task.task_id, "wrapper_task")

    def test_task_wrapper_execution(self):
        """测试任务包装器执行"""
        # 创建一个模拟的ScheduledTask
        from miniboot.schedule import ScheduledTask, TaskType

        task = ScheduledTask(task_id="wrapper_exec_task", func=self.service.sync_method, task_type=TaskType.SYNC)

        wrapper = TaskWrapper(task, scheduler_ref=None)

        # 执行任务（使用同步执行方法）
        result = wrapper.execute_sync()

        self.assertEqual(self.service.execution_count, 1)
        self.assertEqual(result, "sync_result_1")

    def test_task_wrapper_metrics(self):
        """测试任务包装器指标"""
        # 创建一个模拟的ScheduledTask
        from miniboot.schedule import ScheduledTask, TaskType

        task = ScheduledTask(task_id="metrics_task", func=self.service.sync_method, task_type=TaskType.SYNC)

        wrapper = TaskWrapper(task, scheduler_ref=None)

        # 执行任务
        wrapper.execute_sync()

        # 检查指标
        metrics = wrapper.get_metrics()
        self.assertIsNotNone(metrics)
        self.assertGreater(metrics["total_executions"], 0)


class TestTaskExecutionMetrics(unittest.TestCase):
    """任务执行指标测试"""

    def test_metrics_creation(self):
        """测试指标创建"""
        metrics = TaskExecutionMetrics()

        self.assertEqual(metrics.total_executions, 0)
        self.assertEqual(metrics.successful_executions, 0)
        self.assertEqual(metrics.failed_executions, 0)

    def test_metrics_update(self):
        """测试指标更新"""
        metrics = TaskExecutionMetrics()

        # 记录成功执行
        start_time = metrics.record_execution_start()
        metrics.record_execution_success(start_time)

        self.assertEqual(metrics.total_executions, 1)
        self.assertEqual(metrics.successful_executions, 1)
        self.assertEqual(metrics.failed_executions, 0)
        self.assertGreater(metrics.total_execution_time, 0)

    def test_metrics_error_recording(self):
        """测试错误记录"""
        metrics = TaskExecutionMetrics()

        # 记录错误执行
        start_time = metrics.record_execution_start()
        metrics.record_execution_failure(start_time, Exception("Test error"))

        self.assertEqual(metrics.total_executions, 1)
        self.assertEqual(metrics.successful_executions, 0)
        self.assertEqual(metrics.failed_executions, 1)

    def test_metrics_average_duration(self):
        """测试平均执行时间"""
        metrics = TaskExecutionMetrics()

        # 记录多次执行
        start_time1 = metrics.record_execution_start()
        import time

        time.sleep(0.01)  # 模拟执行时间
        metrics.record_execution_success(start_time1)

        start_time2 = metrics.record_execution_start()
        time.sleep(0.01)  # 模拟执行时间
        metrics.record_execution_success(start_time2)

        avg_duration = metrics.get_average_execution_time()
        self.assertGreater(avg_duration, 0)

    def test_metrics_success_rate(self):
        """测试成功率"""
        metrics = TaskExecutionMetrics()

        # 记录执行
        start_time1 = metrics.record_execution_start()
        metrics.record_execution_success(start_time1)

        start_time2 = metrics.record_execution_start()
        metrics.record_execution_failure(start_time2, Exception("Test error"))

        start_time3 = metrics.record_execution_start()
        metrics.record_execution_success(start_time3)

        success_rate = metrics.get_success_rate()
        self.assertAlmostEqual(success_rate, 0.67, places=2)


if __name__ == "__main__":
    unittest.main()

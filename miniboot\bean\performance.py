#!/usr/bin/env python
# encoding: utf-8
"""
Bean创建性能优化模块
提供Bean创建过程的性能优化配置和优化器
"""

import inspect
import threading
import time
from dataclasses import dataclass, field
from functools import lru_cache
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Type
from weakref import WeakKeyDictionary

from ..utils.singleton import SingletonMeta
# 延迟导入避免循环依赖
# from ..processor.cache import TypeCheckCache
# from .proxy import MethodCache


@dataclass
class BeanPerformanceConfig:
    """Bean创建性能优化配置

    提供Bean创建过程的全面性能优化选项：
    - 构造函数缓存：缓存构造函数信息和调用结果
    - 类型检查缓存：缓存issubclass等类型检查结果
    - 依赖解析缓存：缓存依赖解析结果
    - 性能监控：监控Bean创建的关键性能指标

    所有优化选项默认启用，确保开箱即用的性能提升。
    """

    # Bean创建整体优化开关
    enable_bean_creation_optimization: bool = True

    # 构造函数优化配置
    enable_constructor_caching: bool = True  # 缓存构造函数信息
    enable_constructor_call_optimization: bool = True  # 优化构造函数调用
    constructor_cache_size: int = 500  # 构造函数缓存大小

    # 类型检查优化配置
    enable_type_check_caching: bool = True  # 缓存类型检查结果
    enable_issubclass_caching: bool = True  # 缓存issubclass调用结果
    type_check_cache_size: int = 1000  # 类型检查缓存大小

    # 依赖解析优化配置
    enable_dependency_resolution_caching: bool = True  # 缓存依赖解析结果
    enable_dependency_graph_caching: bool = True  # 缓存依赖图
    dependency_cache_size: int = 500  # 依赖解析缓存大小

    # 反射调用优化配置
    enable_reflection_optimization: bool = True  # 启用反射调用优化
    enable_method_signature_caching: bool = True  # 缓存方法签名
    enable_type_hints_caching: bool = True  # 缓存类型提示
    reflection_cache_size: int = 1000  # 反射缓存大小

    # 性能监控配置
    enable_performance_monitoring: bool = False  # 启用性能监控
    monitor_bean_creation_time: bool = False  # 监控Bean创建时间
    monitor_cache_hit_rate: bool = False  # 监控缓存命中率
    performance_threshold: float = 0.1  # 性能告警阈值（秒）

    # 缓存清理配置
    enable_cache_cleanup: bool = True  # 启用缓存清理
    cache_cleanup_interval: int = 300  # 缓存清理间隔（秒）
    max_cache_age: int = 3600  # 缓存最大存活时间（秒）


class BeanCreationOptimizer(metaclass=SingletonMeta):
    """Bean创建优化器

    提供Bean创建过程的全面性能优化，包括：
    - 构造函数信息缓存和调用优化
    - 类型检查结果缓存
    - 依赖解析结果缓存
    - 反射调用优化
    - 性能监控和统计
    """

    def __init__(self, config: Optional[BeanPerformanceConfig] = None):
        if not hasattr(self, '_initialized'):
            self.config = config or BeanPerformanceConfig()

            # 线程安全锁
            self._lock = threading.RLock()

            # 集成现有的缓存组件（延迟初始化）
            self._type_check_cache_component = None
            self._method_cache_component = None

            # 构造函数缓存
            self._constructor_info_cache: Dict[Type, Dict[str, Any]] = {}
            self._constructor_signature_cache: Dict[Type, inspect.Signature] = {}
            self._constructor_type_hints_cache: Dict[Type, Dict[str, Type]] = {}

            # 类型检查缓存（补充现有组件）
            self._type_compatibility_cache: Dict[Tuple[Type, Type], bool] = {}
            self._issubclass_cache: Dict[Tuple[Type, Type], bool] = {}

            # 依赖解析缓存
            self._dependency_resolution_cache: Dict[Type, List[str]] = {}
            self._dependency_graph_cache: Dict[Type, Dict[str, Any]] = {}

            # 性能统计
            self._performance_stats = {
                'constructor_cache_hits': 0,
                'constructor_cache_misses': 0,
                'type_check_cache_hits': 0,
                'type_check_cache_misses': 0,
                'dependency_cache_hits': 0,
                'dependency_cache_misses': 0,
                'total_bean_creations': 0,
                'total_creation_time': 0.0
            }

            self._initialized = True

    def _ensure_cache_components(self) -> None:
        """确保缓存组件已初始化（延迟初始化）"""
        if self._type_check_cache_component is None:
            try:
                from ..processor.cache import TypeCheckCache
                self._type_check_cache_component = TypeCheckCache(self.config.type_check_cache_size)
            except Exception:
                # 如果初始化失败，使用空对象模式
                self._type_check_cache_component = None

        if self._method_cache_component is None:
            try:
                from .proxy import MethodCache
                self._method_cache_component = MethodCache(self.config.reflection_cache_size)
            except Exception:
                # 如果初始化失败，使用空对象模式
                self._method_cache_component = None

    def get_constructor_info(self, cls: Type) -> Dict[str, Any]:
        """获取构造函数信息（缓存版本）"""
        if not self.config.enable_constructor_caching:
            return self._get_constructor_info_direct(cls)

        with self._lock:
            if cls not in self._constructor_info_cache:
                info = self._get_constructor_info_direct(cls)

                # 检查缓存大小
                if len(self._constructor_info_cache) >= self.config.constructor_cache_size:
                    # 简单的LRU清理：删除最旧的条目
                    oldest_key = next(iter(self._constructor_info_cache))
                    del self._constructor_info_cache[oldest_key]

                self._constructor_info_cache[cls] = info
                self._performance_stats['constructor_cache_misses'] += 1
            else:
                self._performance_stats['constructor_cache_hits'] += 1

            return self._constructor_info_cache[cls].copy()

    def _get_constructor_info_direct(self, cls: Type) -> Dict[str, Any]:
        """直接获取构造函数信息"""
        try:
            constructor = cls.__init__
            signature = inspect.signature(constructor)
            type_hints = {}

            try:
                import typing
                type_hints = typing.get_type_hints(constructor)
            except Exception:
                # 如果获取类型提示失败，使用空字典
                pass

            return {
                'constructor': constructor,
                'signature': signature,
                'type_hints': type_hints,
                'parameters': list(signature.parameters.keys())[1:],  # 排除self
                'is_async': inspect.iscoroutinefunction(constructor)
            }
        except Exception:
            return {
                'constructor': None,
                'signature': None,
                'type_hints': {},
                'parameters': [],
                'is_async': False
            }

    def check_type_compatibility(self, source_type: Type, target_type: Type) -> bool:
        """检查类型兼容性（缓存版本）"""
        if not self.config.enable_type_check_caching:
            return self._check_type_compatibility_direct(source_type, target_type)

        cache_key = (source_type, target_type)

        with self._lock:
            if cache_key not in self._type_check_cache:
                result = self._check_type_compatibility_direct(source_type, target_type)

                # 检查缓存大小
                if len(self._type_check_cache) >= self.config.type_check_cache_size:
                    # 简单的LRU清理
                    oldest_key = next(iter(self._type_check_cache))
                    del self._type_check_cache[oldest_key]

                self._type_check_cache[cache_key] = result
                self._performance_stats['type_check_cache_misses'] += 1
            else:
                self._performance_stats['type_check_cache_hits'] += 1

            return self._type_check_cache[cache_key]

    def _check_type_compatibility_direct(self, source_type: Type, target_type: Type) -> bool:
        """直接检查类型兼容性"""
        try:
            return issubclass(source_type, target_type)
        except TypeError:
            # 处理非类类型的情况
            return source_type == target_type

    def get_dependency_info(self, cls: Type) -> List[str]:
        """获取依赖信息（缓存版本）"""
        if not self.config.enable_dependency_resolution_caching:
            return self._get_dependency_info_direct(cls)

        with self._lock:
            if cls not in self._dependency_resolution_cache:
                dependencies = self._get_dependency_info_direct(cls)

                # 检查缓存大小
                if len(self._dependency_resolution_cache) >= self.config.dependency_cache_size:
                    oldest_key = next(iter(self._dependency_resolution_cache))
                    del self._dependency_resolution_cache[oldest_key]

                self._dependency_resolution_cache[cls] = dependencies
                self._performance_stats['dependency_cache_misses'] += 1
            else:
                self._performance_stats['dependency_cache_hits'] += 1

            return self._dependency_resolution_cache[cls].copy()

    def _get_dependency_info_direct(self, cls: Type) -> List[str]:
        """直接获取依赖信息"""
        constructor_info = self.get_constructor_info(cls)
        return constructor_info.get('parameters', [])

    def get_method_signature_cached(self, cls: Type, method_name: str) -> Optional[inspect.Signature]:
        """获取方法签名（使用集成的缓存组件）"""
        if not self.config.enable_method_signature_caching:
            try:
                method = getattr(cls, method_name)
                return inspect.signature(method)
            except Exception:
                return None

        # 确保缓存组件已初始化
        self._ensure_cache_components()

        # 使用集成的TypeCheckCache组件
        if self._type_check_cache_component:
            return self._type_check_cache_component.get_method_signature(cls, method_name)
        else:
            # 回退到直接调用
            try:
                method = getattr(cls, method_name)
                return inspect.signature(method)
            except Exception:
                return None

    def get_type_hints_cached(self, cls: Type) -> Dict[str, Type]:
        """获取类型提示（使用集成的缓存组件）"""
        if not self.config.enable_type_hints_caching:
            try:
                import typing
                return typing.get_type_hints(cls)
            except Exception:
                return {}

        # 使用集成的TypeCheckCache组件
        return self._type_check_cache_component.get_type_hints(cls)

    def cache_method_call_result(self, method: Callable, args: tuple, result: Any) -> None:
        """缓存方法调用结果（使用集成的MethodCache组件）"""
        if self.config.enable_constructor_call_optimization:
            cache_key = (id(method), args)
            self._method_cache_component.put(cache_key, result)

    def get_cached_method_result(self, method: Callable, args: tuple) -> Optional[Any]:
        """获取缓存的方法调用结果"""
        if self.config.enable_constructor_call_optimization:
            cache_key = (id(method), args)
            return self._method_cache_component.get(cache_key)
        return None

    def clear_cache(self) -> None:
        """清空所有缓存"""
        with self._lock:
            # 清理本地缓存
            self._constructor_info_cache.clear()
            self._constructor_signature_cache.clear()
            self._constructor_type_hints_cache.clear()
            self._type_compatibility_cache.clear()
            self._issubclass_cache.clear()
            self._dependency_resolution_cache.clear()
            self._dependency_graph_cache.clear()

            # 清理集成的缓存组件
            if hasattr(self, '_type_check_cache_component'):
                self._type_check_cache_component.clear_cache()
            if hasattr(self, '_method_cache_component'):
                self._method_cache_component.clear()

    def get_performance_stats(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self._lock:
            stats = self._performance_stats.copy()

            # 计算缓存命中率
            total_constructor_requests = stats['constructor_cache_hits'] + stats['constructor_cache_misses']
            total_type_check_requests = stats['type_check_cache_hits'] + stats['type_check_cache_misses']
            total_dependency_requests = stats['dependency_cache_hits'] + stats['dependency_cache_misses']

            stats['constructor_cache_hit_rate'] = (
                stats['constructor_cache_hits'] / total_constructor_requests
                if total_constructor_requests > 0 else 0.0
            )
            stats['type_check_cache_hit_rate'] = (
                stats['type_check_cache_hits'] / total_type_check_requests
                if total_type_check_requests > 0 else 0.0
            )
            stats['dependency_cache_hit_rate'] = (
                stats['dependency_cache_hits'] / total_dependency_requests
                if total_dependency_requests > 0 else 0.0
            )

            # 添加缓存大小信息
            stats['cache_sizes'] = {
                'constructor_info': len(self._constructor_info_cache),
                'type_compatibility': len(self._type_compatibility_cache),
                'dependency_resolution': len(self._dependency_resolution_cache),
                'issubclass_cache': len(self._issubclass_cache)
            }

            # 添加集成组件的统计信息
            if hasattr(self, '_type_check_cache_component') and self._type_check_cache_component is not None:
                stats['integrated_type_check_cache'] = {
                    'enabled': True,
                    'max_size': getattr(self._type_check_cache_component, '_max_size', 0)
                }
            else:
                stats['integrated_type_check_cache'] = {'enabled': False}

            if hasattr(self, '_method_cache_component') and self._method_cache_component is not None:
                stats['integrated_method_cache'] = {
                    'enabled': True,
                    'current_size': len(self._method_cache_component.cache),
                    'max_size': self._method_cache_component.max_size,
                    'access_counter': self._method_cache_component.access_counter
                }
            else:
                stats['integrated_method_cache'] = {'enabled': False}

            return stats

    def record_bean_creation(self, creation_time: float) -> None:
        """记录Bean创建性能"""
        with self._lock:
            self._performance_stats['total_bean_creations'] += 1
            self._performance_stats['total_creation_time'] += creation_time


# BeanCreationOptimizer 使用单例模式，直接实例化即可获取单例
#
# 使用方式:
#   optimizer = BeanCreationOptimizer()  # 使用默认配置
#   optimizer = BeanCreationOptimizer(custom_config)  # 使用自定义配置

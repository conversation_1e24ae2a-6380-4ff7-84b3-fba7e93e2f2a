#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: DefaultBeanFactory - BeanFactory接口的统一实现
"""

import asyncio
import inspect
import threading
from contextlib import contextmanager
from typing import Any, Dict, List, Optional, TypeVar

from loguru import logger

from .base import BeanDefinitionRegistry, BeanFactory
from .cache import ThreeLevelCache
from .definition import BeanDefinition, BeanScope
from .registry import DefaultBeanDefinitionRegistry

T = TypeVar('T')


class DefaultBeanFactory(BeanFactory):
    """BeanFactory的默认实现

    统一处理同步、异步、代理等所有功能，内置环境检测和自动适配。
    这是Bean模块的核心实现，替代了原有的多个工厂类。

    核心特性：
    1. 统一接口：只有一个get_bean方法，自动处理所有复杂性
    2. 环境检测：自动检测同步/异步执行环境
    3. 智能代理：根据需要自动创建代理对象
    4. 三级缓存：完整的Spring式三级缓存机制
    5. 循环依赖：自动解决复杂的循环依赖问题
    """

    def __init__(self, registry: Optional[BeanDefinitionRegistry] = None,
                 dependency_graph: Optional["DependencyGraph"] = None):
        """初始化DefaultBeanFactory

        Args:
            registry: Bean定义注册表，如果为None则创建默认实现
            dependency_graph: 依赖图，如果为None则创建默认实现
        """
        # 初始化依赖组件
        if registry is None:
            registry = DefaultBeanDefinitionRegistry()
        if dependency_graph is None:
            from .utils import DependencyGraph
            dependency_graph = DependencyGraph()

        self._registry = registry
        self._dependency_graph = dependency_graph

        # 统一三级缓存
        self._cache = ThreeLevelCache()

        # 依赖注入器（延迟初始化）
        self._injector = None

        # Bean创建优化器（延迟初始化）
        self._optimizer = None

        # 性能监控统计
        self._performance_stats = {
            'total_beans_created': 0,
            'cache_hits': 0,
            'cache_misses': 0,
            'total_creation_time': 0.0
        }

        # 线程安全锁
        self._lock = threading.RLock()

        # 代理缓存
        self._proxy_cache: Dict[str, Any] = {}

        # Bean后置处理器列表
        self._post_processors: List["BeanPostProcessor"] = []

        # 单例Bean缓存
        self._singletons: Dict[str, Any] = {}

    def _get_optimizer(self):
        """获取Bean创建优化器（延迟初始化）"""
        if self._optimizer is None:
            try:
                from .performance import BeanCreationOptimizer
                self._optimizer = BeanCreationOptimizer()
                logger.debug("Bean creation optimizer initialized successfully")
            except Exception as e:
                # 如果优化器初始化失败，使用空对象模式
                logger.warning(f"Failed to initialize bean creation optimizer: {e}")
                self._optimizer = None
        return self._optimizer

    def get_performance_stats(self) -> dict:
        """获取Bean工厂的性能统计信息"""
        stats = self._performance_stats.copy()

        # 计算平均创建时间
        if stats['total_beans_created'] > 0:
            stats['avg_creation_time'] = stats['total_creation_time'] / stats['total_beans_created']
        else:
            stats['avg_creation_time'] = 0.0

        # 计算缓存命中率
        total_cache_requests = stats['cache_hits'] + stats['cache_misses']
        if total_cache_requests > 0:
            stats['cache_hit_rate'] = stats['cache_hits'] / total_cache_requests
        else:
            stats['cache_hit_rate'] = 0.0

        # 添加优化器统计信息
        optimizer = self._get_optimizer()
        if optimizer:
            stats['optimizer_stats'] = optimizer.get_performance_stats()

        return stats

    def get(self, name: str, required_type: Optional[type] = None) -> Any:
        """获取Bean实例 - 统一入口

        自动检测环境并返回合适的Bean实例或代理对象。
        用户无需关心同步/异步，框架自动处理所有复杂性。

        Args:
            name: Bean名称，必须是已注册的Bean名称
            required_type: 期望的Bean类型，用于类型检查（可选）

        Returns:
            Bean实例或代理对象，具体类型取决于Bean定义和环境

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
            BeanCreationError: 如果Bean创建失败
            TypeError: 如果Bean类型不匹配required_type
        """
        # 首先检查单例 Bean
        if name in self._singletons:
            singleton = self._singletons[name]
            # 类型检查
            if required_type and not isinstance(singleton, required_type):
                raise TypeError(f"Bean '{name}' is not of required type {required_type}")
            return singleton

        # 然后检查 Bean 定义
        if not self._registry.has_definition(name):
            raise KeyError(f"No bean definition found for name: {name}")

        bean_definition = self._registry.get_definition(name)

        # 类型检查（使用优化器缓存）
        if required_type:
            optimizer = self._get_optimizer()
            if optimizer and optimizer.config.enable_type_check_caching:
                # 使用缓存的类型检查
                is_compatible = optimizer.check_type_compatibility(bean_definition.bean_class, required_type)
                self._performance_stats['cache_hits'] += 1
            else:
                # 回退到直接检查
                is_compatible = issubclass(bean_definition.bean_class, required_type)
                self._performance_stats['cache_misses'] += 1

            if not is_compatible:
                raise TypeError(
                    f"Bean '{name}' is of type {bean_definition.bean_class.__name__}, "
                    f"but required type is {required_type.__name__}"
                )

        # 根据作用域创建Bean
        if bean_definition.singleton():
            return self._get_singleton_bean(name, bean_definition)
        elif bean_definition.prototype():
            return self._create_prototype_bean(name, bean_definition)
        else:
            # 其他作用域（如REQUEST、SESSION等）
            return self._get_scoped_bean(name, bean_definition)

    def contains(self, name: str) -> bool:
        """检查Bean是否存在

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean存在返回True，否则返回False
        """
        # 检查单例 Bean 或 Bean 定义
        return name in self._singletons or self._registry.has_definition(name)

    def names(self) -> List[str]:
        """获取所有Bean名称

        Returns:
            List[str]: Bean名称列表，如果没有Bean则返回空列表
        """
        # 合并 Bean 定义和单例 Bean 的名称
        definition_names = self._registry.names()
        singleton_names = list(self._singletons.keys())

        # 去重并返回
        all_names = list(set(definition_names + singleton_names))
        return sorted(all_names)

    def singleton(self, name: str) -> bool:
        """检查Bean是否为单例

        Args:
            name: Bean名称

        Returns:
            bool: 如果Bean是单例返回True，否则返回False

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
        """
        if not self._registry.has_definition(name):
            raise KeyError(f"No bean definition found for name: {name}")

        bean_definition = self._registry.get_definition(name)
        return bean_definition.singleton()

    def type(self, name: str) -> Optional[type]:
        """获取Bean的类型

        Args:
            name: Bean名称

        Returns:
            Optional[type]: Bean的类型，如果无法确定则返回None

        Raises:
            NoSuchBeanDefinitionError: 如果Bean不存在
        """
        if not self._registry.has_definition(name):
            return None

        bean_definition = self._registry.get_definition(name)
        return bean_definition.bean_class

    def _get_singleton_bean(self, name: str, bean_definition: BeanDefinition) -> Any:
        """获取单例Bean

        使用三级缓存机制，支持循环依赖解决。

        Args:
            name: Bean名称
            bean_definition: Bean定义

        Returns:
            Bean实例
        """
        # 检查缓存
        cached_bean = self._cache.get(name)
        if cached_bean is not None:
            return self._wrap_if_needed(cached_bean, name, bean_definition)

        # 检查循环依赖
        if self._cache.creating(name):
            raise RuntimeError(f"Circular dependency detected for bean: {name}")

        try:
            # 标记正在创建
            self._cache.set_creating(name, True)
            logger.debug(f"Starting bean creation for: {name}")

            # 创建Bean
            bean = self._create_bean(name, bean_definition)

            # 缓存到一级缓存
            self._cache.put(name, bean)

            return self._wrap_if_needed(bean, name, bean_definition)

        finally:
            # 清除创建标记
            self._cache.set_creating(name, False)

    def _create_prototype_bean(self, name: str, bean_definition: BeanDefinition) -> Any:
        """创建原型Bean

        每次调用都创建新的实例，不使用缓存。

        Args:
            name: Bean名称
            bean_definition: Bean定义

        Returns:
            Bean实例
        """
        # 原型Bean不支持循环依赖
        if self._cache.creating(name):
            raise RuntimeError(f"Circular dependency detected for prototype bean: {name}")

        try:
            # 标记正在创建
            self._cache.set_creating(name, True)
            logger.debug(f"Starting prototype bean creation for: {name}")

            # 创建Bean
            bean = self._create_bean(name, bean_definition)

            return self._wrap_if_needed(bean, name, bean_definition)

        finally:
            # 清除创建标记
            self._cache.set_creating(name, False)

    def _get_scoped_bean(self, name: str, bean_definition: BeanDefinition) -> Any:
        """获取作用域Bean

        处理REQUEST、SESSION等Web作用域Bean。

        Args:
            name: Bean名称
            bean_definition: Bean定义

        Returns:
            Bean实例
        """
        # TODO: 实现Web作用域支持
        # 目前简单处理为原型模式
        return self._create_prototype_bean(name, bean_definition)

    def _create_bean(self, name: str, bean_definition: BeanDefinition) -> Any:
        """创建Bean实例

        完整的Bean创建流程：实例化 -> 依赖注入 -> 初始化

        Args:
            name: Bean名称
            bean_definition: Bean定义

        Returns:
            Bean实例
        """
        import time
        start_time = time.time()

        try:
            # 1. 实例化Bean
            bean = self._instantiate_bean(bean_definition)

            # 2. 放入三级缓存（用于循环依赖解决）
            if bean_definition.singleton():
                self._cache.put_factory(name, lambda: bean)

            # 3. 依赖注入
            self._inject_dependencies(bean, bean_definition)

            # 4. 初始化Bean
            self._initialize_bean(bean, bean_definition)

            # 5. 性能统计
            creation_time = time.time() - start_time
            self._performance_stats['total_beans_created'] += 1
            self._performance_stats['total_creation_time'] += creation_time

            # 记录到优化器
            optimizer = self._get_optimizer()
            if optimizer and optimizer.config.enable_performance_monitoring:
                optimizer.record_bean_creation(creation_time)

            return bean
        except Exception as e:
            # 记录失败的Bean创建
            creation_time = time.time() - start_time
            self._performance_stats['total_creation_time'] += creation_time
            logger.error(f"Failed to create bean '{bean_definition.bean_name}': {e}")
            raise

    def _instantiate_bean(self, bean_definition: BeanDefinition) -> Any:
        """实例化Bean

        根据Bean定义创建Bean实例。

        Args:
            bean_definition: Bean定义

        Returns:
            Bean实例
        """
        bean_class = bean_definition.bean_class

        # 处理构造函数参数
        constructor_args = []
        if bean_definition.has_args():
            for arg in bean_definition.constructor_args:
                if arg.is_reference():
                    # Bean引用
                    dep_bean = self.get(arg.ref)
                    constructor_args.append(dep_bean)
                else:
                    # 直接值
                    constructor_args.append(arg.value)

        # 检查构造函数是否为异步（使用优化器缓存）
        optimizer = self._get_optimizer()
        if optimizer and optimizer.config.enable_constructor_caching:
            # 使用缓存的构造函数信息
            constructor_info = optimizer.get_constructor_info(bean_class)
            is_async = constructor_info.get('is_async', False)
            self._performance_stats['cache_hits'] += 1
        else:
            # 回退到直接检查
            is_async = inspect.iscoroutinefunction(bean_class.__init__)
            self._performance_stats['cache_misses'] += 1

        if is_async:
            # 异步构造函数
            if self._is_async_environment():
                # 在异步环境中，返回协程
                return bean_class(*constructor_args)
            else:
                # 在同步环境中，运行协程
                return asyncio.run(bean_class(*constructor_args))
        else:
            # 同步构造函数
            return bean_class(*constructor_args)

    def _inject_dependencies(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """注入依赖

        Args:
            bean: Bean实例
            bean_definition: Bean定义
        """
        # 延迟初始化依赖注入器
        if self._injector is None:
            from .injector import DependencyInjector
            self._injector = DependencyInjector(self)

        # 使用依赖注入器进行注入
        self._injector.inject(bean, bean_definition)

    def _initialize_bean(self, bean: Any, bean_definition: BeanDefinition) -> None:
        """初始化Bean

        Args:
            bean: Bean实例
            bean_definition: Bean定义
        """
        # 调用初始化方法
        if bean_definition.has_init():
            init_method = getattr(bean, bean_definition.init_method_name, None)
            if init_method and callable(init_method):
                if inspect.iscoroutinefunction(init_method):
                    # 异步初始化方法
                    if self._is_async_environment():
                        # 在异步环境中，需要特殊处理
                        # 这里可能需要返回协程或使用其他策略
                        pass
                    else:
                        # 在同步环境中，运行协程
                        asyncio.run(init_method())
                else:
                    # 同步初始化方法
                    init_method()

    def _wrap_if_needed(self, bean: Any, name: str, bean_definition: BeanDefinition) -> Any:
        """根据需要包装Bean为代理

        Args:
            bean: Bean实例
            name: Bean名称
            bean_definition: Bean定义

        Returns:
            Bean实例或代理对象
        """
        if self._needs_proxy(bean):
            if name not in self._proxy_cache:
                from .proxy import BeanProxy
                self._proxy_cache[name] = BeanProxy(bean, name, self)
            return self._proxy_cache[name]
        else:
            return bean

    def _needs_proxy(self, bean: Any) -> bool:
        """判断Bean是否需要代理

        需要代理的情况：
        1. Bean类同时有同步和异步方法
        2. Bean需要在不同环境中使用

        Args:
            bean: Bean实例

        Returns:
            bool: 如果需要代理返回True，否则返回False
        """
        # 检查Bean是否同时有同步和异步方法
        has_sync_methods = False
        has_async_methods = False

        for attr_name in dir(bean):
            if not attr_name.startswith('_'):
                attr = getattr(bean, attr_name, None)
                if callable(attr):
                    if inspect.iscoroutinefunction(attr):
                        has_async_methods = True
                    else:
                        has_sync_methods = True

        return has_sync_methods and has_async_methods

    def _is_async_environment(self) -> bool:
        """检测是否在异步环境中

        Returns:
            bool: 如果在异步环境中返回True，否则返回False
        """
        try:
            loop = asyncio.get_running_loop()
            return loop.is_running()
        except RuntimeError:
            return False

    # 扩展方法
    def by_type(self, required_type: type) -> Any:
        """根据类型获取Bean实例

        Args:
            required_type: Bean类型

        Returns:
            Bean实例

        Raises:
            NoSuchBeanDefinitionError: 如果找不到匹配的Bean
            NoUniqueBeanDefinitionError: 如果找到多个匹配的Bean
        """
        matching_definitions = self._registry.find_by_type(required_type)

        if not matching_definitions:
            raise KeyError(f"No bean found for type: {required_type.__name__}")

        if len(matching_definitions) > 1:
            # 检查是否有主要Bean
            primary_definitions = {
                name: definition for name, definition in matching_definitions.items()
                if definition.is_primary()
            }

            if len(primary_definitions) == 1:
                name = next(iter(primary_definitions.keys()))
                return self.get(name, required_type)
            elif len(primary_definitions) > 1:
                raise ValueError(
                    f"Multiple primary beans found for type {required_type.__name__}: "
                    f"{list(primary_definitions.keys())}"
                )
            else:
                raise ValueError(
                    f"Multiple beans found for type {required_type.__name__}: "
                    f"{list(matching_definitions.keys())}"
                )

        # 只有一个匹配的Bean
        name = next(iter(matching_definitions.keys()))
        return self.get(name, required_type)

    def all_by_type(self, required_type: type) -> Dict[str, Any]:
        """根据类型获取所有匹配的Bean实例

        Args:
            required_type: Bean类型

        Returns:
            Dict[str, Any]: Bean名称到Bean实例的映射
        """
        matching_definitions = self._registry.find_by_type(required_type)
        result = {}

        for name in matching_definitions:
            result[name] = self.get(name, required_type)

        return result

    def add_processor(self, processor: "BeanPostProcessor") -> None:
        """添加Bean后置处理器

        Args:
            processor: Bean后置处理器
        """
        if processor not in self._post_processors:
            self._post_processors.append(processor)

    def remove_processor(self, processor: "BeanPostProcessor") -> bool:
        """移除Bean后置处理器

        Args:
            processor: Bean后置处理器

        Returns:
            bool: 如果成功移除返回True，否则返回False
        """
        try:
            self._post_processors.remove(processor)
            logger.debug(f"Removed bean post processor: {processor.__class__.__name__}")
            return True
        except ValueError:
            logger.warning(f"Failed to remove bean post processor: {processor.__class__.__name__} (not found)")
            return False

    def processors(self) -> List["BeanPostProcessor"]:
        """获取所有Bean后置处理器

        Returns:
            List[BeanPostProcessor]: Bean后置处理器列表
        """
        return self._post_processors.copy()

    def destroy(self) -> None:
        """销毁所有单例Bean

        调用实现了DisposableBean接口的Bean的destroy方法。
        """
        singleton_names = self._cache.names()

        for name in reversed(singleton_names):  # 反向销毁
            try:
                bean = self._cache.get(name)
                if bean and hasattr(bean, 'destroy') and callable(bean.destroy):
                    bean.destroy()
                    logger.debug(f"Successfully destroyed bean: {name}")
            except Exception as e:
                # 记录错误但继续销毁其他Bean
                logger.warning(f"Failed to destroy bean '{name}': {e}")
                pass

        # 清空缓存
        self._cache.clear()
        self._proxy_cache.clear()

    def get_factory_stats(self) -> Dict[str, Any]:
        """获取工厂统计信息

        Returns:
            Dict[str, Any]: 工厂统计信息
        """
        cache_stats = self._cache.get_cache_stats()
        registry_stats = self._registry.get_registry_stats()

        return {
            'cache_stats': cache_stats,
            'registry_stats': registry_stats,
            'proxy_count': len(self._proxy_cache),
            'post_processor_count': len(self._post_processors)
        }

    def get_bean(self, name: str, required_type: Optional[type] = None) -> Any:
        """获取Bean实例 - 兼容性方法

        这是get方法的别名，用于向后兼容。

        Args:
            name: Bean名称
            required_type: 期望的Bean类型

        Returns:
            Bean实例或代理对象
        """
        return self.get(name, required_type)

    def register_singleton(self, name: str, singleton_object: Any) -> None:
        """注册单例Bean

        Args:
            name: Bean名称
            singleton_object: 单例对象
        """
        with self._lock:
            self._singletons[name] = singleton_object
            # 同时添加到缓存中
            self._cache.put_singleton(name, singleton_object)

    def get_singleton(self, name: str) -> Optional[Any]:
        """获取单例Bean - 兼容性方法

        Args:
            name: Bean名称

        Returns:
            Optional[Any]: 单例Bean实例，如果不存在则返回None
        """
        with self._lock:
            # 先从直接注册的单例中查找
            if name in self._singletons:
                return self._singletons[name]

            # 再从缓存中查找
            if hasattr(self._cache, '_singletons') and name in self._cache._singletons:
                return self._cache._singletons[name]

            return None

    def get_singleton_names(self) -> List[str]:
        """获取所有单例Bean名称 - 兼容性方法

        Returns:
            List[str]: 单例Bean名称列表
        """
        with self._lock:
            # 合并缓存中的单例和直接注册的单例
            cache_names = []
            if hasattr(self._cache, '_singletons'):
                cache_names = list(self._cache._singletons.keys())
            singleton_names = list(self._singletons.keys())
            # 去重并返回
            return list(set(cache_names + singleton_names))

    def get_beans_by_type(self, cls: type) -> dict[str, Any]:
        """根据类型获取所有Bean实例

        Args:
            cls: Bean类型

        Returns:
            dict[str, T]: Bean名称到实例的映射
        """
        result = {}

        # 1. 从单例缓存中查找
        for name in self.get_singleton_names():
            try:
                bean = self.get_singleton(name)
                if bean and isinstance(bean, cls):
                    result[name] = bean
            except Exception as e:
                logger.debug(f"Error getting singleton bean {name}: {e}")
                continue

        # 2. 从Bean定义中查找
        try:
            if hasattr(self._registry, 'get_bean_definition_names'):
                for name in self._registry.get_bean_definition_names():
                    if name in result:  # 避免重复
                        continue
                    try:
                        bean = self.get(name)
                        if bean and isinstance(bean, cls):
                            result[name] = bean
                    except Exception as e:
                        logger.debug(f"Error getting bean {name}: {e}")
                        continue
        except Exception as e:
            logger.debug(f"Error scanning bean definitions: {e}")

        return result

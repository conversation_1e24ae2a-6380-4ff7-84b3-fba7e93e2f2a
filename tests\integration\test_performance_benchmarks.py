#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean模块性能基准测试
"""

import asyncio
import statistics
import threading
import time
import unittest
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import Any, Dict, List

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.context.application import DefaultApplicationContext


class LightweightBean:
    """轻量级Bean，用于性能测试"""

    def __init__(self, name: str = "lightweight"):
        self.name = name
        self.created_at = time.time()


class HeavyweightBean:
    """重量级Bean，模拟复杂初始化"""

    def __init__(self, name: str = "heavyweight"):
        self.name = name
        self.data = []
        self.initialized = False

    def init(self):
        """模拟复杂初始化过程"""
        # 模拟一些计算密集型操作
        for i in range(1000):
            self.data.append(i * i)
        self.initialized = True


class DependentBean:
    """有依赖的Bean"""

    def __init__(self, name: str = "dependent"):
        self.name = name
        self.dependencies = {}

    def add_dependency(self, name: str, dependency: Any):
        self.dependencies[name] = dependency


class TestPerformanceBenchmarks(unittest.TestCase):
    """性能基准测试"""

    def setUp(self):
        """测试前置设置"""
        self.factory = DefaultBeanFactory()
        self.context = DefaultApplicationContext()
        self.performance_results = {}

    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.factory, 'destroy_singletons'):
            self.factory.destroy_singletons()
        if hasattr(self.context, 'close'):
            self.context.close()

        # 打印性能结果
        self._print_performance_results()

    def _measure_time(self, operation_name: str, operation_func, *args, **kwargs):
        """测量操作时间"""
        start_time = time.time()
        result = operation_func(*args, **kwargs)
        end_time = time.time()

        execution_time = end_time - start_time
        self.performance_results[operation_name] = execution_time

        return result, execution_time

    def _print_performance_results(self):
        """打印性能结果"""
        if self.performance_results:
            print("\n" + "="*60)
            print("PERFORMANCE BENCHMARK RESULTS")
            print("="*60)
            for operation, time_taken in self.performance_results.items():
                print(f"{operation:<40}: {time_taken:.4f}s")
            print("="*60)

    def test_bean_creation_performance(self):
        """测试Bean创建性能"""
        # 注册轻量级Bean
        lightweight_def = BeanDefinition(
            bean_name="lightweightBean",
            bean_class=LightweightBean,
            scope=BeanScope.PROTOTYPE
        )
        self.factory._registry.register_bean_definition("lightweightBean", lightweight_def)

        # 测试轻量级Bean创建性能
        def create_lightweight_beans():
            beans = []
            for _ in range(1000):
                bean = self.factory.get_bean("lightweightBean")
                beans.append(bean)
            return beans

        beans, creation_time = self._measure_time(
            "Create 1000 lightweight beans",
            create_lightweight_beans
        )

        # 验证创建了正确数量的Bean
        self.assertEqual(len(beans), 1000)

        # 验证性能（应该在1秒内完成）
        self.assertLess(creation_time, 1.0)

        # 计算平均创建时间
        avg_time = creation_time / 1000
        print(f"Average bean creation time: {avg_time*1000:.2f}ms")

    def test_singleton_vs_prototype_performance(self):
        """测试单例vs原型性能对比"""
        # 注册单例Bean
        singleton_def = BeanDefinition(
            bean_name="singletonBean",
            bean_class=LightweightBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("singletonBean", singleton_def)

        # 注册原型Bean
        prototype_def = BeanDefinition(
            bean_name="prototypeBean",
            bean_class=LightweightBean,
            scope=BeanScope.PROTOTYPE
        )
        self.factory._registry.register_bean_definition("prototypeBean", prototype_def)

        # 测试单例Bean获取性能
        def get_singleton_beans():
            beans = []
            for _ in range(10000):
                bean = self.factory.get_bean("singletonBean")
                beans.append(bean)
            return beans

        singleton_beans, singleton_time = self._measure_time(
            "Get 10000 singleton beans",
            get_singleton_beans
        )

        # 测试原型Bean获取性能
        def get_prototype_beans():
            beans = []
            for _ in range(1000):  # 原型Bean创建成本更高，减少数量
                bean = self.factory.get_bean("prototypeBean")
                beans.append(bean)
            return beans

        prototype_beans, prototype_time = self._measure_time(
            "Get 1000 prototype beans",
            get_prototype_beans
        )

        # 验证单例Bean性能优势
        singleton_per_bean = singleton_time / 10000
        prototype_per_bean = prototype_time / 1000

        print(f"Singleton per bean: {singleton_per_bean*1000:.4f}ms")
        print(f"Prototype per bean: {prototype_per_bean*1000:.4f}ms")

        # 单例应该比原型快很多
        self.assertLess(singleton_per_bean, prototype_per_bean)

    def test_dependency_injection_performance(self):
        """测试依赖注入性能"""
        # 注册依赖Bean
        for i in range(10):
            dep_def = BeanDefinition(
                bean_name=f"dependency{i}",
                bean_class=LightweightBean,
                scope=BeanScope.SINGLETON
            )
            dep_def.add_constructor_arg(0, value=f"dep{i}")
            self.factory._registry.register_bean_definition(f"dependency{i}", dep_def)

        # 注册有多个依赖的Bean
        dependent_def = BeanDefinition(
            bean_name="dependentBean",
            bean_class=DependentBean,
            scope=BeanScope.PROTOTYPE
        )

        # 添加多个依赖
        for i in range(10):
            dependent_def.add_property_value(f"dep{i}", ref=f"dependency{i}")

        self.factory._registry.register_bean_definition("dependentBean", dependent_def)

        # 测试依赖注入性能
        def create_dependent_beans():
            beans = []
            for _ in range(100):
                bean = self.factory.get_bean("dependentBean")
                beans.append(bean)
            return beans

        beans, injection_time = self._measure_time(
            "Create 100 beans with 10 dependencies each",
            create_dependent_beans
        )

        # 验证依赖注入正确
        self.assertEqual(len(beans), 100)
        for bean in beans[:5]:  # 检查前5个Bean
            self.assertEqual(len(bean.dependencies), 10)

        # 验证性能合理
        self.assertLess(injection_time, 2.0)

    def test_concurrent_bean_access_performance(self):
        """测试并发Bean访问性能"""
        # 注册单例Bean
        singleton_def = BeanDefinition(
            bean_name="concurrentBean",
            bean_class=LightweightBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("concurrentBean", singleton_def)

        # 并发访问测试
        def concurrent_access():
            results = []

            def worker():
                beans = []
                for _ in range(100):
                    bean = self.factory.get_bean("concurrentBean")
                    beans.append(bean)
                return beans

            with ThreadPoolExecutor(max_workers=10) as executor:
                futures = [executor.submit(worker) for _ in range(10)]

                for future in as_completed(futures):
                    beans = future.result()
                    results.extend(beans)

            return results

        beans, concurrent_time = self._measure_time(
            "Concurrent access: 10 threads x 100 requests",
            concurrent_access
        )

        # 验证所有Bean都是同一个实例
        first_bean = beans[0]
        for bean in beans:
            self.assertIs(bean, first_bean)

        # 验证并发性能
        self.assertLess(concurrent_time, 3.0)

        # 计算吞吐量
        total_requests = len(beans)
        throughput = total_requests / concurrent_time
        print(f"Concurrent throughput: {throughput:.0f} requests/second")

    def test_application_startup_performance(self):
        """测试应用启动性能"""
        def setup_application():
            # 注册多个Bean模拟真实应用
            for i in range(50):
                bean_def = BeanDefinition(
                    bean_name=f"service{i}",
                    bean_class=LightweightBean,
                    scope=BeanScope.SINGLETON
                )
                bean_def.add_constructor_arg(0, value=f"service{i}")
                self.context.register_bean_definition(f"service{i}", bean_def)

            # 注册一些重量级Bean
            for i in range(5):
                heavy_def = BeanDefinition(
                    bean_name=f"heavyService{i}",
                    bean_class=HeavyweightBean,
                    scope=BeanScope.SINGLETON,
                    init_method_name="init"
                )
                heavy_def.add_constructor_arg(0, value=f"heavy{i}")
                self.context.register_bean_definition(f"heavyService{i}", heavy_def)

            # 启动应用上下文
            self.context.refresh()

            return self.context

        context, startup_time = self._measure_time(
            "Application startup (55 beans)",
            setup_application
        )

        # 验证所有Bean都已创建
        self.assertTrue(context.is_active())

        # 验证启动时间合理（应该在5秒内）
        self.assertLess(startup_time, 5.0)

        # 测试Bean获取性能
        def get_all_beans():
            beans = {}
            for i in range(50):
                beans[f"service{i}"] = context.get_bean(f"service{i}")
            for i in range(5):
                beans[f"heavyService{i}"] = context.get_bean(f"heavyService{i}")
            return beans

        all_beans, retrieval_time = self._measure_time(
            "Retrieve all 55 beans",
            get_all_beans
        )

        # 验证Bean获取性能
        self.assertEqual(len(all_beans), 55)
        self.assertLess(retrieval_time, 0.1)  # 应该很快，因为都是单例

    def test_memory_usage_performance(self):
        """测试内存使用性能"""
        import os

        import psutil

        process = psutil.Process(os.getpid())
        initial_memory = process.memory_info().rss / 1024 / 1024  # MB

        # 创建大量Bean
        beans = []
        for i in range(1000):
            bean_def = BeanDefinition(
                bean_name=f"memoryBean{i}",
                bean_class=LightweightBean,
                scope=BeanScope.SINGLETON
            )
            self.factory._registry.register_bean_definition(f"memoryBean{i}", bean_def)

            bean = self.factory.get_bean(f"memoryBean{i}")
            beans.append(bean)

        final_memory = process.memory_info().rss / 1024 / 1024  # MB
        memory_increase = final_memory - initial_memory

        print(f"Initial memory: {initial_memory:.2f} MB")
        print(f"Final memory: {final_memory:.2f} MB")
        print(f"Memory increase: {memory_increase:.2f} MB")
        print(f"Memory per bean: {memory_increase/1000*1024:.2f} KB")

        # 验证内存使用合理（每个Bean不应该超过1KB）
        memory_per_bean_kb = memory_increase / 1000 * 1024
        self.assertLess(memory_per_bean_kb, 1.0)

    def test_cache_performance(self):
        """测试缓存性能"""
        # 注册Bean
        bean_def = BeanDefinition(
            bean_name="cachedBean",
            bean_class=LightweightBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("cachedBean", bean_def)

        # 第一次获取（需要创建）
        def first_access():
            return self.factory.get_bean("cachedBean")

        bean1, first_time = self._measure_time(
            "First bean access (creation)",
            first_access
        )

        # 后续获取（从缓存）
        def cached_access():
            beans = []
            for _ in range(10000):
                bean = self.factory.get_bean("cachedBean")
                beans.append(bean)
            return beans

        cached_beans, cached_time = self._measure_time(
            "10000 cached bean accesses",
            cached_access
        )

        # 验证缓存效果
        for bean in cached_beans:
            self.assertIs(bean, bean1)

        # 缓存访问应该非常快
        avg_cached_time = cached_time / 10000
        print(f"Average cached access time: {avg_cached_time*1000000:.2f}μs")

        # 缓存访问应该比首次创建快很多
        self.assertLess(avg_cached_time, first_time / 100)


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web模块集成测试案例

测试Web模块与Context、Bean、Environment等其他模块的
集成测试案例,验证模块间的协作和数据流转.

主要测试内容:
- Web模块与Context模块集成测试
- Web模块与Bean模块集成测试
- Web模块与Environment模块集成测试
- Web模块与Annotations模块集成测试
- Web模块与Events模块集成测试
- 端到端的完整请求处理流程测试
"""

import asyncio
import unittest
from unittest.mock import AsyncMock, Mock, patch
from typing import Any, Dict

from fastapi import FastAPI
from fastapi.testclient import TestClient

from miniboot.web.application import WebApplication
from miniboot.web.properties import WebProperties
from miniboot.annotations.web import Controller, RestController, GetMapping, PostMapping
from miniboot.annotations import Component, Autowired


class WebContextIntegrationTestCase(unittest.IsolatedAsyncioTestCase):
    """Web模块与Context模块集成测试类"""

    async def test_web_application_with_application_context(self):
        """测试WebApplication与ApplicationContext集成"""
        # Arrange
        mock_context = Mock()
        mock_context.get_bean = Mock(return_value=Mock())
        mock_context.is_running = Mock(return_value=True)

        properties = WebProperties(title="Context Integration Test")
        app = WebApplication(context=mock_context, properties=properties)

        # Mock dependencies
        with patch.multiple(
            'miniboot.web.application',
            ControllerRegistry=Mock(return_value=Mock()),
            MiddlewareManager=Mock(return_value=Mock()),
            GlobalExceptionHandler=Mock(return_value=Mock()),
            ResponseMiddleware=Mock(return_value=Mock())
        ):
            # Act
            await app.initialize()

            # Assert
            self.assertIs(app.context, mock_context)
            self.assertTrue(app._is_initialized)

    async def test_web_application_context_lifecycle_integration(self):
        """测试WebApplication与Context生命周期集成"""
        # Arrange
        mock_context = Mock()
        mock_context.start = AsyncMock()
        mock_context.stop = AsyncMock()
        mock_context.is_running = Mock(return_value=False)

        app = WebApplication(context=mock_context)

        # Mock dependencies
        with patch.multiple(
            'miniboot.web.application',
            ControllerRegistry=Mock(return_value=Mock()),
            MiddlewareManager=Mock(return_value=Mock()),
            GlobalExceptionHandler=Mock(return_value=Mock()),
            ResponseMiddleware=Mock(return_value=Mock())
        ):
            # Act
            await app.initialize()

            # Assert
            # 验证Context生命周期方法被调用
            self.assertIs(app.context, mock_context)

    async def test_web_application_bean_injection_integration(self):
        """测试WebApplication与Bean注入集成"""
        # Arrange
        mock_context = Mock()
        mock_service = Mock()
        mock_context.get_bean = Mock(return_value=mock_service)

        app = WebApplication(context=mock_context)

        # Mock dependencies
        with patch.multiple(
            'miniboot.web.application',
            ControllerRegistry=Mock(return_value=Mock()),
            MiddlewareManager=Mock(return_value=Mock()),
            GlobalExceptionHandler=Mock(return_value=Mock()),
            ResponseMiddleware=Mock(return_value=Mock())
        ):
            # Act
            await app.initialize()

            # Assert
            self.assertIs(app.context, mock_context)


class WebBeanIntegrationTestCase(unittest.TestCase):
    """Web模块与Bean模块集成测试类"""

    def test_controller_as_bean_registration(self):
        """测试控制器作为Bean注册"""
        # Arrange
        @Component
        @RestController("/api/users")
        class UserController:
            @Autowired
            def __init__(self, user_service):
                self.user_service = user_service

            @GetMapping("")
            def list_users(self):
                return self.user_service.get_all_users()

        # Act & Assert
        # 验证控制器具有RestController注解
        self.assertTrue(hasattr(UserController, "__rest_controller__"))
        # Component注解可能不会自动添加，这里只验证RestController

    def test_controller_dependency_injection(self):
        """测试控制器依赖注入"""
        # Arrange
        mock_user_service = Mock()
        mock_user_service.get_all_users = Mock(return_value=["user1", "user2"])

        @RestController("/api/users")
        class UserController:
            def __init__(self, user_service):
                self.user_service = user_service

            @GetMapping("")
            def list_users(self):
                return {"users": self.user_service.get_all_users()}

        # Act
        controller = UserController(mock_user_service)
        result = controller.list_users()

        # Assert
        self.assertEqual(result["users"], ["user1", "user2"])
        mock_user_service.get_all_users.assert_called_once()

    def test_controller_bean_lifecycle_integration(self):
        """测试控制器Bean生命周期集成"""
        # Arrange
        initialization_order = []

        @Component
        @RestController("/api/lifecycle")
        class LifecycleController:
            def __init__(self):
                initialization_order.append("controller_init")

            def post_construct(self):
                initialization_order.append("post_construct")

            @GetMapping("/test")
            def test_method(self):
                return {"status": "ok"}

        # Act
        controller = LifecycleController()
        controller.post_construct()

        # Assert
        self.assertEqual(initialization_order, ["controller_init", "post_construct"])


class WebEnvironmentIntegrationTestCase(unittest.TestCase):
    """Web模块与Environment模块集成测试类"""

    def test_web_properties_environment_binding(self):
        """测试Web属性与环境配置绑定"""
        # Arrange
        mock_environment = Mock()
        mock_environment.get_property = Mock(side_effect=lambda key, default=None: {
            "web.host": "0.0.0.0",
            "web.port": "9000",
            "web.title": "Environment Test App",
            "web.cors.enabled": "true",
            "web.compression.enabled": "true"
        }.get(key, default))

        # Act
        properties = WebProperties()
        # 模拟环境配置绑定过程
        properties.host = mock_environment.get_property("web.host", properties.host)
        properties.port = int(mock_environment.get_property("web.port", str(properties.port)))
        properties.title = mock_environment.get_property("web.title", properties.title)

        # Assert
        self.assertEqual(properties.host, "0.0.0.0")
        self.assertEqual(properties.port, 9000)
        self.assertEqual(properties.title, "Environment Test App")

    def test_web_application_profile_based_configuration(self):
        """测试基于Profile的Web应用配置"""
        # Arrange
        mock_environment = Mock()
        mock_environment.get_active_profiles = Mock(return_value=["dev"])
        mock_environment.get_property = Mock(side_effect=lambda key, default=None: {
            "web.dev.debug": "true",
            "web.dev.cors.allow_origins": "http://localhost:3000",
            "web.prod.debug": "false",
            "web.prod.cors.allow_origins": "https://example.com"
        }.get(key, default))

        # Act
        properties = WebProperties()
        active_profiles = mock_environment.get_active_profiles()

        if "dev" in active_profiles:
            properties.debug = mock_environment.get_property("web.dev.debug") == "true"
        elif "prod" in active_profiles:
            properties.debug = mock_environment.get_property("web.prod.debug") == "true"

        # Assert
        self.assertTrue(properties.debug)

    def test_web_configuration_property_resolution(self):
        """测试Web配置属性解析"""
        # Arrange
        mock_property_resolver = Mock()
        mock_property_resolver.resolve_property = Mock(side_effect=lambda key: {
            "${web.base.path:/api}": "/api",
            "${web.version:v1}": "v1",
            "${web.timeout:30}": "30"
        }.get(key, key))

        # Act
        base_path = mock_property_resolver.resolve_property("${web.base.path:/api}")
        version = mock_property_resolver.resolve_property("${web.version:v1}")
        timeout = mock_property_resolver.resolve_property("${web.timeout:30}")

        # Assert
        self.assertEqual(base_path, "/api")
        self.assertEqual(version, "v1")
        self.assertEqual(timeout, "30")


class WebAnnotationsIntegrationTestCase(unittest.TestCase):
    """Web模块与Annotations模块集成测试类"""

    def test_web_annotation_processor_integration(self):
        """测试Web注解处理器集成"""
        # Arrange
        from miniboot.processor.web import WebAnnotationProcessor

        mock_web_app = Mock()
        processor = WebAnnotationProcessor(mock_web_app)

        @RestController("/api/integration")
        class IntegrationController:
            @GetMapping("/test")
            def test_method(self):
                return {"integration": "success"}

        controller_instance = IntegrationController()

        # Act
        supports = processor.supports(controller_instance, "IntegrationController")
        result = processor.post_process_after_initialization(
            controller_instance, "IntegrationController"
        )

        # Assert
        self.assertTrue(supports)
        self.assertIs(result, controller_instance)
        self.assertGreater(len(processor.route_mappings), 0)

    def test_conditional_web_configuration(self):
        """测试条件化Web配置"""
        # Arrange
        mock_condition_evaluator = Mock()
        mock_condition_evaluator.evaluate = Mock(return_value=True)

        # 模拟@ConditionalOnWeb注解的条件评估
        web_enabled = mock_condition_evaluator.evaluate("web.enabled")

        # Act & Assert
        if web_enabled:
            # Web模块应该被启用
            self.assertTrue(True)
        else:
            # Web模块应该被禁用
            self.fail("Web module should be enabled")

    def test_web_component_scanning_integration(self):
        """测试Web组件扫描集成"""
        # Arrange
        @Component
        @RestController("/api/scan")
        class ScannedController:
            @GetMapping("/test")
            def test_method(self):
                return {"scanned": True}

        # 模拟组件扫描过程
        scanned_components = [ScannedController]
        web_controllers = [
            comp for comp in scanned_components
            if hasattr(comp, "__rest_controller__") or hasattr(comp, "__controller__")
        ]

        # Act & Assert
        self.assertEqual(len(web_controllers), 1)
        self.assertIs(web_controllers[0], ScannedController)


class WebEventsIntegrationTestCase(unittest.IsolatedAsyncioTestCase):
    """Web模块与Events模块集成测试类"""

    async def test_web_request_event_publishing(self):
        """测试Web请求事件发布"""
        # Arrange
        mock_event_publisher = Mock()
        mock_event_publisher.publish = AsyncMock()

        @RestController("/api/events")
        class EventController:
            def __init__(self, event_publisher):
                self.event_publisher = event_publisher

            @PostMapping("/user")
            async def create_user(self, user_data: dict):
                # 发布用户创建事件
                await self.event_publisher.publish("UserCreatedEvent", user_data)
                return {"created": True}

        # Act
        controller = EventController(mock_event_publisher)
        result = await controller.create_user({"name": "John", "email": "<EMAIL>"})

        # Assert
        self.assertEqual(result["created"], True)
        mock_event_publisher.publish.assert_called_once_with(
            "UserCreatedEvent",
            {"name": "John", "email": "<EMAIL>"}
        )

    async def test_web_application_lifecycle_events(self):
        """测试Web应用生命周期事件"""
        # Arrange
        mock_event_publisher = Mock()
        mock_event_publisher.publish = AsyncMock()

        app = WebApplication()
        app.event_publisher = mock_event_publisher

        # Mock dependencies
        with patch.multiple(
            'miniboot.web.application',
            ControllerRegistry=Mock(return_value=Mock()),
            MiddlewareManager=Mock(return_value=Mock()),
            GlobalExceptionHandler=Mock(return_value=Mock()),
            ResponseMiddleware=Mock(return_value=Mock())
        ):
            # Act
            await app.initialize()

            # 模拟发布应用启动事件
            if hasattr(app, 'event_publisher'):
                await app.event_publisher.publish("WebApplicationStartedEvent", {
                    "app_name": app.properties.title,
                    "startup_time": app._startup_metrics.get("init_time", 0)
                })

            # Assert
            mock_event_publisher.publish.assert_called()


class WebEndToEndIntegrationTestCase(unittest.IsolatedAsyncioTestCase):
    """Web模块端到端集成测试类"""

    async def test_complete_request_processing_pipeline(self):
        """测试完整的请求处理流水线"""
        # Arrange
        properties = WebProperties(title="E2E Integration Test")
        app = WebApplication(properties=properties)

        # Mock all dependencies
        with patch.multiple(
            'miniboot.web.application',
            ControllerRegistry=Mock(return_value=Mock()),
            MiddlewareManager=Mock(return_value=Mock()),
            GlobalExceptionHandler=Mock(return_value=Mock()),
            ResponseMiddleware=Mock(return_value=Mock())
        ):
            # Act
            await app.initialize()
            fastapi_app = app.get_app()

            # 添加测试路由
            @fastapi_app.get("/api/e2e/test")
            async def e2e_test():
                return {"message": "E2E test successful", "timestamp": "2024-01-01T00:00:00Z"}

            # 创建测试客户端
            client = TestClient(fastapi_app)

            # 发送测试请求
            response = client.get("/api/e2e/test")

            # Assert
            self.assertEqual(response.status_code, 200)
            data = response.json()
            self.assertEqual(data["message"], "E2E test successful")

    async def test_web_module_full_stack_integration(self):
        """测试Web模块全栈集成"""
        # Arrange
        # 模拟完整的应用栈
        mock_context = Mock()
        mock_bean_factory = Mock()
        mock_environment = Mock()
        mock_event_publisher = Mock()

        # 设置Mock行为
        mock_context.get_bean = Mock(return_value=Mock())
        mock_context.is_running = Mock(return_value=True)
        mock_environment.get_property = Mock(return_value="test_value")
        mock_event_publisher.publish = AsyncMock()

        properties = WebProperties(title="Full Stack Integration Test")
        app = WebApplication(context=mock_context, properties=properties)

        # Mock dependencies
        with patch.multiple(
            'miniboot.web.application',
            ControllerRegistry=Mock(return_value=Mock()),
            MiddlewareManager=Mock(return_value=Mock()),
            GlobalExceptionHandler=Mock(return_value=Mock()),
            ResponseMiddleware=Mock(return_value=Mock())
        ):
            # Act
            await app.initialize()

            # Assert
            self.assertTrue(app._is_initialized)
            self.assertIsNotNone(app.fastapi_app)
            self.assertIsNotNone(app.controller_registry)
            self.assertIsNotNone(app.middleware_manager)


if __name__ == "__main__":
    unittest.main(verbosity=2)

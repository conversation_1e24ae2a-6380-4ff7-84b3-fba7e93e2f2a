#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 类型检查缓存测试 - 验证FIX-2.1.2修复的性能优化
"""

import gc
import inspect
import time
import unittest
import weakref
from typing import Optional

from miniboot.annotations.core import Component, Service
from miniboot.annotations.injection import Autowired
from miniboot.processor.cache import (TypeCheckCache, clear_type_cache,
                                      get_type_cache)


class TestService:
    """测试服务类"""

    pass


@Component
class TestComponent:
    """测试组件类"""

    name: str
    age: int
    service: Optional[TestService]

    @Autowired
    def set_service(self, service: TestService):
        self.service = service

    def get_name(self) -> str:
        return self.name

    def set_age(self, age: int):
        self.age = age


@Service
class TestServiceImpl:
    """测试服务实现类"""

    items: list[str]

    def __init__(self):
        self.items = []

    def add_item(self, item: str) -> None:
        self.items.append(item)


class TypeCheckCacheTestCase(unittest.TestCase):
    """类型检查缓存测试"""

    def setUp(self):
        """设置测试环境"""
        # 清理缓存
        clear_type_cache()

    def tearDown(self):
        """清理测试环境"""
        # 清理缓存
        clear_type_cache()

    def test_type_cache_initialization(self):
        """测试类型缓存初始化"""
        cache = TypeCheckCache()

        # 验证初始状态
        stats = cache.get_cache_stats()
        self.assertEqual(stats["type_hints"]["hits"], 0)
        self.assertEqual(stats["type_hints"]["misses"], 0)
        self.assertEqual(stats["signatures"]["hits"], 0)
        self.assertEqual(stats["signatures"]["misses"], 0)

    def test_type_hints_caching(self):
        """测试类型提示缓存"""
        cache = TypeCheckCache()

        # 第一次获取类型提示
        hints1 = cache.get_type_hints(TestComponent)

        # 第二次获取类型提示（应该从缓存返回）
        hints2 = cache.get_type_hints(TestComponent)

        # 验证结果一致
        self.assertEqual(hints1, hints2)

        # 验证包含预期的类型
        self.assertIn("name", hints1)
        self.assertIn("age", hints1)
        self.assertIn("service", hints1)

        # 验证缓存统计
        stats = cache.get_cache_stats()
        self.assertEqual(stats["type_hints"]["hits"], 1)
        self.assertEqual(stats["type_hints"]["misses"], 1)

    def test_method_signature_caching(self):
        """测试方法签名缓存"""
        cache = TypeCheckCache()

        # 第一次获取方法签名
        sig1 = cache.get_method_signature(TestComponent, "set_service")

        # 第二次获取方法签名（应该从缓存返回）
        sig2 = cache.get_method_signature(TestComponent, "set_service")

        # 验证结果一致
        self.assertEqual(sig1, sig2)

        # 验证签名正确
        self.assertIsInstance(sig1, inspect.Signature)
        params = list(sig1.parameters.values())
        self.assertEqual(len(params), 2)  # self + service
        self.assertEqual(params[1].name, "service")

        # 验证缓存统计
        stats = cache.get_cache_stats()
        self.assertEqual(stats["signatures"]["hits"], 1)
        self.assertEqual(stats["signatures"]["misses"], 1)

    def test_class_attributes_caching(self):
        """测试类属性缓存"""
        cache = TypeCheckCache()

        # 第一次获取类属性
        attrs1 = cache.get_class_attributes(TestComponent)

        # 第二次获取类属性（应该从缓存返回）
        attrs2 = cache.get_class_attributes(TestComponent)

        # 验证结果一致
        self.assertEqual(attrs1, attrs2)

        # 验证包含预期的属性
        self.assertIn("set_service", attrs1)
        self.assertIn("get_name", attrs1)
        self.assertIn("set_age", attrs1)

        # 验证不包含私有属性
        self.assertNotIn("__init__", attrs1)
        self.assertNotIn("__class__", attrs1)

        # 验证缓存统计
        stats = cache.get_cache_stats()
        self.assertEqual(stats["attributes"]["hits"], 1)
        self.assertEqual(stats["attributes"]["misses"], 1)

    def test_safe_attribute_access(self):
        """测试安全属性访问"""
        cache = TypeCheckCache()

        # 测试存在的属性
        attr = cache.get_attribute_safely(TestComponent, "set_service")
        self.assertIsNotNone(attr)
        self.assertTrue(callable(attr))

        # 测试不存在的属性
        attr = cache.get_attribute_safely(TestComponent, "non_existent_method")
        self.assertIsNone(attr)

    def test_method_function_check(self):
        """测试方法/函数检查"""
        cache = TypeCheckCache()

        # 测试方法
        method = TestComponent.set_service
        self.assertTrue(cache.is_method_or_function(method))

        # 测试普通函数
        def test_func():
            pass

        self.assertTrue(cache.is_method_or_function(test_func))

        # 测试非函数对象
        self.assertFalse(cache.is_method_or_function("not a function"))
        self.assertFalse(cache.is_method_or_function(123))

    def test_method_parameter_types(self):
        """测试方法参数类型获取"""
        cache = TypeCheckCache()

        # 获取方法参数类型
        param_types = cache.get_method_parameter_types(TestComponent, "set_service")

        # 验证参数类型
        self.assertEqual(len(param_types), 1)  # 跳过self参数
        param_name, param_type = param_types[0]
        self.assertEqual(param_name, "service")
        # 注意：这里的类型可能是字符串表示

    def test_cache_performance(self):
        """测试缓存性能"""
        cache = TypeCheckCache()

        # 创建多个测试类
        test_classes = []
        for i in range(50):

            @Component
            class DynamicTestClass:
                name: str
                value: int

                def test_method(self, param: str) -> str:
                    return param

            # 重命名类以避免冲突
            DynamicTestClass.__name__ = f"DynamicTestClass{i}"
            test_classes.append(DynamicTestClass)

        # 测量第一次访问时间（缓存未命中）
        start_time = time.time()
        for cls in test_classes:
            cache.get_type_hints(cls)
            cache.get_class_attributes(cls)
        first_access_time = time.time() - start_time

        # 测量第二次访问时间（缓存命中）
        start_time = time.time()
        for cls in test_classes:
            cache.get_type_hints(cls)
            cache.get_class_attributes(cls)
        second_access_time = time.time() - start_time

        # 验证缓存提升性能（允许时间相等或略有差异，因为操作很快且时间测量精度有限）
        # 在高性能机器上，缓存的性能提升可能很小，所以我们只验证缓存统计
        print(f"First access time: {first_access_time:.6f}s, Second access time: {second_access_time:.6f}s")

        # 验证缓存统计
        stats = cache.get_cache_stats()
        self.assertEqual(stats["type_hints"]["hits"], 50)
        self.assertEqual(stats["type_hints"]["misses"], 50)
        self.assertEqual(stats["attributes"]["hits"], 50)
        self.assertEqual(stats["attributes"]["misses"], 50)

    def test_weak_reference_cleanup(self):
        """测试弱引用清理"""
        cache = TypeCheckCache()

        # 创建临时类
        def create_temp_class():
            @Component
            class TempClass:
                value: str

            return TempClass

        temp_class = create_temp_class()
        weakref.ref(temp_class)

        # 获取类型信息
        cache.get_type_hints(temp_class)
        cache.get_class_attributes(temp_class)

        # 验证缓存中有数据
        stats = cache.get_cache_stats()
        self.assertGreater(stats["cache_sizes"]["type_hints"], 0)

        # 删除类引用
        del temp_class
        gc.collect()

        # 弱引用缓存应该自动清理
        # 注意：WeakKeyDictionary的清理可能不是立即的

    def test_cache_clear(self):
        """测试缓存清理"""
        cache = TypeCheckCache()

        # 添加一些数据到缓存
        cache.get_type_hints(TestComponent)
        cache.get_class_attributes(TestComponent)
        cache.get_method_signature(TestComponent, "set_service")

        # 验证缓存有数据
        stats_before = cache.get_cache_stats()
        self.assertGreater(stats_before["cache_sizes"]["type_hints"], 0)

        # 清理缓存
        cache.clear_cache()

        # 验证缓存已清理
        stats_after = cache.get_cache_stats()
        self.assertEqual(stats_after["cache_sizes"]["type_hints"], 0)
        self.assertEqual(stats_after["type_hints"]["hits"], 0)
        self.assertEqual(stats_after["type_hints"]["misses"], 0)

    def test_global_cache_instance(self):
        """测试全局缓存实例"""
        # 获取全局缓存实例
        cache1 = get_type_cache()
        cache2 = get_type_cache()

        # 验证是同一个实例
        self.assertIs(cache1, cache2)

        # 测试全局缓存清理
        cache1.get_type_hints(TestComponent)
        stats = cache1.get_cache_stats()
        self.assertGreater(stats["cache_sizes"]["type_hints"], 0)

        clear_type_cache()
        stats = cache1.get_cache_stats()
        self.assertEqual(stats["cache_sizes"]["type_hints"], 0)

    def test_cache_statistics(self):
        """测试缓存统计"""
        cache = TypeCheckCache()

        # 执行一些缓存操作
        cache.get_type_hints(TestComponent)
        cache.get_type_hints(TestServiceImpl)
        cache.get_type_hints(TestComponent)  # 缓存命中

        cache.get_class_attributes(TestComponent)
        cache.get_class_attributes(TestComponent)  # 缓存命中

        # 获取统计信息
        stats = cache.get_cache_stats()

        # 验证类型提示统计
        self.assertEqual(stats["type_hints"]["hits"], 1)
        self.assertEqual(stats["type_hints"]["misses"], 2)
        self.assertAlmostEqual(stats["type_hints"]["hit_ratio"], 1 / 3, places=2)

        # 验证属性统计
        self.assertEqual(stats["attributes"]["hits"], 1)
        self.assertEqual(stats["attributes"]["misses"], 1)
        self.assertAlmostEqual(stats["attributes"]["hit_ratio"], 0.5, places=2)

        # 验证缓存大小
        self.assertGreater(stats["cache_sizes"]["type_hints"], 0)
        self.assertGreater(stats["cache_sizes"]["attributes"], 0)

        # 验证LRU缓存信息
        self.assertIn("lru_cache_info", stats)

    def test_error_handling(self):
        """测试错误处理"""
        cache = TypeCheckCache()

        # 测试无效类的类型提示
        class InvalidClass:
            pass

        # 应该能够处理没有类型注解的类
        hints = cache.get_type_hints(InvalidClass)
        self.assertIsInstance(hints, dict)

        # 测试无效方法签名
        sig = cache.get_method_signature(InvalidClass, "non_existent_method")
        self.assertIsNone(sig)


if __name__ == "__main__":
    unittest.main()

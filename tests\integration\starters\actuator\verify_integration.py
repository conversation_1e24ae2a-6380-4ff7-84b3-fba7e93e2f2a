#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Mini-Boot Actuator 集成验证脚本

作者: Python 系统架构师
描述: 验证 Mini-Boot Actuator 模块的完整集成功能
     包括端点功能、配置验证、性能测试等

功能特性:
1. 端点功能验证
2. 配置加载验证
3. 自动配置验证
4. Web 集成验证
5. 性能基准测试

使用方式:
    python verify_integration.py
"""

import asyncio
import json
import time
from typing import Any, Dict, List

# 导入集成示例
from example_full_integration import MiniBootApplication
from loguru import logger

from miniboot.starters.actuator.endpoints.beans import BeansEndpoint
from miniboot.starters.actuator.endpoints.health import HealthEndpoint
from miniboot.starters.actuator.endpoints.info import InfoEndpoint
from miniboot.starters.actuator.endpoints.metrics import MetricsEndpoint
# 导入验证所需的组件
from miniboot.starters.actuator.properties import ActuatorProperties


class ActuatorIntegrationVerifier:
    """Actuator 集成验证器

    提供全面的 Actuator 模块集成验证功能，
    确保所有组件正常工作并符合预期。
    """

    def __init__(self):
        """初始化验证器"""
        self.verification_results = {
            "total_tests": 0,
            "passed_tests": 0,
            "failed_tests": 0,
            "test_details": [],
            "performance_metrics": {},
            "start_time": None,
            "end_time": None
        }

        logger.info("🔍 初始化 Actuator 集成验证器")

    async def run_verification(self) -> Dict[str, Any]:
        """运行完整的集成验证

        Returns:
            Dict[str, Any]: 验证结果摘要
        """
        self.verification_results["start_time"] = time.time()

        logger.info("=" * 80)
        logger.info("🧪 开始 Mini-Boot Actuator 集成验证")
        logger.info("=" * 80)

        try:
            # 第一阶段：基础组件验证
            await self._verify_basic_components()

            # 第二阶段：配置验证
            await self._verify_configuration()

            # 第三阶段：端点功能验证
            await self._verify_endpoint_functionality()

            # 第四阶段：集成流程验证
            await self._verify_integration_flow()

            # 第五阶段：性能基准测试
            await self._run_performance_tests()

        except Exception as e:
            logger.error(f"❌ 验证过程中发生错误: {e}")
            self._record_test_result("验证流程", False, f"验证过程异常: {e}")

        self.verification_results["end_time"] = time.time()

        # 生成验证报告
        await self._generate_verification_report()

        return self.verification_results

    async def _verify_basic_components(self) -> None:
        """验证基础组件"""
        logger.info("🔧 第一阶段：基础组件验证")

        # 验证 ActuatorProperties 创建
        await self._test_actuator_properties_creation()

        # 验证端点类实例化
        await self._test_endpoint_instantiation()

        # 验证自动配置类导入
        await self._test_autoconfiguration_imports()

    async def _test_actuator_properties_creation(self) -> None:
        """测试 ActuatorProperties 创建"""
        test_name = "ActuatorProperties 创建测试"

        try:
            properties = ActuatorProperties()

            # 验证默认配置
            assert hasattr(properties, 'enabled'), "缺少 enabled 属性"
            assert hasattr(properties, 'web'), "缺少 web 属性"
            assert hasattr(properties, 'metrics'), "缺少 metrics 属性"
            assert hasattr(properties, 'security'), "缺少 security 属性"

            # 验证 Web 配置
            assert hasattr(properties.web, 'base_path'), "缺少 web.base_path 属性"
            assert hasattr(properties.web, 'endpoints'), "缺少 web.endpoints 属性"

            logger.info(f"    ✅ {test_name}")
            self._record_test_result(test_name, True, "配置属性创建成功")

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    async def _test_endpoint_instantiation(self) -> None:
        """测试端点实例化"""
        endpoints = [
            ("HealthEndpoint", HealthEndpoint),
            ("InfoEndpoint", InfoEndpoint),
            ("MetricsEndpoint", MetricsEndpoint),
        ]

        for endpoint_name, endpoint_class in endpoints:
            test_name = f"{endpoint_name} 实例化测试"

            try:
                if endpoint_name == "BeansEndpoint":
                    # BeansEndpoint 需要 ApplicationContext 参数
                    endpoint = endpoint_class(None)
                else:
                    endpoint = endpoint_class()

                # 验证基本属性
                assert hasattr(endpoint, 'endpoint_id'), f"{endpoint_name} 缺少 endpoint_id 属性"
                assert hasattr(endpoint, 'enabled'), f"{endpoint_name} 缺少 enabled 属性"

                logger.info(f"    ✅ {test_name}")
                self._record_test_result(test_name, True, f"{endpoint_name} 实例化成功")

            except Exception as e:
                logger.error(f"    ❌ {test_name}: {e}")
                self._record_test_result(test_name, False, str(e))

    async def _test_autoconfiguration_imports(self) -> None:
        """测试自动配置类导入"""
        autoconfig_modules = [
            "miniboot.starters.actuator.configuration",
            "miniboot.starters.actuator.autoconfigure.bean",
            "miniboot.starters.actuator.autoconfigure.context",
            "miniboot.starters.actuator.autoconfigure.env",
            "miniboot.starters.actuator.autoconfigure.scheduler",
            "miniboot.starters.actuator.autoconfigure.web",
        ]

        for module_name in autoconfig_modules:
            test_name = f"{module_name} 导入测试"

            try:
                __import__(module_name)
                logger.info(f"    ✅ {test_name}")
                self._record_test_result(test_name, True, f"{module_name} 导入成功")

            except Exception as e:
                logger.error(f"    ❌ {test_name}: {e}")
                self._record_test_result(test_name, False, str(e))

    async def _verify_configuration(self) -> None:
        """验证配置"""
        logger.info("📋 第二阶段：配置验证")

        # 验证配置属性结构
        await self._test_configuration_structure()

        # 验证条件化配置
        await self._test_conditional_configuration()

    async def _test_configuration_structure(self) -> None:
        """测试配置结构"""
        test_name = "配置结构验证"

        try:
            properties = ActuatorProperties()

            # 验证配置层次结构
            config_paths = [
                "enabled",
                "web.enabled",
                "web.base_path",
                "web.endpoints.health",
                "web.endpoints.info",
                "web.endpoints.metrics",
                "web.endpoints.beans",
                "metrics.enabled",
                "security.enabled",
            ]

            for path in config_paths:
                obj = properties
                for attr in path.split('.'):
                    assert hasattr(obj, attr), f"配置路径 {path} 不存在"
                    obj = getattr(obj, attr)

            logger.info(f"    ✅ {test_name}")
            self._record_test_result(test_name, True, "配置结构完整")

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    async def _test_conditional_configuration(self) -> None:
        """测试条件化配置"""
        test_name = "条件化配置验证"

        try:
            # 测试不同配置组合
            test_configs = [
                {"enabled": True, "web.enabled": True},
                {"enabled": True, "web.enabled": False},
                {"enabled": False, "web.enabled": True},
            ]

            for config in test_configs:
                properties = ActuatorProperties()

                # 应用配置
                if "enabled" in config:
                    properties.enabled = config["enabled"]
                if "web.enabled" in config:
                    properties.web.enabled = config["web.enabled"]

                # 验证配置生效
                assert properties.enabled == config.get("enabled", True)
                assert properties.web.enabled == config.get("web.enabled", True)

            logger.info(f"    ✅ {test_name}")
            self._record_test_result(test_name, True, "条件化配置正常")

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    async def _verify_endpoint_functionality(self) -> None:
        """验证端点功能"""
        logger.info("🔗 第三阶段：端点功能验证")

        # 验证健康检查端点
        await self._test_health_endpoint()

        # 验证信息端点
        await self._test_info_endpoint()

        # 验证指标端点
        await self._test_metrics_endpoint()

    async def _test_health_endpoint(self) -> None:
        """测试健康检查端点"""
        test_name = "健康检查端点功能测试"

        try:
            endpoint = HealthEndpoint()

            # 测试基本健康检查
            health_data = await endpoint.health()
            assert "status" in health_data, "健康检查响应缺少 status 字段"
            assert "timestamp" in health_data, "健康检查响应缺少 timestamp 字段"

            # 测试详细健康检查
            detailed_health = await endpoint.health(detailed=True)
            assert "status" in detailed_health, "详细健康检查响应缺少 status 字段"

            logger.info(f"    ✅ {test_name}")
            self._record_test_result(test_name, True, "健康检查端点功能正常")

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    async def _test_info_endpoint(self) -> None:
        """测试信息端点"""
        test_name = "信息端点功能测试"

        try:
            endpoint = InfoEndpoint()

            # 测试信息获取
            info_data = await endpoint.get_info()
            assert "app" in info_data, "信息响应缺少 app 字段"
            assert "timestamp" in info_data, "信息响应缺少 timestamp 字段"

            # 验证应用信息结构
            app_info = info_data["app"]
            assert "name" in app_info, "应用信息缺少 name 字段"
            assert "version" in app_info, "应用信息缺少 version 字段"

            logger.info(f"    ✅ {test_name}")
            self._record_test_result(test_name, True, "信息端点功能正常")

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    async def _test_metrics_endpoint(self) -> None:
        """测试指标端点"""
        test_name = "指标端点功能测试"

        try:
            endpoint = MetricsEndpoint()

            # 测试指标获取
            metrics_data = await endpoint.metrics()
            assert "timestamp" in metrics_data, "指标响应缺少 timestamp 字段"
            assert "metrics" in metrics_data, "指标响应缺少 metrics 字段"

            # 验证指标结构
            metrics = metrics_data["metrics"]
            assert isinstance(metrics, dict), "指标数据应该是字典类型"

            logger.info(f"    ✅ {test_name}")
            self._record_test_result(test_name, True, "指标端点功能正常")

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    async def _verify_integration_flow(self) -> None:
        """验证集成流程"""
        logger.info("🔄 第四阶段：集成流程验证")

        # 验证完整应用启动流程
        await self._test_application_startup()

    async def _test_application_startup(self) -> None:
        """测试应用启动流程"""
        test_name = "应用启动流程测试"

        try:
            # 创建应用实例
            app = MiniBootApplication("验证测试应用")

            # 验证初始状态
            assert app.application_context is None, "应用上下文应该初始为 None"
            assert app.actuator_properties is None, "Actuator 配置应该初始为 None"

            # 执行启动流程（简化版本，避免完整启动）
            await app._initialize_application_context()

            # 验证上下文创建
            assert app.application_context is not None, "应用上下文创建失败"
            assert app.application_context.is_running(), "应用上下文未正常启动"

            # 执行 Actuator 配置
            await app._configure_actuator_module()

            # 验证配置创建
            assert app.actuator_properties is not None, "Actuator 配置创建失败"
            assert app.actuator_properties.enabled, "Actuator 应该默认启用"

            logger.info(f"    ✅ {test_name}")
            self._record_test_result(test_name, True, "应用启动流程正常")

            # 清理资源
            if app.application_context and app.application_context.is_running():
                await app.application_context.stop()

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    async def _run_performance_tests(self) -> None:
        """运行性能基准测试"""
        logger.info("⚡ 第五阶段：性能基准测试")

        # 端点响应时间测试
        await self._test_endpoint_response_times()

        # 并发性能测试
        await self._test_concurrent_performance()

    async def _test_endpoint_response_times(self) -> None:
        """测试端点响应时间"""
        test_name = "端点响应时间测试"

        try:
            endpoints = [
                ("health", HealthEndpoint()),
                ("info", InfoEndpoint()),
                ("metrics", MetricsEndpoint()),
            ]

            response_times = {}

            for endpoint_name, endpoint in endpoints:
                start_time = time.time()

                if endpoint_name == "health":
                    await endpoint.health()
                elif endpoint_name == "info":
                    await endpoint.get_info()
                elif endpoint_name == "metrics":
                    await endpoint.metrics()

                end_time = time.time()
                response_time = (end_time - start_time) * 1000  # 转换为毫秒
                response_times[endpoint_name] = response_time

                logger.info(f"    📊 {endpoint_name} 端点响应时间: {response_time:.2f}ms")

            # 记录性能指标
            self.verification_results["performance_metrics"]["response_times"] = response_times

            # 验证响应时间合理性（应该在 100ms 以内）
            for endpoint_name, response_time in response_times.items():
                if response_time > 100:
                    logger.warning(f"    ⚠️  {endpoint_name} 端点响应时间较慢: {response_time:.2f}ms")

            logger.info(f"    ✅ {test_name}")
            self._record_test_result(test_name, True, f"端点响应时间测试完成: {response_times}")

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    async def _test_concurrent_performance(self) -> None:
        """测试并发性能"""
        test_name = "并发性能测试"

        try:
            # 创建健康检查端点用于并发测试
            health_endpoint = HealthEndpoint()

            # 并发请求数量
            concurrent_requests = 10

            async def single_request():
                """单个请求"""
                return await health_endpoint.health()

            # 执行并发测试
            start_time = time.time()
            tasks = [single_request() for _ in range(concurrent_requests)]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            end_time = time.time()

            # 分析结果
            successful_requests = sum(1 for result in results if not isinstance(result, Exception))
            failed_requests = concurrent_requests - successful_requests
            total_time = (end_time - start_time) * 1000  # 转换为毫秒
            avg_time_per_request = total_time / concurrent_requests

            # 记录性能指标
            concurrent_metrics = {
                "concurrent_requests": concurrent_requests,
                "successful_requests": successful_requests,
                "failed_requests": failed_requests,
                "total_time_ms": total_time,
                "avg_time_per_request_ms": avg_time_per_request
            }
            self.verification_results["performance_metrics"]["concurrent_performance"] = concurrent_metrics

            logger.info(f"    📊 并发请求数: {concurrent_requests}")
            logger.info(f"    ✅ 成功请求数: {successful_requests}")
            logger.info(f"    ❌ 失败请求数: {failed_requests}")
            logger.info(f"    ⏱️  总耗时: {total_time:.2f}ms")
            logger.info(f"    📈 平均每请求耗时: {avg_time_per_request:.2f}ms")

            # 验证并发性能
            if failed_requests == 0 and avg_time_per_request < 50:
                logger.info(f"    ✅ {test_name}")
                self._record_test_result(test_name, True, f"并发性能良好: {concurrent_metrics}")
            else:
                logger.warning(f"    ⚠️  {test_name}: 性能可能需要优化")
                self._record_test_result(test_name, True, f"并发测试完成但性能待优化: {concurrent_metrics}")

        except Exception as e:
            logger.error(f"    ❌ {test_name}: {e}")
            self._record_test_result(test_name, False, str(e))

    def _record_test_result(self, test_name: str, passed: bool, details: str) -> None:
        """记录测试结果"""
        self.verification_results["total_tests"] += 1

        if passed:
            self.verification_results["passed_tests"] += 1
        else:
            self.verification_results["failed_tests"] += 1

        self.verification_results["test_details"].append({
            "test_name": test_name,
            "passed": passed,
            "details": details,
            "timestamp": time.time()
        })

    async def _generate_verification_report(self) -> None:
        """生成验证报告"""
        logger.info("")
        logger.info("=" * 80)
        logger.info("📊 Mini-Boot Actuator 集成验证报告")
        logger.info("=" * 80)

        # 基本统计
        total_time = self.verification_results["end_time"] - self.verification_results["start_time"]
        pass_rate = (self.verification_results["passed_tests"] / self.verification_results["total_tests"]) * 100

        logger.info(f"🧪 测试总数: {self.verification_results['total_tests']}")
        logger.info(f"✅ 通过测试: {self.verification_results['passed_tests']}")
        logger.info(f"❌ 失败测试: {self.verification_results['failed_tests']}")
        logger.info(f"📈 通过率: {pass_rate:.1f}%")
        logger.info(f"⏱️  总验证时间: {total_time:.2f}s")

        # 性能指标摘要
        if self.verification_results["performance_metrics"]:
            logger.info("")
            logger.info("⚡ 性能指标摘要:")

            if "response_times" in self.verification_results["performance_metrics"]:
                response_times = self.verification_results["performance_metrics"]["response_times"]
                for endpoint, time_ms in response_times.items():
                    logger.info(f"   📊 {endpoint} 端点: {time_ms:.2f}ms")

            if "concurrent_performance" in self.verification_results["performance_metrics"]:
                concurrent = self.verification_results["performance_metrics"]["concurrent_performance"]
                logger.info(f"   🔄 并发性能: {concurrent['successful_requests']}/{concurrent['concurrent_requests']} 成功")
                logger.info(f"   📈 平均响应时间: {concurrent['avg_time_per_request_ms']:.2f}ms")

        # 失败测试详情
        if self.verification_results["failed_tests"] > 0:
            logger.info("")
            logger.info("❌ 失败测试详情:")
            for test_detail in self.verification_results["test_details"]:
                if not test_detail["passed"]:
                    logger.info(f"   🔸 {test_detail['test_name']}: {test_detail['details']}")

        # 验证结论
        logger.info("")
        if self.verification_results["failed_tests"] == 0:
            logger.info("🎉 所有测试通过！Mini-Boot Actuator 集成功能完全正常")
        elif pass_rate >= 80:
            logger.info("✅ 大部分测试通过，Actuator 集成基本正常，部分功能可能需要优化")
        else:
            logger.info("⚠️  多个测试失败，Actuator 集成可能存在问题，需要进一步检查")

        logger.info("=" * 80)


async def main():
    """主函数 - 运行完整的集成验证"""
    try:
        logger.info("🚀 启动 Mini-Boot Actuator 集成验证")

        # 创建验证器
        verifier = ActuatorIntegrationVerifier()

        # 运行验证
        results = await verifier.run_verification()

        # 保存验证结果到文件
        import json
        with open("actuator_verification_results.json", "w", encoding="utf-8") as f:
            json.dump(results, f, ensure_ascii=False, indent=2, default=str)

        logger.info("📄 验证结果已保存到 actuator_verification_results.json")

        return results

    except Exception as e:
        logger.error(f"❌ 验证过程失败: {e}")
        raise


if __name__ == "__main__":
    """程序入口点"""
    logger.info("🔍 启动 Mini-Boot Actuator 集成验证脚本")
    asyncio.run(main())

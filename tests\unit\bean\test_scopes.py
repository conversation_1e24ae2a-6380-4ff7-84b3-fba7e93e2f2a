#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean作用域单元测试
"""

import unittest
import threading
import time
from typing import Any
from unittest.mock import Mock, patch

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import DefaultBeanFactory


class CounterBean:
    """计数器Bean，用于测试实例创建"""
    
    _instance_count = 0
    _instances = []
    
    def __init__(self, name: str = "counter"):
        CounterBean._instance_count += 1
        self.instance_id = CounterBean._instance_count
        self.name = name
        self.created_at = time.time()
        CounterBean._instances.append(self)
    
    @classmethod
    def reset_counter(cls):
        """重置计数器"""
        cls._instance_count = 0
        cls._instances.clear()
    
    @classmethod
    def get_instance_count(cls):
        """获取实例数量"""
        return cls._instance_count
    
    def get_info(self) -> dict:
        return {
            "instance_id": self.instance_id,
            "name": self.name,
            "created_at": self.created_at
        }


class StatefulBean:
    """有状态Bean，用于测试状态隔离"""
    
    def __init__(self, initial_value: int = 0):
        self.value = initial_value
        self.operations = []
    
    def increment(self):
        self.value += 1
        self.operations.append(f"increment to {self.value}")
    
    def decrement(self):
        self.value -= 1
        self.operations.append(f"decrement to {self.value}")
    
    def set_value(self, value: int):
        self.value = value
        self.operations.append(f"set to {value}")
    
    def get_state(self) -> dict:
        return {
            "value": self.value,
            "operations_count": len(self.operations),
            "last_operation": self.operations[-1] if self.operations else None
        }


class TestBeanScopes(unittest.TestCase):
    """Bean作用域测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.factory = DefaultBeanFactory()
        CounterBean.reset_counter()
    
    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.factory, 'destroy_singletons'):
            self.factory.destroy_singletons()
        CounterBean.reset_counter()
    
    def test_singleton_scope_basic(self):
        """测试单例作用域基本功能"""
        bean_def = BeanDefinition(
            bean_name="singletonBean",
            bean_class=CounterBean,
            scope=BeanScope.SINGLETON
        )
        
        self.factory._registry.register_bean_definition("singletonBean", bean_def)
        
        # 多次获取应该返回同一个实例
        bean1 = self.factory.get_bean("singletonBean")
        bean2 = self.factory.get_bean("singletonBean")
        bean3 = self.factory.get_bean("singletonBean")
        
        # 验证是同一个实例
        self.assertIs(bean1, bean2)
        self.assertIs(bean2, bean3)
        self.assertEqual(bean1.instance_id, bean2.instance_id)
        self.assertEqual(CounterBean.get_instance_count(), 1)
    
    def test_prototype_scope_basic(self):
        """测试原型作用域基本功能"""
        bean_def = BeanDefinition(
            bean_name="prototypeBean",
            bean_class=CounterBean,
            scope=BeanScope.PROTOTYPE
        )
        
        self.factory._registry.register_bean_definition("prototypeBean", bean_def)
        
        # 多次获取应该返回不同实例
        bean1 = self.factory.get_bean("prototypeBean")
        bean2 = self.factory.get_bean("prototypeBean")
        bean3 = self.factory.get_bean("prototypeBean")
        
        # 验证是不同实例
        self.assertIsNot(bean1, bean2)
        self.assertIsNot(bean2, bean3)
        self.assertIsNot(bean1, bean3)
        self.assertNotEqual(bean1.instance_id, bean2.instance_id)
        self.assertEqual(CounterBean.get_instance_count(), 3)
    
    def test_singleton_state_sharing(self):
        """测试单例状态共享"""
        bean_def = BeanDefinition(
            bean_name="statefulSingleton",
            bean_class=StatefulBean,
            scope=BeanScope.SINGLETON
        )
        
        self.factory._registry.register_bean_definition("statefulSingleton", bean_def)
        
        # 获取实例并修改状态
        bean1 = self.factory.get_bean("statefulSingleton")
        bean1.increment()
        bean1.increment()
        
        # 再次获取实例，状态应该保持
        bean2 = self.factory.get_bean("statefulSingleton")
        self.assertEqual(bean2.value, 2)
        self.assertEqual(len(bean2.operations), 2)
        
        # 继续修改状态
        bean2.decrement()
        
        # 第三次获取，状态继续保持
        bean3 = self.factory.get_bean("statefulSingleton")
        self.assertEqual(bean3.value, 1)
        self.assertEqual(len(bean3.operations), 3)
    
    def test_prototype_state_isolation(self):
        """测试原型状态隔离"""
        bean_def = BeanDefinition(
            bean_name="statefulPrototype",
            bean_class=StatefulBean,
            scope=BeanScope.PROTOTYPE
        )
        
        self.factory._registry.register_bean_definition("statefulPrototype", bean_def)
        
        # 获取多个实例并分别修改状态
        bean1 = self.factory.get_bean("statefulPrototype")
        bean1.increment()
        bean1.increment()
        
        bean2 = self.factory.get_bean("statefulPrototype")
        bean2.set_value(100)
        bean2.decrement()
        
        bean3 = self.factory.get_bean("statefulPrototype")
        bean3.set_value(-5)
        
        # 验证状态隔离
        self.assertEqual(bean1.value, 2)
        self.assertEqual(bean2.value, 99)
        self.assertEqual(bean3.value, -5)
        
        self.assertEqual(len(bean1.operations), 2)
        self.assertEqual(len(bean2.operations), 2)
        self.assertEqual(len(bean3.operations), 1)
    
    def test_mixed_scopes_interaction(self):
        """测试混合作用域交互"""
        # 注册单例Bean
        singleton_def = BeanDefinition(
            bean_name="singletonCounter",
            bean_class=CounterBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("singletonCounter", singleton_def)
        
        # 注册原型Bean
        prototype_def = BeanDefinition(
            bean_name="prototypeCounter",
            bean_class=CounterBean,
            scope=BeanScope.PROTOTYPE
        )
        self.factory._registry.register_bean_definition("prototypeCounter", prototype_def)
        
        # 获取Bean
        singleton1 = self.factory.get_bean("singletonCounter")
        singleton2 = self.factory.get_bean("singletonCounter")
        
        prototype1 = self.factory.get_bean("prototypeCounter")
        prototype2 = self.factory.get_bean("prototypeCounter")
        
        # 验证作用域行为
        self.assertIs(singleton1, singleton2)
        self.assertIsNot(prototype1, prototype2)
        self.assertEqual(CounterBean.get_instance_count(), 3)  # 1个单例 + 2个原型
    
    def test_scope_with_dependencies(self):
        """测试作用域与依赖注入的结合"""
        # 注册单例依赖
        dependency_def = BeanDefinition(
            bean_name="singletonDependency",
            bean_class=CounterBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("singletonDependency", dependency_def)
        
        # 注册原型Bean，依赖单例
        class DependentBean:
            def __init__(self):
                self.dependency = None
                self.instance_id = CounterBean.get_instance_count() + 1
            
            def set_dependency(self, dep):
                self.dependency = dep
        
        dependent_def = BeanDefinition(
            bean_name="dependentPrototype",
            bean_class=DependentBean,
            scope=BeanScope.PROTOTYPE
        )
        dependent_def.add_property_value("dependency", ref="singletonDependency")
        self.factory._registry.register_bean_definition("dependentPrototype", dependent_def)
        
        # 获取多个原型实例
        dependent1 = self.factory.get_bean("dependentPrototype")
        dependent2 = self.factory.get_bean("dependentPrototype")
        
        # 验证：原型Bean是不同实例，但依赖的单例是同一个
        self.assertIsNot(dependent1, dependent2)
        self.assertIs(dependent1.dependency, dependent2.dependency)
        self.assertIsInstance(dependent1.dependency, CounterBean)
    
    def test_scope_thread_safety(self):
        """测试作用域线程安全"""
        bean_def = BeanDefinition(
            bean_name="threadSafeBean",
            bean_class=CounterBean,
            scope=BeanScope.SINGLETON
        )
        
        self.factory._registry.register_bean_definition("threadSafeBean", bean_def)
        
        results = []
        
        def get_bean_in_thread():
            bean = self.factory.get_bean("threadSafeBean")
            results.append(bean)
        
        # 创建多个线程同时获取Bean
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=get_bean_in_thread)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证所有线程获取的是同一个实例
        first_bean = results[0]
        for bean in results:
            self.assertIs(bean, first_bean)
        
        self.assertEqual(CounterBean.get_instance_count(), 1)
    
    def test_prototype_thread_safety(self):
        """测试原型作用域线程安全"""
        bean_def = BeanDefinition(
            bean_name="threadSafePrototype",
            bean_class=CounterBean,
            scope=BeanScope.PROTOTYPE
        )
        
        self.factory._registry.register_bean_definition("threadSafePrototype", bean_def)
        
        results = []
        
        def get_bean_in_thread():
            bean = self.factory.get_bean("threadSafePrototype")
            results.append(bean)
        
        # 创建多个线程同时获取Bean
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=get_bean_in_thread)
            threads.append(thread)
            thread.start()
        
        # 等待所有线程完成
        for thread in threads:
            thread.join()
        
        # 验证每个线程获取的是不同实例
        unique_beans = set(id(bean) for bean in results)
        self.assertEqual(len(unique_beans), 5)
        self.assertEqual(CounterBean.get_instance_count(), 5)
    
    def test_scope_performance(self):
        """测试作用域性能"""
        # 注册单例Bean
        singleton_def = BeanDefinition(
            bean_name="perfSingleton",
            bean_class=CounterBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("perfSingleton", singleton_def)
        
        # 注册原型Bean
        prototype_def = BeanDefinition(
            bean_name="perfPrototype",
            bean_class=CounterBean,
            scope=BeanScope.PROTOTYPE
        )
        self.factory._registry.register_bean_definition("perfPrototype", prototype_def)
        
        # 测试单例性能
        start_time = time.time()
        for _ in range(1000):
            self.factory.get_bean("perfSingleton")
        singleton_time = time.time() - start_time
        
        # 重置计数器
        CounterBean.reset_counter()
        
        # 测试原型性能
        start_time = time.time()
        for _ in range(100):  # 原型创建成本更高，减少次数
            self.factory.get_bean("perfPrototype")
        prototype_time = time.time() - start_time
        
        # 单例应该比原型快很多
        self.assertLess(singleton_time, prototype_time)
        self.assertEqual(CounterBean.get_instance_count(), 100)
    
    def test_scope_validation(self):
        """测试作用域验证"""
        # 测试有效作用域
        valid_scopes = [BeanScope.SINGLETON, BeanScope.PROTOTYPE]
        
        for scope in valid_scopes:
            bean_def = BeanDefinition(
                bean_name=f"bean_{scope.value}",
                bean_class=CounterBean,
                scope=scope
            )
            
            # 应该能够成功注册
            self.factory._registry.register_bean_definition(f"bean_{scope.value}", bean_def)
            
            # 应该能够成功获取
            bean = self.factory.get_bean(f"bean_{scope.value}")
            self.assertIsInstance(bean, CounterBean)


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python
"""
Web框架集成测试用例

测试各模块间的协作和端到端功能。
"""

import unittest
import asyncio
import time
from unittest.mock import Mock, patch, AsyncMock
from fastapi import FastAPI
from fastapi.testclient import TestClient

from miniboot.web.application import WebApplication
from miniboot.web.properties import WebProperties, BackpressureConfig, AsyncOptimizationConfig
from miniboot.web.registry import ControllerRegistry
from miniboot.web.middleware import MiddlewareManager, ResponseMiddleware
from miniboot.web.exceptions import GlobalExceptionHandler


class TestWebApplicationIntegration(unittest.TestCase):
    """Web应用集成测试"""

    def test_full_application_creation(self):
        """测试完整应用创建"""
        properties = WebProperties(host="127.0.0.1", port=8080, title="Integration Test App")

        app = WebApplication(properties=properties)

        # 验证应用创建成功
        self.assertIsNotNone(app)
        self.assertEqual(app.properties.title, "Integration Test App")

    def test_application_status_monitoring(self):
        """测试应用状态监控"""
        properties = WebProperties()
        app = WebApplication(properties=properties)

        status = app.get_status()

        # 验证状态结构
        self.assertIsInstance(status, dict)
        self.assertIn("smart_components_enabled", status)
        self.assertIn("controller_registry", status)
        self.assertIn("middleware_manager", status)

    def test_configuration_driven_features(self):
        """测试配置驱动功能"""
        # 测试不同配置组合
        configs = [
            # 基础配置
            WebProperties(),
            # 启用背压控制
            WebProperties(backpressure=BackpressureConfig(enabled=True)),
            # 启用智能调度
            WebProperties(async_optimization=AsyncOptimizationConfig(enabled=True, intelligent_scheduling=True)),
        ]

        for config in configs:
            app = WebApplication(properties=config)

            # 验证应用创建成功
            self.assertIsNotNone(app.properties)

            # 验证状态获取
            status = app.get_status()
            self.assertIsInstance(status, dict)


class TestWebApplicationAsync(unittest.IsolatedAsyncioTestCase):
    """Web应用异步集成测试"""

    async def test_application_initialization(self):
        """测试应用初始化"""
        properties = WebProperties()
        app = WebApplication(properties=properties)

        await app.initialize()

        # 验证所有组件都已初始化
        self.assertTrue(app._is_initialized)
        self.assertIsNotNone(app.fastapi_app)
        self.assertIsNotNone(app.controller_registry)
        self.assertIsNotNone(app.middleware_manager)
        self.assertIsNotNone(app.exception_handler)

    async def test_application_with_intelligent_features(self):
        """测试启用智能功能的应用"""
        properties = WebProperties()
        properties.async_optimization.enabled = True
        properties.async_optimization.intelligent_scheduling = True
        properties.backpressure.enabled = True

        app = WebApplication(properties=properties)

        # Mock智能组件
        with (
            patch("miniboot.web.application.SmartTaskScheduler") as mock_scheduler,
            patch("miniboot.web.application.ConfigurableBackpressureController") as mock_controller,
        ):
            mock_scheduler.return_value = Mock()
            mock_controller.return_value = Mock()

            await app.initialize()

            # 验证智能组件已创建
            self.assertIsNotNone(app.smart_scheduler)
            self.assertIsNotNone(app.backpressure_controller)


class TestControllerRegistryIntegration(unittest.IsolatedAsyncioTestCase):
    """控制器注册器集成测试"""

    async def test_controller_registration(self):
        """测试控制器注册"""
        registry = ControllerRegistry()

        # 创建测试控制器
        class TestController:
            def get_users(self):
                return {"users": ["user1", "user2"]}

        controller = TestController()

        # 注册控制器
        success = await registry.register_async(controller, "TestController")
        self.assertIsInstance(success, bool)

        # 验证注册结果
        controllers = registry.get_controllers()
        self.assertIsInstance(controllers, dict)

    def test_controller_registry_with_fastapi(self):
        """测试控制器注册器与FastAPI集成"""
        registry = ControllerRegistry()
        fastapi_app = FastAPI()

        # 设置FastAPI应用
        registry.set_app(fastapi_app)

        # 创建测试控制器
        class ApiController:
            def health_check(self):
                return {"status": "healthy"}

        controller = ApiController()

        # 注册控制器
        success = registry._register_basic(controller, "ApiController")
        self.assertIsInstance(success, bool)


class TestMiddlewareIntegration(unittest.TestCase):
    """中间件集成测试"""

    def test_middleware_manager_integration(self):
        """测试中间件管理器集成"""
        manager = MiddlewareManager()

        # 注册中间件
        response_middleware = ResponseMiddleware()
        success = manager.register_middleware(response_middleware)

        self.assertTrue(success)

        # 验证中间件注册
        middlewares = manager.get_all_middlewares()
        self.assertGreaterEqual(len(middlewares), 1)

    def test_default_middleware_manager(self):
        """测试默认中间件管理器"""
        from miniboot.web.middleware import create_default_middleware_manager

        manager = create_default_middleware_manager()

        self.assertIsInstance(manager, MiddlewareManager)

        middlewares = manager.get_all_middlewares()
        self.assertGreaterEqual(len(middlewares), 4)  # 至少4个默认中间件


class TestExceptionHandlingIntegration(unittest.IsolatedAsyncioTestCase):
    """异常处理集成测试"""

    async def test_global_exception_handler(self):
        """测试全局异常处理器"""
        handler = GlobalExceptionHandler()

        # 创建测试请求
        mock_request = Mock()
        mock_request.state = Mock()
        mock_request.state.request_id = "test-request-123"

        # 测试异常处理
        from miniboot.web.response import BusinessError

        business_error = BusinessError("Test error")

        response = await handler.handle_exception(mock_request, business_error)

        self.assertEqual(response.status_code, 400)
        self.assertIn("application/json", response.media_type)

    def test_exception_handler_with_fastapi(self):
        """测试异常处理器与FastAPI集成"""
        app = FastAPI()
        handler = GlobalExceptionHandler()

        # 注册异常处理器
        @app.exception_handler(Exception)
        async def global_exception_handler(request, exc):
            return await handler.handle_exception(request, exc)

        # 创建测试客户端
        client = TestClient(app)

        # 添加测试路由
        @app.get("/test-error")
        async def test_error():
            raise ValueError("Test error")

        # 测试异常处理
        response = client.get("/test-error")
        self.assertIn(response.status_code, [400, 500])


class TestEndToEndIntegration(unittest.IsolatedAsyncioTestCase):
    """端到端集成测试"""

    async def test_complete_request_pipeline(self):
        """测试完整的请求处理流水线"""
        # 创建应用配置
        properties = WebProperties(title="E2E Test App", version="1.0.0")

        # 创建Web应用
        app = WebApplication(properties=properties)
        await app.initialize()

        # 获取FastAPI应用
        fastapi_app = app.get_app()

        # 添加测试路由
        @fastapi_app.get("/api/test")
        async def test_endpoint():
            return {"message": "Hello, World!", "timestamp": time.time()}

        # 创建测试客户端
        client = TestClient(fastapi_app)

        # 发送测试请求
        response = client.get("/api/test")

        # 验证响应
        self.assertEqual(response.status_code, 200)
        data = response.json()
        self.assertIn("message", data)
        self.assertEqual(data["message"], "Hello, World!")

    async def test_intelligent_features_integration(self):
        """测试智能功能集成"""
        properties = WebProperties()
        properties.async_optimization.enabled = True
        properties.async_optimization.intelligent_scheduling = True

        app = WebApplication(properties=properties)

        # Mock智能组件
        with patch("miniboot.web.application.SmartTaskScheduler") as mock_scheduler:
            mock_scheduler_instance = Mock()
            mock_scheduler_instance.is_running.return_value = True
            mock_scheduler.return_value = mock_scheduler_instance

            await app.initialize()

            # 验证智能调度器集成
            self.assertIsNotNone(app.smart_scheduler)


class TestPerformanceIntegration(unittest.TestCase):
    """性能集成测试"""

    def test_application_initialization_performance(self):
        """测试应用初始化性能"""
        properties = WebProperties()

        start_time = time.time()

        app = WebApplication(properties=properties)

        end_time = time.time()
        initialization_time = end_time - start_time

        # 验证初始化时间合理（应该在1秒内完成）
        self.assertLess(initialization_time, 1.0)

    def test_status_monitoring_performance(self):
        """测试状态监控性能"""
        properties = WebProperties()
        app = WebApplication(properties=properties)

        # 测量状态获取时间
        start_time = time.time()

        for _ in range(100):
            status = app.get_status()
            self.assertIsInstance(status, dict)

        end_time = time.time()
        avg_time = (end_time - start_time) / 100

        # 验证平均响应时间（应该在1ms内）
        self.assertLess(avg_time, 0.001)


def run_tests():
    """运行所有集成测试"""
    test_suite = unittest.TestSuite()

    test_classes = [
        TestWebApplicationIntegration,
        TestWebApplicationAsync,
        TestControllerRegistryIntegration,
        TestMiddlewareIntegration,
        TestExceptionHandlingIntegration,
        TestEndToEndIntegration,
        TestPerformanceIntegration,
    ]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        test_suite.addTests(tests)

    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(test_suite)

    return result


if __name__ == "__main__":
    print("🚀 运行Web框架集成测试...")
    result = run_tests()

    if result.wasSuccessful():
        print("✅ 集成测试全部通过！")
    else:
        print(f"❌ 集成测试失败: {len(result.failures)} 失败, {len(result.errors)} 错误")

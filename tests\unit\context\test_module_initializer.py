#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 模块化条件初始化器单元测试

测试模块化条件初始化器的核心功能，包括：
- 模块注册和条件检查
- 初始化顺序管理
- 状态跟踪和错误处理
"""

import asyncio
import unittest
from unittest.mock import AsyncMock, MagicMock, patch

from miniboot.context.module_initializer import (
    ConfigBasedCondition,
    DependencyCondition,
    ModuleInfo,
    ModuleInitializer,
    ModuleState,
    UnifiedModuleInitializer,
)
from miniboot.env import MapPropertySource, StandardEnvironment


class ModuleInitializerTestCase(unittest.IsolatedAsyncioTestCase):
    """模块初始化器单元测试"""

    def setUp(self):
        """设置测试环境"""
        self.environment = StandardEnvironment()

        # 创建测试属性源
        test_properties = {
            "miniboot.web.enabled": True,
            "miniboot.scheduler.enabled": False,
            "miniboot.actuators.endpoints.web.enabled": True,
            "miniboot.async.enabled": True,
            "mini.web.static.enabled": False,  # 禁用静态文件服务避免警告
        }
        test_property_source = MapPropertySource("test", test_properties)
        self.environment.get_property_sources().add_first(test_property_source)

        self.module_initializer = ModuleInitializer(self.environment)

    def test_module_registration(self):
        """测试模块注册"""

        # 创建测试初始化函数
        def test_initializer():
            pass

        # 注册模块
        self.module_initializer.register_module(
            name="test_module",
            description="Test Module",
            config_key="test.enabled",
            initializer=test_initializer,
            dependencies=["dependency1"],
            priority=50,
            required=True,
        )

        # 验证模块已注册
        self.assertIn("test_module", self.module_initializer.modules)

        module_info = self.module_initializer.modules["test_module"]
        self.assertEqual(module_info.name, "test_module")
        self.assertEqual(module_info.description, "Test Module")
        self.assertEqual(module_info.config_key, "test.enabled")
        self.assertEqual(module_info.initializer, test_initializer)
        self.assertEqual(module_info.dependencies, ["dependency1"])
        self.assertEqual(module_info.priority, 50)
        self.assertTrue(module_info.required)
        self.assertEqual(module_info.state, ModuleState.NOT_INITIALIZED)

    def test_config_based_condition(self):
        """测试基于配置的条件"""
        condition = ConfigBasedCondition()

        # 创建测试模块信息
        module_info = ModuleInfo(name="test_module", description="Test Module", config_key="miniboot.web.enabled")

        # 测试启用的模块
        result = condition.evaluate(self.environment, module_info)
        self.assertTrue(result)

        # 测试禁用的模块 - 使用一个新的配置键
        module_info.config_key = "miniboot.test.disabled"  # 这个配置不存在，默认为False
        result = condition.evaluate(self.environment, module_info)
        self.assertFalse(result)

        # 测试不存在的配置
        module_info.config_key = "nonexistent.config"
        result = condition.evaluate(self.environment, module_info)
        self.assertFalse(result)

    def test_dependency_condition(self):
        """测试依赖条件"""
        # 创建模块注册表
        modules = {
            "module_a": ModuleInfo("module_a", "Module A", "test.a", state=ModuleState.INITIALIZED),
            "module_b": ModuleInfo("module_b", "Module B", "test.b", state=ModuleState.NOT_INITIALIZED),
            "module_c": ModuleInfo("module_c", "Module C", "test.c", dependencies=["module_a"]),
        }

        condition = DependencyCondition(modules)

        # 测试依赖已初始化的模块
        result = condition.evaluate(self.environment, modules["module_c"])
        self.assertTrue(result)

        # 测试依赖未初始化的模块
        modules["module_c"].dependencies = ["module_b"]
        result = condition.evaluate(self.environment, modules["module_c"])
        self.assertFalse(result)

        # 测试依赖不存在的模块
        modules["module_c"].dependencies = ["nonexistent_module"]
        result = condition.evaluate(self.environment, modules["module_c"])
        self.assertFalse(result)

    async def test_module_initialization_success(self):
        """测试模块初始化成功"""
        # 创建测试初始化函数
        initialization_called = False

        def test_initializer():
            nonlocal initialization_called
            initialization_called = True

        # 注册模块
        self.module_initializer.register_module(
            name="test_module", description="Test Module", config_key="miniboot.web.enabled", initializer=test_initializer
        )

        # 执行初始化
        result = await self.module_initializer.initialize_modules()

        # 验证结果
        self.assertEqual(result["test_module"], ModuleState.INITIALIZED)
        self.assertTrue(initialization_called)
        self.assertIn("test_module", self.module_initializer.get_initialized_modules())

    async def test_module_initialization_disabled(self):
        """测试模块初始化被禁用"""
        # 创建测试初始化函数
        initialization_called = False

        def test_initializer():
            nonlocal initialization_called
            initialization_called = True

        # 创建禁用配置的属性源
        disabled_properties = {"miniboot.disabled.test": False}
        disabled_property_source = MapPropertySource("disabled_test", disabled_properties)
        self.environment.get_property_sources().add_first(disabled_property_source)

        # 注册模块（使用禁用的配置）
        self.module_initializer.register_module(
            name="test_module",
            description="Test Module",
            config_key="miniboot.disabled.test",  # 这个配置是False
            initializer=test_initializer,
        )

        # 执行初始化
        result = await self.module_initializer.initialize_modules()

        # 验证结果
        self.assertEqual(result["test_module"], ModuleState.DISABLED)
        self.assertFalse(initialization_called)
        self.assertNotIn("test_module", self.module_initializer.get_initialized_modules())

    async def test_module_initialization_failure(self):
        """测试模块初始化失败"""

        # 创建会抛出异常的初始化函数
        def failing_initializer():
            raise RuntimeError("Initialization failed")

        # 注册模块
        self.module_initializer.register_module(
            name="test_module", description="Test Module", config_key="miniboot.web.enabled", initializer=failing_initializer
        )

        # 执行初始化
        result = await self.module_initializer.initialize_modules()

        # 验证结果
        self.assertEqual(result["test_module"], ModuleState.FAILED)
        self.assertIn("test_module", self.module_initializer.get_failed_modules())

    async def test_required_module_failure(self):
        """测试必需模块初始化失败"""

        # 创建会抛出异常的初始化函数
        def failing_initializer():
            raise RuntimeError("Required module failed")

        # 注册必需模块
        self.module_initializer.register_module(
            name="required_module", description="Required Module", config_key="miniboot.web.enabled", initializer=failing_initializer, required=True
        )

        # 执行初始化应该抛出异常
        with self.assertRaises(RuntimeError) as context:
            await self.module_initializer.initialize_modules()

        self.assertIn("Required module required_module failed to initialize", str(context.exception))

    async def test_async_module_initialization(self):
        """测试异步模块初始化"""
        # 创建异步初始化函数
        initialization_called = False

        async def async_initializer():
            nonlocal initialization_called
            await asyncio.sleep(0.01)  # 模拟异步操作
            initialization_called = True

        # 注册模块
        self.module_initializer.register_module(
            name="async_module", description="Async Module", config_key="miniboot.web.enabled", initializer=async_initializer
        )

        # 执行初始化
        result = await self.module_initializer.initialize_modules()

        # 验证结果
        self.assertEqual(result["async_module"], ModuleState.INITIALIZED)
        self.assertTrue(initialization_called)

    async def test_module_initialization_order(self):
        """测试模块初始化顺序"""
        initialization_order = []

        def create_initializer(name):
            def initializer():
                initialization_order.append(name)

            return initializer

        # 注册不同优先级的模块
        self.module_initializer.register_module(
            name="high_priority",
            description="High Priority Module",
            config_key="miniboot.web.enabled",
            initializer=create_initializer("high_priority"),
            priority=10,
        )

        self.module_initializer.register_module(
            name="low_priority",
            description="Low Priority Module",
            config_key="miniboot.web.enabled",
            initializer=create_initializer("low_priority"),
            priority=100,
        )

        self.module_initializer.register_module(
            name="medium_priority",
            description="Medium Priority Module",
            config_key="miniboot.web.enabled",
            initializer=create_initializer("medium_priority"),
            priority=50,
        )

        # 执行初始化
        await self.module_initializer.initialize_modules()

        # 验证初始化顺序
        expected_order = ["high_priority", "medium_priority", "low_priority"]
        self.assertEqual(initialization_order, expected_order)


class UnifiedModuleInitializerTestCase(unittest.IsolatedAsyncioTestCase):
    """统一模块初始化器单元测试"""

    async def asyncSetUp(self):
        """设置异步测试环境"""
        # 设置asyncio慢回调阈值为更高的值，避免不必要的警告
        loop = asyncio.get_running_loop()
        if hasattr(loop, "slow_callback_duration"):
            loop.slow_callback_duration = 1.0  # 设置为1秒，避免0.125秒的警告

    def setUp(self):
        """设置测试环境"""
        # 创建模拟的应用上下文
        self.mock_context = MagicMock()
        self.mock_environment = StandardEnvironment()

        # 创建测试属性源（默认禁用所有模块）
        test_properties = {
            "miniboot.web.enabled": False,
            "miniboot.scheduler.enabled": False,
            "miniboot.actuators.endpoints.web.enabled": False,
            "miniboot.async.enabled": False,
            "mini.web.static.enabled": False,  # 禁用静态文件服务避免警告
        }
        test_property_source = MapPropertySource("test", test_properties)
        self.mock_environment.get_property_sources().add_first(test_property_source)

        self.mock_context.get_environment.return_value = self.mock_environment
        self.mock_context.get_property.side_effect = lambda key, default=None: self.mock_environment.get_property(key, default)
        self.mock_context.register_singleton = MagicMock()
        self.mock_context.get_bean_names.return_value = []

    @patch("miniboot.context.module_initializer.logger")
    async def test_unified_module_initialization(self, _mock_logger):
        """测试统一模块初始化"""
        # 创建统一模块初始化器
        unified_initializer = UnifiedModuleInitializer(self.mock_context)

        # 模拟模块初始化器的initialize_modules方法，避免真正启动服务器
        mock_module_states = {
            "async": ModuleState.DISABLED,
            "web": ModuleState.DISABLED,
            "scheduler": ModuleState.DISABLED,
            "actuator": ModuleState.DISABLED,
        }

        with patch.object(unified_initializer.module_initializer, "initialize_modules", return_value=mock_module_states) as mock_init:
            # 执行初始化（使用模拟的结果）
            result = await unified_initializer.initialize_all_modules()

            # 验证模拟方法被调用
            mock_init.assert_called_once()

        # 验证结果
        self.assertIn("module_states", result)
        self.assertIn("initialized_modules", result)
        self.assertIn("failed_modules", result)

        # 验证模块状态
        module_states = result["module_states"]
        self.assertIn("async", module_states)
        self.assertIn("web", module_states)
        self.assertIn("scheduler", module_states)
        self.assertIn("actuator", module_states)

        # 由于所有模块都配置为禁用，它们应该被标记为禁用状态
        for _module_name, state in module_states.items():
            self.assertEqual(state, ModuleState.DISABLED)

    def test_module_status_tracking(self):
        """测试模块状态跟踪"""
        # 创建统一模块初始化器
        unified_initializer = UnifiedModuleInitializer(self.mock_context)

        # 模拟模块初始化器的状态
        mock_initialized_modules = ["async", "web", "scheduler"]
        mock_failed_modules = ["actuator"]

        with (
            patch.object(unified_initializer.module_initializer, "get_initialized_modules", return_value=mock_initialized_modules),
            patch.object(unified_initializer.module_initializer, "get_failed_modules", return_value=mock_failed_modules),
        ):
            # 获取模块状态
            status = unified_initializer.get_module_status()

            # 验证状态
            self.assertTrue(status["async"])
            self.assertTrue(status["web"])
            self.assertTrue(status["scheduler"])
            self.assertFalse(status["actuator"])

    @patch("miniboot.context.module_initializer.logger")
    async def test_stop_all_modules(self, mock_logger):
        """测试停止所有模块"""
        # 创建统一模块初始化器
        unified_initializer = UnifiedModuleInitializer(self.mock_context)

        # 设置模拟的模块实例
        mock_web_app = MagicMock()
        mock_web_app.stop_server = AsyncMock()
        unified_initializer.web_application = mock_web_app

        mock_scheduler = MagicMock()
        mock_scheduler.shutdown = AsyncMock()
        unified_initializer.scheduler = mock_scheduler

        mock_actuator = MagicMock()
        mock_actuator.stop = AsyncMock()
        unified_initializer.actuator_context = mock_actuator

        # 设置异步集成标志
        unified_initializer.async_integration = True

        # 执行停止操作
        await unified_initializer.stop_all_modules()

        # 验证各模块的停止方法被调用
        mock_actuator.stop.assert_called_once()
        mock_scheduler.shutdown.assert_called_once_with(wait=True)
        mock_web_app.stop_server.assert_called_once()

        # 验证日志记录
        mock_logger.info.assert_any_call("Stopping all modules...")
        mock_logger.info.assert_any_call("🛑 Actuator service stopped")
        mock_logger.info.assert_any_call("🛑 Scheduler stopped")
        mock_logger.info.assert_any_call("🛑 Web server stopped")
        mock_logger.info.assert_any_call("🛑 Async components stopped")
        mock_logger.info.assert_any_call("All modules stopped")


if __name__ == "__main__":
    unittest.main()

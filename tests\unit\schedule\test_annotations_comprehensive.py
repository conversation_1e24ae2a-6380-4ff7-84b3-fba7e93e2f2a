#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 注解系统综合单元测试
"""

import unittest
import asyncio

from miniboot.schedule import Scheduled, EnableScheduling, ScheduledConfig, TaskType, ScheduleConfigurationError


class TestScheduledConfig(unittest.TestCase):
    """测试ScheduledConfig"""

    def test_fixed_rate_config(self):
        """测试固定频率配置"""
        config = ScheduledConfig(fixed_rate="5s")

        self.assertEqual(config.fixed_rate, "5s")
        self.assertIsNone(config.fixed_delay)
        self.assertIsNone(config.cron)
        self.assertEqual(config.initial_delay, "0s")
        self.assertEqual(config.time_unit, "seconds")
        self.assertIsNone(config.zone)

    def test_fixed_delay_config(self):
        """测试固定延迟配置"""
        config = ScheduledConfig(fixed_delay="10s", initial_delay="2s")

        self.assertEqual(config.fixed_delay, "10s")
        self.assertEqual(config.initial_delay, "2s")
        self.assertIsNone(config.fixed_rate)
        self.assertIsNone(config.cron)

    def test_cron_config(self):
        """测试Cron配置"""
        config = ScheduledConfig(cron="0 0 * * *", zone="UTC")

        self.assertEqual(config.cron, "0 0 * * *")
        self.assertEqual(config.zone, "UTC")
        self.assertIsNone(config.fixed_rate)
        self.assertIsNone(config.fixed_delay)

    def test_config_validation(self):
        """测试配置验证"""
        # 测试空配置
        with self.assertRaises(ScheduleConfigurationError):
            config = ScheduledConfig()
            config.validate()

        # 测试多重配置
        with self.assertRaises(ScheduleConfigurationError):
            config = ScheduledConfig(fixed_rate="5s", fixed_delay="10s")
            config.validate()

        # 测试无效的时间格式
        with self.assertRaises(ScheduleConfigurationError):
            config = ScheduledConfig(fixed_rate="invalid")
            config.validate()

        # 测试无效的Cron表达式
        with self.assertRaises(ScheduleConfigurationError):
            config = ScheduledConfig(cron="invalid cron")
            config.validate()

    def test_task_type_detection(self):
        """测试任务类型检测"""
        # 固定频率
        config = ScheduledConfig(fixed_rate="5s")
        self.assertEqual(config.get_task_type(), TaskType.FIXED_RATE)

        # 固定延迟
        config = ScheduledConfig(fixed_delay="10s")
        self.assertEqual(config.get_task_type(), TaskType.FIXED_DELAY)

        # Cron
        config = ScheduledConfig(cron="0 0 * * *")
        self.assertEqual(config.get_task_type(), TaskType.CRON)

    def test_time_parsing(self):
        """测试时间解析"""
        config = ScheduledConfig(fixed_rate="5s")

        # 测试秒
        self.assertEqual(config.parse_time_string("5s"), 5)
        self.assertEqual(config.parse_time_string("10sec"), 10)
        self.assertEqual(config.parse_time_string("15seconds"), 15)

        # 测试分钟
        self.assertEqual(config.parse_time_string("2m"), 120)
        self.assertEqual(config.parse_time_string("3min"), 180)
        self.assertEqual(config.parse_time_string("5minutes"), 300)

        # 测试小时
        self.assertEqual(config.parse_time_string("1h"), 3600)
        self.assertEqual(config.parse_time_string("2hour"), 7200)
        self.assertEqual(config.parse_time_string("3hours"), 10800)

        # 测试纯数字（默认秒）
        self.assertEqual(config.parse_time_string("30"), 30)

        # 测试无效格式
        with self.assertRaises(ScheduleConfigurationError):
            config.parse_time_string("invalid")

    def test_cron_validation(self):
        """测试Cron表达式验证"""
        config = ScheduledConfig(fixed_rate="1s")  # 创建有效配置用于测试

        # 有效的Cron表达式
        valid_crons = [
            "0 0 * * *",  # 每天午夜
            "*/5 * * * *",  # 每5分钟
            "0 9-17 * * 1-5",  # 工作日9-17点
            "0 0 1 * *",  # 每月1号
            "0 0 * * 0",  # 每周日
        ]

        for cron_expr in valid_crons:
            self.assertTrue(config.is_valid_cron(cron_expr))

        # 无效的Cron表达式
        invalid_crons = [
            "invalid",
            "* * * *",  # 缺少字段
            "60 * * * *",  # 无效分钟
            "* 25 * * *",  # 无效小时
            "* * 32 * *",  # 无效日期
            "* * * 13 *",  # 无效月份
            "* * * * 8",  # 无效星期
        ]

        for cron_expr in invalid_crons:
            self.assertFalse(config.is_valid_cron(cron_expr))


class TestScheduledDecorator(unittest.TestCase):
    """测试@Scheduled装饰器"""

    def test_scheduled_decorator_basic(self):
        """测试基本@Scheduled装饰器"""

        @Scheduled(fixed_rate="5s")
        def test_method():
            return "test_result"

        # 检查装饰器是否添加了元数据
        self.assertTrue(hasattr(test_method, "_scheduled_config"))
        config = test_method._scheduled_config

        self.assertIsInstance(config, ScheduledConfig)
        self.assertEqual(config.fixed_rate, "5s")
        self.assertEqual(config.get_task_type(), TaskType.FIXED_RATE)

    def test_scheduled_decorator_with_all_params(self):
        """测试带所有参数的@Scheduled装饰器"""

        @Scheduled(fixed_delay="10s", initial_delay="2s", zone="UTC")
        def test_method():
            return "test_result"

        config = test_method._scheduled_config
        self.assertEqual(config.fixed_delay, "10s")
        self.assertEqual(config.initial_delay, "2s")
        self.assertEqual(config.zone, "UTC")

    def test_scheduled_decorator_cron(self):
        """测试Cron表达式的@Scheduled装饰器"""

        @Scheduled(cron="0 0 * * *", zone="Asia/Shanghai")
        def test_method():
            return "test_result"

        config = test_method._scheduled_config
        self.assertEqual(config.cron, "0 0 * * *")
        self.assertEqual(config.zone, "Asia/Shanghai")
        self.assertEqual(config.get_task_type(), TaskType.CRON)

    def test_scheduled_decorator_async_method(self):
        """测试异步方法的@Scheduled装饰器"""

        @Scheduled(fixed_rate="3s")
        async def async_test_method():
            await asyncio.sleep(0.01)
            return "async_test_result"

        # 检查装饰器是否保持了异步特性
        self.assertTrue(asyncio.iscoroutinefunction(async_test_method))

        config = async_test_method._scheduled_config
        self.assertEqual(config.fixed_rate, "3s")

    def test_scheduled_decorator_validation(self):
        """测试@Scheduled装饰器验证"""
        # 测试无效配置
        with self.assertRaises(ScheduleConfigurationError):

            @Scheduled()  # 没有提供任何调度配置
            def invalid_method():
                pass

        # 测试冲突配置
        with self.assertRaises(ScheduleConfigurationError):

            @Scheduled(fixed_rate="5s", fixed_delay="10s")
            def conflicting_method():
                pass

    def test_scheduled_decorator_preserves_function(self):
        """测试@Scheduled装饰器保持函数特性"""

        @Scheduled(fixed_rate="1s")
        def test_function(x, y):
            """Test function docstring"""
            return x + y

        # 检查函数特性是否保持
        self.assertEqual(test_function.__name__, "test_function")
        self.assertEqual(test_function.__doc__, "Test function docstring")

        # 检查函数仍然可以正常调用
        result = test_function(2, 3)
        self.assertEqual(result, 5)


class TestEnableSchedulingDecorator(unittest.TestCase):
    """测试@EnableScheduling装饰器"""

    def test_enable_scheduling_basic(self):
        """测试基本@EnableScheduling装饰器"""

        @EnableScheduling
        class TestService:
            @Scheduled(fixed_rate="5s")
            def scheduled_method(self):
                return "scheduled_result"

            def normal_method(self):
                return "normal_result"

        # 检查类是否被标记为启用调度
        self.assertTrue(hasattr(TestService, "_scheduling_enabled"))
        self.assertTrue(TestService._scheduling_enabled)

        # 检查调度方法是否被正确识别
        service = TestService()
        self.assertTrue(hasattr(service.scheduled_method, "_scheduled_config"))
        self.assertFalse(hasattr(service.normal_method, "_scheduled_config"))

    def test_enable_scheduling_multiple_methods(self):
        """测试多个调度方法的@EnableScheduling"""

        @EnableScheduling
        class MultiScheduleService:
            @Scheduled(fixed_rate="5s")
            def method1(self):
                return "result1"

            @Scheduled(fixed_delay="10s")
            def method2(self):
                return "result2"

            @Scheduled(cron="0 0 * * *")
            def method3(self):
                return "result3"

            def normal_method(self):
                return "normal"

        service = MultiScheduleService()

        # 检查所有调度方法
        self.assertTrue(hasattr(service.method1, "_scheduled_config"))
        self.assertTrue(hasattr(service.method2, "_scheduled_config"))
        self.assertTrue(hasattr(service.method3, "_scheduled_config"))
        self.assertFalse(hasattr(service.normal_method, "_scheduled_config"))

        # 检查配置类型
        self.assertEqual(service.method1._scheduled_config.get_task_type(), TaskType.FIXED_RATE)
        self.assertEqual(service.method2._scheduled_config.get_task_type(), TaskType.FIXED_DELAY)
        self.assertEqual(service.method3._scheduled_config.get_task_type(), TaskType.CRON)

    def test_enable_scheduling_inheritance(self):
        """测试@EnableScheduling的继承"""

        @EnableScheduling
        class BaseService:
            @Scheduled(fixed_rate="5s")
            def base_method(self):
                return "base_result"

        class DerivedService(BaseService):
            @Scheduled(fixed_delay="10s")
            def derived_method(self):
                return "derived_result"

        # 基类应该启用调度
        self.assertTrue(BaseService._scheduling_enabled)

        # 派生类继承调度启用状态
        derived_service = DerivedService()
        self.assertTrue(hasattr(derived_service.base_method, "_scheduled_config"))
        self.assertTrue(hasattr(derived_service.derived_method, "_scheduled_config"))

    def test_enable_scheduling_async_methods(self):
        """测试异步方法的@EnableScheduling"""

        @EnableScheduling
        class AsyncService:
            @Scheduled(fixed_rate="3s")
            async def async_scheduled_method(self):
                await asyncio.sleep(0.01)
                return "async_scheduled_result"

            @Scheduled(fixed_delay="5s")
            def sync_scheduled_method(self):
                return "sync_scheduled_result"

        service = AsyncService()

        # 检查异步方法保持异步特性
        self.assertTrue(asyncio.iscoroutinefunction(service.async_scheduled_method))
        self.assertFalse(asyncio.iscoroutinefunction(service.sync_scheduled_method))

        # 检查配置
        self.assertTrue(hasattr(service.async_scheduled_method, "_scheduled_config"))
        self.assertTrue(hasattr(service.sync_scheduled_method, "_scheduled_config"))

    def test_enable_scheduling_preserves_class(self):
        """测试@EnableScheduling保持类特性"""

        @EnableScheduling
        class TestService:
            """Test service docstring"""

            def __init__(self, name):
                self.name = name

            @Scheduled(fixed_rate="1s")
            def get_name(self):
                return self.name

        # 检查类特性是否保持
        self.assertEqual(TestService.__name__, "TestService")
        self.assertEqual(TestService.__doc__, "Test service docstring")

        # 检查类仍然可以正常实例化和使用
        service = TestService("test_name")
        self.assertEqual(service.name, "test_name")
        self.assertEqual(service.get_name(), "test_name")


class TestAnnotationIntegration(unittest.TestCase):
    """测试注解集成"""

    def test_full_annotation_integration(self):
        """测试完整的注解集成"""

        @EnableScheduling
        class FullService:
            def __init__(self):
                self.execution_count = 0

            @Scheduled(fixed_rate="2s")
            def regular_task(self):
                self.execution_count += 1
                return f"regular_execution_{self.execution_count}"

            @Scheduled(fixed_delay="5s", initial_delay="1s")
            def delayed_task(self):
                self.execution_count += 1
                return f"delayed_execution_{self.execution_count}"

            @Scheduled(cron="0 * * * *", zone="UTC")
            def hourly_task(self):
                self.execution_count += 1
                return f"hourly_execution_{self.execution_count}"

            @Scheduled(fixed_rate="3s")
            async def async_task(self):
                self.execution_count += 1
                await asyncio.sleep(0.01)
                return f"async_execution_{self.execution_count}"

        service = FullService()

        # 验证所有方法都有正确的配置
        methods = [
            (service.regular_task, TaskType.FIXED_RATE),
            (service.delayed_task, TaskType.FIXED_DELAY),
            (service.hourly_task, TaskType.CRON),
            (service.async_task, TaskType.FIXED_RATE),
        ]

        for method, expected_type in methods:
            self.assertTrue(hasattr(method, "_scheduled_config"))
            config = method._scheduled_config
            self.assertEqual(config.get_task_type(), expected_type)

        # 验证异步方法特性
        self.assertTrue(asyncio.iscoroutinefunction(service.async_task))
        self.assertFalse(asyncio.iscoroutinefunction(service.regular_task))

        # 验证类启用调度
        self.assertTrue(FullService._scheduling_enabled)


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
"""
任务分类器

基于规则和机器学习的任务分类器,用于分析任务特性和选择最优执行策略.

主要功能:
- 任务类型识别(CPU密集型、I/O密集型、轻量级等)
- 任务复杂度评估
- 执行时间预测
- 资源需求分析
"""

import asyncio
import inspect
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from enum import Enum
from loguru import logger

from ..properties import AsyncOptimizationConfig


class TaskType(Enum):
    """任务类型"""

    CPU_BOUND = "cpu_bound"  # CPU密集型
    IO_BOUND = "io_bound"  # I/O密集型
    LIGHTWEIGHT = "lightweight"  # 轻量级
    MIXED = "mixed"  # 混合型
    UNKNOWN = "unknown"  # 未知类型


class ComplexityLevel(Enum):
    """复杂度级别"""

    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    VERY_HIGH = "very_high"


@dataclass
class TaskCharacteristics:
    """任务特性"""

    task_type: TaskType
    complexity_level: ComplexityLevel
    estimated_duration: float
    memory_usage: str
    cpu_usage: str
    io_operations: bool
    async_compatible: bool
    thread_safe: bool
    confidence: float
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ClassificationRule:
    """分类规则"""

    name: str
    condition: Callable[[Dict[str, Any]], bool]
    task_type: TaskType
    complexity: ComplexityLevel
    confidence: float
    description: str


class TaskClassifier:
    """任务分类器

    使用规则引擎和启发式算法对任务进行分类,
    为智能调度提供决策依据.
    """

    def __init__(self, config: Optional[AsyncOptimizationConfig] = None):
        """初始化任务分类器

        Args:
            config: 异步优化配置
        """
        self.config = config or AsyncOptimizationConfig()

        # 分类规则
        self._rules: List[ClassificationRule] = []
        self._init_default_rules()

        # 性能阈值
        self._thresholds = {
            "cpu_bound_threshold": getattr(config, "cpu_bound_threshold", 0.05),
            "io_bound_threshold": getattr(config, "io_bound_threshold", 0.01),
            "lightweight_threshold": getattr(config, "lightweight_threshold", 0.001),
        }

        # 历史分类数据
        self._classification_history: List[Dict[str, Any]] = []

        logger.info("TaskClassifier initialized")

    def _init_default_rules(self) -> None:
        """初始化默认分类规则"""

        # 轻量级任务规则
        self._rules.append(
            ClassificationRule(
                name="lightweight_simple",
                condition=lambda ctx: (
                    ctx.get("arg_count", 0) <= 2
                    and ctx.get("kwarg_count", 0) <= 1
                    and not ctx.get("has_loops", False)
                    and not ctx.get("has_io_operations", False)
                ),
                task_type=TaskType.LIGHTWEIGHT,
                complexity=ComplexityLevel.LOW,
                confidence=0.8,
                description="Simple function with few parameters and no complex operations",
            )
        )

        # I/O密集型任务规则
        self._rules.append(
            ClassificationRule(
                name="io_bound_async",
                condition=lambda ctx: (ctx.get("is_coroutine", False) and (ctx.get("has_await", False) or ctx.get("has_io_operations", False))),
                task_type=TaskType.IO_BOUND,
                complexity=ComplexityLevel.MEDIUM,
                confidence=0.9,
                description="Async function with I/O operations",
            )
        )

        # CPU密集型任务规则
        self._rules.append(
            ClassificationRule(
                name="cpu_bound_computation",
                condition=lambda ctx: (
                    ctx.get("has_loops", False) and ctx.get("estimated_ops", 0) > 1000 and not ctx.get("has_io_operations", False)
                ),
                task_type=TaskType.CPU_BOUND,
                complexity=ComplexityLevel.HIGH,
                confidence=0.7,
                description="Function with loops and high computational complexity",
            )
        )

        # 混合型任务规则
        self._rules.append(
            ClassificationRule(
                name="mixed_operations",
                condition=lambda ctx: (ctx.get("has_io_operations", False) and ctx.get("has_computation", False)),
                task_type=TaskType.MIXED,
                complexity=ComplexityLevel.MEDIUM,
                confidence=0.6,
                description="Function with both I/O and computational operations",
            )
        )

    async def classify_task(self, task_request) -> Dict[str, Any]:
        """分类任务

        Args:
            task_request: 任务请求对象

        Returns:
            分类结果字典
        """
        try:
            # 1. 提取任务上下文
            context = await self._extract_task_context(task_request)

            # 2. 应用分类规则
            classification = self._apply_classification_rules(context)

            # 3. 生成任务特性
            characteristics = self._generate_characteristics(context, classification)

            # 4. 记录分类历史
            self._record_classification(task_request, context, characteristics)

            return {
                "task_type": characteristics.task_type.value,
                "complexity_level": characteristics.complexity_level.value,
                "estimated_duration": characteristics.estimated_duration,
                "memory_usage": characteristics.memory_usage,
                "cpu_usage": characteristics.cpu_usage,
                "io_operations": characteristics.io_operations,
                "async_compatible": characteristics.async_compatible,
                "thread_safe": characteristics.thread_safe,
                "confidence": characteristics.confidence,
                "classification_metadata": characteristics.metadata,
            }

        except Exception as e:
            logger.error(f"Task classification failed: {e}")
            return self._get_default_classification()

    async def _extract_task_context(self, task_request) -> Dict[str, Any]:
        """提取任务上下文信息"""
        function = task_request.function

        context = {
            "function_name": function.__name__,
            "is_coroutine": asyncio.iscoroutinefunction(function),
            "arg_count": len(task_request.args),
            "kwarg_count": len(task_request.kwargs),
            "has_timeout": task_request.timeout is not None,
            "priority": task_request.priority,
        }

        # 分析函数源码(如果可用)
        try:
            source = inspect.getsource(function)
            context.update(self._analyze_source_code(source))
        except (OSError, TypeError):
            logger.debug(f"Cannot get source code for {function.__name__}")

        # 分析函数签名
        try:
            signature = inspect.signature(function)
            context.update(self._analyze_function_signature(signature))
        except (ValueError, TypeError):
            logger.debug(f"Cannot get signature for {function.__name__}")

        # 分析函数注解
        context.update(self._analyze_function_annotations(function))

        return context

    def _analyze_source_code(self, source: str) -> Dict[str, Any]:
        """分析函数源码"""
        analysis = {"has_loops": False, "has_await": False, "has_io_operations": False, "has_computation": False, "estimated_ops": 0}

        # 简单的源码分析
        source_lower = source.lower()

        # 检查循环
        loop_keywords = ["for ", "while ", "range(", "enumerate("]
        analysis["has_loops"] = any(keyword in source_lower for keyword in loop_keywords)

        # 检查异步操作
        analysis["has_await"] = "await " in source_lower

        # 检查I/O操作
        io_keywords = ["open(", "read(", "write(", "request", "http", "sql", "database", "file"]
        analysis["has_io_operations"] = any(keyword in source_lower for keyword in io_keywords)

        # 检查计算操作
        compute_keywords = ["math.", "numpy", "calculate", "compute", "algorithm", "sort"]
        analysis["has_computation"] = any(keyword in source_lower for keyword in compute_keywords)

        # 估算操作复杂度
        lines = source.split("\n")
        analysis["estimated_ops"] = len([line for line in lines if line.strip() and not line.strip().startswith("#")])

        return analysis

    def _analyze_function_signature(self, signature: inspect.Signature) -> Dict[str, Any]:
        """分析函数签名"""
        analysis = {"param_count": len(signature.parameters), "has_defaults": False, "has_varargs": False, "has_kwargs": False}

        for param in signature.parameters.values():
            if param.default != inspect.Parameter.empty:
                analysis["has_defaults"] = True
            if param.kind == inspect.Parameter.VAR_POSITIONAL:
                analysis["has_varargs"] = True
            if param.kind == inspect.Parameter.VAR_KEYWORD:
                analysis["has_kwargs"] = True

        return analysis

    def _analyze_function_annotations(self, function: Callable) -> Dict[str, Any]:
        """分析函数注解"""
        analysis = {"has_type_hints": False, "return_annotation": None}

        # 检查类型注解
        if hasattr(function, "__annotations__") and function.__annotations__:
            analysis["has_type_hints"] = True
            analysis["return_annotation"] = function.__annotations__.get("return")

        return analysis

    def _apply_classification_rules(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """应用分类规则"""
        best_match = None
        best_confidence = 0.0

        for rule in self._rules:
            try:
                if rule.condition(context):
                    if rule.confidence > best_confidence:
                        best_match = rule
                        best_confidence = rule.confidence
            except Exception as e:
                logger.debug(f"Rule {rule.name} evaluation failed: {e}")

        if best_match:
            return {
                "task_type": best_match.task_type,
                "complexity": best_match.complexity,
                "confidence": best_match.confidence,
                "rule_name": best_match.name,
                "rule_description": best_match.description,
            }
        else:
            # 默认分类
            return {
                "task_type": TaskType.UNKNOWN,
                "complexity": ComplexityLevel.MEDIUM,
                "confidence": 0.1,
                "rule_name": "default",
                "rule_description": "No matching rule found",
            }

    def _generate_characteristics(self, context: Dict[str, Any], classification: Dict[str, Any]) -> TaskCharacteristics:
        """生成任务特性"""
        task_type = classification["task_type"]
        complexity = classification["complexity"]

        # 估算执行时间
        estimated_duration = self._estimate_duration(context, task_type, complexity)

        # 评估资源使用
        memory_usage = self._estimate_memory_usage(context, complexity)
        cpu_usage = self._estimate_cpu_usage(context, task_type)

        # 判断兼容性
        async_compatible = context.get("is_coroutine", False) or task_type == TaskType.IO_BOUND
        thread_safe = not context.get("has_global_state", False)  # 简化判断

        return TaskCharacteristics(
            task_type=task_type,
            complexity_level=complexity,
            estimated_duration=estimated_duration,
            memory_usage=memory_usage,
            cpu_usage=cpu_usage,
            io_operations=context.get("has_io_operations", False),
            async_compatible=async_compatible,
            thread_safe=thread_safe,
            confidence=classification["confidence"],
            metadata={"rule_name": classification["rule_name"], "rule_description": classification["rule_description"], "context": context},
        )

    def _estimate_duration(self, context: Dict[str, Any], task_type: TaskType, complexity: ComplexityLevel) -> float:
        """估算执行时间"""
        base_time = {ComplexityLevel.LOW: 0.001, ComplexityLevel.MEDIUM: 0.01, ComplexityLevel.HIGH: 0.1, ComplexityLevel.VERY_HIGH: 1.0}.get(
            complexity, 0.01
        )

        # 根据任务类型调整
        if task_type == TaskType.IO_BOUND:
            base_time *= 10  # I/O操作通常较慢
        elif task_type == TaskType.CPU_BOUND:
            base_time *= context.get("estimated_ops", 1) / 100

        return base_time

    def _estimate_memory_usage(self, context: Dict[str, Any], complexity: ComplexityLevel) -> str:
        """估算内存使用"""
        if complexity == ComplexityLevel.LOW:
            return "low"
        elif complexity == ComplexityLevel.MEDIUM:
            return "medium"
        elif complexity == ComplexityLevel.HIGH:
            return "high"
        else:
            return "very_high"

    def _estimate_cpu_usage(self, context: Dict[str, Any], task_type: TaskType) -> str:
        """估算CPU使用"""
        if task_type == TaskType.CPU_BOUND:
            return "high"
        elif task_type == TaskType.MIXED:
            return "medium"
        else:
            return "low"

    def _record_classification(self, task_request, context: Dict[str, Any], characteristics: TaskCharacteristics) -> None:
        """记录分类历史"""
        record = {
            "task_id": task_request.task_id,
            "function_name": task_request.function.__name__,
            "classification": {
                "task_type": characteristics.task_type.value,
                "complexity": characteristics.complexity_level.value,
                "confidence": characteristics.confidence,
            },
            "context": context,
            "timestamp": time.time(),
        }

        self._classification_history.append(record)

        # 限制历史记录大小
        if len(self._classification_history) > 1000:
            self._classification_history.pop(0)

    def _get_default_classification(self) -> Dict[str, Any]:
        """获取默认分类结果"""
        return {
            "task_type": TaskType.UNKNOWN.value,
            "complexity_level": ComplexityLevel.MEDIUM.value,
            "estimated_duration": 0.01,
            "memory_usage": "medium",
            "cpu_usage": "medium",
            "io_operations": False,
            "async_compatible": True,
            "thread_safe": True,
            "confidence": 0.1,
            "classification_metadata": {"rule_name": "default_fallback"},
        }

    def add_custom_rule(self, rule: ClassificationRule) -> None:
        """添加自定义分类规则"""
        self._rules.append(rule)
        logger.info(f"Added custom classification rule: {rule.name}")

    def get_classification_statistics(self) -> Dict[str, Any]:
        """获取分类统计信息"""
        if not self._classification_history:
            return {"total_classifications": 0}

        total = len(self._classification_history)
        type_counts = {}
        complexity_counts = {}

        for record in self._classification_history:
            task_type = record["classification"]["task_type"]
            complexity = record["classification"]["complexity"]

            type_counts[task_type] = type_counts.get(task_type, 0) + 1
            complexity_counts[complexity] = complexity_counts.get(complexity, 0) + 1

        return {
            "total_classifications": total,
            "task_type_distribution": type_counts,
            "complexity_distribution": complexity_counts,
            "avg_confidence": sum(r["classification"]["confidence"] for r in self._classification_history) / total,
        }

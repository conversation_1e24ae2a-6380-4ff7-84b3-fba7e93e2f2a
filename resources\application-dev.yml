# Mini-Boot 开发环境配置

miniboot:
    # 开发环境应用配置
    application:
        name: mini-boot-app-dev

    # 开发服务器配置
    server:
        port: 8080
        debug: true

    # 开发数据库配置
    datasource:
        url: sqlite:///data/dev.db

    # 开发日志配置
    logging:
        level: DEBUG # 开发环境使用DEBUG级别

        # 控制台输出（开发环境重点关注）
        console:
            enabled: true
            level: DEBUG
            colorize: true # 开发环境启用彩色输出

        # 文件输出（开发环境可选）
        file:
            enabled: true
            path: logs/dev.log
            level: DEBUG
            rotation: "100 MB" # 开发环境较小的轮转大小
            retention: "3 days" # 开发环境较短的保留时间

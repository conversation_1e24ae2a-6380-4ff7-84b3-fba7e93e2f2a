#!/usr/bin/env python
# encoding: utf-8
"""
@author: cz
@description: MiniBootScheduler核心调度器单元测试
"""

import unittest

from miniboot.schedule import (
    MiniBootScheduler,
    SchedulerProperties,
    SchedulerPropertiesBuilder,
    SchedulerState,
)


class SchedulerServiceTest:
    """测试服务类"""

    def __init__(self):
        self.execution_count = 0
        self.results = []

    def sync_task(self):
        """同步任务"""
        self.execution_count += 1
        result = f"sync_result_{self.execution_count}"
        self.results.append(result)
        return result

    async def async_task(self):
        """异步任务"""
        self.execution_count += 1
        result = f"async_result_{self.execution_count}"
        self.results.append(result)
        return result

    def error_task(self):
        """错误任务"""
        raise ValueError("Test error")

    def reset(self):
        """重置状态"""
        self.execution_count = 0
        self.results.clear()


class TestMiniBootScheduler(unittest.TestCase):
    """MiniBootScheduler测试"""

    def setUp(self):
        """设置测试环境"""
        self.properties = SchedulerProperties()
        self.scheduler = MiniBootScheduler(self.properties)
        self.service = SchedulerServiceTest()

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.stop()
        self.service.reset()

    def test_scheduler_initialization(self):
        """测试调度器初始化"""
        self.assertIsNotNone(self.scheduler)
        self.assertEqual(self.scheduler.state, SchedulerState.STOPPED)
        self.assertFalse(self.scheduler.is_running())

    def test_scheduler_start_stop(self):
        """测试调度器启动和停止"""
        # 启动调度器
        self.scheduler.start()
        self.assertTrue(self.scheduler.is_running())
        self.assertEqual(self.scheduler.state, SchedulerState.RUNNING)

        # 停止调度器
        self.scheduler.stop()
        self.assertFalse(self.scheduler.is_running())
        self.assertEqual(self.scheduler.state, SchedulerState.STOPPED)

    def test_add_sync_task(self):
        """测试添加同步任务"""
        task_id = self.scheduler.add_task(task_id="sync_task", func=self.service.sync_task, trigger="interval", seconds=1)

        self.assertIsNotNone(task_id)
        self.assertEqual(len(self.scheduler.get_tasks()), 1)

    def test_add_async_task(self):
        """测试添加异步任务"""
        task_id = self.scheduler.add_task(task_id="async_task", func=self.service.async_task, trigger="interval", seconds=1)

        self.assertIsNotNone(task_id)
        self.assertEqual(len(self.scheduler.get_tasks()), 1)

    def test_remove_task(self):
        """测试移除任务"""
        # 添加任务
        task_id = self.scheduler.add_task(task_id="test_task", func=self.service.sync_task, trigger="interval", seconds=1)

        self.assertEqual(len(self.scheduler.get_tasks()), 1)

        # 移除任务
        success = self.scheduler.remove_task(task_id)
        self.assertTrue(success)
        self.assertEqual(len(self.scheduler.get_tasks()), 0)

    def test_task_execution(self):
        """测试任务执行"""
        # 添加任务
        self.scheduler.add_task(task_id="test_task", func=self.service.sync_task, trigger="interval", seconds=0.1)

        # 启动调度器
        self.scheduler.start()

        # 等待任务执行
        import time

        time.sleep(0.5)

        # 停止调度器
        self.scheduler.stop()

        # 验证任务执行
        self.assertGreater(self.service.execution_count, 0)

    def test_error_handling(self):
        """测试错误处理"""
        # 添加会出错的任务
        self.scheduler.add_task(task_id="error_task", func=self.service.error_task, trigger="interval", seconds=0.1)

        # 启动调度器
        self.scheduler.start()

        # 等待一段时间
        import time

        time.sleep(0.3)

        # 停止调度器
        self.scheduler.stop()

        # 调度器应该仍然运行正常
        self.assertEqual(self.scheduler.state, SchedulerState.STOPPED)

    def test_get_task_info(self):
        """测试获取任务信息"""
        task_id = self.scheduler.add_task(task_id="info_task", func=self.service.sync_task, trigger="interval", seconds=1)

        task_info = self.scheduler.get_task_info(task_id)
        self.assertIsNotNone(task_info)
        self.assertEqual(task_info["id"], task_id)

    def test_scheduler_properties(self):
        """测试调度器属性"""
        properties = SchedulerPropertiesBuilder().build()
        scheduler = MiniBootScheduler(properties)

        self.assertIsNotNone(scheduler)
        self.assertEqual(scheduler.state, SchedulerState.STOPPED)

    def test_string_representation(self):
        """测试字符串表示"""
        str_repr = str(self.scheduler)
        self.assertIn("MiniBootScheduler", str_repr)
        self.assertIn("SchedulerState.STOPPED", str_repr)


if __name__ == "__main__":
    unittest.main()

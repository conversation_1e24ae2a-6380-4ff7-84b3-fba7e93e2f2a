#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
参数绑定和路由处理测试案例

测试ParameterBinder和RouteHandler的参数解析、类型转换、
验证、路由匹配等功能的完整测试案例.

主要测试内容:
- ParameterBinder参数绑定功能测试
- 参数解析和类型转换测试
- 参数验证和错误处理测试
- RouteHandler路由处理测试
- 智能调度和性能优化测试
- 异常处理和边界条件测试
"""

import asyncio
import json
import time
import unittest
from typing import Any, Dict, List, Optional
from unittest.mock import AsyncMock, Mock, patch

from fastapi import Request

from miniboot.web.params import (
    ParameterBinder, ParameterComplexity, ParameterSource,
    ParameterInfo
)
from miniboot.web.router import RouteHandler, SmartRouteInfo, RouteBackpressureController


class ParameterBinderBasicTestCase(unittest.IsolatedAsyncioTestCase):
    """参数绑定器基本功能测试类"""

    async def test_parameter_binder_initialization(self):
        """测试参数绑定器初始化"""
        # Arrange & Act
        binder = ParameterBinder()

        # Assert
        self.assertIsNotNone(binder.parser)
        self.assertIsNotNone(binder.validator)
        self.assertIsInstance(binder._parameter_cache, dict)
        self.assertIsInstance(binder._stats, dict)
        self.assertEqual(binder._stats["total_bindings"], 0)

    async def test_parameter_binder_with_smart_scheduler(self):
        """测试带智能调度器的参数绑定器"""
        # Arrange
        mock_scheduler = Mock()

        # Act
        binder = ParameterBinder(smart_scheduler=mock_scheduler)

        # Assert
        self.assertIs(binder.smart_scheduler, mock_scheduler)
        self.assertIs(binder.parser.smart_scheduler, mock_scheduler)
        self.assertIs(binder.validator.smart_scheduler, mock_scheduler)

    async def test_bind_parameters_simple_function(self):
        """测试绑定简单函数参数"""
        # Arrange
        binder = ParameterBinder()

        def test_function(name: str, age: int = 25):
            return {"name": name, "age": age}

        mock_request = Mock(spec=Request)
        mock_request.query_params = {"name": "John", "age": "30"}
        mock_request.path_params = {}
        mock_request.headers = {}

        # Mock request body
        async def mock_body():
            return b""
        mock_request.body = mock_body

        # Act
        bound_params = await binder.bind_parameters(test_function, mock_request)

        # Assert
        self.assertIsInstance(bound_params, dict)
        # 由于实际实现较复杂，这里主要验证方法能正常调用

    async def test_bind_parameters_with_path_params(self):
        """测试绑定路径参数"""
        # Arrange
        binder = ParameterBinder()

        def test_function(user_id: int, post_id: str):
            return {"user_id": user_id, "post_id": post_id}

        mock_request = Mock(spec=Request)
        mock_request.query_params = {}
        mock_request.headers = {}

        async def mock_body():
            return b""
        mock_request.body = mock_body

        path_params = {"user_id": "123", "post_id": "abc"}

        # Act
        bound_params = await binder.bind_parameters(test_function, mock_request, path_params)

        # Assert
        self.assertIsInstance(bound_params, dict)

    async def test_bind_parameters_with_request_body(self):
        """测试绑定请求体参数"""
        # Arrange
        binder = ParameterBinder()

        def test_function(user_data: dict):
            return user_data

        mock_request = Mock(spec=Request)
        mock_request.query_params = {}
        mock_request.path_params = {}
        mock_request.headers = {"content-type": "application/json"}

        test_data = {"name": "John", "email": "<EMAIL>"}
        async def mock_body():
            return json.dumps(test_data).encode()
        mock_request.body = mock_body

        # Act
        bound_params = await binder.bind_parameters(test_function, mock_request)

        # Assert
        self.assertIsInstance(bound_params, dict)

    async def test_parameter_binding_statistics(self):
        """测试参数绑定统计信息"""
        # Arrange
        binder = ParameterBinder()

        def test_function(name: str):
            return {"name": name}

        mock_request = Mock(spec=Request)
        mock_request.query_params = {"name": "Test"}
        mock_request.path_params = {}
        mock_request.headers = {}

        async def mock_body():
            return b""
        mock_request.body = mock_body

        # Act
        await binder.bind_parameters(test_function, mock_request)
        stats = binder.get_statistics()

        # Assert
        self.assertIsInstance(stats, dict)
        self.assertIn("total_bindings", stats)
        self.assertIn("successful_bindings", stats)
        self.assertIn("failed_bindings", stats)


class ParameterParserTestCase(unittest.IsolatedAsyncioTestCase):
    """参数解析器测试类"""

    async def test_parameter_parser_initialization(self):
        """测试参数解析器初始化"""
        # Arrange & Act
        parser = ParameterParser()

        # Assert
        self.assertIsInstance(parser._type_converters, dict)
        self.assertIsInstance(parser._parsing_cache, dict)

    async def test_parameter_parser_with_scheduler(self):
        """测试带调度器的参数解析器"""
        # Arrange
        mock_scheduler = Mock()

        # Act
        parser = ParameterParser(smart_scheduler=mock_scheduler)

        # Assert
        self.assertIs(parser.smart_scheduler, mock_scheduler)

    async def test_parse_query_parameters(self):
        """测试解析查询参数"""
        # Arrange
        parser = ParameterParser()
        query_params = {"name": "John", "age": "30", "active": "true"}

        # Act
        parsed_params = await parser.parse_query_params(query_params)

        # Assert
        self.assertIsInstance(parsed_params, dict)
        self.assertEqual(len(parsed_params), 3)

    async def test_parse_path_parameters(self):
        """测试解析路径参数"""
        # Arrange
        parser = ParameterParser()
        path_params = {"user_id": "123", "category": "books"}

        # Act
        parsed_params = await parser.parse_path_params(path_params)

        # Assert
        self.assertIsInstance(parsed_params, dict)
        self.assertEqual(len(parsed_params), 2)

    async def test_parse_json_body(self):
        """测试解析JSON请求体"""
        # Arrange
        parser = ParameterParser()
        json_data = {"name": "John", "age": 30, "tags": ["user", "active"]}
        body_bytes = json.dumps(json_data).encode()

        # Act
        parsed_body = await parser.parse_json_body(body_bytes)

        # Assert
        self.assertEqual(parsed_body, json_data)

    async def test_parse_form_data(self):
        """测试解析表单数据"""
        # Arrange
        parser = ParameterParser()
        form_data = b"name=John&age=30&active=true"

        # Act
        parsed_form = await parser.parse_form_data(form_data)

        # Assert
        self.assertIsInstance(parsed_form, dict)

    async def test_type_conversion(self):
        """测试类型转换"""
        # Arrange
        parser = ParameterParser()

        # Act & Assert
        # 测试字符串转整数
        result = await parser.convert_type("123", int)
        self.assertEqual(result, 123)

        # 测试字符串转布尔值
        result = await parser.convert_type("true", bool)
        self.assertTrue(result)

        # 测试字符串转浮点数
        result = await parser.convert_type("3.14", float)
        self.assertEqual(result, 3.14)

    async def test_type_conversion_error_handling(self):
        """测试类型转换错误处理"""
        # Arrange
        parser = ParameterParser()

        # Act & Assert
        with self.assertRaises(ValueError):
            await parser.convert_type("invalid", int)


class AsyncParameterValidatorTestCase(unittest.IsolatedAsyncioTestCase):
    """异步参数验证器测试类"""

    async def test_validator_initialization(self):
        """测试验证器初始化"""
        # Arrange & Act
        validator = AsyncParameterValidator()

        # Assert
        self.assertIsInstance(validator._validation_rules, dict)
        self.assertIsInstance(validator._validation_cache, dict)

    async def test_validator_with_scheduler(self):
        """测试带调度器的验证器"""
        # Arrange
        mock_scheduler = Mock()

        # Act
        validator = AsyncParameterValidator(smart_scheduler=mock_scheduler)

        # Assert
        self.assertIs(validator.smart_scheduler, mock_scheduler)

    async def test_validate_required_parameters(self):
        """测试验证必需参数"""
        # Arrange
        validator = AsyncParameterValidator()
        param_info = ParameterInfo(
            name="user_id",
            param_type=ParameterType.PATH,
            python_type=int,
            required=True
        )
        value = 123

        # Act
        result = await validator.validate_parameter(param_info, value)

        # Assert
        self.assertIsInstance(result, ValidationResult)
        self.assertTrue(result.is_valid)

    async def test_validate_optional_parameters(self):
        """测试验证可选参数"""
        # Arrange
        validator = AsyncParameterValidator()
        param_info = ParameterInfo(
            name="page",
            param_type=ParameterType.QUERY,
            python_type=int,
            required=False,
            default_value=1
        )
        value = None

        # Act
        result = await validator.validate_parameter(param_info, value)

        # Assert
        self.assertIsInstance(result, ValidationResult)
        # 可选参数为None时应该使用默认值

    async def test_validate_parameter_constraints(self):
        """测试验证参数约束"""
        # Arrange
        validator = AsyncParameterValidator()
        param_info = ParameterInfo(
            name="age",
            param_type=ParameterType.QUERY,
            python_type=int,
            required=True,
            constraints={"min": 0, "max": 150}
        )

        # Act & Assert
        # 测试有效值
        result = await validator.validate_parameter(param_info, 25)
        self.assertTrue(result.is_valid)

        # 测试无效值（超出范围）
        result = await validator.validate_parameter(param_info, 200)
        self.assertFalse(result.is_valid)

    async def test_validate_multiple_parameters(self):
        """测试验证多个参数"""
        # Arrange
        validator = AsyncParameterValidator()
        param_infos = [
            ParameterInfo("name", ParameterType.QUERY, str, True),
            ParameterInfo("age", ParameterType.QUERY, int, False, default_value=25),
            ParameterInfo("user_id", ParameterType.PATH, int, True)
        ]
        values = {"name": "John", "age": 30, "user_id": 123}

        # Act
        results = await validator.validate_parameters(param_infos, values)

        # Assert
        self.assertIsInstance(results, list)
        self.assertEqual(len(results), 3)
        for result in results:
            self.assertIsInstance(result, ValidationResult)


class RouteHandlerTestCase(unittest.IsolatedAsyncioTestCase):
    """路由处理器测试类"""

    async def test_route_handler_initialization(self):
        """测试路由处理器初始化"""
        # Arrange & Act
        handler = RouteHandler()

        # Assert
        self.assertIsInstance(handler._route_cache, dict)
        self.assertIsInstance(handler._performance_stats, dict)

    async def test_route_handler_with_scheduler(self):
        """测试带调度器的路由处理器"""
        # Arrange
        mock_scheduler = Mock()

        # Act
        handler = RouteHandler(smart_scheduler=mock_scheduler)

        # Assert
        self.assertIs(handler.smart_scheduler, mock_scheduler)

    async def test_register_route(self):
        """测试注册路由"""
        # Arrange
        handler = RouteHandler()
        route_path = "/api/users/{user_id}"
        route_info = SmartRouteInfo(
            path=route_path,
            method="GET",
            handler_func=lambda user_id: {"user_id": user_id}
        )

        # Act
        handler.register_route(route_path, route_info)

        # Assert
        self.assertIn(route_path, handler._route_cache)

    async def test_match_route(self):
        """测试路由匹配"""
        # Arrange
        handler = RouteHandler()
        route_path = "/api/users/{user_id}"
        route_info = SmartRouteInfo(
            path=route_path,
            method="GET",
            handler_func=lambda user_id: {"user_id": user_id}
        )
        handler.register_route(route_path, route_info)

        # Act
        match_result = await handler.match_route("/api/users/123", "GET")

        # Assert
        self.assertIsNotNone(match_result)

    async def test_route_performance_monitoring(self):
        """测试路由性能监控"""
        # Arrange
        handler = RouteHandler()
        route_path = "/api/test"

        # Act
        start_time = time.time()
        await handler.record_route_performance(route_path, start_time, time.time())
        stats = handler.get_performance_stats()

        # Assert
        self.assertIsInstance(stats, dict)


class ParameterBindingPerformanceTestCase(unittest.IsolatedAsyncioTestCase):
    """参数绑定性能测试类"""

    async def test_parameter_binding_performance(self):
        """测试参数绑定性能"""
        # Arrange
        binder = ParameterBinder()

        def test_function(name: str, age: int, email: str):
            return {"name": name, "age": age, "email": email}

        mock_request = Mock(spec=Request)
        mock_request.query_params = {"name": "John", "age": "30", "email": "<EMAIL>"}
        mock_request.path_params = {}
        mock_request.headers = {}

        async def mock_body():
            return b""
        mock_request.body = mock_body

        # Act
        start_time = time.time()
        for _ in range(100):
            await binder.bind_parameters(test_function, mock_request)
        end_time = time.time()

        # Assert
        total_time = end_time - start_time
        avg_time = total_time / 100
        self.assertLess(avg_time, 0.01)  # 平均绑定时间应小于10ms

    async def test_parameter_cache_effectiveness(self):
        """测试参数缓存有效性"""
        # Arrange
        binder = ParameterBinder()

        def test_function(name: str, age: int):
            return {"name": name, "age": age}

        mock_request = Mock(spec=Request)
        mock_request.query_params = {"name": "John", "age": "30"}
        mock_request.path_params = {}
        mock_request.headers = {}

        async def mock_body():
            return b""
        mock_request.body = mock_body

        # Act
        # 第一次调用（应该缓存参数信息）
        await binder.bind_parameters(test_function, mock_request)

        # 第二次调用（应该使用缓存）
        start_time = time.time()
        await binder.bind_parameters(test_function, mock_request)
        end_time = time.time()

        # Assert
        cache_time = end_time - start_time
        stats = binder.get_statistics()
        self.assertGreater(stats.get("cache_hits", 0), 0)


if __name__ == "__main__":
    unittest.main(verbosity=2)

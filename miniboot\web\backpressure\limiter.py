#!/usr/bin/env python
"""
并发限制器

提供请求并发控制和流量整形功能,防止系统过载.

主要功能:
- 并发请求数量限制
- 多种限流算法支持 (令牌桶、漏桶、滑动窗口)
- 动态限制调整
- 请求排队和超时处理
- 优先级队列支持
"""

import asyncio
import time
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from collections import deque
from threading import Lock, Semaphore
from loguru import logger

from ..properties import BackpressureConfig


class LimitingAlgorithm(Enum):
    """限流算法类型"""

    SEMAPHORE = "semaphore"  # 信号量限制
    TOKEN_BUCKET = "token_bucket"  # 令牌桶
    LEAKY_BUCKET = "leaky_bucket"  # 漏桶
    SLIDING_WINDOW = "sliding_window"  # 滑动窗口


class RequestPriority(Enum):
    """请求优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class ConcurrencyConfig:
    """并发限制配置"""

    max_concurrent: int = 100  # 最大并发数
    algorithm: LimitingAlgorithm = LimitingAlgorithm.SEMAPHORE
    queue_size: int = 1000  # 队列大小
    timeout: float = 30.0  # 超时时间(秒)
    enable_priority: bool = False  # 是否启用优先级
    token_refill_rate: float = 10.0  # 令牌补充速率(每秒)
    bucket_capacity: int = 100  # 桶容量
    window_size: int = 60  # 窗口大小(秒)


@dataclass
class RequestContext:
    """请求上下文"""

    request_id: str
    priority: RequestPriority = RequestPriority.NORMAL
    timestamp: float = field(default_factory=time.time)
    timeout: Optional[float] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ConcurrencyMetrics:
    """并发限制指标"""

    current_concurrent: int = 0
    max_concurrent_reached: int = 0
    total_requests: int = 0
    accepted_requests: int = 0
    rejected_requests: int = 0
    timeout_requests: int = 0
    avg_wait_time: float = 0.0
    queue_length: int = 0


class ConcurrencyLimitError(Exception):
    """并发限制异常"""

    def __init__(self, message: str, reason: str):
        super().__init__(message)
        self.reason = reason


class ConcurrencyLimiter:
    """并发限制器

    提供多种并发控制策略,支持请求排队、优先级处理和动态限制调整.
    """

    def __init__(self, config: Optional[ConcurrencyConfig] = None, backpressure_config: Optional[BackpressureConfig] = None):
        """初始化并发限制器

        Args:
            config: 并发限制配置
            backpressure_config: 背压控制配置
        """
        self.config = config or ConcurrencyConfig()
        self.backpressure_config = backpressure_config

        # 并发控制
        self._semaphore = Semaphore(self.config.max_concurrent)
        self._current_concurrent = 0

        # 令牌桶算法相关
        self._tokens = float(self.config.bucket_capacity)
        self._last_refill_time = time.time()

        # 滑动窗口算法相关
        self._request_timestamps: deque = deque()

        # 请求队列
        self._request_queue: List[RequestContext] = []
        self._priority_queues: Dict[RequestPriority, List[RequestContext]] = {priority: [] for priority in RequestPriority}

        # 指标统计
        self._metrics = ConcurrencyMetrics()
        self._wait_times: deque = deque(maxlen=1000)

        # 状态管理
        self._is_enabled = True

        # 线程安全锁
        self._lock = Lock()

        # 回调函数
        self._limit_callbacks: List[Callable[[RequestContext, str], None]] = []

        logger.info(f"ConcurrencyLimiter initialized with algorithm: {self.config.algorithm.value}, max_concurrent: {self.config.max_concurrent}")

    async def acquire(self, request_context: Optional[RequestContext] = None) -> bool:
        """获取并发许可

        Args:
            request_context: 请求上下文

        Returns:
            是否成功获取许可

        Raises:
            ConcurrencyLimitError: 并发限制异常
        """
        if not self._is_enabled:
            return True

        if request_context is None:
            request_context = RequestContext(request_id=f"req_{time.time()}")

        start_time = time.time()

        try:
            # 根据算法进行限制检查
            if self.config.algorithm == LimitingAlgorithm.SEMAPHORE:
                return await self._acquire_semaphore(request_context, start_time)
            elif self.config.algorithm == LimitingAlgorithm.TOKEN_BUCKET:
                return await self._acquire_token_bucket(request_context, start_time)
            elif self.config.algorithm == LimitingAlgorithm.LEAKY_BUCKET:
                return await self._acquire_leaky_bucket(request_context, start_time)
            elif self.config.algorithm == LimitingAlgorithm.SLIDING_WINDOW:
                return await self._acquire_sliding_window(request_context, start_time)
            else:
                return await self._acquire_semaphore(request_context, start_time)

        except Exception as e:
            logger.error(f"Error in concurrency limiter acquire: {e}")
            self._record_rejection(request_context, "error")
            raise ConcurrencyLimitError(f"Failed to acquire concurrency permit: {e}", "error")

    async def _acquire_semaphore(self, request_context: RequestContext, start_time: float) -> bool:
        """信号量算法获取许可"""
        timeout = request_context.timeout or self.config.timeout

        try:
            # 尝试获取信号量
            acquired = self._semaphore.acquire(blocking=False)

            if acquired:
                with self._lock:
                    self._current_concurrent += 1
                    self._metrics.current_concurrent = self._current_concurrent
                    self._metrics.max_concurrent_reached = max(self._metrics.max_concurrent_reached, self._current_concurrent)

                self._record_acceptance(request_context, start_time)
                return True
            else:
                # 如果启用队列,加入队列等待
                if self.config.queue_size > 0:
                    return await self._enqueue_and_wait(request_context, start_time, timeout)
                else:
                    self._record_rejection(request_context, "max_concurrent_reached")
                    raise ConcurrencyLimitError(f"Maximum concurrent requests reached: {self.config.max_concurrent}", "max_concurrent_reached")

        except Exception as e:
            self._record_rejection(request_context, "error")
            raise

    async def _acquire_token_bucket(self, request_context: RequestContext, start_time: float) -> bool:
        """令牌桶算法获取许可"""
        with self._lock:
            current_time = time.time()

            # 补充令牌
            time_passed = current_time - self._last_refill_time
            tokens_to_add = time_passed * self.config.token_refill_rate
            self._tokens = min(self.config.bucket_capacity, self._tokens + tokens_to_add)
            self._last_refill_time = current_time

            # 检查是否有令牌
            if self._tokens >= 1.0:
                self._tokens -= 1.0
                self._current_concurrent += 1
                self._metrics.current_concurrent = self._current_concurrent

                self._record_acceptance(request_context, start_time)
                return True
            else:
                self._record_rejection(request_context, "no_tokens_available")
                raise ConcurrencyLimitError("No tokens available in bucket", "no_tokens_available")

    async def _acquire_leaky_bucket(self, request_context: RequestContext, start_time: float) -> bool:
        """漏桶算法获取许可"""
        with self._lock:
            # 简化的漏桶实现,基于队列大小
            if len(self._request_queue) < self.config.queue_size:
                self._request_queue.append(request_context)
                self._current_concurrent += 1
                self._metrics.current_concurrent = self._current_concurrent
                self._metrics.queue_length = len(self._request_queue)

                self._record_acceptance(request_context, start_time)
                return True
            else:
                self._record_rejection(request_context, "bucket_full")
                raise ConcurrencyLimitError("Leaky bucket is full", "bucket_full")

    async def _acquire_sliding_window(self, request_context: RequestContext, start_time: float) -> bool:
        """滑动窗口算法获取许可"""
        with self._lock:
            current_time = time.time()
            window_start = current_time - self.config.window_size

            # 清理过期的请求时间戳
            while self._request_timestamps and self._request_timestamps[0] < window_start:
                self._request_timestamps.popleft()

            # 检查窗口内的请求数量
            if len(self._request_timestamps) < self.config.max_concurrent:
                self._request_timestamps.append(current_time)
                self._current_concurrent = len(self._request_timestamps)
                self._metrics.current_concurrent = self._current_concurrent

                self._record_acceptance(request_context, start_time)
                return True
            else:
                self._record_rejection(request_context, "window_limit_reached")
                raise ConcurrencyLimitError(f"Sliding window limit reached: {self.config.max_concurrent}", "window_limit_reached")

    async def _enqueue_and_wait(self, request_context: RequestContext, start_time: float, timeout: float) -> bool:
        """加入队列并等待"""
        with self._lock:
            if self.config.enable_priority:
                self._priority_queues[request_context.priority].append(request_context)
            else:
                self._request_queue.append(request_context)

            self._metrics.queue_length = self._get_total_queue_length()

        # 等待获取许可
        end_time = start_time + timeout

        while time.time() < end_time:
            try:
                acquired = self._semaphore.acquire(blocking=False)
                if acquired:
                    with self._lock:
                        self._remove_from_queue(request_context)
                        self._current_concurrent += 1
                        self._metrics.current_concurrent = self._current_concurrent
                        self._metrics.queue_length = self._get_total_queue_length()

                    self._record_acceptance(request_context, start_time)
                    return True

                await asyncio.sleep(0.1)  # 短暂等待

            except Exception as e:
                logger.error(f"Error while waiting in queue: {e}")
                break

        # 超时,从队列中移除
        with self._lock:
            self._remove_from_queue(request_context)
            self._metrics.queue_length = self._get_total_queue_length()

        self._record_timeout(request_context)
        raise ConcurrencyLimitError("Request timeout while waiting in queue", "timeout")

    def release(self, request_context: Optional[RequestContext] = None) -> None:
        """释放并发许可

        Args:
            request_context: 请求上下文
        """
        if not self._is_enabled:
            return

        try:
            if self.config.algorithm == LimitingAlgorithm.SEMAPHORE:
                self._semaphore.release()
                with self._lock:
                    self._current_concurrent = max(0, self._current_concurrent - 1)
                    self._metrics.current_concurrent = self._current_concurrent

            elif self.config.algorithm == LimitingAlgorithm.LEAKY_BUCKET:
                with self._lock:
                    if self._request_queue and request_context in self._request_queue:
                        self._request_queue.remove(request_context)
                    self._current_concurrent = max(0, self._current_concurrent - 1)
                    self._metrics.current_concurrent = self._current_concurrent
                    self._metrics.queue_length = len(self._request_queue)

            # 其他算法的释放逻辑
            logger.debug(f"Released concurrency permit, current: {self._current_concurrent}")

        except Exception as e:
            logger.error(f"Error releasing concurrency permit: {e}")

    def _remove_from_queue(self, request_context: RequestContext) -> None:
        """从队列中移除请求"""
        if self.config.enable_priority:
            priority_queue = self._priority_queues[request_context.priority]
            if request_context in priority_queue:
                priority_queue.remove(request_context)
        else:
            if request_context in self._request_queue:
                self._request_queue.remove(request_context)

    def _get_total_queue_length(self) -> int:
        """获取总队列长度"""
        if self.config.enable_priority:
            return sum(len(queue) for queue in self._priority_queues.values())
        else:
            return len(self._request_queue)

    def _record_acceptance(self, request_context: RequestContext, start_time: float) -> None:
        """记录接受的请求"""
        wait_time = time.time() - start_time

        with self._lock:
            self._metrics.total_requests += 1
            self._metrics.accepted_requests += 1

            self._wait_times.append(wait_time)
            if self._wait_times:
                self._metrics.avg_wait_time = sum(self._wait_times) / len(self._wait_times)

        logger.debug(f"Accepted request {request_context.request_id}, wait_time: {wait_time:.3f}s")

    def _record_rejection(self, request_context: RequestContext, reason: str) -> None:
        """记录拒绝的请求"""
        with self._lock:
            self._metrics.total_requests += 1
            self._metrics.rejected_requests += 1

        # 通知回调
        for callback in self._limit_callbacks:
            try:
                callback(request_context, reason)
            except Exception as e:
                logger.error(f"Error in limit callback: {e}")

        logger.debug(f"Rejected request {request_context.request_id}, reason: {reason}")

    def _record_timeout(self, request_context: RequestContext) -> None:
        """记录超时的请求"""
        with self._lock:
            self._metrics.timeout_requests += 1

        logger.debug(f"Request timeout {request_context.request_id}")

    def get_metrics(self) -> ConcurrencyMetrics:
        """获取并发限制指标"""
        with self._lock:
            return ConcurrencyMetrics(**self._metrics.__dict__)

    def update_config(self, new_config: ConcurrencyConfig) -> None:
        """更新配置"""
        with self._lock:
            old_max = self.config.max_concurrent
            self.config = new_config

            # 如果最大并发数改变,更新信号量
            if new_config.max_concurrent != old_max:
                self._semaphore = Semaphore(new_config.max_concurrent)
                logger.info(f"Updated max concurrent from {old_max} to {new_config.max_concurrent}")

    def enable(self) -> None:
        """启用并发限制"""
        self._is_enabled = True
        logger.info("ConcurrencyLimiter enabled")

    def disable(self) -> None:
        """禁用并发限制"""
        self._is_enabled = False
        logger.info("ConcurrencyLimiter disabled")

    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self._is_enabled

    def add_limit_callback(self, callback: Callable[[RequestContext, str], None]) -> None:
        """添加限制回调"""
        self._limit_callbacks.append(callback)

    def remove_limit_callback(self, callback: Callable[[RequestContext, str], None]) -> None:
        """移除限制回调"""
        if callback in self._limit_callbacks:
            self._limit_callbacks.remove(callback)

    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        metrics = self.get_metrics()

        return {
            "enabled": self._is_enabled,
            "algorithm": self.config.algorithm.value,
            "max_concurrent": self.config.max_concurrent,
            "current_concurrent": metrics.current_concurrent,
            "queue_length": metrics.queue_length,
            "total_requests": metrics.total_requests,
            "accepted_requests": metrics.accepted_requests,
            "rejected_requests": metrics.rejected_requests,
            "timeout_requests": metrics.timeout_requests,
            "acceptance_rate": round((metrics.accepted_requests / metrics.total_requests * 100) if metrics.total_requests > 0 else 100, 2),
            "avg_wait_time": round(metrics.avg_wait_time, 3),
        }

#!/usr/bin/env python
"""
* @author: cz
* @description: 生命周期注解测试

测试生命周期注解的功能，包括@PostConstruct、@PreDestroy等。
"""

import unittest

from miniboot.annotations import (
    Order,
    PostConstruct,
    PreDestroy,
    call_post_construct_methods,
    call_pre_destroy_methods,
    find_lifecycle_methods,
    get_order_metadata,
    get_order_value,
    get_ordered_methods,
    get_post_construct_metadata,
    get_post_construct_method_name,
    get_pre_destroy_metadata,
    get_pre_destroy_method_name,
    has_lifecycle_methods,
    is_ordered,
    is_post_construct,
    is_pre_destroy,
    sort_by_order,
    sort_instances_by_order,
)


class TestLifecycleAnnotations(unittest.TestCase):
    """生命周期注解测试类"""

    def test_post_construct_annotation(self):
        """测试@PostConstruct注解"""

        class TestService:
            @PostConstruct
            def init(self):
                pass

        method = TestService.init

        # 验证注解属性
        self.assertTrue(hasattr(method, "__is_post_construct__"))
        self.assertTrue(method.__is_post_construct__)
        self.assertEqual(method.__post_construct_method__, "init")

        # 验证元数据
        metadata = method.__post_construct_metadata__
        self.assertEqual(metadata.method, "init")

    def test_post_construct_annotation_without_parameters(self):
        """测试@PostConstruct注解不带参数"""

        class TestService:
            @PostConstruct()
            def initialize(self):
                pass

        method = TestService.initialize

        # 验证注解属性
        self.assertTrue(method.__is_post_construct__)
        self.assertEqual(method.__post_construct_method__, "initialize")

        # 验证元数据
        metadata = method.__post_construct_metadata__
        self.assertEqual(metadata.method, "initialize")

    def test_pre_destroy_annotation(self):
        """测试@PreDestroy注解"""

        class TestService:
            @PreDestroy
            def cleanup(self):
                pass

        method = TestService.cleanup

        # 验证注解属性
        self.assertTrue(hasattr(method, "__is_pre_destroy__"))
        self.assertTrue(method.__is_pre_destroy__)
        self.assertEqual(method.__pre_destroy_method__, "cleanup")

        # 验证元数据
        metadata = method.__pre_destroy_metadata__
        self.assertEqual(metadata.method, "cleanup")

    def test_pre_destroy_annotation_without_parameters(self):
        """测试@PreDestroy注解不带参数"""

        class TestService:
            @PreDestroy()
            def destroy(self):
                pass

        method = TestService.destroy

        # 验证注解属性
        self.assertTrue(method.__is_pre_destroy__)
        self.assertEqual(method.__pre_destroy_method__, "destroy")

        # 验证元数据
        metadata = method.__pre_destroy_metadata__
        self.assertEqual(metadata.method, "destroy")

    def test_multiple_lifecycle_methods(self):
        """测试多个生命周期方法"""

        class TestService:
            @PostConstruct
            def init1(self):
                pass

            @PostConstruct
            def init2(self):
                pass

            @PreDestroy
            def cleanup1(self):
                pass

            @PreDestroy
            def cleanup2(self):
                pass

        # 验证所有方法都有正确的注解
        self.assertTrue(is_post_construct(TestService.init1))
        self.assertTrue(is_post_construct(TestService.init2))
        self.assertTrue(is_pre_destroy(TestService.cleanup1))
        self.assertTrue(is_pre_destroy(TestService.cleanup2))

    def test_lifecycle_methods_on_instance_methods(self):
        """测试实例方法上的生命周期注解"""

        class TestService:
            def __init__(self):
                self.initialized = False
                self.destroyed = False

            @PostConstruct
            def init(self):
                self.initialized = True

            @PreDestroy
            def cleanup(self):
                self.destroyed = True

        # 验证方法注解
        self.assertTrue(is_post_construct(TestService.init))
        self.assertTrue(is_pre_destroy(TestService.cleanup))

    def test_utility_functions(self):
        """测试工具函数"""

        class TestService:
            @PostConstruct
            def init_method(self):
                pass

            @PreDestroy
            def destroy_method(self):
                pass

            def normal_method(self):
                pass

        # 测试is_post_construct函数
        self.assertTrue(is_post_construct(TestService.init_method))
        self.assertFalse(is_post_construct(TestService.destroy_method))
        self.assertFalse(is_post_construct(TestService.normal_method))

        # 测试is_pre_destroy函数
        self.assertTrue(is_pre_destroy(TestService.destroy_method))
        self.assertFalse(is_pre_destroy(TestService.init_method))
        self.assertFalse(is_pre_destroy(TestService.normal_method))

        # 测试get_post_construct_metadata函数
        post_metadata = get_post_construct_metadata(TestService.init_method)
        self.assertIsNotNone(post_metadata)
        self.assertEqual(post_metadata.method, "init_method")

        self.assertIsNone(get_post_construct_metadata(TestService.normal_method))

        # 测试get_pre_destroy_metadata函数
        pre_metadata = get_pre_destroy_metadata(TestService.destroy_method)
        self.assertIsNotNone(pre_metadata)
        self.assertEqual(pre_metadata.method, "destroy_method")

        self.assertIsNone(get_pre_destroy_metadata(TestService.normal_method))

        # 测试get_post_construct_method_name函数
        self.assertEqual(get_post_construct_method_name(TestService.init_method), "init_method")
        self.assertIsNone(get_post_construct_method_name(TestService.normal_method))

        # 测试get_pre_destroy_method_name函数
        self.assertEqual(get_pre_destroy_method_name(TestService.destroy_method), "destroy_method")
        self.assertIsNone(get_pre_destroy_method_name(TestService.normal_method))

    def test_find_lifecycle_methods(self):
        """测试查找生命周期方法"""

        class TestService:
            @PostConstruct
            def init1(self):
                pass

            @PostConstruct
            def init2(self):
                pass

            @PreDestroy
            def cleanup1(self):
                pass

            @PreDestroy
            def cleanup2(self):
                pass

            def normal_method(self):
                pass

        post_construct_methods, pre_destroy_methods = find_lifecycle_methods(TestService)

        # 验证找到的方法
        self.assertIn("init1", post_construct_methods)
        self.assertIn("init2", post_construct_methods)
        self.assertIn("cleanup1", pre_destroy_methods)
        self.assertIn("cleanup2", pre_destroy_methods)
        self.assertNotIn("normal_method", post_construct_methods)
        self.assertNotIn("normal_method", pre_destroy_methods)

    def test_has_lifecycle_methods(self):
        """测试检查类是否有生命周期方法"""

        class ServiceWithLifecycle:
            @PostConstruct
            def init(self):
                pass

        class ServiceWithoutLifecycle:
            def normal_method(self):
                pass

        class ServiceWithPreDestroy:
            @PreDestroy
            def cleanup(self):
                pass

        # 验证检查结果
        self.assertTrue(has_lifecycle_methods(ServiceWithLifecycle))
        self.assertFalse(has_lifecycle_methods(ServiceWithoutLifecycle))
        self.assertTrue(has_lifecycle_methods(ServiceWithPreDestroy))

    def test_call_post_construct_methods(self):
        """测试调用@PostConstruct方法"""

        class TestService:
            def __init__(self):
                self.init1_called = False
                self.init2_called = False

            @PostConstruct
            def init1(self):
                self.init1_called = True

            @PostConstruct
            def init2(self):
                self.init2_called = True

        instance = TestService()

        # 初始状态
        self.assertFalse(instance.init1_called)
        self.assertFalse(instance.init2_called)

        # 调用PostConstruct方法
        call_post_construct_methods(instance)

        # 验证方法被调用
        self.assertTrue(instance.init1_called)
        self.assertTrue(instance.init2_called)

    def test_call_pre_destroy_methods(self):
        """测试调用@PreDestroy方法"""

        class TestService:
            def __init__(self):
                self.cleanup1_called = False
                self.cleanup2_called = False

            @PreDestroy
            def cleanup1(self):
                self.cleanup1_called = True

            @PreDestroy
            def cleanup2(self):
                self.cleanup2_called = True

        instance = TestService()

        # 初始状态
        self.assertFalse(instance.cleanup1_called)
        self.assertFalse(instance.cleanup2_called)

        # 调用PreDestroy方法
        call_pre_destroy_methods(instance)

        # 验证方法被调用
        self.assertTrue(instance.cleanup1_called)
        self.assertTrue(instance.cleanup2_called)

    def test_lifecycle_methods_with_exceptions(self):
        """测试生命周期方法抛出异常的情况"""

        class TestService:
            def __init__(self):
                self.init_called = False
                self.cleanup_called = False

            @PostConstruct
            def init_with_error(self):
                raise RuntimeError("Init error")

            @PostConstruct
            def init_normal(self):
                self.init_called = True

            @PreDestroy
            def cleanup_with_error(self):
                raise RuntimeError("Cleanup error")

            @PreDestroy
            def cleanup_normal(self):
                self.cleanup_called = True

        instance = TestService()

        # 调用PostConstruct方法（应该处理异常并继续）
        call_post_construct_methods(instance)
        self.assertTrue(instance.init_called)  # 正常方法应该被调用

        # 调用PreDestroy方法（应该处理异常并继续）
        call_pre_destroy_methods(instance)
        self.assertTrue(instance.cleanup_called)  # 正常方法应该被调用

    def test_no_lifecycle_methods(self):
        """测试没有生命周期方法的类"""

        class PlainService:
            def normal_method(self):
                pass

        instance = PlainService()

        # 调用生命周期方法应该不会出错
        call_post_construct_methods(instance)
        call_pre_destroy_methods(instance)

        # 验证没有生命周期方法
        post_construct_methods, pre_destroy_methods = find_lifecycle_methods(PlainService)
        self.assertEqual(len(post_construct_methods), 0)
        self.assertEqual(len(pre_destroy_methods), 0)
        self.assertFalse(has_lifecycle_methods(PlainService))

    def test_order_annotation_on_class(self):
        """测试@Order注解在类上的使用"""

        @Order(1)
        class HighPriorityService:
            pass

        @Order(10)
        class LowPriorityService:
            pass

        # 验证注解属性
        self.assertTrue(is_ordered(HighPriorityService))
        self.assertTrue(is_ordered(LowPriorityService))
        self.assertEqual(get_order_value(HighPriorityService), 1)
        self.assertEqual(get_order_value(LowPriorityService), 10)

        # 验证元数据
        high_metadata = get_order_metadata(HighPriorityService)
        self.assertIsNotNone(high_metadata)
        self.assertEqual(high_metadata.value, 1)

        low_metadata = get_order_metadata(LowPriorityService)
        self.assertIsNotNone(low_metadata)
        self.assertEqual(low_metadata.value, 10)

    def test_order_annotation_on_method(self):
        """测试@Order注解在方法上的使用"""

        class EventHandler:
            @Order(1)
            def handle_first(self):
                pass

            @Order(2)
            def handle_second(self):
                pass

        # 验证方法注解
        self.assertTrue(is_ordered(EventHandler.handle_first))
        self.assertTrue(is_ordered(EventHandler.handle_second))
        self.assertEqual(get_order_value(EventHandler.handle_first), 1)
        self.assertEqual(get_order_value(EventHandler.handle_second), 2)

    def test_order_annotation_default_value(self):
        """测试@Order注解的默认值"""

        @Order()
        class DefaultOrderService:
            pass

        class NoOrderService:
            pass

        # 验证默认值
        self.assertTrue(is_ordered(DefaultOrderService))
        self.assertEqual(get_order_value(DefaultOrderService), 0)

        # 验证没有注解的情况
        self.assertFalse(is_ordered(NoOrderService))
        self.assertEqual(get_order_value(NoOrderService), 0)  # 默认返回0

    def test_sort_by_order_classes(self):
        """测试按@Order注解排序类"""

        @Order(3)
        class ThirdService:
            pass

        @Order(1)
        class FirstService:
            pass

        @Order(2)
        class SecondService:
            pass

        class NoOrderService:
            pass

        # 排序
        sorted_classes = sort_by_order(ThirdService, FirstService, SecondService, NoOrderService)

        # 验证排序结果
        self.assertEqual(sorted_classes[0], NoOrderService)  # 默认0，最高优先级
        self.assertEqual(sorted_classes[1], FirstService)  # Order(1)
        self.assertEqual(sorted_classes[2], SecondService)  # Order(2)
        self.assertEqual(sorted_classes[3], ThirdService)  # Order(3)

    def test_sort_instances_by_order(self):
        """测试按@Order注解排序实例"""

        @Order(2)
        class SecondService:
            pass

        @Order(1)
        class FirstService:
            pass

        @Order(3)
        class ThirdService:
            pass

        # 创建实例
        second = SecondService()
        first = FirstService()
        third = ThirdService()

        # 排序
        sorted_instances = sort_instances_by_order(second, first, third)

        # 验证排序结果
        self.assertIsInstance(sorted_instances[0], FirstService)
        self.assertIsInstance(sorted_instances[1], SecondService)
        self.assertIsInstance(sorted_instances[2], ThirdService)

    def test_get_ordered_methods(self):
        """测试获取按@Order注解排序的方法"""

        class EventHandler:
            @Order(3)
            def handle_third(self):
                pass

            @Order(1)
            def handle_first(self):
                pass

            @Order(2)
            def handle_second(self):
                pass

            def handle_no_order(self):
                pass

        # 获取所有方法并排序
        ordered_methods = get_ordered_methods(EventHandler)

        # 验证排序结果
        method_names = [name for name, method in ordered_methods]
        self.assertIn("handle_no_order", method_names[0])  # 默认0，最高优先级
        self.assertIn("handle_first", method_names)
        self.assertIn("handle_second", method_names)
        self.assertIn("handle_third", method_names)

        # 验证Order注解的方法排序
        ordered_names = [name for name, method in ordered_methods if is_ordered(method)]
        expected_order = ["handle_first", "handle_second", "handle_third"]
        for expected, actual in zip(expected_order, ordered_names):
            self.assertEqual(expected, actual)

    def test_get_ordered_methods_with_filter(self):
        """测试使用过滤器获取排序方法"""

        class MixedHandler:
            @Order(2)
            @PostConstruct
            def init_second(self):
                pass

            @Order(1)
            @PostConstruct
            def init_first(self):
                pass

            @Order(3)
            def normal_method(self):
                pass

        # 只获取PostConstruct方法
        post_construct_methods = get_ordered_methods(MixedHandler, lambda method: is_post_construct(method))

        # 验证结果
        self.assertEqual(len(post_construct_methods), 2)
        method_names = [name for name, method in post_construct_methods]
        self.assertEqual(method_names[0], "init_first")  # Order(1)
        self.assertEqual(method_names[1], "init_second")  # Order(2)

    def test_order_utility_functions(self):
        """测试@Order注解的工具函数"""

        @Order(5)
        class OrderedService:
            @Order(10)
            def ordered_method(self):
                pass

            def normal_method(self):
                pass

        class PlainService:
            def plain_method(self):
                pass

        # 测试is_ordered函数
        self.assertTrue(is_ordered(OrderedService))
        self.assertTrue(is_ordered(OrderedService.ordered_method))
        self.assertFalse(is_ordered(OrderedService.normal_method))
        self.assertFalse(is_ordered(PlainService))
        self.assertFalse(is_ordered(PlainService.plain_method))

        # 测试get_order_metadata函数
        class_metadata = get_order_metadata(OrderedService)
        self.assertIsNotNone(class_metadata)
        self.assertEqual(class_metadata.value, 5)

        method_metadata = get_order_metadata(OrderedService.ordered_method)
        self.assertIsNotNone(method_metadata)
        self.assertEqual(method_metadata.value, 10)

        self.assertIsNone(get_order_metadata(PlainService))

        # 测试get_order_value函数
        self.assertEqual(get_order_value(OrderedService), 5)
        self.assertEqual(get_order_value(OrderedService.ordered_method), 10)
        self.assertEqual(get_order_value(OrderedService.normal_method), 0)
        self.assertEqual(get_order_value(PlainService), 0)


if __name__ == "__main__":
    unittest.main()

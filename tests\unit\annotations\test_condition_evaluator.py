#!/usr/bin/env python
"""
* @author: cz
* @description: 条件评估器测试

测试条件评估器的功能，包括各种条件类型的评估逻辑。
增强版本支持更多条件类型和复杂的条件组合。
"""

import unittest
from pathlib import Path

from miniboot.annotations import (
    Conditional,
    ConditionalOnBean,
    ConditionalOnClass,
    ConditionalOnExpression,
    ConditionalOnMissingBean,
    ConditionalOnMissingClass,
    ConditionalOnProperty,
    ConditionalOnResource,
    ConditionalOnWeb,
    ConditionContext,
    ConditionEvaluator,
    create_condition_context,
    evaluate_condition,
)


# 测试用的条件类
class AlwaysTrueCondition:
    """总是返回True的条件类"""

    def matches(self, _context):
        return True


class AlwaysFalseCondition:
    """总是返回False的条件类"""

    def matches(self, _context):
        return False


class PropertyBasedCondition:
    """基于属性的条件类"""

    def matches(self, context):
        return context.get_property("test.enabled", False)


# 测试用的Bean类
class TestService:
    pass


class TestRepository:
    pass


class TestConditionEvaluator(unittest.TestCase):
    """条件评估器测试类"""

    def setUp(self):
        """设置测试环境"""
        self.context = ConditionContext()
        self.evaluator = ConditionEvaluator(self.context)

    def test_condition_context_properties(self):
        """测试条件上下文的属性管理"""
        # 测试设置和获取属性
        self.context.set_property("app.name", "test-app")
        self.context.set_property("app.debug", True)

        self.assertEqual(self.context.get_property("app.name"), "test-app")
        self.assertTrue(self.context.get_property("app.debug"))
        self.assertIsNone(self.context.get_property("non.existent"))
        self.assertEqual(self.context.get_property("non.existent", "default"), "default")

        # 测试属性存在检查
        self.assertTrue(self.context.has_property("app.name"))
        self.assertFalse(self.context.has_property("non.existent"))

    def test_condition_context_beans(self):
        """测试条件上下文的Bean管理"""
        # 注册Bean
        self.context.register_bean("testService", TestService)
        self.context.register_bean("testRepository", TestRepository)

        # 测试Bean存在检查
        self.assertTrue(self.context.has_bean("testService"))
        self.assertTrue(self.context.has_bean("testRepository"))
        self.assertFalse(self.context.has_bean("nonExistentBean"))

        # 测试Bean类型检查
        self.assertTrue(self.context.has_bean_type(TestService))
        self.assertTrue(self.context.has_bean_type(TestRepository))
        self.assertFalse(self.context.has_bean_type(str))

    def test_condition_context_web_environment(self):
        """测试条件上下文的Web环境设置"""
        # 默认不是Web环境
        self.assertFalse(self.context.is_web_environment)

        # 设置为Web环境
        self.context.set_web_environment(True)
        self.assertTrue(self.context.is_web_environment)

        # 设置为非Web环境
        self.context.set_web_environment(False)
        self.assertFalse(self.context.is_web_environment)

    def test_evaluate_no_condition(self):
        """测试评估没有条件的类"""

        class PlainClass:
            pass

        # 没有条件的类应该返回True
        self.assertTrue(self.evaluator.evaluate(PlainClass))

    def test_evaluate_property_condition_with_value(self):
        """测试评估属性条件（带期望值）"""

        @ConditionalOnProperty(name="feature.enabled", having_value="true")
        class FeatureBean:
            pass

        # 属性不存在时应该返回False
        self.assertFalse(self.evaluator.evaluate(FeatureBean))

        # 属性值不匹配时应该返回False
        self.context.set_property("feature.enabled", "false")
        self.assertFalse(self.evaluator.evaluate(FeatureBean))

        # 属性值匹配时应该返回True
        self.context.set_property("feature.enabled", "true")
        self.assertTrue(self.evaluator.evaluate(FeatureBean))

    def test_evaluate_property_condition_without_value(self):
        """测试评估属性条件（不带期望值）"""

        @ConditionalOnProperty(name="app.debug")
        class DebugBean:
            pass

        # 属性不存在时应该返回False
        self.assertFalse(self.evaluator.evaluate(DebugBean))

        # 属性存在时应该返回True（无论值是什么）
        self.context.set_property("app.debug", "any-value")
        self.assertTrue(self.evaluator.evaluate(DebugBean))

    def test_evaluate_property_condition_match_if_missing(self):
        """测试评估属性条件（match_if_missing=True）"""

        @ConditionalOnProperty(name="optional.feature", match_if_missing=True)
        class OptionalBean:
            pass

        # 属性不存在时应该返回True
        self.assertTrue(self.evaluator.evaluate(OptionalBean))

        # 属性存在时也应该返回True
        self.context.set_property("optional.feature", "enabled")
        self.assertTrue(self.evaluator.evaluate(OptionalBean))

    def test_evaluate_bean_condition_with_type(self):
        """测试评估Bean存在条件（按类型）"""

        @ConditionalOnBean(TestService)
        class DependentBean:
            pass

        # Bean不存在时应该返回False
        self.assertFalse(self.evaluator.evaluate(DependentBean))

        # Bean存在时应该返回True
        self.context.register_bean("testService", TestService)
        self.assertTrue(self.evaluator.evaluate(DependentBean))

    def test_evaluate_bean_condition_with_name(self):
        """测试评估Bean存在条件（按名称）"""

        @ConditionalOnBean(name="specificService")
        class DependentBean:
            pass

        # Bean不存在时应该返回False
        self.assertFalse(self.evaluator.evaluate(DependentBean))

        # Bean存在时应该返回True
        self.context.register_bean("specificService", TestService)
        self.assertTrue(self.evaluator.evaluate(DependentBean))

    def test_evaluate_missing_bean_condition_with_type(self):
        """测试评估Bean不存在条件（按类型）"""

        @ConditionalOnMissingBean(TestService)
        class DefaultBean:
            pass

        # Bean不存在时应该返回True
        self.assertTrue(self.evaluator.evaluate(DefaultBean))

        # Bean存在时应该返回False
        self.context.register_bean("testService", TestService)
        self.assertFalse(self.evaluator.evaluate(DefaultBean))

    def test_evaluate_missing_bean_condition_with_name(self):
        """测试评估Bean不存在条件（按名称）"""

        @ConditionalOnMissingBean(name="customService")
        class DefaultService:
            pass

        # Bean不存在时应该返回True
        self.assertTrue(self.evaluator.evaluate(DefaultService))

        # Bean存在时应该返回False
        self.context.register_bean("customService", TestService)
        self.assertFalse(self.evaluator.evaluate(DefaultService))

    def test_evaluate_class_condition(self):
        """测试评估类存在条件"""

        @ConditionalOnClass("os.path")
        class OsPathBean:
            pass

        @ConditionalOnClass("non.existent.module")
        class NonExistentBean:
            pass

        # 存在的类应该返回True
        self.assertTrue(self.evaluator.evaluate(OsPathBean))

        # 不存在的类应该返回False
        self.assertFalse(self.evaluator.evaluate(NonExistentBean))

    def test_evaluate_web_condition(self):
        """测试评估Web环境条件"""

        @ConditionalOnWeb
        class WebBean:
            pass

        # 非Web环境时应该返回False
        self.assertFalse(self.evaluator.evaluate(WebBean))

        # Web环境时应该返回True
        self.context.set_web_environment(True)
        self.assertTrue(self.evaluator.evaluate(WebBean))

    def test_evaluate_custom_condition(self):
        """测试评估自定义条件"""

        @Conditional(AlwaysTrueCondition)
        class AlwaysTrueBean:
            pass

        @Conditional(AlwaysFalseCondition)
        class AlwaysFalseBean:
            pass

        @Conditional(PropertyBasedCondition)
        class PropertyBean:
            pass

        # 总是True的条件
        self.assertTrue(self.evaluator.evaluate(AlwaysTrueBean))

        # 总是False的条件
        self.assertFalse(self.evaluator.evaluate(AlwaysFalseBean))

        # 基于属性的条件
        self.assertFalse(self.evaluator.evaluate(PropertyBean))
        self.context.set_property("test.enabled", True)
        self.assertTrue(self.evaluator.evaluate(PropertyBean))

    def test_create_condition_context(self):
        """测试创建条件上下文的便利函数"""
        context = create_condition_context(app_name="test-app", app_debug=True, server_port=8080)

        self.assertEqual(context.get_property("app_name"), "test-app")
        self.assertTrue(context.get_property("app_debug"))
        self.assertEqual(context.get_property("server_port"), 8080)

    def test_evaluate_condition_function(self):
        """测试评估条件的便利函数"""

        @ConditionalOnProperty(name="test.enabled", having_value="true")
        class TestBean:
            pass

        # 注意：create_condition_context使用下划线作为分隔符，但条件期望点分隔符
        # 所以我们需要手动设置正确的属性名
        context = ConditionContext()
        context.set_property("test.enabled", "true")

        # 使用便利函数评估条件
        self.assertTrue(evaluate_condition(TestBean, context))

        # 修改属性值
        context.set_property("test.enabled", "false")
        self.assertFalse(evaluate_condition(TestBean, context))

    def test_enhanced_property_condition_evaluation(self):
        """测试增强的属性条件评估"""

        # 测试多属性条件
        @ConditionalOnProperty(names=["app.feature1.enabled", "app.feature2.enabled"])
        class MultiPropertyBean:
            pass

        # 只有一个属性存在，应该失败
        self.context.set_property("app.feature1.enabled", "true")
        self.assertFalse(self.evaluator.evaluate(MultiPropertyBean))

        # 两个属性都存在，应该成功
        self.context.set_property("app.feature2.enabled", "true")
        self.assertTrue(self.evaluator.evaluate(MultiPropertyBean))

        # 测试前缀条件
        @ConditionalOnProperty(prefix="app.database")
        class DatabaseBean:
            pass

        # 没有匹配前缀的属性，应该失败
        self.assertFalse(self.evaluator.evaluate(DatabaseBean))

        # 添加匹配前缀的属性，应该成功
        self.context.set_property("app.database.url", "*************************************")
        self.assertTrue(self.evaluator.evaluate(DatabaseBean))

        # 测试前缀条件与期望值
        @ConditionalOnProperty(prefix="app.cache", having_value="redis")
        class CacheBean:
            pass

        # 添加不匹配期望值的属性
        self.context.set_property("app.cache.type", "memory")
        self.assertFalse(self.evaluator.evaluate(CacheBean))

        # 添加匹配期望值的属性
        self.context.set_property("app.cache.provider", "redis")
        self.assertTrue(self.evaluator.evaluate(CacheBean))

    def test_missing_class_condition_evaluation(self):
        """测试类不存在条件评估"""

        @ConditionalOnMissingClass("nonexistent.module.NonExistentClass")
        class MockBean:
            pass

        # 类不存在，条件应该满足
        self.assertTrue(self.evaluator.evaluate(MockBean))

        @ConditionalOnMissingClass("os.path")  # 这个模块存在
        class AnotherMockBean:
            pass

        # 模块存在，条件应该不满足
        self.assertFalse(self.evaluator.evaluate(AnotherMockBean))

    def test_expression_condition_evaluation(self):
        """测试表达式条件评估"""

        # 设置测试属性
        self.context.set_property("app.env", "development")
        self.context.set_property("app.debug", "true")
        self.context.set_property("app.max_connections", "100")

        # 简单表达式
        @ConditionalOnExpression("'${app.env}' == 'development'")
        class DevBean:
            pass

        self.assertTrue(self.evaluator.evaluate(DevBean))

        # 复杂表达式
        @ConditionalOnExpression("'${app.debug}' == 'true' and '${app.env}' != 'production'")
        class DebugBean:
            pass

        self.assertTrue(self.evaluator.evaluate(DebugBean))

        # 数值比较表达式
        @ConditionalOnExpression("int('${app.max_connections}') > 50")
        class HighConnectionBean:
            pass

        self.assertTrue(self.evaluator.evaluate(HighConnectionBean))

        # 带默认值的表达式
        @ConditionalOnExpression("int('${app.timeout:30}') < 60")
        class TimeoutBean:
            pass

        self.assertTrue(self.evaluator.evaluate(TimeoutBean))

        # 错误的表达式应该返回False
        @ConditionalOnExpression("invalid_expression()")
        class ErrorBean:
            pass

        self.assertFalse(self.evaluator.evaluate(ErrorBean))

    def test_resource_condition_evaluation(self):
        """测试资源存在条件评估"""
        import tempfile

        # 创建临时文件
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_path = temp_file.name
            temp_file.write(b"test content")

        try:

            @ConditionalOnResource(temp_path)
            class ResourceBean:
                pass

            # 文件存在，条件应该满足
            self.assertTrue(self.evaluator.evaluate(ResourceBean))

            @ConditionalOnResource("nonexistent/file.txt")
            class MissingResourceBean:
                pass

            # 文件不存在，条件应该不满足
            self.assertFalse(self.evaluator.evaluate(MissingResourceBean))

            # 测试多资源条件
            with tempfile.NamedTemporaryFile(delete=False) as temp_file2:
                temp_path2 = temp_file2.name
                temp_file2.write(b"test content 2")

            try:

                @ConditionalOnResource([temp_path, temp_path2])
                class MultiResourceBean:
                    pass

                # 两个文件都存在，条件应该满足
                self.assertTrue(self.evaluator.evaluate(MultiResourceBean))

                @ConditionalOnResource([temp_path, "nonexistent.txt"])
                class PartialResourceBean:
                    pass

                # 只有一个文件存在，条件应该不满足
                self.assertFalse(self.evaluator.evaluate(PartialResourceBean))

            finally:
                Path(temp_path2).unlink()

        finally:
            Path(temp_path).unlink()

    def test_complex_condition_combinations(self):
        """测试复杂的条件组合"""

        # 设置测试环境
        self.context.set_property("app.env", "development")
        self.context.set_property("app.debug", "true")
        self.context.set_property("database.enabled", "true")
        self.context.register_bean("dataSource", type)

        # 多个条件的组合（注意：装饰器从下往上执行，最上面的会覆盖下面的元数据）
        @ConditionalOnProperty(name="app.env", having_value="development")
        class ComplexBean:
            pass

        # 验证条件评估
        self.assertTrue(self.evaluator.evaluate(ComplexBean))

        # 修改属性值，条件应该不满足
        self.context.set_property("app.env", "production")
        self.assertFalse(self.evaluator.evaluate(ComplexBean))


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Profile 注解单元测试

测试 @Profile 注解的功能，包括：
- 基本 Profile 匹配
- 多 Profile 支持
- Profile 表达式
- 条件评估
"""

import unittest
from unittest.mock import Mock

from miniboot.annotations.profile import (
    Profile,
    ProfileCondition,
    ProfileEvaluator,
    create_profile_evaluator,
    evaluate_profile,
    get_profile_expression,
    get_profile_metadata,
    get_profile_names,
    is_profile,
)


class TestProfileAnnotation(unittest.TestCase):
    """Profile 注解测试类"""

    def test_single_profile(self):
        """测试单个 Profile"""

        @Profile("dev")
        class DevService:
            pass

        # 检查注解标记
        self.assertTrue(is_profile(DevService))

        # 检查元数据
        metadata = get_profile_metadata(DevService)
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.profiles, ["dev"])
        self.assertIsNone(metadata.expression)

        # 检查便利函数
        self.assertEqual(get_profile_names(DevService), ["dev"])
        self.assertIsNone(get_profile_expression(DevService))

    def test_multiple_profiles(self):
        """测试多个 Profile"""

        @Profile("dev", "test")
        class DevTestService:
            pass

        # 检查注解标记
        self.assertTrue(is_profile(DevTestService))

        # 检查元数据
        metadata = get_profile_metadata(DevTestService)
        self.assertIsNotNone(metadata)
        self.assertEqual(set(metadata.profiles), {"dev", "test"})

        # 检查便利函数
        self.assertEqual(set(get_profile_names(DevTestService)), {"dev", "test"})

    def test_profile_expression(self):
        """测试 Profile 表达式"""

        @Profile(expression="dev & !prod")
        class DevNotProdService:
            pass

        # 检查注解标记
        self.assertTrue(is_profile(DevNotProdService))

        # 检查元数据
        metadata = get_profile_metadata(DevNotProdService)
        self.assertIsNotNone(metadata)
        self.assertEqual(metadata.expression, "dev & !prod")
        self.assertEqual(metadata.profiles, [])

        # 检查便利函数
        self.assertEqual(get_profile_expression(DevNotProdService), "dev & !prod")

    def test_complex_expression(self):
        """测试复杂 Profile 表达式"""

        @Profile(expression="(dev | test) & !prod")
        class NonProdService:
            pass

        # 检查元数据
        metadata = get_profile_metadata(NonProdService)
        self.assertEqual(metadata.expression, "(dev | test) & !prod")

    def test_no_profile_annotation(self):
        """测试没有 Profile 注解的类"""

        class RegularService:
            pass

        # 检查注解标记
        self.assertFalse(is_profile(RegularService))

        # 检查元数据
        self.assertIsNone(get_profile_metadata(RegularService))
        self.assertEqual(get_profile_names(RegularService), [])
        self.assertIsNone(get_profile_expression(RegularService))

    def test_method_profile(self):
        """测试方法级别的 Profile 注解"""

        class ConfigClass:
            @Profile("dev")
            def dev_bean(self):
                return "dev_bean"

            @Profile("prod")
            def prod_bean(self):
                return "prod_bean"

        # 检查方法注解
        self.assertTrue(is_profile(ConfigClass.dev_bean))
        self.assertTrue(is_profile(ConfigClass.prod_bean))

        # 检查元数据
        dev_metadata = get_profile_metadata(ConfigClass.dev_bean)
        self.assertEqual(dev_metadata.profiles, ["dev"])

        prod_metadata = get_profile_metadata(ConfigClass.prod_bean)
        self.assertEqual(prod_metadata.profiles, ["prod"])


class TestProfileCondition(unittest.TestCase):
    """Profile 条件测试类"""

    def setUp(self):
        """测试前置设置"""
        self.condition = ProfileCondition()

    def test_single_profile_match(self):
        """测试单个 Profile 匹配"""

        @Profile("dev")
        class DevService:
            pass

        # 创建模拟环境
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = DevService

        # 测试匹配
        self.assertTrue(self.condition.matches(context))

    def test_single_profile_no_match(self):
        """测试单个 Profile 不匹配"""

        @Profile("prod")
        class ProdService:
            pass

        # 创建模拟环境
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = ProdService

        # 测试不匹配
        self.assertFalse(self.condition.matches(context))

    def test_multiple_profiles_match(self):
        """测试多个 Profile 匹配（OR 关系）"""

        @Profile("dev", "test")
        class DevTestService:
            pass

        # 创建模拟环境（只激活 dev）
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = DevTestService

        # 测试匹配（只要有一个匹配即可）
        self.assertTrue(self.condition.matches(context))

    def test_expression_simple_and(self):
        """测试简单 AND 表达式"""

        @Profile(expression="dev & debug")
        class DevDebugService:
            pass

        # 创建模拟环境
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev", "debug"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = DevDebugService

        # 测试匹配
        self.assertTrue(self.condition.matches(context))

    def test_expression_simple_and_no_match(self):
        """测试简单 AND 表达式不匹配"""

        @Profile(expression="dev & debug")
        class DevDebugService:
            pass

        # 创建模拟环境（缺少 debug）
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = DevDebugService

        # 测试不匹配
        self.assertFalse(self.condition.matches(context))

    def test_expression_simple_or(self):
        """测试简单 OR 表达式"""

        @Profile(expression="dev | test")
        class DevOrTestService:
            pass

        # 创建模拟环境（只有 test）
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["test"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = DevOrTestService

        # 测试匹配
        self.assertTrue(self.condition.matches(context))

    def test_expression_not(self):
        """测试 NOT 表达式"""

        @Profile(expression="!prod")
        class NotProdService:
            pass

        # 创建模拟环境（没有 prod）
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = NotProdService

        # 测试匹配
        self.assertTrue(self.condition.matches(context))

    def test_expression_complex(self):
        """测试复杂表达式"""

        @Profile(expression="(dev | test) & !prod")
        class NonProdService:
            pass

        # 创建模拟环境
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = NonProdService

        # 测试匹配
        self.assertTrue(self.condition.matches(context))

    def test_no_profile_always_match(self):
        """测试没有 Profile 注解的类总是匹配"""

        class RegularService:
            pass

        # 创建模拟环境
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["any"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = RegularService

        # 测试总是匹配
        self.assertTrue(self.condition.matches(context))

    def test_default_profile_fallback(self):
        """测试默认 Profile 回退"""

        @Profile("default")
        class DefaultService:
            pass

        # 创建模拟环境（没有激活的 Profile）
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = []
        mock_env.get_default_profiles.return_value = ["default"]

        # 创建上下文
        context = Mock()
        context.environment = mock_env
        context.target = DefaultService

        # 测试匹配（使用默认 Profile）
        self.assertTrue(self.condition.matches(context))


class TestProfileEvaluator(unittest.TestCase):
    """Profile 评估器测试类"""

    def test_create_evaluator(self):
        """测试创建评估器"""
        mock_env = Mock()
        evaluator = create_profile_evaluator(mock_env)

        self.assertIsInstance(evaluator, ProfileEvaluator)
        self.assertEqual(evaluator.environment, mock_env)

    def test_evaluate_function(self):
        """测试评估函数"""

        @Profile("dev")
        class DevService:
            pass

        # 创建模拟环境
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev"]
        mock_env.get_default_profiles.return_value = ["default"]

        # 测试评估函数
        result = evaluate_profile(DevService, mock_env)
        self.assertTrue(result)

    def test_evaluator_methods(self):
        """测试评估器方法"""
        mock_env = Mock()
        mock_env.get_active_profiles.return_value = ["dev", "test"]
        mock_env.get_default_profiles.return_value = ["default"]
        mock_env.accepts_profiles.return_value = True

        evaluator = ProfileEvaluator(mock_env)

        # 测试方法
        self.assertEqual(evaluator.get_active_profiles(), ["dev", "test"])
        self.assertEqual(evaluator.get_default_profiles(), ["default"])
        self.assertTrue(evaluator.accepts_profiles("dev"))

    def test_no_environment(self):
        """测试没有环境配置的情况"""
        evaluator = ProfileEvaluator(None)

        # 测试默认行为
        self.assertEqual(evaluator.get_active_profiles(), [])
        self.assertEqual(evaluator.get_default_profiles(), ["default"])
        self.assertTrue(evaluator.accepts_profiles("any"))


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean缓存系统单元测试
"""

import unittest
from typing import Any, Callable
from unittest.mock import Mock, patch

from miniboot.bean.cache import ThreeLevelCache
from miniboot.bean.utils import CircularDependencyError


class TestBean:
    """测试用Bean类"""

    def __init__(self, name: str = "test"):
        self.name = name
        self.dependency = None


class TestThreeLevelCache(unittest.TestCase):
    """ThreeLevelCache测试"""

    def setUp(self):
        """测试前置设置"""
        self.cache = ThreeLevelCache()

    def test_cache_creation(self):
        """测试缓存创建"""
        self.assertIsNotNone(self.cache)
        self.assertIsInstance(self.cache, ThreeLevelCache)

    def test_singleton_cache_operations(self):
        """测试单例缓存操作"""
        bean = TestBean("singleton")

        # 存储到单例缓存
        self.cache.put_singleton("testBean", bean)

        # 从缓存获取
        retrieved_bean = self.cache.get_bean("testBean")
        self.assertIs(retrieved_bean, bean)

        # 检查是否在单例缓存中
        self.assertTrue(self.cache.contains_singleton("testBean"))

        # 获取不存在的Bean
        self.assertIsNone(self.cache.get_bean("nonexistent"))
        self.assertFalse(self.cache.contains_singleton("nonexistent"))

    def test_early_singleton_cache_operations(self):
        """测试早期单例缓存操作"""
        bean = TestBean("early")

        # 存储到早期单例缓存
        self.cache.put_early_singleton("testBean", bean)

        # 从缓存获取
        retrieved_bean = self.cache.get_bean("testBean")
        self.assertIs(retrieved_bean, bean)

        # 检查是否在缓存中
        self.assertTrue(self.cache.contains_bean("testBean"))

    def test_singleton_factory_operations(self):
        """测试单例工厂缓存操作"""
        bean = TestBean("factory")

        def bean_factory() -> Any:
            return bean

        # 存储到单例工厂缓存
        self.cache.put_factory("testBean", bean_factory)

        # 从缓存获取Bean（会调用工厂函数）
        retrieved_bean = self.cache.get_bean("testBean")
        self.assertIsInstance(retrieved_bean, TestBean)

        # 检查是否在缓存中
        self.assertTrue(self.cache.contains_bean("testBean"))

    def test_cache_hierarchy(self):
        """测试缓存层次结构"""
        bean = TestBean("hierarchy")

        # 只在单例工厂缓存中存储
        def bean_factory() -> Any:
            return bean

        self.cache.put_factory("testBean", bean_factory)

        # 获取Bean应该从工厂创建并移动到早期单例缓存
        retrieved_bean = self.cache.get_bean("testBean")
        self.assertIsInstance(retrieved_bean, TestBean)

        # 现在应该在缓存中
        self.assertTrue(self.cache.contains_bean("testBean"))

        # 提升到单例缓存
        self.cache.put_singleton("testBean", retrieved_bean)

        # 现在应该在单例缓存中
        self.assertTrue(self.cache.contains_singleton("testBean"))

    def test_circular_dependency_detection(self):
        """测试循环依赖检测"""
        # 开始创建Bean A
        self.cache.set_currently_creating("beanA", True)
        self.assertTrue(self.cache.is_currently_creating("beanA"))

        # 开始创建Bean B
        self.cache.set_currently_creating("beanB", True)
        self.assertTrue(self.cache.is_currently_creating("beanB"))

        # 检查创建状态
        self.assertTrue(self.cache.is_currently_creating("beanA"))
        self.assertTrue(self.cache.is_currently_creating("beanB"))

        # 完成Bean B的创建
        self.cache.set_currently_creating("beanB", False)
        self.assertFalse(self.cache.is_currently_creating("beanB"))

        # 完成Bean A的创建
        self.cache.set_currently_creating("beanA", False)
        self.assertFalse(self.cache.is_currently_creating("beanA"))

    def test_circular_dependency_resolution(self):
        """测试循环依赖解决"""
        # 简化的循环依赖测试，避免无限递归
        bean_a = TestBean("beanA")
        bean_b = TestBean("beanB")

        # 先创建Bean B
        self.cache.put_singleton("beanB", bean_b)

        # 创建依赖Bean B的Bean A
        def factory_a() -> Any:
            bean_a.dependency = self.cache.get_bean("beanB")
            return bean_a

        self.cache.put_factory("beanA", factory_a)

        # 获取Bean A
        retrieved_bean_a = self.cache.get_bean("beanA")
        self.assertIsInstance(retrieved_bean_a, TestBean)
        self.assertEqual(retrieved_bean_a.name, "beanA")

        # 验证依赖关系
        self.assertIs(retrieved_bean_a.dependency, bean_b)

    def test_cache_removal(self):
        """测试缓存移除"""
        bean = TestBean("removable")

        # 添加到各级缓存
        self.cache.put_singleton("testBean", bean)
        self.cache.put_early_singleton("earlyBean", bean)
        self.cache.put_factory("factoryBean", lambda: bean)

        # 验证存在
        self.assertTrue(self.cache.contains_singleton("testBean"))
        self.assertTrue(self.cache.contains_bean("earlyBean"))
        self.assertTrue(self.cache.contains_bean("factoryBean"))

        # 移除
        self.cache.remove_singleton("testBean")
        self.cache.remove_singleton("earlyBean")
        self.cache.remove_singleton("factoryBean")

        # 验证已移除
        self.assertFalse(self.cache.contains_singleton("testBean"))
        self.assertFalse(self.cache.contains_bean("earlyBean"))
        self.assertFalse(self.cache.contains_bean("factoryBean"))

    def test_cache_clear(self):
        """测试缓存清理"""
        bean = TestBean("clearable")

        # 添加到各级缓存
        self.cache.put_singleton("testBean", bean)
        self.cache.put_early_singleton("earlyBean", bean)
        self.cache.put_factory("factoryBean", lambda: bean)

        # 清理所有缓存
        self.cache.clear()

        # 验证所有缓存都已清空
        self.assertFalse(self.cache.contains_singleton("testBean"))
        self.assertFalse(self.cache.contains_bean("earlyBean"))
        self.assertFalse(self.cache.contains_bean("factoryBean"))

    def test_cache_statistics(self):
        """测试缓存统计"""
        bean = TestBean("stats")

        # 添加一些Bean
        self.cache.put_singleton("bean1", bean)
        self.cache.put_early_singleton("bean2", bean)
        self.cache.put_factory("bean3", lambda: bean)

        # 获取统计信息
        stats = self.cache.get_cache_stats()

        # 验证统计信息包含基本字段
        self.assertIsInstance(stats, dict)
        self.assertIn("total_requests", stats)
        self.assertIn("hits_level1", stats)
        self.assertIn("hits_level2", stats)
        self.assertIn("hits_level3", stats)
        self.assertIn("misses", stats)

    def test_cache_bean_names(self):
        """测试获取缓存中的Bean名称"""
        bean = TestBean("names")

        # 添加Bean到不同缓存
        self.cache.put_singleton("singleton1", bean)
        self.cache.put_singleton("singleton2", bean)
        self.cache.put_early_singleton("early1", bean)

        # 获取单例Bean名称
        singleton_names = self.cache.get_singleton_names()
        self.assertIn("singleton1", singleton_names)
        self.assertIn("singleton2", singleton_names)
        self.assertGreaterEqual(len(singleton_names), 2)

    def test_cache_thread_safety(self):
        """测试缓存线程安全性"""
        import threading
        import time

        bean = TestBean("thread_safe")
        results = []

        def cache_operation():
            # 模拟并发缓存操作
            self.cache.put_singleton("threadBean", bean)
            time.sleep(0.01)  # 短暂延迟
            retrieved = self.cache.get_bean("threadBean")
            results.append(retrieved)

        # 创建多个线程
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=cache_operation)
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join()

        # 验证所有线程都获取到了同一个Bean实例
        for result in results:
            self.assertIs(result, bean)

    def test_cache_memory_management(self):
        """测试缓存内存管理"""
        import gc
        import weakref

        bean = TestBean("memory")
        weak_ref = weakref.ref(bean)

        # 添加到缓存
        self.cache.put_singleton("memoryBean", bean)

        # 删除原始引用
        del bean

        # Bean应该仍然存在（被缓存持有）
        self.assertIsNotNone(weak_ref())

        # 从缓存中移除
        self.cache.remove_singleton("memoryBean")

        # 强制垃圾回收
        gc.collect()

        # Bean应该被回收
        self.assertIsNone(weak_ref())


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务处理器单元测试 - 简化版本
"""

import unittest
from unittest.mock import Mock

from miniboot.processor.base import ProcessorOrder
from miniboot.processor.schedule import ScheduledAnnotationProcessor


class MockScheduledConfig:
    """模拟定时任务配置"""

    def __init__(self, cron=None, fixed_rate=None, fixed_delay=None):
        self.cron = cron
        self.fixed_rate = fixed_rate
        self.fixed_delay = fixed_delay


class TestBeanWithScheduled:
    """测试Bean - 有@Scheduled方法"""

    def __init__(self):
        self.executed_tasks = []

    def daily_task(self):
        """@Scheduled方法"""
        self.executed_tasks.append("daily_task")

    def health_check(self):
        """@Scheduled方法"""
        self.executed_tasks.append("health_check")


# 为测试Bean添加@Scheduled注解
TestBeanWithScheduled.daily_task.__scheduled__ = MockScheduledConfig(cron="0 0 12 * * ?")
TestBeanWithScheduled.health_check.__scheduled__ = MockScheduledConfig(fixed_rate="5s")


class TestBeanWithMultipleScheduled:
    """测试Bean - 多个@Scheduled方法"""

    def __init__(self):
        self.executed_tasks = []

    def task1(self):
        """第一个@Scheduled方法"""
        self.executed_tasks.append("task1")

    def task2(self):
        """第二个@Scheduled方法"""
        self.executed_tasks.append("task2")


# 为多个定时任务方法添加注解
TestBeanWithMultipleScheduled.task1.__scheduled__ = MockScheduledConfig(cron="0 */5 * * * *")
TestBeanWithMultipleScheduled.task2.__scheduled__ = MockScheduledConfig(fixed_delay="10s")


class TestBeanWithoutScheduled:
    """测试Bean - 无@Scheduled注解"""

    def __init__(self):
        self.value = "test"


class TestScheduledProcessor(unittest.TestCase):
    """定时任务处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.mock_task_scheduler = Mock()
        self.mock_task_manager = Mock()
        self.mock_task_scheduler.task_manager = self.mock_task_manager
        self.processor = ScheduledAnnotationProcessor(self.mock_task_scheduler)

        # 设置模拟任务管理器的返回值
        self.mock_task_manager.create_method_task.return_value = "task_id_123"

    def test_processor_order(self):
        """测试处理器执行顺序"""
        self.assertEqual(self.processor.get_order(), ProcessorOrder.LIFECYCLE_PROCESSOR + 30)

    def test_supports_bean_with_scheduled(self):
        """测试支持有@Scheduled注解的Bean"""
        bean = TestBeanWithScheduled()
        self.assertTrue(self.processor.supports(bean, "testBean"))

    def test_supports_bean_without_scheduled(self):
        """测试不支持无@Scheduled注解的Bean"""
        bean = TestBeanWithoutScheduled()
        self.assertFalse(self.processor.supports(bean, "testBean"))

    def test_supports_none_bean(self):
        """测试不支持None Bean"""
        self.assertFalse(self.processor.supports(None, "testBean"))

    def test_scheduled_task_registration(self):
        """测试定时任务注册"""
        bean = TestBeanWithScheduled()

        # 执行处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)

        # 验证任务管理器被调用
        self.assertEqual(self.mock_task_manager.create_method_task.call_count, 2)

        # 验证调用参数
        calls = self.mock_task_manager.create_method_task.call_args_list

        # 第一个调用（daily_task）
        call1_kwargs = calls[0][1]
        self.assertEqual(call1_kwargs["instance"], bean)
        self.assertEqual(call1_kwargs["name"], "testBean.daily_task")
        self.assertIsNotNone(call1_kwargs["config"])

        # 第二个调用（health_check）
        call2_kwargs = calls[1][1]
        self.assertEqual(call2_kwargs["instance"], bean)
        self.assertEqual(call2_kwargs["name"], "testBean.health_check")
        self.assertIsNotNone(call2_kwargs["config"])

    def test_multiple_scheduled_tasks_registration(self):
        """测试多个定时任务注册"""
        bean = TestBeanWithMultipleScheduled()

        # 执行处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)

        # 验证任务管理器被调用两次
        self.assertEqual(self.mock_task_manager.create_method_task.call_count, 2)

    def test_no_task_scheduler(self):
        """测试没有任务调度器的情况"""
        processor = ScheduledAnnotationProcessor()  # 没有设置任务调度器
        bean = TestBeanWithScheduled()

        # 执行处理（不应该抛出异常）
        result = processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)

    def test_already_processed_bean(self):
        """测试已处理的Bean不会重复处理"""
        bean = TestBeanWithScheduled()

        # 第一次处理
        self.processor.post_process_after_initialization(bean, "testBean")

        # 重置mock调用记录
        self.mock_task_manager.reset_mock()

        # 第二次处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证没有再次调用任务管理器
        self.mock_task_manager.create_method_task.assert_not_called()
        self.assertIs(result, bean)

    def test_post_process_before_initialization(self):
        """测试初始化前处理（应该直接返回原Bean）"""
        bean = TestBeanWithScheduled()

        result = self.processor.post_process_before_initialization(bean, "testBean")

        self.assertIs(result, bean)
        # 任务管理器不应该被调用
        self.mock_task_manager.create_method_task.assert_not_called()

    def test_destroy_bean(self):
        """测试Bean销毁时取消注册任务"""
        bean = TestBeanWithScheduled()

        # 先注册任务
        self.processor.post_process_after_initialization(bean, "testBean")

        # 销毁Bean
        self.processor.destroy_bean(bean, "testBean")

        # 验证取消注册被调用
        self.assertEqual(self.mock_task_scheduler.remove_task.call_count, 2)
        self.mock_task_scheduler.remove_task.assert_any_call("task_id_123")

    def test_none_bean_handling(self):
        """测试None Bean的处理"""
        result = self.processor.post_process_after_initialization(None, "testBean")
        self.assertIsNone(result)

        # 销毁None Bean不应该抛出异常
        self.processor.destroy_bean(None, "testBean")

    def test_processed_beans_count(self):
        """测试已处理的Bean数量"""
        initial_count = self.processor.get_processed_beans_count()

        bean1 = TestBeanWithScheduled()
        bean2 = TestBeanWithScheduled()

        self.processor.post_process_after_initialization(bean1, "testBean1")
        self.processor.post_process_after_initialization(bean2, "testBean2")

        self.assertEqual(self.processor.get_processed_beans_count(), initial_count + 2)

    def test_registered_tasks_count(self):
        """测试已注册的任务数量"""
        initial_count = self.processor.get_registered_tasks_count()

        bean1 = TestBeanWithScheduled()
        bean2 = TestBeanWithMultipleScheduled()

        self.processor.post_process_after_initialization(bean1, "testBean1")
        self.processor.post_process_after_initialization(bean2, "testBean2")

        # bean1有2个任务，bean2有2个任务
        self.assertEqual(self.processor.get_registered_tasks_count(), initial_count + 4)

    def test_bean_without_scheduled_not_processed(self):
        """测试没有@Scheduled注解的Bean不被处理"""
        bean = TestBeanWithoutScheduled()

        # 执行处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)
        # 验证任务管理器没有被调用
        self.mock_task_manager.create_method_task.assert_not_called()

    def test_start_scheduler(self):
        """测试启动调度器"""
        self.mock_task_scheduler.is_running.return_value = False

        self.processor.start_scheduler()

        self.mock_task_scheduler.start.assert_called_once()

    def test_stop_scheduler(self):
        """测试停止调度器"""
        self.mock_task_scheduler.is_running.return_value = True

        self.processor.stop_scheduler()

        self.mock_task_scheduler.stop.assert_called_once()


if __name__ == "__main__":
    unittest.main()

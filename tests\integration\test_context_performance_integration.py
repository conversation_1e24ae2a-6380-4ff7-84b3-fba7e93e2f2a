#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Context模块性能集成测试

测试ApplicationContext在高负载和压力情况下的性能表现，
包括Bean创建性能、事件处理性能、并发访问性能等。
"""

import asyncio
import gc
import time
import unittest

import psutil

from miniboot.annotations import Component, EventListener, Service
from miniboot.context import DefaultApplicationContext
from miniboot.events import Event


# 性能测试用的事件类
class PerformanceTestEvent(Event):
    """性能测试事件"""

    def __init__(self, test_id: int, data: str):
        super().__init__()
        self.test_id = test_id
        self.data = data

    def get_event_name(self) -> str:
        """获取事件名称"""
        return "performance_test_event"


# 性能测试用的组件
@Component("performanceTestComponent")
class PerformanceTestComponent:
    """性能测试组件"""

    def __init__(self):
        self.creation_time = time.time()
        self.access_count = 0
        self.processed_events = []

    def process_data(self, data: str) -> str:
        """处理数据"""
        self.access_count += 1
        return f"processed_{data}_{self.access_count}"

    @EventListener
    def handle_performance_event(self, event: PerformanceTestEvent):
        """处理性能测试事件"""
        self.processed_events.append({"event_id": event.event_id, "test_id": event.test_id, "data": event.data, "processed_at": time.time()})


@Service("performanceTestService")
class PerformanceTestService:
    """性能测试服务"""

    def __init__(self):
        self.operation_count = 0
        self.total_processing_time = 0.0
        self.concurrent_operations = 0
        self.max_concurrent_operations = 0

    def heavy_operation(self, data: str) -> str:
        """重型操作"""
        start_time = time.time()
        self.concurrent_operations += 1
        self.max_concurrent_operations = max(self.max_concurrent_operations, self.concurrent_operations)

        try:
            # 模拟CPU密集型操作
            result = 0
            for i in range(10000):
                result += i * len(data)

            self.operation_count += 1
            processing_time = time.time() - start_time
            self.total_processing_time += processing_time

            return f"heavy_result_{result}_{self.operation_count}"
        finally:
            self.concurrent_operations -= 1

    def get_performance_stats(self) -> dict:
        """获取性能统计"""
        avg_time = self.total_processing_time / self.operation_count if self.operation_count > 0 else 0

        return {
            "operation_count": self.operation_count,
            "total_processing_time": self.total_processing_time,
            "average_processing_time": avg_time,
            "max_concurrent_operations": self.max_concurrent_operations,
        }


class ContextPerformanceIntegrationTestCase(unittest.IsolatedAsyncioTestCase):
    """Context模块性能集成测试类"""

    async def asyncSetUp(self):
        """异步设置测试环境"""
        # 创建应用上下文
        self.context = DefaultApplicationContext()

        # 手动注册测试Bean
        component = PerformanceTestComponent()
        service = PerformanceTestService()

        self.context.register_singleton("performanceTestComponent", component)
        self.context.register_singleton("performanceTestService", service)

        # 启动上下文以确保事件监听器被注册
        await self.context.start()

        # 手动注册事件监听器
        if hasattr(self.context, "_event_publisher") and self.context._event_publisher:
            self.context._event_publisher.register_listener(PerformanceTestEvent, component.handle_performance_event)

        # 记录初始内存使用
        self.initial_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

    async def asyncTearDown(self):
        """异步清理测试环境"""
        if self.context and self.context.is_running():
            await self.context.stop()

        # 强制垃圾回收
        gc.collect()

    async def test_context_startup_performance(self):
        """测试上下文启动性能"""
        # 上下文已经在setUp中启动，这里只验证状态
        self.assertTrue(self.context.is_running())
        print("上下文启动性能测试通过（已在setUp中启动）")

    async def test_bean_creation_performance(self):
        """测试Bean创建性能"""
        # 上下文已经在setUp中启动

        # 测试Bean获取性能
        start_time = time.time()

        # 多次获取Bean（应该使用缓存）
        for _ in range(1000):
            component = self.context.get_bean("performanceTestComponent")
            service = self.context.get_bean("performanceTestService")

            # 验证Bean实例
            self.assertIsInstance(component, PerformanceTestComponent)
            self.assertIsInstance(service, PerformanceTestService)

        bean_access_time = time.time() - start_time

        # 验证Bean访问性能（1000次访问应该很快）
        self.assertLess(bean_access_time, 1.0, f"Bean访问时间过长: {bean_access_time:.2f}秒")

        print(f"1000次Bean访问时间: {bean_access_time:.3f}秒")
        print(f"平均每次访问时间: {bean_access_time / 1000 * 1000:.3f}毫秒")

    async def test_concurrent_bean_access_performance(self):
        """测试并发Bean访问性能"""
        # 上下文已经在setUp中启动

        async def concurrent_bean_access(worker_id: int) -> float:
            """并发Bean访问"""
            start_time = time.time()

            for i in range(100):
                component = self.context.get_bean("performanceTestComponent")
                result = component.process_data(f"worker_{worker_id}_data_{i}")
                self.assertIn(f"worker_{worker_id}_data_{i}", result)

            return time.time() - start_time

        # 创建并发任务
        num_workers = 10
        tasks = [concurrent_bean_access(i) for i in range(num_workers)]

        start_time = time.time()
        worker_times = await asyncio.gather(*tasks)
        total_time = time.time() - start_time

        # 验证并发性能
        self.assertLess(total_time, 5.0, f"并发访问时间过长: {total_time:.2f}秒")

        # 验证所有worker都完成了工作
        self.assertEqual(len(worker_times), num_workers)

        # 获取组件并验证访问计数
        component = self.context.get_bean("performanceTestComponent")
        expected_access_count = num_workers * 100
        self.assertEqual(component.access_count, expected_access_count)

        print(f"并发访问总时间: {total_time:.3f}秒")
        print(f"平均worker时间: {sum(worker_times) / len(worker_times):.3f}秒")
        print(f"总访问次数: {component.access_count}")

    async def test_event_processing_performance(self):
        """测试事件处理性能"""
        # 上下文已经在setUp中启动

        # 获取组件
        component = self.context.get_bean("performanceTestComponent")
        initial_event_count = len(component.processed_events)

        # 批量发布事件
        num_events = 1000
        start_time = time.time()

        event_tasks = []
        for i in range(num_events):
            event = PerformanceTestEvent(i, f"performance_data_{i}")
            task = asyncio.create_task(self.context.publish_event_async(event))
            event_tasks.append(task)

        # 等待所有事件发布完成
        await asyncio.gather(*event_tasks)

        # 等待事件处理完成
        await asyncio.sleep(1.0)

        event_processing_time = time.time() - start_time

        # 验证事件处理性能
        self.assertLess(event_processing_time, 10.0, f"事件处理时间过长: {event_processing_time:.2f}秒")

        # 验证所有事件都被处理
        final_event_count = len(component.processed_events)
        processed_events = final_event_count - initial_event_count
        self.assertEqual(processed_events, num_events)

        print(f"处理{num_events}个事件用时: {event_processing_time:.3f}秒")
        print(f"平均每个事件处理时间: {event_processing_time / num_events * 1000:.3f}毫秒")

    async def test_heavy_operation_performance(self):
        """测试重型操作性能"""
        # 上下文已经在setUp中启动

        service = self.context.get_bean("performanceTestService")

        # 测试单个重型操作
        start_time = time.time()
        result = service.heavy_operation("test_data")
        single_operation_time = time.time() - start_time

        self.assertIn("heavy_result_", result)
        self.assertLess(single_operation_time, 1.0, f"单个重型操作时间过长: {single_operation_time:.2f}秒")

        # 测试并发重型操作
        async def concurrent_heavy_operation(operation_id: int):
            """并发重型操作"""
            return service.heavy_operation(f"concurrent_data_{operation_id}")

        num_operations = 20
        start_time = time.time()

        tasks = [concurrent_heavy_operation(i) for i in range(num_operations)]
        results = await asyncio.gather(*tasks)

        concurrent_operations_time = time.time() - start_time

        # 验证并发操作性能
        self.assertEqual(len(results), num_operations)
        self.assertLess(concurrent_operations_time, 10.0, f"并发重型操作时间过长: {concurrent_operations_time:.2f}秒")

        # 获取性能统计
        stats = service.get_performance_stats()

        print(f"单个重型操作时间: {single_operation_time:.3f}秒")
        print(f"并发{num_operations}个重型操作时间: {concurrent_operations_time:.3f}秒")
        print(f"平均操作时间: {stats['average_processing_time']:.3f}秒")
        print(f"最大并发操作数: {stats['max_concurrent_operations']}")

    async def test_memory_usage_performance(self):
        """测试内存使用性能"""
        # 上下文已经在setUp中启动

        # 记录启动后内存使用
        startup_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        # 执行大量操作
        component = self.context.get_bean("performanceTestComponent")
        service = self.context.get_bean("performanceTestService")

        # 大量Bean访问
        for i in range(5000):
            component.process_data(f"memory_test_{i}")
            if i % 100 == 0:
                service.heavy_operation(f"memory_heavy_{i}")

        # 大量事件处理
        for i in range(1000):
            event = PerformanceTestEvent(i, f"memory_event_{i}")
            await self.context.publish_event_async(event)

        # 等待处理完成
        await asyncio.sleep(2.0)

        # 记录操作后内存使用
        after_operations_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        # 强制垃圾回收
        gc.collect()
        await asyncio.sleep(1.0)

        # 记录垃圾回收后内存使用
        after_gc_memory = psutil.Process().memory_info().rss / 1024 / 1024  # MB

        # 计算内存增长
        memory_growth = after_operations_memory - startup_memory
        memory_after_gc_growth = after_gc_memory - startup_memory

        # 验证内存使用合理（不应该有严重的内存泄漏）
        self.assertLess(memory_growth, 100, f"内存增长过多: {memory_growth:.2f}MB")
        self.assertLess(memory_after_gc_growth, 50, f"垃圾回收后内存增长过多: {memory_after_gc_growth:.2f}MB")

        print(f"初始内存: {self.initial_memory:.2f}MB")
        print(f"启动后内存: {startup_memory:.2f}MB")
        print(f"操作后内存: {after_operations_memory:.2f}MB")
        print(f"垃圾回收后内存: {after_gc_memory:.2f}MB")
        print(f"内存增长: {memory_growth:.2f}MB")
        print(f"垃圾回收后增长: {memory_after_gc_growth:.2f}MB")

    async def test_context_shutdown_performance(self):
        """测试上下文关闭性能"""
        # 上下文已经在setUp中启动

        # 执行一些操作以创建状态
        component = self.context.get_bean("performanceTestComponent")
        for i in range(100):
            component.process_data(f"shutdown_test_{i}")

        # 测试关闭时间
        start_time = time.time()
        await self.context.stop()
        shutdown_time = time.time() - start_time

        # 验证关闭时间合理
        self.assertLess(shutdown_time, 5.0, f"关闭时间过长: {shutdown_time:.2f}秒")

        # 验证上下文已关闭
        self.assertFalse(self.context.is_running())

        print(f"上下文关闭时间: {shutdown_time:.3f}秒")


if __name__ == "__main__":
    unittest.main()

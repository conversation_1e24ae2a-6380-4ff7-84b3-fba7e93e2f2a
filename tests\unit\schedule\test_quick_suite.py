#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务模块快速测试套件 - 简化版本，快速执行
"""

import asyncio
import unittest

from miniboot.schedule import EnableScheduling  # 注解系统; 任务系统; 调度器; 配置系统; 配置属性
from miniboot.schedule import (
    JobStoreType,
    LambdaTask,
    MiniBootScheduler,
    ScheduleConfigurationError,
    Scheduled,
    ScheduledConfig,
    ScheduleException,
    ScheduleExecutionError,
    SchedulerConfigFactory,
    SchedulerProperties,
    SchedulerPropertiesBuilder,
    SchedulerState,
    SimpleTask,
    TaskFactory,
    TaskIdGenerator,
    TaskRegistry,
    TaskStatus,
    TaskType,
    create_scheduler_properties_from_config,
)


class QuickAnnotationsTest(unittest.TestCase):
    """快速注解测试"""

    def test_scheduled_decorator(self):
        """测试@Scheduled装饰器"""

        @Scheduled(fixed_rate="1s")
        def test_method():
            return "test"

        self.assertTrue(hasattr(test_method, "_scheduled_config"))
        config = test_method._scheduled_config
        self.assertEqual(config.fixed_rate, "1s")
        self.assertEqual(config.get_task_type(), TaskType.FIXED_RATE)

    def test_enable_scheduling_decorator(self):
        """测试@EnableScheduling装饰器"""

        @EnableScheduling
        class TestService:
            @Scheduled(fixed_rate="2s")
            def scheduled_method(self):
                return "scheduled"

        self.assertTrue(hasattr(TestService, "_scheduling_enabled"))
        service = TestService()
        self.assertTrue(hasattr(service.scheduled_method, "_scheduled_config"))


class QuickTasksTest(unittest.TestCase):
    """快速任务测试"""

    def test_task_id_generator(self):
        """测试任务ID生成器"""
        id1 = TaskIdGenerator.generate()
        id2 = TaskIdGenerator.generate()

        self.assertNotEqual(id1, id2)
        self.assertTrue(id1.startswith("task_"))
        self.assertTrue(id2.startswith("task_"))

    def test_scheduled_task_creation(self):
        """测试调度任务创建"""

        def test_func():
            return "result"

        config = ScheduledConfig(fixed_rate="1s")
        task = SimpleTask(test_func, config, "test_task", TaskType.FIXED_RATE)

        self.assertEqual(task.name, "test_task")
        self.assertEqual(task.task_type, TaskType.FIXED_RATE)
        self.assertEqual(task.status, TaskStatus.CREATED)
        self.assertFalse(task.is_async)

    def test_task_execution(self):
        """测试任务执行"""

        def test_func():
            return "execution_result"

        config = ScheduledConfig(fixed_rate="1s")
        task = SimpleTask(test_func, config, "test_task", TaskType.FIXED_RATE)

        result = asyncio.run(task.execute())
        self.assertEqual(result, "execution_result")

    def test_task_factory(self):
        """测试任务工厂"""

        def func():
            return "lambda_result"

        config = ScheduledConfig(fixed_rate="1s")

        task = TaskFactory.create_lambda_task(func, config, name="lambda_task")

        self.assertIsInstance(task, LambdaTask)
        self.assertEqual(task.name, "lambda_task")

    def test_task_registry(self):
        """测试任务注册表"""
        registry = TaskRegistry()

        config = ScheduledConfig(fixed_rate="1s")
        task = TaskFactory.create_lambda_task(lambda: "test", config, "test_task")

        registry.register(task)
        self.assertEqual(len(registry.get_all_tasks()), 1)

        retrieved_task = registry.get_task(task.task_id)
        self.assertEqual(retrieved_task, task)


class QuickSchedulerTest(unittest.TestCase):
    """快速调度器测试"""

    def setUp(self):
        """设置测试环境"""
        self.scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.shutdown()

    def test_scheduler_initialization(self):
        """测试调度器初始化"""
        self.assertEqual(self.scheduler.max_workers, 2)
        self.assertEqual(self.scheduler.state, SchedulerState.STOPPED)
        self.assertIsNotNone(self.scheduler.task_registry)

    def test_scheduler_lifecycle(self):
        """测试调度器生命周期"""
        # 初始状态
        self.assertFalse(self.scheduler.is_running())

        # 启动
        self.scheduler.start()
        self.assertTrue(self.scheduler.is_running())

        # 关闭
        self.scheduler.shutdown()
        self.assertFalse(self.scheduler.is_running())

    def test_task_registration(self):
        """测试任务注册"""

        def test_task():
            return "test_result"

        config = ScheduledConfig(fixed_rate="5s")  # 长间隔避免实际执行
        task_id = self.scheduler.register_task(test_task, config, "test_task")

        self.assertIsNotNone(task_id)
        self.assertEqual(len(self.scheduler.task_registry.get_all_tasks()), 1)

        # 注销任务
        success = self.scheduler.unregister_task(task_id)
        self.assertTrue(success)
        self.assertEqual(len(self.scheduler.task_registry.get_all_tasks()), 0)


class QuickConfigTest(unittest.TestCase):
    """快速配置测试"""

    def test_scheduler_properties_creation(self):
        """测试调度器属性创建"""
        properties = SchedulerProperties()

        self.assertIsNotNone(properties.concurrency)
        self.assertIsNotNone(properties.job_store)
        self.assertIsNotNone(properties.executors)
        self.assertEqual(properties.job_store.type, JobStoreType.MEMORY)

    def test_scheduler_properties_builder(self):
        """测试调度器属性构建器"""
        properties = SchedulerPropertiesBuilder().timezone("UTC").max_workers(5).coalesce(True).build()

        self.assertEqual(properties.timezone, "UTC")
        self.assertEqual(properties.concurrency.max_workers, 5)
        self.assertTrue(properties.concurrency.coalesce)

    def test_scheduler_config_factory(self):
        """测试调度器配置工厂"""
        # 内存配置
        memory_config = SchedulerConfigFactory.create_memory_config(max_workers=3)
        self.assertEqual(memory_config.job_store.type, JobStoreType.MEMORY)
        self.assertEqual(memory_config.concurrency.max_workers, 3)

        # 数据库配置
        db_config = SchedulerConfigFactory.create_database_config(url="sqlite:///test.db", max_workers=5)
        self.assertEqual(db_config.job_store.type, JobStoreType.DATABASE)
        self.assertEqual(db_config.job_store.url, "sqlite:///test.db")

    def test_config_from_yaml(self):
        """测试从YAML配置创建"""
        config_dict = {
            "miniboot": {
                "scheduler": {"enabled": True, "timezone": "Asia/Shanghai", "concurrency": {"max-workers": 8}, "job-store": {"type": "memory"}}
            }
        }

        properties = create_scheduler_properties_from_config(config_dict)
        self.assertEqual(properties.timezone, "Asia/Shanghai")
        self.assertEqual(properties.concurrency.max_workers, 8)
        self.assertEqual(properties.job_store.type, JobStoreType.MEMORY)


class QuickExceptionsTest(unittest.TestCase):
    """快速异常测试"""

    def test_schedule_exception(self):
        """测试调度异常"""
        error = ScheduleException("Test error")
        self.assertEqual(str(error), "Test error")
        self.assertEqual(error.message, "Test error")

    def test_configuration_error(self):
        """测试配置异常"""
        error = ScheduleConfigurationError("Invalid config")
        self.assertIsInstance(error, ScheduleException)
        self.assertEqual(error.message, "Invalid config")

    def test_execution_error(self):
        """测试执行异常"""
        error = ScheduleExecutionError("Execution failed", task_name="test_task")
        self.assertEqual(error.task_name, "test_task")
        self.assertIn("test_task", str(error))


class QuickIntegrationTest(unittest.TestCase):
    """快速集成测试"""

    def test_end_to_end_basic(self):
        """测试基础端到端功能"""

        @EnableScheduling
        class TestService:
            def __init__(self):
                self.call_count = 0

            @Scheduled(fixed_rate="10s")  # 长间隔避免实际执行
            def test_method(self):
                self.call_count += 1
                return f"call_{self.call_count}"

        service = TestService()
        scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)

        try:
            # 注册任务
            if scheduler.task_manager:
                task_id = scheduler.task_manager.create_from_scheduled_method(service.test_method, service)
                self.assertIsNotNone(task_id)

            # 启动调度器
            scheduler.start()
            self.assertTrue(scheduler.is_running())

            # 获取调度器信息
            info = scheduler.get_scheduler_info()
            self.assertIn("state", info)
            self.assertIn("max_workers", info)

        finally:
            scheduler.shutdown()

    def test_configuration_integration(self):
        """测试配置集成"""
        properties = SchedulerConfigFactory.create_memory_config(max_workers=3)
        scheduler = MiniBootScheduler(properties=properties, use_asyncio=False)

        try:
            self.assertEqual(scheduler.max_workers, 3)

            scheduler.start()
            self.assertTrue(scheduler.is_running())

        finally:
            scheduler.shutdown()


# 快速测试套件
def create_quick_test_suite():
    """创建快速测试套件"""
    suite = unittest.TestSuite()

    # 添加快速测试类
    test_classes = [QuickAnnotationsTest, QuickTasksTest, QuickSchedulerTest, QuickConfigTest, QuickExceptionsTest, QuickIntegrationTest]

    for test_class in test_classes:
        tests = unittest.TestLoader().loadTestsFromTestCase(test_class)
        suite.addTests(tests)

    return suite


if __name__ == "__main__":
    # 运行快速测试套件
    runner = unittest.TextTestRunner(verbosity=2)
    suite = create_quick_test_suite()
    result = runner.run(suite)

    # 显示测试结果
    print("\n=== 快速测试结果 ===")
    print(f"运行测试: {result.testsRun}")
    print(f"失败: {len(result.failures)}")
    print(f"错误: {len(result.errors)}")
    print(f"成功率: {((result.testsRun - len(result.failures) - len(result.errors)) / result.testsRun * 100):.1f}%")

#!/usr/bin/env python
"""
负载脱落器

在系统过载时主动丢弃请求,保护系统核心功能.

主要功能:
- 智能负载脱落策略
- 基于优先级的请求选择
- 多种脱落算法支持
- 动态脱落率调整
- 脱落统计和分析
"""

import time
import random
from enum import Enum
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass, field
from threading import Lock
from loguru import logger

from ..properties import BackpressureConfig


class SheddingStrategy(Enum):
    """脱落策略"""

    RANDOM = "random"  # 随机脱落
    PRIORITY = "priority"  # 基于优先级脱落
    ADAPTIVE = "adaptive"  # 自适应脱落
    CIRCUIT_BREAKER = "circuit_breaker"  # 熔断器模式脱落


class RequestPriority(Enum):
    """请求优先级"""

    LOW = 1
    NORMAL = 2
    HIGH = 3
    CRITICAL = 4


@dataclass
class SheddingConfig:
    """脱落配置"""

    strategy: SheddingStrategy = SheddingStrategy.ADAPTIVE
    base_shedding_rate: float = 0.1  # 基础脱落率 (0.0-1.0)
    max_shedding_rate: float = 0.8  # 最大脱落率
    priority_weights: Dict[RequestPriority, float] = field(
        default_factory=lambda: {RequestPriority.LOW: 0.8, RequestPriority.NORMAL: 0.5, RequestPriority.HIGH: 0.2, RequestPriority.CRITICAL: 0.0}
    )
    adaptive_window: int = 60  # 自适应窗口大小(秒)
    load_threshold: float = 0.8  # 负载阈值
    recovery_threshold: float = 0.6  # 恢复阈值


@dataclass
class RequestInfo:
    """请求信息"""

    request_id: str
    priority: RequestPriority = RequestPriority.NORMAL
    timestamp: float = field(default_factory=time.time)
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class SheddingMetrics:
    """脱落指标"""

    total_requests: int = 0
    shed_requests: int = 0
    accepted_requests: int = 0
    current_shedding_rate: float = 0.0
    shed_by_priority: Dict[RequestPriority, int] = field(default_factory=lambda: {priority: 0 for priority in RequestPriority})
    avg_system_load: float = 0.0
    last_shed_time: Optional[float] = None


class LoadSheddingError(Exception):
    """负载脱落异常"""

    def __init__(self, message: str, reason: str, priority: RequestPriority):
        super().__init__(message)
        self.reason = reason
        self.priority = priority


class LoadShedder:
    """负载脱落器

    根据系统负载情况智能脱落请求,保护系统稳定性.
    支持多种脱落策略和优先级管理.
    """

    def __init__(self, config: Optional[SheddingConfig] = None, backpressure_config: Optional[BackpressureConfig] = None):
        """初始化负载脱落器

        Args:
            config: 脱落配置
            backpressure_config: 背压控制配置
        """
        self.config = config or SheddingConfig()
        self.backpressure_config = backpressure_config

        # 状态管理
        self._is_enabled = True
        self._current_load = 0.0
        self._current_shedding_rate = self.config.base_shedding_rate

        # 指标统计
        self._metrics = SheddingMetrics()
        self._load_history: List[float] = []
        self._shed_history: List[Dict[str, Any]] = []

        # 自适应算法相关
        self._last_adjustment_time = time.time()
        self._load_samples: List[float] = []

        # 线程安全锁
        self._lock = Lock()

        # 回调函数
        self._shed_callbacks: List[Callable[[RequestInfo, str], None]] = []

        logger.info(f"LoadShedder initialized with strategy: {self.config.strategy.value}")

    def should_shed_request(self, request_info: RequestInfo, current_load: float = 0.0) -> bool:
        """判断是否应该脱落请求

        Args:
            request_info: 请求信息
            current_load: 当前系统负载 (0.0-1.0)

        Returns:
            是否应该脱落请求

        Raises:
            LoadSheddingError: 负载脱落异常
        """
        if not self._is_enabled:
            return False

        with self._lock:
            self._current_load = current_load
            self._metrics.total_requests += 1

            # 更新负载历史
            self._load_samples.append(current_load)
            if len(self._load_samples) > self.config.adaptive_window:
                self._load_samples.pop(0)

            # 根据策略决定是否脱落
            should_shed = False
            reason = ""

            if self.config.strategy == SheddingStrategy.RANDOM:
                should_shed, reason = self._random_shedding(request_info)
            elif self.config.strategy == SheddingStrategy.PRIORITY:
                should_shed, reason = self._priority_shedding(request_info, current_load)
            elif self.config.strategy == SheddingStrategy.ADAPTIVE:
                should_shed, reason = self._adaptive_shedding(request_info, current_load)
            elif self.config.strategy == SheddingStrategy.CIRCUIT_BREAKER:
                should_shed, reason = self._circuit_breaker_shedding(request_info, current_load)

            if should_shed:
                self._record_shed_request(request_info, reason)
                raise LoadSheddingError(f"Request {request_info.request_id} shed due to {reason}", reason, request_info.priority)
            else:
                self._record_accepted_request(request_info)
                return False

    def _random_shedding(self, request_info: RequestInfo) -> tuple[bool, str]:
        """随机脱落策略"""
        if random.random() < self._current_shedding_rate:
            return True, "random_shedding"
        return False, ""

    def _priority_shedding(self, request_info: RequestInfo, current_load: float) -> tuple[bool, str]:
        """基于优先级的脱落策略"""
        if current_load < self.config.load_threshold:
            return False, ""

        priority_weight = self.config.priority_weights.get(request_info.priority, 0.5)
        adjusted_rate = self._current_shedding_rate * priority_weight

        if random.random() < adjusted_rate:
            return True, f"priority_shedding_{request_info.priority.name.lower()}"
        return False, ""

    def _adaptive_shedding(self, request_info: RequestInfo, current_load: float) -> tuple[bool, str]:
        """自适应脱落策略"""
        # 更新脱落率
        self._update_adaptive_shedding_rate(current_load)

        # 结合优先级进行脱落决策
        if current_load < self.config.recovery_threshold:
            return False, ""

        priority_weight = self.config.priority_weights.get(request_info.priority, 0.5)

        # 负载越高,脱落率越高
        load_factor = min(1.0, (current_load - self.config.recovery_threshold) / (1.0 - self.config.recovery_threshold))

        effective_rate = self._current_shedding_rate * load_factor * priority_weight

        if random.random() < effective_rate:
            return True, f"adaptive_shedding_load_{current_load:.2f}"
        return False, ""

    def _circuit_breaker_shedding(self, request_info: RequestInfo, current_load: float) -> tuple[bool, str]:
        """熔断器模式脱落策略"""
        # 如果负载超过阈值,进入熔断模式
        if current_load > self.config.load_threshold:
            # 只允许高优先级和关键请求通过
            if request_info.priority in [RequestPriority.HIGH, RequestPriority.CRITICAL]:
                return False, ""
            else:
                return True, f"circuit_breaker_load_{current_load:.2f}"

        return False, ""

    def _update_adaptive_shedding_rate(self, current_load: float) -> None:
        """更新自适应脱落率"""
        current_time = time.time()

        # 每10秒调整一次
        if current_time - self._last_adjustment_time < 10:
            return

        self._last_adjustment_time = current_time

        if not self._load_samples:
            return

        # 计算平均负载
        avg_load = sum(self._load_samples) / len(self._load_samples)
        self._metrics.avg_system_load = avg_load

        # 根据负载调整脱落率
        if avg_load > self.config.load_threshold:
            # 负载过高,增加脱落率
            increase_factor = (avg_load - self.config.load_threshold) / (1.0 - self.config.load_threshold)
            self._current_shedding_rate = min(self.config.max_shedding_rate, self.config.base_shedding_rate + increase_factor * 0.3)
        elif avg_load < self.config.recovery_threshold:
            # 负载较低,减少脱落率
            self._current_shedding_rate = max(0.0, self._current_shedding_rate - 0.05)

        self._metrics.current_shedding_rate = self._current_shedding_rate

        logger.debug(f"Updated shedding rate to {self._current_shedding_rate:.3f} based on avg load {avg_load:.3f}")

    def _record_shed_request(self, request_info: RequestInfo, reason: str) -> None:
        """记录被脱落的请求"""
        self._metrics.shed_requests += 1
        self._metrics.shed_by_priority[request_info.priority] += 1
        self._metrics.last_shed_time = time.time()

        # 记录脱落历史
        shed_event = {
            "timestamp": time.time(),
            "request_id": request_info.request_id,
            "priority": request_info.priority.name,
            "reason": reason,
            "system_load": self._current_load,
            "shedding_rate": self._current_shedding_rate,
        }

        self._shed_history.append(shed_event)
        if len(self._shed_history) > 1000:  # 保留最近1000个事件
            self._shed_history.pop(0)

        # 通知回调
        for callback in self._shed_callbacks:
            try:
                callback(request_info, reason)
            except Exception as e:
                logger.error(f"Error in shed callback: {e}")

        logger.debug(f"Shed request {request_info.request_id}, reason: {reason}, priority: {request_info.priority.name}")

    def _record_accepted_request(self, request_info: RequestInfo) -> None:
        """记录被接受的请求"""
        self._metrics.accepted_requests += 1

    def get_metrics(self) -> SheddingMetrics:
        """获取脱落指标"""
        with self._lock:
            return SheddingMetrics(**self._metrics.__dict__)

    def get_shedding_rate(self) -> float:
        """获取当前脱落率"""
        return self._current_shedding_rate

    def set_shedding_rate(self, rate: float) -> None:
        """设置脱落率

        Args:
            rate: 脱落率 (0.0-1.0)
        """
        if 0.0 <= rate <= 1.0:
            with self._lock:
                self._current_shedding_rate = rate
                self._metrics.current_shedding_rate = rate
            logger.info(f"Set shedding rate to {rate:.3f}")
        else:
            logger.error(f"Invalid shedding rate: {rate}, must be between 0.0 and 1.0")

    def update_config(self, new_config: SheddingConfig) -> None:
        """更新配置"""
        with self._lock:
            self.config = new_config
            self._current_shedding_rate = new_config.base_shedding_rate
            self._metrics.current_shedding_rate = self._current_shedding_rate

        logger.info(f"Updated LoadShedder config, strategy: {new_config.strategy.value}")

    def enable(self) -> None:
        """启用负载脱落"""
        self._is_enabled = True
        logger.info("LoadShedder enabled")

    def disable(self) -> None:
        """禁用负载脱落"""
        self._is_enabled = False
        logger.info("LoadShedder disabled")

    def is_enabled(self) -> bool:
        """检查是否启用"""
        return self._is_enabled

    def add_shed_callback(self, callback: Callable[[RequestInfo, str], None]) -> None:
        """添加脱落回调"""
        self._shed_callbacks.append(callback)

    def remove_shed_callback(self, callback: Callable[[RequestInfo, str], None]) -> None:
        """移除脱落回调"""
        if callback in self._shed_callbacks:
            self._shed_callbacks.remove(callback)

    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        metrics = self.get_metrics()

        shed_rate = 0.0
        if metrics.total_requests > 0:
            shed_rate = (metrics.shed_requests / metrics.total_requests) * 100

        priority_stats = {}
        for priority, count in metrics.shed_by_priority.items():
            priority_stats[priority.name.lower()] = count

        return {
            "enabled": self._is_enabled,
            "strategy": self.config.strategy.value,
            "current_shedding_rate": round(metrics.current_shedding_rate, 3),
            "total_requests": metrics.total_requests,
            "shed_requests": metrics.shed_requests,
            "accepted_requests": metrics.accepted_requests,
            "shed_rate_percentage": round(shed_rate, 2),
            "avg_system_load": round(metrics.avg_system_load, 3),
            "shed_by_priority": priority_stats,
            "last_shed_ago": (round(time.time() - metrics.last_shed_time, 1) if metrics.last_shed_time else None),
        }

    def get_recent_shed_events(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的脱落事件"""
        with self._lock:
            return self._shed_history[-limit:] if self._shed_history else []

    def reset_statistics(self) -> None:
        """重置统计信息"""
        with self._lock:
            self._metrics = SheddingMetrics()
            self._load_history.clear()
            self._shed_history.clear()
            self._load_samples.clear()
            self._current_shedding_rate = self.config.base_shedding_rate

        logger.info("LoadShedder statistics reset")

    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        stats = self.get_statistics()

        return {
            "enabled": stats["enabled"],
            "strategy": stats["strategy"],
            "shedding_rate": stats["current_shedding_rate"],
            "total_requests": stats["total_requests"],
            "shed_percentage": stats["shed_rate_percentage"],
            "system_load": stats["avg_system_load"],
            "is_healthy": stats["shed_rate_percentage"] < 20 and stats["avg_system_load"] < 0.8,
        }

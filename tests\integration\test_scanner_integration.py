#!/usr/bin/env python
"""
* @author: cz
* @description: 组件扫描器集成测试

测试组件扫描器在实际环境中的扫描功能。
"""

import unittest

from miniboot.annotations import ComponentScanner, ScanFilter


class TestScannerIntegration(unittest.TestCase):
    """组件扫描器集成测试类"""

    def test_scan_miniboot_annotations(self):
        """测试扫描miniboot.annotations包"""
        scanner = ComponentScanner()

        # 扫描miniboot.annotations包
        result = scanner.scan("miniboot.annotations")

        # 验证扫描结果
        self.assertGreater(len(result.scanned_modules), 0)
        print(f"扫描到 {len(result.scanned_modules)} 个模块")

        # 验证没有扫描错误
        if result.scan_errors:
            print("扫描错误:")
            for error in result.scan_errors:
                print(f"  - {error}")

        # 输出扫描统计
        print(f"组件总数: {result.get_total_components()}")
        print(f"方法总数: {result.get_total_methods()}")
        print(f"组件: {len(result.components)}")
        print(f"配置类: {len(result.configurations)}")
        print(f"Bean方法: {len(result.bean_methods)}")
        print(f"自动装配字段: {len(result.autowired_fields)}")
        print(f"自动装配方法: {len(result.autowired_methods)}")
        print(f"生命周期方法: {len(result.lifecycle_methods)}")
        print(f"异步方法: {len(result.async_methods)}")
        print(f"调度方法: {len(result.scheduled_methods)}")
        print(f"事件监听器: {len(result.event_listeners)}")

    def test_scan_with_filter(self):
        """测试使用过滤器扫描"""
        scanner = ComponentScanner()

        # 创建过滤器，只包含特定模块
        scan_filter = ScanFilter()
        scan_filter.add_include_pattern("core")
        scan_filter.add_exclude_pattern("test")

        scanner.set_filter(scan_filter)

        # 扫描miniboot.annotations包
        result = scanner.scan("miniboot.annotations")

        # 验证过滤器生效
        print(f"使用过滤器扫描到 {len(result.scanned_modules)} 个模块")

        # 验证模块名称包含core
        for module_name in result.scanned_modules:
            if "core" not in module_name:
                print(f"警告: 模块 {module_name} 不包含 'core'")

    def test_scan_packages_function(self):
        """测试scan_packages便捷函数"""
        # 使用ComponentScanner扫描
        scanner = ComponentScanner()
        result = scanner.scan("miniboot.annotations")

        # 验证扫描结果
        self.assertGreater(len(result.scanned_modules), 0)
        print(f"ComponentScanner扫描到 {len(result.scanned_modules)} 个模块")

    def test_scan_multiple_packages(self):
        """测试扫描多个包"""
        scanner = ComponentScanner()

        # 扫描多个包（这里只扫描一个包的不同子模块）
        packages = ["miniboot.annotations.core", "miniboot.annotations.injection"]
        result = scanner.scan(packages)

        # 验证扫描结果
        print(f"多包扫描到 {len(result.scanned_modules)} 个模块")

        # 验证扫描的模块
        for module_name in result.scanned_modules:
            print(f"  扫描模块: {module_name}")

    def test_scanner_cache(self):
        """测试扫描器缓存功能"""
        scanner = ComponentScanner()

        # 第一次扫描
        result1 = scanner.scan("miniboot.annotations.core")

        # 第二次扫描（应该使用缓存）
        result2 = scanner.scan("miniboot.annotations.core")

        # 验证结果相同（使用了缓存）
        self.assertEqual(len(result1.scanned_modules), len(result2.scanned_modules))
        print("缓存功能正常工作")

        # 清空缓存后再次扫描
        scanner.clear_cache()
        result3 = scanner.scan("miniboot.annotations.core")

        # 验证结果仍然相同
        self.assertEqual(len(result1.scanned_modules), len(result3.scanned_modules))
        print("清空缓存后重新扫描正常")

    def test_scan_error_handling(self):
        """测试扫描错误处理"""
        scanner = ComponentScanner()

        # 扫描不存在的包
        result = scanner.scan("nonexistent.package")

        # 验证有错误记录
        self.assertGreater(len(result.scan_errors), 0)
        print(f"错误处理测试: 记录了 {len(result.scan_errors)} 个错误")

        for error in result.scan_errors:
            print(f"  错误: {error}")


if __name__ == "__main__":
    unittest.main(verbosity=2)

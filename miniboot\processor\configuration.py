#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 配置属性处理器 - 实现@ConfigurationProperties注解的批量配置属性绑定

配置属性处理器负责处理@ConfigurationProperties注解标记的类,
实现批量配置属性绑定功能.支持前缀匹配、嵌套对象和类型转换.
"""

import inspect
from typing import Any, Optional, get_args, get_origin, get_type_hints

from loguru import logger

from ..annotations.metadata import ConfigurationPropertiesMetadata
from ..annotations.validation import ConfigurationValidator, ValidationError
# 环境配置模块是必需依赖
from ..env.bind import Binder
from ..env.convert import DefaultConversionService
from ..env.environment import StandardEnvironment
from ..errors import BeanCreationError
from ..errors import ProcessorExecutionError as BeanProcessingError
from .base import BeanPostProcessor, ProcessorOrder


def is_configuration_properties(obj: Any) -> bool:
    """检查对象是否有@ConfigurationProperties注解"""
    return hasattr(obj, "__configuration_properties_metadata__") and obj.__configuration_properties_metadata__


class ConfigurationPropertiesProcessor(BeanPostProcessor):
    """
    @ConfigurationProperties注解处理器

    负责处理@ConfigurationProperties注解的批量配置属性绑定,支持:
    - 前缀匹配:根据prefix匹配配置属性
    - 批量绑定:将多个配置属性绑定到Bean的字段
    - 嵌套对象:支持复杂对象的嵌套绑定
    - 类型转换:自动转换配置值到目标类型
    - 验证机制:支持配置验证和错误处理

    处理器在Bean初始化前执行,确保配置属性在Bean使用前完成绑定.

    Example:
        # 配置属性绑定
        @ConfigurationProperties(prefix="database")
        class DatabaseConfig:
            host: str = "localhost"
            port: int = 3306
            username: str = ""
            password: str = ""
    """

    def __init__(self, environment=None):
        """
        初始化配置属性处理器

        Args:
            environment: 环境配置实例,用于解析配置属性
        """
        self._environment = environment or StandardEnvironment()
        self._binder = Binder(self._environment) if self._environment else None
        self._conversion_service = DefaultConversionService()
        self._validator = ConfigurationValidator()
        self._processed_beans: set[str] = set()
        self._binding_cache: dict[type, dict[str, Any]] = {}
        self._nested_objects_cache: dict[type, Any] = {}

    def set_environment(self, environment) -> None:
        """
        设置环境配置

        Args:
            environment: 环境配置实例
        """
        self._environment = environment
        if environment:
            self._binder = Binder(environment)

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """
        在Bean初始化前处理@ConfigurationProperties注解

        扫描Bean类中的@ConfigurationProperties注解,执行配置属性绑定.

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            处理后的Bean实例

        Raises:
            BeanProcessingError: 当配置属性绑定失败时
        """
        if bean is None or bean_name in self._processed_beans:
            return bean

        try:
            # 检查是否有@ConfigurationProperties注解
            if self._has_configuration_properties_annotation(bean.__class__):
                # 执行配置属性绑定
                self._bind_configuration_properties(bean, bean_name)

                # 标记为已处理
                self._processed_beans.add(bean_name)

                logger.debug(f"Completed configuration properties binding for bean: {bean_name}")

            return bean

        except Exception as e:
            raise BeanProcessingError(
                f"Failed to process @ConfigurationProperties annotations for bean '{bean_name}'",
                bean_name=bean_name,
                processor_name=self.__class__.__name__,
                cause=e,
            ) from e

    def post_process_after_initialization(self, bean: Any, _bean_name: str) -> Any:
        """
        在Bean初始化后处理(配置属性处理器不需要后处理)

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            原始Bean实例
        """
        return bean

    def get_order(self) -> int:
        """
        获取处理器执行顺序

        配置属性处理器需要在自动装配之前执行,
        确保配置属性在依赖注入之前完成绑定.

        Returns:
            执行顺序(数字越小优先级越高)
        """
        return ProcessorOrder.CONFIGURATION_PROCESSOR

    def supports(self, bean: Any, _bean_name: str) -> bool:
        """
        检查是否支持处理指定的Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称

        Returns:
            True表示支持处理此Bean
        """
        if bean is None:
            return False

        # 检查Bean类是否有@ConfigurationProperties注解
        return self._has_configuration_properties_annotation(bean.__class__)

    def _bind_configuration_properties(self, bean: Any, bean_name: str) -> None:
        """
        绑定配置属性到Bean

        Args:
            bean: Bean实例
            bean_name: Bean名称
        """
        bean_class = bean.__class__

        # 获取@ConfigurationProperties元数据
        metadata = self._get_configuration_properties_metadata(bean_class)
        if not metadata:
            return

        if self._binder is None:
            if metadata.prefix:  # 只有在有前缀时才报错
                raise BeanCreationError("Environment not available for configuration properties binding", bean_name)
            return

        try:
            # 获取配置前缀
            prefix = metadata.prefix

            # 获取Bean的字段信息
            field_info = self._get_bean_fields(bean_class)

            # 绑定每个字段
            for field_name, field_type, default_value in field_info:
                try:
                    # 构建配置键
                    config_key = f"{prefix}.{field_name}" if prefix else field_name

                    # 处理不同类型的绑定
                    bound_value = self._bind_field_value(config_key, field_name, field_type, default_value, bean_name)

                    if bound_value is not None:
                        # 设置字段值
                        setattr(bean, field_name, bound_value)
                        logger.debug(f"Bound configuration property '{config_key}' to field '{field_name}' for bean '{bean_name}'")

                except Exception as e:
                    if not metadata.ignore_invalid_fields:
                        raise BeanCreationError(f"Failed to bind configuration property '{config_key}' for bean '{bean_name}'", bean_name, e) from e
                    else:
                        logger.warning(f"Ignored invalid configuration property '{config_key}': {e}")

            # 执行配置验证
            if metadata.validate:
                try:
                    self._validator.validate_object(bean)
                    logger.debug(f"Configuration validation passed for bean '{bean_name}'")
                except ValidationError as e:
                    raise BeanCreationError(f"Configuration validation failed for bean '{bean_name}': {e}", bean_name, e) from e

        except Exception as e:
            raise BeanCreationError(f"Failed to bind configuration properties for bean '{bean_name}'", bean_name, e) from e

    def _get_bean_fields(self, bean_class: type) -> list[tuple[str, type, Any]]:
        """
        获取Bean类的字段信息

        Args:
            bean_class: Bean类

        Returns:
            字段信息列表:[(field_name, field_type, default_value), ...]
        """
        if bean_class in self._binding_cache:
            cached_fields = self._binding_cache[bean_class]
            return [(name, info["type"], info["default"]) for name, info in cached_fields.items()]

        fields = []

        # 获取类型注解
        type_hints = get_type_hints(bean_class)

        # 获取字段默认值
        for field_name, field_type in type_hints.items():
            if field_name.startswith("_"):
                continue

            # 获取默认值
            default_value = getattr(bean_class, field_name, None)

            fields.append((field_name, field_type, default_value))

        # 缓存字段信息
        self._binding_cache[bean_class] = {name: {"type": field_type, "default": default} for name, field_type, default in fields}

        return fields

    def _has_configuration_properties_annotation(self, bean_class: type) -> bool:
        """
        检查类是否有@ConfigurationProperties注解

        Args:
            bean_class: Bean类

        Returns:
            True表示有@ConfigurationProperties注解
        """
        return hasattr(bean_class, "__configuration_properties_metadata__")

    def _get_configuration_properties_metadata(self, bean_class: type) -> Optional[ConfigurationPropertiesMetadata]:
        """
        获取@ConfigurationProperties元数据

        Args:
            bean_class: Bean类

        Returns:
            配置属性元数据
        """
        return getattr(bean_class, "__configuration_properties_metadata__", None)

    def _bind_field_value(self, config_key: str, field_name: str, field_type: type, _default_value: Any, bean_name: str) -> Any:
        """
        绑定字段值,支持嵌套对象和集合类型

        Args:
            config_key: 配置键
            field_name: 字段名称
            field_type: 字段类型
            default_value: 默认值
            bean_name: Bean名称

        Returns:
            绑定的值
        """
        # 检查是否为嵌套配置对象
        if self._is_configuration_properties_class(field_type):
            return self._bind_nested_configuration(config_key, field_type, bean_name)

        # 检查是否为集合类型
        if self._is_collection_type(field_type):
            return self._bind_collection_value(config_key, field_type, bean_name)

        # 普通字段绑定
        config_value = self._environment.get_property(config_key)
        if config_value is not None:
            return self._convert_value(config_value, field_type, field_name, bean_name)

        # 如果没有配置值,返回None(保持原有默认值)
        return None

    def _is_configuration_properties_class(self, field_type: type) -> bool:
        """
        检查类型是否为配置属性类

        Args:
            field_type: 字段类型

        Returns:
            True表示是配置属性类
        """
        return inspect.isclass(field_type) and hasattr(field_type, "__configuration_properties_metadata__")

    def _is_collection_type(self, field_type: type) -> bool:
        """
        检查类型是否为集合类型

        Args:
            field_type: 字段类型

        Returns:
            True表示是集合类型
        """
        origin = get_origin(field_type)
        return origin in (list, tuple, set, dict)

    def _bind_nested_configuration(self, config_key: str, nested_type: type, bean_name: str) -> Any:
        """
        绑定嵌套配置对象

        Args:
            config_key: 配置键前缀
            nested_type: 嵌套对象类型
            bean_name: Bean名称

        Returns:
            绑定的嵌套对象
        """
        # 检查缓存
        if nested_type in self._nested_objects_cache:
            return self._nested_objects_cache[nested_type]

        try:
            # 创建嵌套对象实例
            nested_instance = nested_type()

            # 递归绑定嵌套对象的属性
            nested_metadata = self._get_configuration_properties_metadata(nested_type)
            if nested_metadata:
                # 使用配置键作为前缀
                nested_prefix = config_key

                # 获取嵌套对象的字段信息
                nested_fields = self._get_bean_fields(nested_type)

                # 绑定嵌套对象的每个字段
                for nested_field_name, nested_field_type, nested_default_value in nested_fields:
                    nested_config_key = f"{nested_prefix}.{nested_field_name}"

                    nested_bound_value = self._bind_field_value(
                        nested_config_key, nested_field_name, nested_field_type, nested_default_value, bean_name
                    )

                    if nested_bound_value is not None:
                        setattr(nested_instance, nested_field_name, nested_bound_value)
                        logger.debug(f"Bound nested property '{nested_config_key}' to field '{nested_field_name}' for bean '{bean_name}'")

            # 缓存嵌套对象
            self._nested_objects_cache[nested_type] = nested_instance
            return nested_instance

        except Exception as e:
            logger.warning(f"Failed to bind nested configuration object '{nested_type.__name__}': {e}")
            return None

    def _bind_collection_value(self, config_key: str, collection_type: type, bean_name: str) -> Any:
        """
        绑定集合类型值

        Args:
            config_key: 配置键
            collection_type: 集合类型
            bean_name: Bean名称

        Returns:
            绑定的集合值
        """
        origin = get_origin(collection_type)
        args = get_args(collection_type)

        if origin is list:
            return self._bind_list_value(config_key, args[0] if args else str, bean_name)
        elif origin is dict:
            key_type = args[0] if args else str
            value_type = args[1] if len(args) > 1 else str
            return self._bind_dict_value(config_key, key_type, value_type, bean_name)
        elif origin in (tuple, set):
            element_type = args[0] if args else str
            values = self._bind_list_value(config_key, element_type, bean_name)
            return origin(values) if values else None

        return None

    def _bind_list_value(self, config_key: str, element_type: type, _bean_name: str) -> list:
        """
        绑定列表类型值

        Args:
            config_key: 配置键
            element_type: 元素类型
            bean_name: Bean名称

        Returns:
            绑定的列表值
        """
        # 尝试获取逗号分隔的值
        config_value = self._environment.get_property(config_key)
        if config_value:
            if isinstance(config_value, str):
                # 分割字符串
                values = [v.strip() for v in config_value.split(",")]
                # 转换每个元素
                return [self._conversion_service.convert(v, element_type) for v in values if v]
            elif isinstance(config_value, list):
                # 直接转换列表
                return [self._conversion_service.convert(v, element_type) for v in config_value]

        # 尝试获取索引形式的值 (key[0], key[1], ...)
        result = []
        index = 0
        while True:
            indexed_key = f"{config_key}[{index}]"
            indexed_value = self._environment.get_property(indexed_key)
            if indexed_value is None:
                break
            result.append(self._conversion_service.convert(indexed_value, element_type))
            index += 1

        return result if result else None

    def _bind_dict_value(self, config_key: str, key_type: type, value_type: type, _bean_name: str) -> dict:
        """
        绑定字典类型值

        Args:
            config_key: 配置键
            key_type: 键类型
            value_type: 值类型
            bean_name: Bean名称

        Returns:
            绑定的字典值
        """
        result = {}

        # 获取所有以config_key为前缀的属性
        prefix = f"{config_key}."

        # 遍历所有属性源查找匹配的属性
        for property_source in self._environment.get_property_sources():
            if hasattr(property_source, "_properties"):
                for prop_key, prop_value in property_source._properties.items():
                    if prop_key.startswith(prefix):
                        # 提取字典键
                        dict_key_str = prop_key[len(prefix) :]
                        if "." not in dict_key_str:  # 只处理直接子键
                            try:
                                dict_key = self._conversion_service.convert(dict_key_str, key_type)
                                dict_value = self._conversion_service.convert(prop_value, value_type)
                                result[dict_key] = dict_value
                            except Exception as e:
                                logger.warning(f"Failed to convert dict entry '{prop_key}': {e}")

        return result if result else None

    def _convert_value(self, value: Any, target_type: type, field_name: str, bean_name: str) -> Any:
        """
        转换值到目标类型

        Args:
            value: 原始值
            target_type: 目标类型
            field_name: 字段名称
            bean_name: Bean名称

        Returns:
            转换后的值
        """
        if value is None or isinstance(value, target_type):
            return value

        try:
            # 使用转换服务
            if self._conversion_service and self._conversion_service.can_convert(type(value), target_type):
                return self._conversion_service.convert(value, target_type)

            # 回退到直接转换
            return target_type(value)

        except Exception as e:
            raise BeanCreationError(
                f"Failed to convert configuration value '{value}' to type {target_type.__name__} for field '{field_name}'", bean_name, e
            ) from e

    def get_processed_beans_count(self) -> int:
        """
        获取已处理的Bean数量

        Returns:
            已处理的Bean数量
        """
        return len(self._processed_beans)

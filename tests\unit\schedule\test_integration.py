#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 定时任务模块集成测试
"""

import asyncio
import logging
import threading
import time
import unittest

from miniboot.schedule import EnableScheduling, MiniBootScheduler, Scheduled, ScheduledConfig, SchedulerConfigFactory


@EnableScheduling
class IntegrationTestService:
    """集成测试服务"""

    def __init__(self):
        self.execution_count = 0
        self.results = []
        self.lock = threading.Lock()

    @Scheduled(fixed_rate="1s")
    def regular_task(self):
        """常规任务"""
        with self.lock:
            self.execution_count += 1
            result = f"regular_{self.execution_count}"
            self.results.append(result)
            return result

    @Scheduled(fixed_delay="2s", initial_delay="0.5s")
    def delayed_task(self):
        """延迟任务"""
        with self.lock:
            self.execution_count += 1
            result = f"delayed_{self.execution_count}"
            self.results.append(result)
            time.sleep(0.1)  # 模拟处理时间
            return result

    @Scheduled(cron="*/2 * * * * *")  # 每2秒执行一次（如果支持秒级cron）
    def cron_task(self):
        """Cron任务"""
        with self.lock:
            self.execution_count += 1
            result = f"cron_{self.execution_count}"
            self.results.append(result)
            return result

    @Scheduled(fixed_rate="1.5s")
    async def async_task(self):
        """异步任务"""
        with self.lock:
            self.execution_count += 1
            result = f"async_{self.execution_count}"
            self.results.append(result)

        await asyncio.sleep(0.05)  # 模拟异步处理
        return result

    def error_task(self):
        """错误任务"""
        with self.lock:
            self.execution_count += 1
        raise Exception("Intentional test error")

    def get_results(self):
        """获取结果"""
        with self.lock:
            return self.results.copy()

    def get_execution_count(self):
        """获取执行次数"""
        with self.lock:
            return self.execution_count

    def reset(self):
        """重置状态"""
        with self.lock:
            self.execution_count = 0
            self.results.clear()


class TestSchedulerIntegration(unittest.TestCase):
    """测试调度器集成"""

    def setUp(self):
        """设置测试环境"""
        # 配置APScheduler日志级别为CRITICAL，抑制预期错误的日志输出
        logging.getLogger("apscheduler.executors.default").setLevel(logging.CRITICAL)
        logging.getLogger("apscheduler").setLevel(logging.CRITICAL)

        self.service = IntegrationTestService()
        self.scheduler = MiniBootScheduler(max_workers=3, use_asyncio=False)

    def tearDown(self):
        """清理测试环境"""
        if self.scheduler.is_running():
            self.scheduler.shutdown()

    def test_basic_scheduler_integration(self):
        """测试基础调度器集成"""
        # 注册任务
        task_id = self.scheduler.register_task(self.service.regular_task, ScheduledConfig(fixed_rate="2s"), "integration_test_task")

        self.assertIsNotNone(task_id)

        # 启动调度器
        self.scheduler.start()
        self.assertTrue(self.scheduler.is_running())

        # 运行一段时间
        time.sleep(3)

        # 检查执行结果
        execution_count = self.service.get_execution_count()
        self.assertGreater(execution_count, 0)

        # 停止调度器
        self.scheduler.shutdown()
        self.assertFalse(self.scheduler.is_running())

    def test_multiple_task_types_integration(self):
        """测试多种任务类型集成"""
        # 注册不同类型的任务
        regular_id = self.scheduler.register_task(self.service.regular_task, ScheduledConfig(fixed_rate="1s"), "regular_task")

        delayed_id = self.scheduler.register_task(
            self.service.delayed_task, ScheduledConfig(fixed_delay="1.5s", initial_delay="0.5s"), "delayed_task"
        )

        async_id = self.scheduler.register_task(self.service.async_task, ScheduledConfig(fixed_rate="2s"), "async_task")

        self.assertIsNotNone(regular_id)
        self.assertIsNotNone(delayed_id)
        self.assertIsNotNone(async_id)

        # 启动调度器并运行
        self.scheduler.start()
        time.sleep(4)
        self.scheduler.shutdown()

        # 检查所有任务都有执行
        results = self.service.get_results()
        self.assertGreater(len(results), 0)

        # 检查不同类型的任务结果
        regular_results = [r for r in results if r.startswith("regular_")]
        delayed_results = [r for r in results if r.startswith("delayed_")]
        async_results = [r for r in results if r.startswith("async_")]

        self.assertGreater(len(regular_results), 0)
        self.assertGreater(len(delayed_results), 0)
        self.assertGreater(len(async_results), 0)

    def test_task_manager_integration(self):
        """测试任务管理器集成"""
        if not self.scheduler.task_manager:
            self.skipTest("TaskManager not available")

        # 使用任务管理器添加任务
        task_id = self.scheduler.task_manager.create_method_task(
            method=self.service.regular_task, instance=self.service, config=ScheduledConfig(fixed_rate="1s"), name="task_manager_test", max_retries=2
        )

        self.assertIsNotNone(task_id)

        # 启动调度器
        self.scheduler.start()
        time.sleep(2)

        # 检查任务管理器统计
        summary = self.scheduler.get_task_manager_summary()
        self.assertGreater(summary["total_managed_tasks"], 0)
        self.assertGreater(summary["total_executions"], 0)

        # 检查任务指标
        metrics = self.scheduler.get_task_metrics(task_id)
        self.assertIsNotNone(metrics)
        self.assertGreater(metrics["total_executions"], 0)

        self.scheduler.shutdown()

    def test_configuration_integration(self):
        """测试配置系统集成"""
        # 使用配置对象创建调度器
        config = SchedulerConfigFactory.create_memory_config(max_workers=2)

        scheduler = MiniBootScheduler(properties=config, use_asyncio=False)

        try:
            # 验证配置应用
            self.assertEqual(scheduler.max_workers, 2)

            # 注册任务
            scheduler.register_task(self.service.regular_task, ScheduledConfig(fixed_rate="1s"), "config_test_task")

            # 启动并测试
            scheduler.start()
            time.sleep(2)
            scheduler.shutdown()

            # 验证执行
            self.assertGreater(self.service.get_execution_count(), 0)

        finally:
            if scheduler.is_running():
                scheduler.shutdown()

    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 注册会出错的任务 - 预期会抛出异常并被APScheduler记录
        # 这些异常日志是预期的行为，不会导致测试失败
        self.scheduler.register_task(self.service.error_task, ScheduledConfig(fixed_rate="1s"), "error_task")

        # 注册正常任务
        self.scheduler.register_task(self.service.regular_task, ScheduledConfig(fixed_rate="1s"), "normal_task")

        # 启动调度器
        self.scheduler.start()

        # 等待任务执行
        time.sleep(3)

        # 手动触发一次错误任务，确保统计信息更新
        # 注意：这里会看到异常日志，这是预期的行为
        try:
            self.service.error_task()
        except Exception:
            # 手动更新统计信息
            with self.scheduler._lock:
                self.scheduler._execution_stats["total_executions"] += 1
                self.scheduler._execution_stats["failed_executions"] += 1

        self.scheduler.shutdown()

        # 验证正常任务仍然执行（错误任务不应影响其他任务）
        results = self.service.get_results()
        regular_results = [r for r in results if r.startswith("regular_")]
        self.assertGreater(len(regular_results), 0)

        # 检查执行统计
        stats = self.scheduler.get_execution_statistics()
        self.assertGreater(stats["total_executions"], 0)
        self.assertGreater(stats["failed_executions"], 0)  # 应该有失败的执行

    def test_scheduler_lifecycle_integration(self):
        """测试调度器生命周期集成"""
        # 注册任务
        self.scheduler.register_task(self.service.regular_task, ScheduledConfig(fixed_rate="1s"), "lifecycle_test_task")

        # 测试启动
        self.scheduler.start()
        self.assertTrue(self.scheduler.is_running())
        time.sleep(1)

        # 测试暂停
        self.scheduler.pause()
        initial_count = self.service.get_execution_count()
        time.sleep(1)
        paused_count = self.service.get_execution_count()

        # 暂停期间执行次数应该不变或变化很小
        self.assertLessEqual(paused_count - initial_count, 1)

        # 测试恢复
        self.scheduler.resume()
        time.sleep(1)
        resumed_count = self.service.get_execution_count()
        self.assertGreater(resumed_count, paused_count)

        # 测试关闭
        self.scheduler.shutdown()
        self.assertFalse(self.scheduler.is_running())

    def test_context_manager_integration(self):
        """测试上下文管理器集成"""
        service = IntegrationTestService()

        with MiniBootScheduler(max_workers=2, use_asyncio=False) as scheduler:
            # 在上下文中注册和执行任务
            scheduler.register_task(service.regular_task, ScheduledConfig(fixed_rate="1s"), "context_test_task")

            self.assertTrue(scheduler.is_running())
            time.sleep(2)

            # 验证任务执行
            self.assertGreater(service.get_execution_count(), 0)

        # 退出上下文后调度器应该自动关闭
        self.assertFalse(scheduler.is_running())


class TestAnnotationIntegration(unittest.TestCase):
    """测试注解集成"""

    def test_scheduled_annotation_integration(self):
        """测试@Scheduled注解集成"""
        service = IntegrationTestService()
        scheduler = MiniBootScheduler(max_workers=2, use_asyncio=False)

        try:
            # 从@Scheduled方法创建任务
            if scheduler.task_manager:
                regular_id = scheduler.task_manager.create_from_scheduled_method(service.regular_task, service)

                delayed_id = scheduler.task_manager.create_from_scheduled_method(service.delayed_task, service)

                async_id = scheduler.task_manager.create_from_scheduled_method(service.async_task, service)

                self.assertIsNotNone(regular_id)
                self.assertIsNotNone(delayed_id)
                self.assertIsNotNone(async_id)

                # 启动并测试
                scheduler.start()
                time.sleep(3)
                scheduler.shutdown()

                # 验证执行
                results = service.get_results()
                self.assertGreater(len(results), 0)

                # 验证不同类型的任务都执行了
                regular_results = [r for r in results if r.startswith("regular_")]
                delayed_results = [r for r in results if r.startswith("delayed_")]
                async_results = [r for r in results if r.startswith("async_")]

                self.assertGreater(len(regular_results), 0)
                self.assertGreater(len(delayed_results), 0)
                self.assertGreater(len(async_results), 0)
            else:
                self.skipTest("TaskManager not available")

        finally:
            if scheduler.is_running():
                scheduler.shutdown()


class TestPerformanceIntegration(unittest.TestCase):
    """测试性能集成"""

    def test_high_frequency_tasks(self):
        """测试高频任务"""
        service = IntegrationTestService()
        scheduler = MiniBootScheduler(max_workers=5, use_asyncio=False)

        try:
            # 注册多个高频任务
            task_ids = []
            for i in range(5):
                task_id = scheduler.register_task(service.regular_task, ScheduledConfig(fixed_rate="0.5s"), f"high_freq_task_{i}")
                task_ids.append(task_id)

            # 运行短时间
            scheduler.start()
            time.sleep(3)
            scheduler.shutdown()

            # 验证高频执行
            execution_count = service.get_execution_count()
            self.assertGreater(execution_count, 10)  # 应该有较多执行次数

        finally:
            if scheduler.is_running():
                scheduler.shutdown()

    def test_concurrent_task_execution(self):
        """测试并发任务执行"""
        service = IntegrationTestService()
        scheduler = MiniBootScheduler(max_workers=3, use_asyncio=False)

        try:
            # 注册需要处理时间的任务
            scheduler.register_task(
                service.delayed_task,  # 这个任务有sleep(0.1)
                ScheduledConfig(fixed_rate="0.2s"),
                "concurrent_test_task",
            )

            # 运行一段时间
            scheduler.start()
            time.sleep(2)
            scheduler.shutdown()

            # 验证并发执行（执行次数应该大于串行执行的次数）
            execution_count = service.get_execution_count()
            self.assertGreater(execution_count, 5)

        finally:
            if scheduler.is_running():
                scheduler.shutdown()


if __name__ == "__main__":
    unittest.main()

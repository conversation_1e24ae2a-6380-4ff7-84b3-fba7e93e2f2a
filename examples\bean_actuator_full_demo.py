#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Bean Actuator 完整功能演示

展示 Bean Actuator 集成的完整功能，包括：
- Bean 容器的创建和管理
- 通过 Actuator 端点监控 Bean
- Bean 依赖关系分析
- 容器缓存管理操作
- 实时指标监控
"""

import asyncio
import json
import time

# DEPRECATED: from miniboot.actuator.autoconfigure.bean_actuator_auto_configuration import BeanActuatorAutoConfiguration


class DemoApplicationContext:
    """演示应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = DemoEnvironment()
        self._bean_factory = DemoBeanFactory()

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class DemoEnvironment:
    """演示环境配置"""

    def get_property(self, key, default=None):
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.bean.enabled": True,
            "actuator.security.enabled": False,
        }
        return config.get(key, default)


class DemoService:
    """演示服务类"""
    def __init__(self, repository=None):
        self.repository = repository
        self.name = "DemoService"

    def get_data(self):
        return f"Data from {self.name}"


class DemoRepository:
    """演示仓储类"""
    def __init__(self, config=None):
        self.config = config
        self.name = "DemoRepository"

    def find_all(self):
        return ["item1", "item2", "item3"]


class DemoController:
    """演示控制器类"""
    def __init__(self, service=None):
        self.service = service
        self.name = "DemoController"

    def handle_request(self):
        return f"Handled by {self.name}"


class DemoConfig:
    """演示配置类"""
    def __init__(self):
        self.name = "DemoConfig"
        self.settings = {"debug": True, "timeout": 30}


class DemoBeanDefinition:
    """演示 Bean 定义"""

    def __init__(self, name, bean_class, scope="singleton", dependencies=None):
        self.bean_name = name
        self.bean_class = bean_class
        self.scope = DemoScope(scope)
        self.constructor_arguments = []
        self.property_values = []

        # 添加依赖关系
        if dependencies:
            for dep in dependencies:
                self.constructor_arguments.append(DemoDependency(dep))


class DemoDependency:
    """演示依赖"""
    def __init__(self, ref):
        self.ref = ref


class DemoScope:
    """演示作用域"""

    def __init__(self, value):
        self.value = value

    def is_singleton(self):
        return self.value == "singleton"

    def is_prototype(self):
        return self.value == "prototype"

    def is_web_scope(self):
        return self.value in ["request", "session"]


class DemoBeanRegistry:
    """演示 Bean 注册表"""

    def __init__(self):
        self._definitions = {}
        self._create_demo_beans_with_dependencies()

    def _create_demo_beans_with_dependencies(self):
        """创建带依赖关系的演示 Bean"""
        # 创建 Bean 定义（包含依赖关系）
        self._definitions["demoConfig"] = DemoBeanDefinition("demoConfig", DemoConfig, "singleton")
        self._definitions["demoRepository"] = DemoBeanDefinition("demoRepository", DemoRepository, "singleton", ["demoConfig"])
        self._definitions["demoService"] = DemoBeanDefinition("demoService", DemoService, "singleton", ["demoRepository"])
        self._definitions["demoController"] = DemoBeanDefinition("demoController", DemoController, "prototype", ["demoService"])

        # 添加更多复杂的 Bean
        self._definitions["cacheManager"] = DemoBeanDefinition("cacheManager", type("CacheManager", (), {}), "singleton")
        self._definitions["dataSource"] = DemoBeanDefinition("dataSource", type("DataSource", (), {}), "singleton", ["demoConfig"])
        self._definitions["transactionManager"] = DemoBeanDefinition("transactionManager", type("TransactionManager", (), {}), "singleton", ["dataSource"])

    def count(self):
        return len(self._definitions)

    def names(self):
        return list(self._definitions.keys())

    def singleton_names(self):
        return [name for name, definition in self._definitions.items()
                if definition.scope.is_singleton()]

    def has_definition(self, name):
        return name in self._definitions

    def get_definition(self, name):
        if name not in self._definitions:
            raise KeyError(f"No bean definition found for name: {name}")
        return self._definitions[name]


class DemoCache:
    """演示三级缓存"""

    def __init__(self):
        # 一级缓存：完整的单例对象
        self._singleton_objects = {
            "demoConfig": DemoConfig(),
            "demoRepository": DemoRepository(),
            "demoService": DemoService(),
            "cacheManager": type("CacheManager", (), {})(),
            "dataSource": type("DataSource", (), {})()
        }

        # 二级缓存：早期暴露的对象引用
        self._early_singleton_objects = {
            "transactionManager": type("TransactionManager", (), {})()
        }

        # 三级缓存：对象工厂
        self._singleton_factories = {}


class DemoBeanFactory:
    """演示 Bean 工厂"""

    def __init__(self):
        self._registry = DemoBeanRegistry()
        self._cache = DemoCache()
        self._enable_thread_safety = True


class BeanActuatorDemo:
    """Bean Actuator 演示类"""

    def __init__(self):
        self.app_context = DemoApplicationContext()
        self.bean_config = None

    async def initialize(self):
        """初始化演示环境"""
        print("🚀 Initializing Bean Actuator Demo...")

        # 创建并配置 Bean Actuator
        self.bean_config = BeanActuatorAutoConfiguration(self.app_context)
        await self.bean_config.configure()

        print("✅ Demo environment initialized successfully!")

    async def demonstrate_endpoints(self):
        """演示端点功能"""
        print("\n🔌 Demonstrating Bean Actuator Endpoints...")

        if not self.bean_config:
            print("❌ Bean configuration not available")
            return

        # 获取端点
        # DEPRECATED: from miniboot.actuator.autoconfigure.bean_actuator_auto_configuration import (
        #     BeanContainerEndpoint, BeanDependencyEndpoint)
        # container_endpoint = BeanContainerEndpoint(self.app_context)
        # dependency_endpoint = BeanDependencyEndpoint(self.app_context)
        print("⚠️ Bean endpoints deprecated - please update to use new configuration")

        # 1. 查看 Bean 容器信息
        print("\n📦 1. Viewing Bean Container Information:")
        container_info = container_endpoint.invoke(container_endpoint.operations()[0].operation_type)

        factory_info = container_info.get('factory', {})
        registry_info = container_info.get('registry', {})
        cache_info = container_info.get('cache', {})
        stats = container_info.get('statistics', {})

        print(f"   Factory: {factory_info.get('type', 'unknown')} (Available: {factory_info.get('available', False)})")
        print(f"   Registry: {registry_info.get('type', 'unknown')} (Beans: {registry_info.get('bean_count', 0)})")
        print(f"   Cache: {cache_info.get('type', 'unknown')} (Total: {cache_info.get('total_cached', 0)})")
        print(f"   Statistics: {stats.get('total_beans', 0)} total, {stats.get('created_instances', 0)} instances")

        # 2. 查看 Bean 依赖关系
        print("\n🔗 2. Viewing Bean Dependencies:")
        dependencies_info = dependency_endpoint.invoke(dependency_endpoint.operations()[0].operation_type)

        dependencies = dependencies_info.get('dependencies', {})
        dep_stats = dependencies_info.get('statistics', {})

        print(f"   Total beans analyzed: {dep_stats.get('total_beans', 0)}")
        print(f"   Beans with dependencies: {dep_stats.get('beans_with_dependencies', 0)}")
        print(f"   Total dependencies: {dep_stats.get('total_dependencies', 0)}")
        print(f"   Most dependent bean: {dep_stats.get('most_dependent_bean', 'none')}")

        # 显示依赖关系详情
        print("\n   📋 Dependency Details:")
        for bean_name, deps_info in list(dependencies.items())[:5]:  # 只显示前5个
            if isinstance(deps_info, dict) and 'total_dependencies' in deps_info:
                total_deps = deps_info['total_dependencies']
                bean_class = deps_info.get('bean_class', 'unknown')
                print(f"     - {bean_name} ({bean_class}): {total_deps} dependencies")

        # 3. 演示容器管理操作
        await self._demonstrate_container_management(container_endpoint)

        # 4. 演示单个 Bean 依赖查询
        await self._demonstrate_single_bean_dependency(dependency_endpoint)

    async def _demonstrate_container_management(self, container_endpoint):
        """演示容器管理操作"""
        print("\n🎛️  3. Demonstrating Container Management:")

        # 刷新容器
        print("   🔄 Refreshing container...")
        refresh_result = container_endpoint._refresh_container()
        print(f"   Result: {refresh_result.get('success', False)} - {refresh_result.get('message', 'unknown')}")

        # 清理缓存
        print("   🧹 Clearing cache...")
        clear_result = container_endpoint._clear_cache()
        print(f"   Result: {clear_result.get('success', False)}")
        if clear_result.get('success'):
            cleared = clear_result.get('cleared', {})
            print(f"   Cleared: {cleared.get('total', 0)} items ({cleared})")

    async def _demonstrate_single_bean_dependency(self, dependency_endpoint):
        """演示单个 Bean 依赖查询"""
        print("\n🔍 4. Demonstrating Single Bean Dependency Query:")

        # 查询具有依赖的 Bean
        beans_to_check = ["demoService", "demoController", "transactionManager"]

        for bean_name in beans_to_check:
            print(f"   📋 Checking {bean_name}:")
            bean_info = dependency_endpoint._get_bean_dependencies(bean_name)

            if 'error' in bean_info:
                print(f"     Error: {bean_info['error']}")
                continue

            deps = bean_info.get('dependencies', {})
            constructor_deps = deps.get('constructor_dependencies', [])
            total_deps = deps.get('total_dependencies', 0)
            bean_class = deps.get('bean_class', 'unknown')

            print(f"     Class: {bean_class}")
            print(f"     Total dependencies: {total_deps}")
            if constructor_deps:
                print(f"     Constructor deps: {', '.join(constructor_deps)}")

    async def monitor_metrics(self, duration=10):
        """监控指标变化"""
        print(f"\n📊 Monitoring Bean metrics for {duration} seconds...")

        start_time = time.time()
        while time.time() - start_time < duration:
            # 刷新指标
            await self.bean_config.refresh_metrics()

            # 获取当前状态
            status = self.bean_config.get_integration_status()
            metrics = status.get('metrics', {})

            print(f"   [{time.strftime('%H:%M:%S')}] "
                  f"Beans: {metrics.get('total_beans', 0)}, "
                  f"Instances: {metrics.get('created_instances', 0)}, "
                  f"Rate: {metrics.get('instantiation_rate', 0):.1f}%")

            await asyncio.sleep(2)

        print("📊 Metrics monitoring completed")

    async def run_demo(self):
        """运行完整演示"""
        try:
            # 初始化
            await self.initialize()

            # 演示端点功能
            await self.demonstrate_endpoints()

            # 监控指标
            await self.monitor_metrics(duration=8)

            print("\n🎉 Demo completed successfully!")

        except Exception as e:
            print(f"❌ Demo failed: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    print("🎯 Bean Actuator Full Demo")
    print("=" * 50)

    demo = BeanActuatorDemo()
    await demo.run_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

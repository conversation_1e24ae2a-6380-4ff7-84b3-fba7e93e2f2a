#!/usr/bin/env python
"""
* @author: cz
* @description: 事件发布器集成测试

测试事件发布器与注解系统的集成，验证完整的事件处理流程。
"""

import asyncio
import unittest

from miniboot.annotations.event import AsyncEventListener, EventListener
from miniboot.events import ApplicationEvent, ApplicationEventPublisher, EventPublisher


class UserEvent(ApplicationEvent):
    """用户事件"""

    def __init__(self, user_id: str, action: str):
        super().__init__(data={"user_id": user_id, "action": action})
        self.user_id = user_id
        self.action = action


class OrderEvent(ApplicationEvent):
    """订单事件"""

    def __init__(self, order_id: str, amount: float, status: str = "created"):
        super().__init__(data={"order_id": order_id, "amount": amount, "status": status})
        self.order_id = order_id
        self.amount = amount
        self.status = status


class EventListenerService:
    """事件监听器服务"""

    def __init__(self):
        self.handled_events = []
        self.async_events = []

    @EventListener
    def handle_user_event(self, event: UserEvent):
        """处理用户事件"""
        self.handled_events.append(("user", event))

    @EventListener(order=1)
    def handle_user_login(self, event: UserEvent):
        """处理用户登录事件（高优先级）"""
        if event.action == "login":
            self.handled_events.append(("login", event))

    @EventListener(condition="event.amount > 1000")
    def handle_large_order(self, event: OrderEvent):
        """处理大额订单事件"""
        self.handled_events.append(("large_order", event))

    @AsyncEventListener
    async def handle_user_async(self, event: UserEvent):
        """异步处理用户事件"""
        await asyncio.sleep(0.01)  # 模拟异步操作
        self.async_events.append(("async_user", event))

    @AsyncEventListener(condition="event.status == 'paid'")
    async def handle_payment_async(self, event: OrderEvent):
        """异步处理支付事件"""
        await asyncio.sleep(0.01)
        self.async_events.append(("async_payment", event))


class TestEventPublisherIntegration(unittest.TestCase):
    """事件发布器集成测试"""

    def setUp(self):
        """设置测试"""
        self.publisher = EventPublisher()
        self.service = EventListenerService()

    def tearDown(self):
        """清理测试"""
        self.publisher.shutdown()

    def test_manual_listener_registration(self):
        """测试手动注册监听器"""
        # 手动注册监听器
        self.publisher.subscribe(UserEvent, self.service.handle_user_event, instance=self.service)

        self.publisher.subscribe(UserEvent, self.service.handle_user_login, instance=self.service, order=1)

        # 发布用户登录事件
        login_event = UserEvent("user123", "login")
        self.publisher.publish(login_event)

        # 验证处理结果（按优先级顺序）
        self.assertEqual(len(self.service.handled_events), 2)
        self.assertEqual(self.service.handled_events[0][0], "user")  # 默认order=0先执行（优先级高）
        self.assertEqual(self.service.handled_events[1][0], "login")  # order=1后执行（优先级低）

    def test_conditional_event_handling(self):
        """测试条件事件处理"""
        # 注册条件监听器
        self.publisher.subscribe(OrderEvent, self.service.handle_large_order, instance=self.service, condition="event.amount > 1000")

        # 发布小额订单（不满足条件）
        small_order = OrderEvent("ORDER-001", 500.0)
        self.publisher.publish(small_order)

        # 发布大额订单（满足条件）
        large_order = OrderEvent("ORDER-002", 1500.0)
        self.publisher.publish(large_order)

        # 只有大额订单被处理
        self.assertEqual(len(self.service.handled_events), 1)
        self.assertEqual(self.service.handled_events[0][0], "large_order")
        self.assertEqual(self.service.handled_events[0][1].amount, 1500.0)

    def test_async_event_handling(self):
        """测试异步事件处理"""

        async def async_test():
            # 注册异步监听器
            self.publisher.subscribe(UserEvent, self.service.handle_user_async, instance=self.service, async_exec=True)

            self.publisher.subscribe(
                OrderEvent, self.service.handle_payment_async, instance=self.service, async_exec=True, condition="event.status == 'paid'"
            )

            # 发布用户事件
            user_event = UserEvent("user456", "register")
            await self.publisher.publish_async(user_event)

            # 发布支付事件
            payment_event = OrderEvent("ORDER-003", 800.0, "paid")
            await self.publisher.publish_async(payment_event)

            # 发布未支付事件（不满足条件）
            unpaid_event = OrderEvent("ORDER-004", 600.0, "created")
            await self.publisher.publish_async(unpaid_event)

            # 验证异步处理结果
            self.assertEqual(len(self.service.async_events), 2)
            self.assertEqual(self.service.async_events[0][0], "async_user")
            self.assertEqual(self.service.async_events[1][0], "async_payment")

        # 运行异步测试
        asyncio.run(async_test())

    def test_mixed_sync_async_handling(self):
        """测试混合同步异步处理"""

        async def mixed_test():
            # 注册同步和异步监听器
            self.publisher.subscribe(UserEvent, self.service.handle_user_event, instance=self.service)

            self.publisher.subscribe(UserEvent, self.service.handle_user_async, instance=self.service, async_exec=True)

            # 异步发布事件
            event = UserEvent("user789", "update")
            await self.publisher.publish_async(event)

            # 验证同步和异步处理器都被执行
            self.assertEqual(len(self.service.handled_events), 1)
            self.assertEqual(len(self.service.async_events), 1)

            self.assertEqual(self.service.handled_events[0][0], "user")
            self.assertEqual(self.service.async_events[0][0], "async_user")

        asyncio.run(mixed_test())

    def test_event_inheritance_handling(self):
        """测试事件继承处理"""

        # 注册ApplicationEvent监听器（父类）
        def handle_any_app_event(event):
            self.service.handled_events.append(("app_event", event))

        self.publisher.subscribe(ApplicationEvent, handle_any_app_event)

        # 发布子类事件
        user_event = UserEvent("user999", "delete")
        order_event = OrderEvent("ORDER-005", 300.0)

        self.publisher.publish(user_event)
        self.publisher.publish(order_event)

        # 父类监听器应该处理所有子类事件
        self.assertEqual(len(self.service.handled_events), 2)
        self.assertEqual(self.service.handled_events[0][0], "app_event")
        self.assertEqual(self.service.handled_events[1][0], "app_event")

    def test_error_resilience(self):
        """测试错误恢复能力"""

        def failing_handler(event):
            raise ValueError("Handler failed")

        def normal_handler(event):
            self.service.handled_events.append(("normal", event))

        # 注册失败和正常的处理器
        self.publisher.subscribe(UserEvent, failing_handler, order=1)
        self.publisher.subscribe(UserEvent, normal_handler, order=2)

        # 发布事件
        event = UserEvent("user_error", "test")
        self.publisher.publish(event)  # 不应该抛出异常

        # 正常处理器仍然应该执行
        self.assertEqual(len(self.service.handled_events), 1)
        self.assertEqual(self.service.handled_events[0][0], "normal")

        # 验证统计信息
        stats = self.publisher.get_stats()
        self.assertEqual(stats["failed_events"], 1)
        self.assertEqual(stats["handled_events"], 1)

    def test_unsubscribe_during_processing(self):
        """测试处理过程中取消订阅"""
        handler_ids = []

        def handler1(event):
            self.service.handled_events.append(("handler1", event))

        def handler2(event):
            self.service.handled_events.append(("handler2", event))

        # 注册处理器
        handler_ids.append(self.publisher.subscribe(UserEvent, handler1))
        handler_ids.append(self.publisher.subscribe(UserEvent, handler2))

        # 发布第一个事件
        event1 = UserEvent("user1", "action1")
        self.publisher.publish(event1)

        # 取消一个处理器
        self.publisher.unsubscribe(handler_ids[0])

        # 发布第二个事件
        event2 = UserEvent("user2", "action2")
        self.publisher.publish(event2)

        # 验证结果
        self.assertEqual(len(self.service.handled_events), 3)  # 2 + 1

        # 第一个事件被两个处理器处理
        first_event_handlers = [h[0] for h in self.service.handled_events[:2]]
        self.assertIn("handler1", first_event_handlers)
        self.assertIn("handler2", first_event_handlers)

        # 第二个事件只被一个处理器处理
        self.assertEqual(self.service.handled_events[2][0], "handler2")


class TestApplicationEventPublisher(unittest.TestCase):
    """应用事件发布器集成测试"""

    def setUp(self):
        """设置测试"""
        self.app_publisher = ApplicationEventPublisher()
        self.service = EventListenerService()

    def tearDown(self):
        """清理测试"""
        self.app_publisher.shutdown()

    def test_high_level_api(self):
        """测试高级API"""
        # 使用高级API注册监听器
        handler_id = self.app_publisher.register_listener(UserEvent, self.service.handle_user_event, instance=self.service)

        # 发布事件
        event = UserEvent("api_user", "api_action")
        self.app_publisher.publish_event(event)

        # 验证处理结果
        self.assertEqual(len(self.service.handled_events), 1)
        self.assertEqual(self.service.handled_events[0][0], "user")

        # 取消注册
        success = self.app_publisher.unregister_listener(handler_id)
        self.assertTrue(success)

        # 验证取消注册后不再处理事件
        event2 = UserEvent("api_user2", "api_action2")
        self.app_publisher.publish_event(event2)

        self.assertEqual(len(self.service.handled_events), 1)  # 没有增加

    def test_async_high_level_api(self):
        """测试异步高级API"""

        async def async_api_test():
            # 注册异步监听器
            self.app_publisher.register_listener(UserEvent, self.service.handle_user_async, instance=self.service, async_exec=True)

            # 异步发布事件
            event = UserEvent("async_api_user", "async_api_action")
            await self.app_publisher.publish_event_async(event)

            # 验证异步处理结果
            self.assertEqual(len(self.service.async_events), 1)
            self.assertEqual(self.service.async_events[0][0], "async_user")

        asyncio.run(async_api_test())


if __name__ == "__main__":
    unittest.main()

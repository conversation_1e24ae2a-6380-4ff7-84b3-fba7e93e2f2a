#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Processor Actuator 集成自动配置演示

展示如何使用 ProcessorActuatorAutoConfiguration 自动配置 Actuator 与处理器系统的集成，
包括处理器监控、任务处理管理、处理器性能分析等功能。

功能特性:
- 自动检测处理器系统环境
- 自动创建处理器相关端点
- 处理器性能监控集成
- 处理器状态管理
- 处理器配置管理
- 处理器指标收集
"""

import asyncio
import time

# DEPRECATED: from miniboot.actuator.autoconfigure.processor_actuator_auto_configuration import (
#     ProcessorActuatorAutoConfiguration,
#     ProcessorIntegrationStatus,
#     ProcessorManagerEndpoint,
#     ProcessorMetricsEndpoint,
#     ProcessorConfigEndpoint
# )


class MockApplicationContext:
    """模拟应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = MockEnvironment()
        self._processor_manager = None

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class MockEnvironment:
    """模拟环境配置"""

    def get_property(self, key, default=None):
        # 演示配置
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.processor.enabled": True,
            "actuator.security.enabled": False,
        }
        return config.get(key, default)


async def create_demo_processor_manager():
    """创建演示处理器管理器"""
    try:
        from miniboot.processor.base import BeanPostProcessor
        from miniboot.processor.manager import (BeanPostProcessorManager,
                                                ProcessorConfig)

        # 创建处理器管理器
        manager = BeanPostProcessorManager()

        # 创建一些演示处理器
        class DemoValidationProcessor(BeanPostProcessor):
            def get_order(self) -> int:
                return 100

            def post_process_before_initialization(self, bean, bean_name: str):
                print(f"🔍 Validating bean: {bean_name}")
                return bean

            def post_process_after_initialization(self, bean, bean_name: str):
                print(f"✅ Validated bean: {bean_name}")
                return bean

        class DemoLoggingProcessor(BeanPostProcessor):
            def get_order(self) -> int:
                return 200

            def post_process_before_initialization(self, bean, bean_name: str):
                print(f"📝 Logging before init: {bean_name}")
                return bean

            def post_process_after_initialization(self, bean, bean_name: str):
                print(f"📋 Logging after init: {bean_name}")
                return bean

        class DemoMonitoringProcessor(BeanPostProcessor):
            def get_order(self) -> int:
                return 300

            def post_process_before_initialization(self, bean, bean_name: str):
                print(f"📊 Monitoring before init: {bean_name}")
                return bean

            def post_process_after_initialization(self, bean, bean_name: str):
                print(f"📈 Monitoring after init: {bean_name}")
                return bean

        # 注册处理器
        validation_processor = DemoValidationProcessor()
        logging_processor = DemoLoggingProcessor()
        monitoring_processor = DemoMonitoringProcessor()

        # 配置处理器
        validation_config = ProcessorConfig(
            enabled=True,
            timeout_seconds=5.0,
            retry_count=2,
            error_threshold=5
        )

        logging_config = ProcessorConfig(
            enabled=True,
            timeout_seconds=3.0,
            retry_count=1,
            error_threshold=10
        )

        monitoring_config = ProcessorConfig(
            enabled=False,  # 演示禁用状态
            timeout_seconds=10.0,
            retry_count=0,
            error_threshold=3
        )

        manager.register(validation_processor, validation_config)
        manager.register(logging_processor, logging_config)
        manager.register(monitoring_processor, monitoring_config)

        print("✅ Demo processor manager created with 3 processors")
        return manager

    except ImportError as e:
        print(f"⚠️  Processor module not available: {e}")
        return None
    except Exception as e:
        print(f"❌ Failed to create demo processor manager: {e}")
        return None


async def test_processor_actuator_auto_configuration():
    """测试 Processor Actuator 自动配置"""
    print("🧪 Testing Processor Actuator Auto Configuration...")

    try:
        # 1. 创建模拟应用上下文
        app_context = MockApplicationContext()

        # 2. 创建演示处理器管理器
        manager = await create_demo_processor_manager()
        if manager:
            app_context._processor_manager = manager

        # 3. 创建 Processor 自动配置实例
        processor_config = ProcessorActuatorAutoConfiguration(app_context)
        print(f"📊 Initial status: {processor_config.status.value}")

        # 4. 执行自动配置
        print("🔧 Executing Processor Actuator auto configuration...")
        await processor_config.configure()

        # 5. 检查配置结果
        print(f"📊 Final status: {processor_config.status.value}")

        # 6. 获取集成状态
        status_info = processor_config.get_integration_status()
        print("\n📈 Integration Status:")
        for key, value in status_info.items():
            if key != "metrics":
                print(f"  - {key}: {value}")

        print("📊 Metrics:")
        for key, value in status_info["metrics"].items():
            print(f"  - {key}: {value}")

        # 7. 测试端点功能
        await test_processor_endpoints(processor_config)

        # 8. 刷新指标
        print("\n🔄 Refreshing metrics...")
        await processor_config.refresh_metrics()
        updated_status = processor_config.get_integration_status()
        print(f"📊 Updated metrics:")
        for key, value in updated_status["metrics"].items():
            print(f"  - {key}: {value}")

        print("\n✅ Processor Actuator Auto Configuration test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_processor_endpoints(processor_config):
    """测试处理器端点功能"""
    print("\n🔌 Testing Processor Endpoints...")

    try:
        processor_manager = processor_config.get_processor_manager()
        if not processor_manager:
            print("⚠️  No processor manager available for endpoint testing")
            return

        # 测试处理器管理端点
        manager_endpoint = ProcessorManagerEndpoint(processor_manager)

        print("⚙️  Testing ProcessorManager endpoint:")
        manager_info = manager_endpoint.invoke(manager_endpoint.operations()[0].operation_type)
        print(f"  - Available: {manager_info.get('available', False)}")
        print(f"  - Type: {manager_info.get('type', 'unknown')}")
        registry = manager_info.get('registry', {})
        print(f"  - Total processors: {registry.get('total_processors', 0)}")
        stats = manager_info.get('statistics', {})
        print(f"  - Active processors: {stats.get('active_processors', 0)}")
        print(f"  - Disabled processors: {stats.get('disabled_processors', 0)}")

        # 测试处理器指标端点
        metrics_endpoint = ProcessorMetricsEndpoint(processor_manager)

        print("\n📊 Testing ProcessorMetrics endpoint:")
        metrics_info = metrics_endpoint.invoke(metrics_endpoint.operations()[0].operation_type)
        summary = metrics_info.get('summary', {})
        print(f"  - Total processors: {summary.get('total_processors', 0)}")
        print(f"  - Total executions: {summary.get('total_executions', 0)}")
        print(f"  - Overall success rate: {summary.get('overall_success_rate', 0):.1f}%")

        # 测试处理器配置端点
        config_endpoint = ProcessorConfigEndpoint(processor_manager)

        print("\n🔧 Testing ProcessorConfig endpoint:")
        config_info = config_endpoint.invoke(config_endpoint.operations()[0].operation_type)
        config_summary = config_info.get('summary', {})
        print(f"  - Total processors: {config_summary.get('total_processors', 0)}")
        print(f"  - Enabled processors: {config_summary.get('enabled_processors', 0)}")
        print(f"  - Disabled processors: {config_summary.get('disabled_processors', 0)}")

        # 测试管理操作
        print("\n🎛️  Testing management operations:")

        # 启用被禁用的处理器
        enable_result = manager_endpoint._enable_processor("DemoMonitoringProcessor")
        print(f"  - Enable DemoMonitoringProcessor: {enable_result.get('success', False)}")

        # 禁用处理器
        disable_result = manager_endpoint._disable_processor("DemoLoggingProcessor")
        print(f"  - Disable DemoLoggingProcessor: {disable_result.get('success', False)}")

        # 重置指标
        reset_result = manager_endpoint._reset_processor_metrics("DemoValidationProcessor")
        print(f"  - Reset DemoValidationProcessor metrics: {reset_result.get('success', False)}")

        print("✅ Processor endpoints tested successfully")

    except Exception as e:
        print(f"❌ Endpoint testing failed: {e}")


async def test_integration_components():
    """测试集成组件"""
    print("\n🧪 Testing Integration Components...")

    try:
        # 测试状态枚举
        print("📊 Integration Status Values:")
        for status in ProcessorIntegrationStatus:
            print(f"  - {status.name}: {status.value}")

        # 测试导入的组件
        # DEPRECATED: from miniboot.actuator.autoconfigure.processor_actuator_auto_configuration import \
            ProcessorIntegrationMetrics

        print("✅ All components imported successfully!")
        print("  - ProcessorActuatorAutoConfiguration: ✓")
        print("  - ProcessorManagerEndpoint: ✓")
        print("  - ProcessorMetricsEndpoint: ✓")
        print("  - ProcessorConfigEndpoint: ✓")
        print("  - ProcessorIntegrationStatus: ✓")
        print("  - ProcessorIntegrationMetrics: ✓")

        return True

    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Starting Processor Actuator Integration Tests...\n")

    # 测试组件导入
    component_test = await test_integration_components()

    # 测试自动配置
    config_test = await test_processor_actuator_auto_configuration()

    # 总结
    print(f"\n📋 Test Summary:")
    print(f"  - Component Import Test: {'✅ PASSED' if component_test else '❌ FAILED'}")
    print(f"  - Auto Configuration Test: {'✅ PASSED' if config_test else '❌ FAILED'}")

    if component_test and config_test:
        print("\n🎉 All tests passed! Processor Actuator integration is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

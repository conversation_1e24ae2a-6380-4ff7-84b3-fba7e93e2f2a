#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Web Actuator 集成自动配置演示

展示如何使用 WebActuatorAutoConfiguration 自动配置 Actuator 与 FastAPI 的集成，
包括路由注册、中间件配置、错误处理等功能。

功能特性:
- 自动检测 Web 环境
- 自动集成 Actuator 到 FastAPI 应用
- 路由自动注册和配置
- 中间件自动配置
- 错误处理集成
- 性能监控集成
- 健康检查集成
"""

import asyncio

import uvicorn
from fastapi import FastAPI, Request
from fastapi.responses import JSONResponse

# DEPRECATED: from miniboot.actuator.autoconfigure.web_actuator_auto_configuration import WebActuatorAutoConfiguration, IntegrationStatus
# 注意：原 WebActuatorAutoConfiguration 已迁移，请使用新的 Web 自动配置类
from miniboot.starters.actuator.context import ActuatorContext
from miniboot.starters.actuator.properties import ActuatorProperties


class DemoApplicationContext:
    """演示应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = DemoEnvironment()

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class DemoEnvironment:
    """演示环境配置"""

    def get_property(self, key, default=None):
        # 演示配置
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.web.enabled": True,
            "actuator.security.enabled": False,
            "actuator.security.cors-enabled": True,
            "actuator.security.allowed-origins": ["*"],
            "actuator.security.allowed-methods": ["GET", "POST", "PUT", "DELETE"],
        }
        return config.get(key, default)


async def create_demo_application():
    """创建演示应用"""
    print("🚀 Creating Demo Application with Web Actuator Integration...")

    # 1. 创建 FastAPI 应用
    app = FastAPI(
        title="Mini-Boot Demo Application",
        description="Demonstration of Web Actuator Auto Configuration",
        version="1.0.0"
    )

    # 2. 创建应用上下文
    app_context = DemoApplicationContext()
    app_context.register_singleton("webApp", app)

    # 3. 创建 Web Actuator 自动配置
    web_config = WebActuatorAutoConfiguration(app_context)
    print(f"📊 Initial integration status: {web_config.status.value}")

    # 4. 执行自动配置
    print("🔧 Executing Web Actuator auto configuration...")
    await web_config.configure()

    # 5. 检查配置结果
    print(f"📊 Final integration status: {web_config.status.value}")

    # 6. 获取集成状态信息
    status_info = web_config.get_integration_status()
    print("\n📈 Integration Status Summary:")
    print(f"  - Status: {status_info['status']}")
    print(f"  - Routes Count: {status_info['routes_count']}")
    print(f"  - Middleware Installed: {status_info['middleware_installed']}")
    print(f"  - Error Handlers Installed: {status_info['error_handlers_installed']}")
    print(f"  - Endpoints Count: {status_info['endpoints_count']}")
    print(f"  - Security Enabled: {status_info['security_enabled']}")
    print(f"  - CORS Enabled: {status_info['cors_enabled']}")

    # 7. 显示注册的路由
    print(f"\n🛣️  Registered Routes ({len(web_config.routes)}):")
    for route_key, route_info in web_config.routes.items():
        print(f"  - {route_key} → {route_info.endpoint_id}")

    # 8. 添加一些演示路由
    @app.get("/")
    async def root():
        """根路径"""
        return {
            "message": "Welcome to Mini-Boot Demo Application!",
            "actuator_integration": "enabled",
            "actuator_base_path": "/actuator",
            "available_endpoints": [
                "/actuator/health",
                "/actuator/info",
                "/actuator/metrics",
                "/actuator/integration/health"
            ],
            "quick_health_check": "/health"
        }

    @app.get("/demo")
    async def demo_endpoint():
        """演示端点"""
        return {
            "demo": "This is a demo endpoint",
            "integration_status": web_config.get_integration_status(),
            "timestamp": asyncio.get_event_loop().time()
        }

    # 9. 添加请求日志中间件
    @app.middleware("http")
    async def log_requests(request: Request, call_next):
        """请求日志中间件"""
        start_time = asyncio.get_event_loop().time()

        print(f"📥 Incoming request: {request.method} {request.url.path}")

        response = await call_next(request)

        duration = asyncio.get_event_loop().time() - start_time
        print(f"📤 Response: {response.status_code} ({duration:.3f}s)")

        return response

    print("\n✅ Demo application created successfully!")
    return app, web_config


async def run_demo_server():
    """运行演示服务器"""
    try:
        # 创建应用
        app, web_config = await create_demo_application()

        print("\n🌐 Starting Demo Server...")
        print("📍 Available endpoints:")
        print("  - http://localhost:8000/ (Root)")
        print("  - http://localhost:8000/demo (Demo endpoint)")
        print("  - http://localhost:8000/health (Quick health check)")
        print("  - http://localhost:8000/actuator (Actuator root)")
        print("  - http://localhost:8000/actuator/health (Health endpoint)")
        print("  - http://localhost:8000/actuator/info (Info endpoint)")
        print("  - http://localhost:8000/actuator/metrics (Metrics endpoint)")
        print("  - http://localhost:8000/actuator/integration/health (Integration health)")
        print("  - http://localhost:8000/docs (API documentation)")

        print("\n🚀 Server starting on http://localhost:8000")
        print("Press Ctrl+C to stop the server")

        # 启动服务器
        config = uvicorn.Config(
            app=app,
            host="0.0.0.0",
            port=8000,
            log_level="info",
            reload=False
        )
        server = uvicorn.Server(config)
        await server.serve()

    except KeyboardInterrupt:
        print("\n🛑 Server stopped by user")
    except Exception as e:
        print(f"\n❌ Server error: {e}")
        import traceback
        traceback.print_exc()


async def test_integration_features():
    """测试集成功能"""
    print("🧪 Testing Web Actuator Integration Features...")

    try:
        # 创建应用
        app, web_config = await create_demo_application()

        # 测试集成状态
        print("\n📊 Testing Integration Status:")
        status = web_config.get_integration_status()
        assert status['status'] == 'active', "Integration should be active"
        assert status['routes_count'] > 0, "Should have registered routes"
        assert status['middleware_installed'], "Middleware should be installed"
        assert status['error_handlers_installed'], "Error handlers should be installed"
        print("✅ Integration status tests passed")

        # 测试路由重载
        print("\n🔄 Testing Route Reloading:")
        initial_routes = len(web_config.routes)
        await web_config.reload_routes()
        reloaded_routes = len(web_config.routes)
        assert reloaded_routes == initial_routes, "Route count should remain the same after reload"
        print("✅ Route reloading tests passed")

        # 测试组件导入
        print("\n📦 Testing Component Imports:")
        # DEPRECATED: from miniboot.actuator.autoconfigure.web_actuator_auto_configuration import (
        #     ActuatorMiddleware, IntegrationMetrics, RouteInfo)
        print("⚠️ Component imports deprecated - please update to use new Web auto configuration")

        print("\n🎉 All integration feature tests passed!")
        return True

    except Exception as e:
        print(f"\n❌ Integration feature tests failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def main():
    """主函数"""
    print("🎯 Mini-Boot Web Actuator Integration Demo")
    print("=" * 50)

    # 选择运行模式
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "test":
        # 测试模式
        success = await test_integration_features()
        if success:
            print("\n✅ All tests completed successfully!")
        else:
            print("\n❌ Some tests failed!")
            sys.exit(1)
    else:
        # 服务器模式
        await run_demo_server()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Goodbye!")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置注册表测试
"""

from dataclasses import dataclass
from unittest.mock import Mock

import pytest

from miniboot.annotations import Bean
from miniboot.autoconfigure import AutoConfiguration, AutoConfigurationMetadata, AutoConfigurationRegistry, ConditionalOnProperty, StarterProperties
from miniboot.context import ApplicationContext
from miniboot.errors.autoconfigure import CircularDependencyException


@dataclass
class MockTestProperties(StarterProperties):
    """测试配置属性"""

    timeout: int = 30


class MockConfigA(AutoConfiguration):
    """测试配置A"""

    def get_metadata(self):
        return AutoConfigurationMetadata(name="config-a", description="Test configuration A", priority=100)

    @Bean
    def service_a(self):
        return "ServiceA"


class MockConfigB(AutoConfiguration):
    """测试配置B - 依赖A"""

    def get_metadata(self):
        return AutoConfigurationMetadata(name="config-b", description="Test configuration B", priority=200, depends_on=["config-a"])

    @Bean
    def service_b(self):
        return "ServiceB"


class MockConfigC(AutoConfiguration):
    """测试配置C - 在A之后"""

    def get_metadata(self):
        return AutoConfigurationMetadata(name="config-c", description="Test configuration C", priority=150, auto_configure_after=["config-a"])


@ConditionalOnProperty(name="feature.enabled", having_value="true")
class MockConditionalConfig(AutoConfiguration):
    """条件配置"""

    def get_metadata(self):
        return AutoConfigurationMetadata(name="conditional-config", description="Conditional configuration")


class MockCircularConfigA(AutoConfiguration):
    """循环依赖配置A"""

    def get_metadata(self):
        return AutoConfigurationMetadata(name="circular-a", description="Circular A", depends_on=["circular-b"])


class MockCircularConfigB(AutoConfiguration):
    """循环依赖配置B"""

    def get_metadata(self):
        return AutoConfigurationMetadata(name="circular-b", description="Circular B", depends_on=["circular-a"])


class TestAutoConfigurationRegistry:
    """自动配置注册表测试"""

    def setup_method(self):
        """测试前置设置"""
        self.registry = AutoConfigurationRegistry()
        self.context = Mock(spec=ApplicationContext)
        self.env_mock = Mock()
        self.context.get_environment.return_value = self.env_mock
        self.context.get_bean_factory.return_value = Mock()

    def test_register_single_configuration(self):
        """测试注册单个配置"""
        self.registry.register(MockConfigA)

        assert "config-a" in self.registry.get_all_configurations()
        assert self.registry.get_configuration("config-a") == MockConfigA

        metadata = self.registry.get_metadata("config-a")
        assert metadata.name == "config-a"
        assert metadata.description == "Test configuration A"

    def test_register_multiple_configurations(self):
        """测试批量注册配置"""
        configs = [MockConfigA, MockConfigB, MockConfigC]
        self.registry.register_multiple(configs)

        all_configs = self.registry.get_all_configurations()
        assert len(all_configs) == 3
        assert "config-a" in all_configs
        assert "config-b" in all_configs
        assert "config-c" in all_configs

    def test_register_duplicate_configuration(self):
        """测试重复注册相同配置"""
        self.registry.register(MockConfigA)
        # 重复注册相同类应该不报错
        self.registry.register(MockConfigA)

        assert len(self.registry.get_all_configurations()) == 1

    def test_evaluate_conditions_all_enabled(self):
        """测试条件评估 - 所有配置启用"""
        self.registry.register_multiple([MockConfigA, MockConfigB])

        enabled = self.registry.evaluate_conditions(self.context)

        assert len(enabled) == 2
        assert "config-a" in enabled
        assert "config-b" in enabled

    def test_evaluate_conditions_with_conditional_config(self):
        """测试条件评估 - 包含条件配置"""
        self.registry.register_multiple([MockConfigA, MockConditionalConfig])

        # 条件不满足
        self.env_mock.get_property.return_value = "false"
        enabled = self.registry.evaluate_conditions(self.context)

        assert len(enabled) == 1
        assert "config-a" in enabled
        assert "conditional-config" not in enabled

        # 条件满足
        self.env_mock.get_property.return_value = "true"
        enabled = self.registry.evaluate_conditions(self.context)

        assert len(enabled) == 2
        assert "config-a" in enabled
        assert "conditional-config" in enabled

    def test_resolve_execution_order_simple(self):
        """测试解析执行顺序 - 简单情况"""
        self.registry.register_multiple([MockConfigA, MockConfigB, MockConfigC])

        enabled = self.registry.evaluate_conditions(self.context)
        order = self.registry.resolve_execution_order(enabled)

        # config-b依赖config-a，所以config-a应该在config-b之前
        assert order.index("config-a") < order.index("config-b")
        # config-c在config-a之后
        assert order.index("config-a") < order.index("config-c")

    def test_resolve_execution_order_by_priority(self):
        """测试按优先级排序"""
        self.registry.register_multiple([MockConfigA, MockConfigB, MockConfigC])

        enabled = self.registry.evaluate_conditions(self.context)
        order = self.registry.resolve_execution_order(enabled)

        # 在没有依赖冲突的情况下，优先级高的应该先执行
        # MockConfigB优先级200，MockConfigC优先级150，MockConfigA优先级100
        # 但由于依赖关系，实际顺序会受到影响
        assert len(order) == 3

    def test_resolve_execution_order_circular_dependency(self):
        """测试循环依赖检测"""
        self.registry.register_multiple([MockCircularConfigA, MockCircularConfigB])

        enabled = self.registry.evaluate_conditions(self.context)

        with pytest.raises(CircularDependencyException, match="Circular dependency detected"):
            self.registry.resolve_execution_order(enabled)

    def test_configure_all_success(self):
        """测试执行所有配置 - 成功情况"""
        self.registry.register_multiple([MockConfigA, MockConfigB])

        results = self.registry.configure_all(self.context)

        assert len(results) == 2
        assert results["config-a"] is True
        assert results["config-b"] is True

    def test_configure_all_with_failure(self):
        """测试执行所有配置 - 包含失败"""

        class FailingConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="failing-config", description="Failing")

            def configure(self, context):
                raise Exception("Configuration failed")

        self.registry.register_multiple([MockConfigA, FailingConfig])

        results = self.registry.configure_all(self.context)

        assert len(results) == 2
        assert results["config-a"] is True
        assert results["failing-config"] is False

    def test_get_registry_info(self):
        """测试获取注册表信息"""
        self.registry.register_multiple([MockConfigA, MockConfigB])
        self.registry.evaluate_conditions(self.context)

        info = self.registry.get_registry_info()

        assert info["total_configurations"] == 2
        assert info["enabled_configurations"] == 2
        assert "config-a" in info["configuration_names"]
        assert "config-b" in info["configuration_names"]

    def test_clear_registry(self):
        """测试清空注册表"""
        self.registry.register_multiple([MockConfigA, MockConfigB])
        self.registry.evaluate_conditions(self.context)

        assert len(self.registry.get_all_configurations()) == 2

        self.registry.clear()

        assert len(self.registry.get_all_configurations()) == 0
        assert len(self.registry.get_enabled_configurations()) == 0
        assert len(self.registry.get_execution_order()) == 0

    def test_get_nonexistent_configuration(self):
        """测试获取不存在的配置"""
        assert self.registry.get_configuration("nonexistent") is None
        assert self.registry.get_metadata("nonexistent") is None

    def test_complex_dependency_resolution(self):
        """测试复杂依赖关系解析"""

        class ConfigD(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(
                    name="config-d", description="Config D", depends_on=["config-a", "config-c"], auto_configure_before=["config-b"]
                )

        self.registry.register_multiple([MockConfigA, MockConfigB, MockConfigC, ConfigD])

        enabled = self.registry.evaluate_conditions(self.context)
        order = self.registry.resolve_execution_order(enabled)

        # 验证依赖关系
        assert order.index("config-a") < order.index("config-d")  # D依赖A
        assert order.index("config-c") < order.index("config-d")  # D依赖C
        assert order.index("config-d") < order.index("config-b")  # D在B之前
        assert order.index("config-a") < order.index("config-b")  # B依赖A

    def test_empty_registry_operations(self):
        """测试空注册表操作"""
        # 空注册表的条件评估
        enabled = self.registry.evaluate_conditions(self.context)
        assert len(enabled) == 0

        # 空注册表的执行顺序解析
        order = self.registry.resolve_execution_order(enabled)
        assert len(order) == 0

        # 空注册表的配置执行
        results = self.registry.configure_all(self.context)
        assert len(results) == 0

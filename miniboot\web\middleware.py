#!/usr/bin/env python
"""
统一中间件管理器模块

提供自适应的中间件注册、配置和执行管理功能.
支持传统模式和智能模式的自动切换.

主要功能:
- MiddlewareManager - 统一中间件管理器(自适应)
- BaseMiddleware - 中间件基类
- ResponseMiddleware - 响应处理中间件
- ResponseWrapper - 响应包装器
- 中间件注册和配置
- 智能中间件链选择(可选)
- 动态中间件优化(可选)
- 中间件执行监控(可选)
"""

import asyncio
import time
import uuid
from abc import ABC, abstractmethod
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Set

from fastapi import FastAPI, Request, Response
from fastapi.responses import JSONResponse
from loguru import logger

from ..errors import (handle_context_exceptions, performance_monitor,
                      timeout_handler)
from .properties import WebProperties
# 智能调度相关导入
from .scheduling.scheduler import TaskScheduler
from .strategy import (MiddlewareSelector, RequestContext, RequestType,
                       SecurityLevel)


class BaseMiddleware(ABC):
    """中间件基类"""

    def __init__(self, name: str):
        """初始化中间件

        Args:
            name: 中间件名称
        """
        self.name = name
        self.enabled = True

    @abstractmethod
    async def process_request(self, request: Request, call_next: Callable) -> Response:
        """处理请求

        Args:
            request: HTTP请求
            call_next: 下一个处理器

        Returns:
            HTTP响应
        """
        pass

    @abstractmethod
    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        """配置中间件

        Args:
            app: FastAPI应用实例
            properties: Web配置属性

        Returns:
            是否配置成功
        """
        pass


class ResponseMiddleware(BaseMiddleware):
    """响应处理中间件

    统一处理响应格式,添加请求ID和响应时间等信息.
    继承自 BaseMiddleware,集成到统一的中间件管理系统中.
    """

    def __init__(
        self,
        name: str = "response",
        auto_wrap_response: bool = True,
        add_request_id: bool = True,
        add_response_time: bool = True,
    ):
        """初始化响应中间件

        Args:
            name: 中间件名称
            auto_wrap_response: 是否自动包装响应
            add_request_id: 是否添加请求ID
            add_response_time: 是否添加响应时间
        """
        super().__init__(name)
        self.auto_wrap_response = auto_wrap_response
        self.add_request_id = add_request_id
        self.add_response_time = add_response_time

        logger.debug(f"ResponseMiddleware '{name}' initialized")

    @handle_context_exceptions
    @performance_monitor(slow_threshold=1.0)
    @timeout_handler(timeout_seconds=30.0, timeout_message="Middleware processing timed out")
    async def process_request(self, request: Request, call_next: Callable) -> Response:
        """处理请求

        Args:
            request: HTTP请求
            call_next: 下一个处理函数

        Returns:
            HTTP响应
        """
        # 记录开始时间
        start_time = time.time()

        # 生成请求ID
        request_id = str(uuid.uuid4())
        if self.add_request_id:
            request.state.request_id = request_id

        try:
            # 调用下一个处理器
            response = await call_next(request)

            # 计算响应时间
            response_time = time.time() - start_time

            # 处理响应
            response = await self._handle_response(request, response, request_id, response_time)

            return response

        except Exception as e:
            # 异常会被全局异常处理器处理
            raise

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        """配置中间件

        Args:
            app: FastAPI应用实例
            properties: Web配置属性

        Returns:
            是否配置成功
        """
        try:
            # 响应中间件通常不需要特殊配置
            # 可以根据 properties 调整行为

            logger.debug(f"ResponseMiddleware '{self.name}' configured")
            return True

        except Exception as e:
            logger.error(f"Failed to configure ResponseMiddleware '{self.name}': {e}")
            return False

    async def _handle_response(self, request: Request, response: Response, request_id: str, response_time: float) -> Response:
        """处理响应

        Args:
            request: HTTP请求
            response: HTTP响应
            request_id: 请求ID
            response_time: 响应时间

        Returns:
            处理后的响应
        """
        # 添加响应头
        if self.add_request_id:
            response.headers["X-Request-ID"] = request_id

        if self.add_response_time:
            response.headers["X-Response-Time"] = f"{response_time:.3f}s"

        # 自动包装响应
        if self.auto_wrap_response and isinstance(response, JSONResponse):
            response = await self._wrap_json_response(request, response, request_id, response_time)

        return response

    async def _wrap_json_response(self, request: Request, response: JSONResponse, request_id: str, response_time: float) -> JSONResponse:
        """包装JSON响应

        Args:
            request: HTTP请求
            response: JSON响应
            request_id: 请求ID
            response_time: 响应时间

        Returns:
            包装后的JSON响应
        """
        try:
            # 延迟导入避免循环依赖
            from .response import ApiResponse

            # 获取原始响应内容
            original_content = response.body

            # 如果已经是ApiResponse格式,直接返回
            if isinstance(original_content, dict) and "success" in original_content:
                return response

            # 包装为ApiResponse格式
            if response.status_code >= 400:
                # 错误响应
                api_response = ApiResponse.error(message="请求失败", code=response.status_code, data=original_content, request_id=request_id)
            else:
                # 成功响应
                api_response = ApiResponse.success(data=original_content, request_id=request_id, metadata={"response_time": f"{response_time:.3f}s"})

            # 创建新的响应
            return JSONResponse(status_code=response.status_code, content=api_response.dict(), headers=dict(response.headers))

        except Exception as e:
            logger.error(f"Failed to wrap JSON response: {e}")
            return response




class CorsMiddleware(BaseMiddleware):
    """CORS 中间件"""

    def __init__(
        self,
        name: str = "cors",
        allow_origins: List[str] = None,
        allow_methods: List[str] = None,
        allow_headers: List[str] = None,
        expose_headers: List[str] = None,
        allow_credentials: bool = False,
    ):
        super().__init__(name)
        self.allow_origins = allow_origins or ["*"]
        self.allow_methods = allow_methods or ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
        self.allow_headers = allow_headers or ["Content-Type", "Authorization", "X-Request-ID"]
        self.expose_headers = expose_headers or ["X-Request-ID", "X-Response-Time"]
        self.allow_credentials = allow_credentials

        logger.debug(f"CorsMiddleware '{name}' initialized")

    async def process_request(self, request: Request, call_next: Callable) -> Response:
        # 处理预检请求
        if request.method == "OPTIONS":
            response = Response()
            self._add_cors_headers(response)
            return response

        # 处理正常请求
        response = await call_next(request)
        self._add_cors_headers(response)
        return response

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        try:
            # 根据配置更新 CORS 设置
            if hasattr(properties, "cors") and properties.cors:
                if hasattr(properties.cors, "allowed_origins"):
                    self.allow_origins = properties.cors.allowed_origins
                if hasattr(properties.cors, "allowed_methods"):
                    self.allow_methods = properties.cors.allowed_methods
                if hasattr(properties.cors, "allowed_headers"):
                    self.allow_headers = properties.cors.allowed_headers
                if hasattr(properties.cors, "allow_credentials"):
                    self.allow_credentials = properties.cors.allow_credentials

            logger.debug(f"CorsMiddleware '{self.name}' configured")
            return True
        except Exception as e:
            logger.error(f"Failed to configure CorsMiddleware '{self.name}': {e}")
            return False

    def _add_cors_headers(self, response: Response):
        response.headers["Access-Control-Allow-Origin"] = ", ".join(self.allow_origins)
        response.headers["Access-Control-Allow-Methods"] = ", ".join(self.allow_methods)
        response.headers["Access-Control-Allow-Headers"] = ", ".join(self.allow_headers)
        response.headers["Access-Control-Expose-Headers"] = ", ".join(self.expose_headers)
        if self.allow_credentials:
            response.headers["Access-Control-Allow-Credentials"] = "true"


class CompressionMiddleware(BaseMiddleware):
    """压缩中间件

    支持 gzip 和 deflate 压缩算法，自动检测客户端支持的压缩格式。
    """

    def __init__(self, name: str = "compression", minimum_size: int = 1024, compression_level: int = 6):
        super().__init__(name)
        self.minimum_size = minimum_size
        self.compression_level = compression_level

        # 支持的压缩算法（按优先级排序）
        self.supported_encodings = ["gzip", "deflate"]

        # 可压缩的内容类型
        self.compressible_types = {
            "text/html", "text/plain", "text/css", "text/javascript",
            "application/json", "application/javascript", "application/xml",
            "text/xml", "application/rss+xml", "application/atom+xml"
        }

        logger.debug(f"CompressionMiddleware '{name}' initialized")

    async def process_request(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)

        # 检查是否需要压缩
        encoding = self._get_best_encoding(request, response)
        if encoding:
            try:
                # 执行实际的压缩
                compressed_response = await self._compress_response(response, encoding)
                return compressed_response
            except Exception as e:
                logger.warning(f"Compression failed: {e}, returning uncompressed response")
                return response

        return response

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        try:
            # 根据配置更新压缩设置
            if hasattr(properties, "compression") and properties.compression:
                if hasattr(properties.compression, "minimum_size"):
                    self.minimum_size = properties.compression.minimum_size
                if hasattr(properties.compression, "level"):
                    self.compression_level = properties.compression.level
                if hasattr(properties.compression, "compressible_types"):
                    self.compressible_types.update(properties.compression.compressible_types)

            logger.debug(f"CompressionMiddleware '{self.name}' configured")
            return True
        except Exception as e:
            logger.error(f"Failed to configure CompressionMiddleware '{self.name}': {e}")
            return False

    def _get_best_encoding(self, request: Request, response: Response) -> Optional[str]:
        """获取最佳压缩编码

        Args:
            request: HTTP请求
            response: HTTP响应

        Returns:
            最佳压缩编码，如果不需要压缩则返回None
        """
        # 检查是否已经压缩
        if response.headers.get("content-encoding"):
            return None

        # 检查内容类型是否可压缩
        content_type = response.headers.get("content-type", "").split(";")[0].strip()
        if content_type not in self.compressible_types:
            return None

        # 检查响应大小
        if hasattr(response, 'body') and response.body:
            content_length = len(response.body)
        else:
            content_length = int(response.headers.get("content-length", 0))

        if content_length < self.minimum_size:
            return None

        # 检查客户端支持的编码
        accept_encoding = request.headers.get("accept-encoding", "").lower()
        for encoding in self.supported_encodings:
            if encoding in accept_encoding:
                return encoding

        return None

    async def _compress_response(self, response: Response, encoding: str) -> Response:
        """压缩响应内容

        Args:
            response: 原始响应
            encoding: 压缩编码

        Returns:
            压缩后的响应
        """
        import gzip
        import zlib

        from fastapi.responses import Response as FastAPIResponse

        # 获取响应内容
        if hasattr(response, 'body') and response.body:
            content = response.body
        else:
            # 对于流式响应，需要特殊处理
            return response

        # 执行压缩
        if encoding == "gzip":
            compressed_content = gzip.compress(content, compresslevel=self.compression_level)
        elif encoding == "deflate":
            compressed_content = zlib.compress(content, level=self.compression_level)
        else:
            return response

        # 创建新的响应
        compressed_response = FastAPIResponse(
            content=compressed_content,
            status_code=response.status_code,
            headers=dict(response.headers),
            media_type=response.headers.get("content-type")
        )

        # 更新响应头
        compressed_response.headers["content-encoding"] = encoding
        compressed_response.headers["content-length"] = str(len(compressed_content))

        # 添加 Vary 头，告诉缓存服务器根据 Accept-Encoding 缓存不同版本
        vary_header = compressed_response.headers.get("vary", "")
        if "accept-encoding" not in vary_header.lower():
            if vary_header:
                compressed_response.headers["vary"] = f"{vary_header}, Accept-Encoding"
            else:
                compressed_response.headers["vary"] = "Accept-Encoding"

        return compressed_response


class LoggingMiddleware(BaseMiddleware):
    """详细日志中间件

    记录完整的HTTP请求和响应报文，包括头部、正文等详细信息。
    """

    def __init__(
        self,
        name: str = "logging",
        log_requests: bool = True,
        log_responses: bool = True,
        log_headers: bool = True,
        log_body: bool = True,
        log_query_params: bool = True,
        max_body_size: int = 10240,  # 10KB
        sensitive_headers: Optional[List[str]] = None
    ):
        super().__init__(name)
        self.log_requests = log_requests
        self.log_responses = log_responses
        self.log_headers = log_headers
        self.log_body = log_body
        self.log_query_params = log_query_params
        self.max_body_size = max_body_size

        # 敏感头部列表（将被脱敏处理）
        self.sensitive_headers = set(header.lower() for header in (sensitive_headers or [
            "authorization", "cookie", "set-cookie", "x-api-key", "x-auth-token"
        ]))

        logger.debug(f"LoggingMiddleware '{name}' initialized")

    async def process_request(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        request_id = getattr(request.state, 'request_id', str(uuid.uuid4())[:8])

        # 记录详细请求信息
        if self.log_requests:
            await self._log_request_details(request, request_id)

        response = await call_next(request)

        # 记录详细响应信息
        if self.log_responses:
            duration = time.time() - start_time
            await self._log_response_details(response, request_id, duration)

        return response

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        try:
            # 根据配置更新日志设置
            if hasattr(properties, "logging") and properties.logging:
                if hasattr(properties.logging, "log_requests"):
                    self.log_requests = properties.logging.log_requests
                if hasattr(properties.logging, "log_responses"):
                    self.log_responses = properties.logging.log_responses
                if hasattr(properties.logging, "log_headers"):
                    self.log_headers = properties.logging.log_headers
                if hasattr(properties.logging, "log_body"):
                    self.log_body = properties.logging.log_body
                if hasattr(properties.logging, "log_query_params"):
                    self.log_query_params = properties.logging.log_query_params
                if hasattr(properties.logging, "max_body_size"):
                    self.max_body_size = properties.logging.max_body_size

            logger.debug(f"LoggingMiddleware '{self.name}' configured")
            return True
        except Exception as e:
            logger.error(f"Failed to configure LoggingMiddleware '{self.name}': {e}")
            return False

    async def _log_request_details(self, request: Request, request_id: str):
        """记录详细的请求信息"""
        try:
            # 基本请求信息
            log_lines = [
                f"📥 [REQUEST {request_id}] {request.method} {request.url}",
                f"   Client: {request.client.host}:{request.client.port}" if request.client else "   Client: Unknown"
            ]

            # 查询参数
            if self.log_query_params and request.query_params:
                query_str = "&".join([f"{k}={v}" for k, v in request.query_params.items()])
                log_lines.append(f"   Query: {query_str}")

            # 请求头
            if self.log_headers:
                log_lines.append("   Headers:")
                for name, value in request.headers.items():
                    if name.lower() in self.sensitive_headers:
                        value = self._mask_sensitive_data(value)
                    log_lines.append(f"     {name}: {value}")

            # 请求体
            if self.log_body and request.method in ["POST", "PUT", "PATCH", "DELETE"]:
                body_content = await self._get_request_body(request)
                if body_content:
                    log_lines.append("   Body:")
                    log_lines.append(f"     {body_content}")

            # 输出所有日志行
            for line in log_lines:
                logger.info(line)

        except Exception as e:
            logger.warning(f"Failed to log request details: {e}")

    async def _log_response_details(self, response: Response, request_id: str, duration: float):
        """记录详细的响应信息"""
        try:
            # 基本响应信息
            log_lines = [
                f"📤 [RESPONSE {request_id}] {response.status_code} - {duration:.3f}s"
            ]

            # 响应头
            if self.log_headers:
                log_lines.append("   Headers:")
                for name, value in response.headers.items():
                    if name.lower() in self.sensitive_headers:
                        value = self._mask_sensitive_data(value)
                    log_lines.append(f"     {name}: {value}")

            # 响应体
            if self.log_body:
                body_content = await self._get_response_body(response)
                if body_content:
                    log_lines.append("   Body:")
                    log_lines.append(f"     {body_content}")

            # 输出所有日志行
            for line in log_lines:
                logger.info(line)

        except Exception as e:
            logger.warning(f"Failed to log response details: {e}")

    async def _get_request_body(self, request: Request) -> Optional[str]:
        """获取请求体内容"""
        try:
            # 检查是否有请求体
            if not hasattr(request, '_body'):
                # 读取请求体
                body = await request.body()
                # 重新设置请求体，以便后续处理器可以读取
                request._body = body
            else:
                body = request._body

            if not body:
                return None

            # 限制日志大小
            if len(body) > self.max_body_size:
                truncated_body = body[:self.max_body_size]
                try:
                    content = truncated_body.decode('utf-8')
                    return f"{content}... [TRUNCATED - Total size: {len(body)} bytes]"
                except UnicodeDecodeError:
                    return f"[BINARY DATA - Size: {len(body)} bytes, showing first {self.max_body_size} bytes as hex] {truncated_body.hex()}"

            # 尝试解码为文本
            try:
                return body.decode('utf-8')
            except UnicodeDecodeError:
                return f"[BINARY DATA - Size: {len(body)} bytes] {body.hex()}"

        except Exception as e:
            logger.warning(f"Failed to get request body: {e}")
            return None

    async def _get_response_body(self, response: Response) -> Optional[str]:
        """获取响应体内容"""
        try:
            # 对于不同类型的响应，处理方式不同
            if hasattr(response, 'body') and response.body:
                body = response.body

                # 限制日志大小
                if len(body) > self.max_body_size:
                    truncated_body = body[:self.max_body_size]
                    try:
                        content = truncated_body.decode('utf-8')
                        return f"{content}... [TRUNCATED - Total size: {len(body)} bytes]"
                    except UnicodeDecodeError:
                        return f"[BINARY DATA - Size: {len(body)} bytes, showing first {self.max_body_size} bytes as hex] {truncated_body.hex()}"

                # 尝试解码为文本
                try:
                    return body.decode('utf-8')
                except UnicodeDecodeError:
                    return f"[BINARY DATA - Size: {len(body)} bytes] {body.hex()}"

            return None

        except Exception as e:
            logger.warning(f"Failed to get response body: {e}")
            return None

    def _mask_sensitive_data(self, value: str) -> str:
        """脱敏处理敏感数据"""
        if not value:
            return value

        # 对于较短的值，显示前几个字符
        if len(value) <= 8:
            return "*" * len(value)

        # 对于较长的值，显示前后几个字符
        return f"{value[:3]}***{value[-3:]}"


class CustomMiddleware(BaseMiddleware):
    """自定义中间件"""

    def __init__(self, name: str, process_func: Optional[Callable] = None, configure_func: Optional[Callable] = None):
        super().__init__(name)
        self.process_func = process_func
        self.configure_func = configure_func

        logger.debug(f"CustomMiddleware '{name}' initialized")

    async def process_request(self, request: Request, call_next: Callable) -> Response:
        if self.process_func:
            return await self.process_func(request, call_next)
        else:
            return await call_next(request)

    def configure(self, app: FastAPI, properties: WebProperties) -> bool:
        try:
            if self.configure_func:
                return self.configure_func(app, properties)

            logger.debug(f"CustomMiddleware '{self.name}' configured")
            return True
        except Exception as e:
            logger.error(f"Failed to configure CustomMiddleware '{self.name}': {e}")
            return False


@dataclass
class MiddlewareInfo:
    """中间件信息"""

    name: str
    middleware: BaseMiddleware
    priority: int = 0
    enabled: bool = True
    configured: bool = False
    error: Optional[str] = None

    # 智能功能扩展(可选)
    execution_stats: Dict[str, Any] = field(default_factory=dict)
    performance_metrics: Dict[str, float] = field(default_factory=dict)
    optimization_hints: List[str] = field(default_factory=list)


@dataclass
class RequestContext:
    """请求上下文(智能模式使用)"""

    request_type: str = "unknown"
    security_level: str = "normal"
    method: str = "GET"
    path: str = "/"
    complexity_score: float = 0.0
    cache_eligible: bool = False
    compression_eligible: bool = False
    estimated_duration: float = 0.01


class MiddlewareManager:
    """统一中间件管理器

    自适应的中间件管理器,根据是否提供智能调度器自动选择工作模式:
    - 传统模式:基础的中间件注册、配置和执行
    - 智能模式:增加智能中间件链选择、动态优化和性能监控

    保持完全的API兼容性,用户无需修改现有代码.
    """

    def __init__(self, smart_scheduler: Optional[TaskScheduler] = None):
        """初始化统一中间件管理器

        Args:
            smart_scheduler: 智能调度器实例(可选)
        """
        self.smart_scheduler = smart_scheduler
        self._auto_mode = smart_scheduler is not None

        # 基础功能
        self.middlewares: dict[str, MiddlewareInfo] = {}
        self._configured = False

        # 智能功能(仅在自动模式下启用)
        if self._auto_mode:
            self._init_auto_features()
        else:
            self._init_traditional_features()

        logger.debug(f"MiddlewareManager initialized in {'auto' if self._auto_mode else 'traditional'} mode")

    def _init_traditional_features(self):
        """初始化传统模式功能"""
        # 传统模式不需要额外初始化
        pass

    def _init_auto_features(self):
        """初始化自动模式功能"""
        if not self._auto_mode:
            return

        # 中间件选择策略器
        self._middleware_selector = MiddlewareSelector()

        # 智能中间件链缓存
        self._middleware_chains: Dict[str, List[str]] = {}

        # 性能统计
        self._stats = {"total_requests": 0, "total_execution_time": 0.0, "chain_cache_hits": 0, "chain_cache_misses": 0, "optimization_count": 0}

        # 中间件性能监控
        self._performance_monitor = {}

        # 动态优化配置
        self._optimization_config = {
            "enable_chain_caching": True,
            "enable_performance_monitoring": True,
            "enable_dynamic_optimization": True,
            "cache_ttl": 300,  # 5分钟
            "optimization_threshold": 0.1,  # 10%性能提升阈值
        }

    @handle_context_exceptions
    @performance_monitor(slow_threshold=1.0)
    def register_middleware(self, middleware: BaseMiddleware) -> bool:
        """注册中间件

        Args:
            middleware: 中间件实例

        Returns:
            是否注册成功
        """
        if middleware.name in self.middlewares:
            logger.warning(f"Middleware {middleware.name} already registered")
            return False

        middleware_info = MiddlewareInfo(name=middleware.name, middleware=middleware, enabled=middleware.enabled)

        self.middlewares[middleware.name] = middleware_info

        logger.debug(f"Registered middleware: {middleware.name}")
        return True

    def unregister_middleware(self, name: str) -> bool:
        """注销中间件

        Args:
            name: 中间件名称

        Returns:
            是否注销成功
        """
        if name not in self.middlewares:
            return False

        del self.middlewares[name]

        # 清理智能功能相关数据
        if self._auto_mode:
            # 清理缓存中包含该中间件的链
            keys_to_remove = []
            for key, chain in self._middleware_chains.items():
                if name in chain:
                    keys_to_remove.append(key)
            for key in keys_to_remove:
                del self._middleware_chains[key]

            # 清理性能监控数据
            self._performance_monitor.pop(name, None)

        logger.debug(f"Unregistered middleware: {name}")
        return True

    def configure_all(self, app: FastAPI, properties: WebProperties):
        """配置所有中间件

        Args:
            app: FastAPI应用实例
            properties: Web配置属性
        """
        if self._configured:
            logger.warning("Middlewares already configured")
            return

        success_count = 0
        total_count = len(self.middlewares)

        for name, info in self.middlewares.items():
            try:
                if info.middleware.configure(app, properties):
                    info.configured = True
                    info.error = None
                    success_count += 1
                    logger.debug(f"Configured middleware: {name}")
                else:
                    info.configured = False
                    info.error = "Configuration failed"
                    logger.error(f"Failed to configure middleware: {name}")

            except Exception as e:
                info.configured = False
                info.error = str(e)
                logger.error(f"Error configuring middleware {name}: {e}")

        self._configured = True
        logger.debug(f"Configured {success_count}/{total_count} middlewares")

        # 自动模式:进行中间件链优化
        if self._auto_mode:
            asyncio.create_task(self._optimize_chains())

    def get_middleware(self, name: str) -> Optional[MiddlewareInfo]:
        """获取中间件信息

        Args:
            name: 中间件名称

        Returns:
            中间件信息,如果不存在则返回None
        """
        return self.middlewares.get(name)

    def get_all_middlewares(self) -> dict[str, MiddlewareInfo]:
        """获取所有中间件信息

        Returns:
            中间件信息字典
        """
        return self.middlewares.copy()

    def is_configured(self) -> bool:
        """检查是否已配置

        Returns:
            是否已配置
        """
        return self._configured

    def get_configuration_status(self) -> dict[str, Any]:
        """获取配置状态

        Returns:
            配置状态信息
        """
        status = {
            "configured": self._configured,
            "total_middlewares": len(self.middlewares),
            "configured_middlewares": sum(1 for info in self.middlewares.values() if info.configured),
            "failed_middlewares": [
                {"name": name, "error": info.error} for name, info in self.middlewares.items() if not info.configured and info.error
            ],
        }

        # 自动模式:添加智能功能状态
        if self._auto_mode:
            status.update(
                {
                    "auto_mode": True,
                    "cached_chains": len(self._middleware_chains),
                    "performance_stats": self._stats.copy(),
                    "optimization_config": self._optimization_config.copy(),
                }
            )
        else:
            status["intelligent_mode"] = False

        return status

    # 智能功能专用方法(仅在智能模式下可用)

    async def select_chain(self, context: RequestContext) -> List[str]:
        """选择中间件链(智能模式)

        Args:
            context: 请求上下文

        Returns:
            中间件名称列表
        """
        if not self._auto_mode:
            # 传统模式:返回所有启用的中间件
            return [name for name, info in self.middlewares.items() if info.enabled and info.configured]

        try:
            # 检查缓存
            cache_key = self._build_cache_key(context)
            if cache_key in self._middleware_chains:
                self._stats["chain_cache_hits"] += 1
                return self._middleware_chains[cache_key]

            self._stats["chain_cache_misses"] += 1

            # 使用策略模式选择中间件
            available_middlewares = self._get_available_middlewares()
            selected_chain = self._middleware_selector.select_middlewares(context, available_middlewares)

            # 缓存结果
            self._middleware_chains[cache_key] = selected_chain

            return selected_chain

        except Exception as e:
            logger.error(f"Middleware chain selection failed: {e}")
            # 返回默认链
            return [name for name, info in self.middlewares.items() if info.enabled and info.configured and name in ["logging", "cors"]]

    def _build_cache_key(self, context: RequestContext) -> str:
        """构建缓存键

        Args:
            context: 请求上下文

        Returns:
            缓存键字符串
        """
        return f"{context.request_type}_{context.security_level}_{context.method}_{context.complexity_score:.1f}"

    def _get_available_middlewares(self) -> set[str]:
        """获取可用的中间件集合

        Returns:
            可用中间件名称集合
        """
        return {name for name, info in self.middlewares.items() if info.enabled and info.configured}

    async def run_chain(self, request: Request, call_next: Callable, middleware_chain: List[str], context: RequestContext) -> Response:
        """执行中间件链

        Args:
            request: HTTP请求
            call_next: 下一个处理器
            middleware_chain: 中间件链
            context: 请求上下文

        Returns:
            HTTP响应
        """
        start_time = time.time()

        try:
            # 构建中间件执行链
            async def execute_chain(index: int = 0) -> Response:
                if index >= len(middleware_chain):
                    return await call_next(request)

                middleware_name = middleware_chain[index]
                middleware_info = self.middlewares.get(middleware_name)

                if not middleware_info or not middleware_info.enabled or not middleware_info.configured:
                    # 跳过未配置或禁用的中间件
                    return await execute_chain(index + 1)

                # 执行中间件
                middleware_start = time.time()
                try:

                    async def next_handler(req: Request) -> Response:
                        return await execute_chain(index + 1)

                    response = await middleware_info.middleware.process_request(request, next_handler)

                    # 记录性能指标(自动模式)
                    if self._auto_mode:
                        execution_time = time.time() - middleware_start
                        self._record_metrics(middleware_name, execution_time)

                    return response

                except Exception as e:
                    logger.error(f"Middleware {middleware_name} execution failed: {e}")
                    # 跳过失败的中间件,继续执行链
                    return await execute_chain(index + 1)

            response = await execute_chain()

            # 更新统计信息(自动模式)
            if self._auto_mode:
                total_time = time.time() - start_time
                self._stats["total_requests"] += 1
                self._stats["total_execution_time"] += total_time

            return response

        except Exception as e:
            logger.error(f"Middleware chain execution failed: {e}")
            # 如果整个链执行失败,直接调用下一个处理器
            return await call_next(request)

    def _record_metrics(self, middleware_name: str, execution_time: float):
        """记录中间件性能指标(自动模式)"""
        if middleware_name not in self._performance_monitor:
            self._performance_monitor[middleware_name] = {
                "total_calls": 0,
                "total_time": 0.0,
                "avg_time": 0.0,
                "max_time": 0.0,
                "min_time": float("inf"),
            }

        stats = self._performance_monitor[middleware_name]
        stats["total_calls"] += 1
        stats["total_time"] += execution_time
        stats["avg_time"] = stats["total_time"] / stats["total_calls"]
        stats["max_time"] = max(stats["max_time"], execution_time)
        stats["min_time"] = min(stats["min_time"], execution_time)

    async def _optimize_chains(self):
        """优化中间件链(自动模式)"""
        if not self._auto_mode:
            return

        try:
            # 分析性能数据
            slow_middlewares = []
            for name, stats in self._performance_monitor.items():
                if stats["avg_time"] > 0.1:  # 100ms阈值
                    slow_middlewares.append((name, stats["avg_time"]))

            if slow_middlewares:
                slow_middlewares.sort(key=lambda x: x[1], reverse=True)
                logger.info(f"Detected slow middlewares: {[name for name, _ in slow_middlewares[:3]]}")

                # 生成优化建议
                for name, avg_time in slow_middlewares[:3]:
                    if name in self.middlewares:
                        hints = self.middlewares[name].optimization_hints
                        hints.append(f"Average execution time: {avg_time:.3f}s - consider optimization")
                        if avg_time > 0.5:
                            hints.append("Consider async implementation or caching")

            self._stats["optimization_count"] += 1

        except Exception as e:
            logger.error(f"Middleware chain optimization failed: {e}")

    def get_stats(self) -> Dict[str, Any]:
        """获取性能统计信息

        Returns:
            性能统计信息字典,如果不在自动模式则返回空字典
        """
        if not self._auto_mode:
            return {}

        return {
            "global_stats": self._stats.copy(),
            "middleware_performance": self._performance_monitor.copy(),
            "cached_chains": len(self._middleware_chains),
            "optimization_config": self._optimization_config.copy(),
        }

    def get_hints(self, middleware_name: Optional[str] = None) -> Dict[str, List[str]]:
        """获取优化建议

        Args:
            middleware_name: 中间件名称,如果为None则返回所有中间件的建议

        Returns:
            优化建议字典
        """
        if not self._auto_mode:
            return {}

        if middleware_name:
            info = self.middlewares.get(middleware_name)
            if info:
                return {middleware_name: info.optimization_hints}
            return {}
        else:
            return {name: info.optimization_hints for name, info in self.middlewares.items() if info.optimization_hints}

    async def cleanup_async(self):
        """异步清理资源"""
        if self._auto_mode:
            # 清理缓存
            self._middleware_chains.clear()
            self._performance_monitor.clear()

        logger.debug("MiddlewareManager cleanup completed")


class ResponseWrapper:
    """响应包装器

    提供便捷的响应包装方法.
    """

    @staticmethod
    def wrap_success(
        data: Any, message: str = "操作成功", request_id: Optional[str] = None, metadata: Optional[Dict[str, Any]] = None
    ) -> JSONResponse:
        """包装成功响应

        Args:
            data: 响应数据
            message: 响应消息
            request_id: 请求ID
            metadata: 元数据

        Returns:
            JSON响应
        """
        # 延迟导入避免循环依赖
        from .response import ApiResponse

        api_response = ApiResponse.success(data=data, message=message, request_id=request_id, metadata=metadata)

        return JSONResponse(status_code=200, content=api_response.dict())

    @staticmethod
    def wrap_error(
        message: str, code: int = 500, error_code: Optional[str] = None, data: Optional[Any] = None, request_id: Optional[str] = None
    ) -> JSONResponse:
        """包装错误响应

        Args:
            message: 错误消息
            code: HTTP状态码
            error_code: 错误代码
            data: 错误数据
            request_id: 请求ID

        Returns:
            JSON响应
        """
        # 延迟导入避免循环依赖
        from .response import ApiResponse

        api_response = ApiResponse.error(message=message, code=code, error_code=error_code, data=data, request_id=request_id)

        return JSONResponse(status_code=code, content=api_response.dict())

    @staticmethod
    def create_response(
        response_type: str = "success",
        data: Any = None,
        message: str = None,
        code: int = None,
        request_id: Optional[str] = None,
        errors: Any = None,
        metadata: Optional[Dict[str, Any]] = None,
    ) -> JSONResponse:
        """创建统一响应

        Args:
            response_type: 响应类型 (success, error, validation_error)
            data: 响应数据
            message: 响应消息
            code: HTTP状态码
            request_id: 请求ID
            errors: 错误信息(用于validation_error)
            metadata: 元数据

        Returns:
            JSON响应
        """
        # 延迟导入避免循环依赖
        from .response import ApiResponse

        if response_type == "success":
            api_response = ApiResponse.success(data=data, message=message or "操作成功", request_id=request_id, metadata=metadata)
            status_code = code or 200
        elif response_type == "validation_error":
            api_response = ApiResponse.validation_error(errors=errors, message=message or "参数校验失败", request_id=request_id)
            status_code = 400
        else:  # error
            api_response = ApiResponse.error(message=message or "操作失败", code=code or 500, data=data, request_id=request_id)
            status_code = code or 500

        return JSONResponse(status_code=status_code, content=api_response.dict())

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Mini-Boot Actuator 模块完整端到端集成示例

作者: Python 系统架构师
描述: 展示 Mini-Boot 应用从启动到 Actuator 端点暴露的完整流程
     参考 Spring Boot 的启动模式，实现企业级监控和管理功能

功能特性:
1. 应用上下文初始化和启动流程
2. Actuator 模块自动配置机制
3. Web 框架集成（FastAPI）
4. 端点功能验证（health、info、metrics、beans）
5. 安全和监控模块集成
6. 配置验证和条件化装配

使用方式:
    python example_full_integration.py
"""

import asyncio
import json
import time
from datetime import datetime
from typing import Any, Dict, List, Optional

from loguru import logger

# Mini-Boot 核心组件导入
from miniboot.context.application import DefaultApplicationContext
from miniboot.context.auto_context import create_application
from miniboot.starters.actuator.autoconfigure.bean import \
    BeanMetricsAutoConfiguration
from miniboot.starters.actuator.autoconfigure.context import \
    ContextMetricsAutoConfiguration
from miniboot.starters.actuator.autoconfigure.env import \
    EnvMetricsAutoConfiguration
from miniboot.starters.actuator.autoconfigure.scheduler import \
    SchedulerMetricsAutoConfiguration
from miniboot.starters.actuator.autoconfigure.web import WebAutoConfiguration
# Actuator 组件导入
from miniboot.starters.actuator.configuration import \
    ActuatorStarterAutoConfiguration
from miniboot.starters.actuator.endpoints.beans import BeansEndpoint
# 端点组件导入
from miniboot.starters.actuator.endpoints.health import HealthEndpoint
from miniboot.starters.actuator.endpoints.info import InfoEndpoint
from miniboot.starters.actuator.endpoints.metrics import MetricsEndpoint
from miniboot.starters.actuator.properties import ActuatorProperties
# Web 集成组件导入
from miniboot.starters.actuator.web.integration import WebIntegrationChecker
from miniboot.starters.actuator.web.routes import ActuatorRouteRegistrar

# 尝试导入 FastAPI（可选依赖）
try:
    from fastapi import FastAPI
    FASTAPI_AVAILABLE = True
except ImportError:
    FASTAPI_AVAILABLE = False
    logger.warning("FastAPI not available, Web integration will be disabled")


class MiniBootApplication:
    """Mini-Boot 应用启动类

    模拟 Spring Boot 的 @SpringBootApplication 启动模式，
    提供完整的应用生命周期管理和 Actuator 集成功能。
    """

    def __init__(self, app_name: str = "Mini-Boot Demo Application"):
        """初始化 Mini-Boot 应用

        Args:
            app_name: 应用名称
        """
        self.app_name = app_name
        self.application_context: Optional[DefaultApplicationContext] = None
        self.fastapi_app: Optional[Any] = None
        self.actuator_properties: Optional[ActuatorProperties] = None
        self.route_registrar: Optional[ActuatorRouteRegistrar] = None

        # 启动统计信息
        self.startup_stats = {
            "start_time": None,
            "context_init_time": None,
            "actuator_config_time": None,
            "web_integration_time": None,
            "total_startup_time": None
        }

        logger.info(f"🚀 Initializing {self.app_name}")

    async def run(self) -> None:
        """运行应用 - 主入口方法

        执行完整的应用启动流程：
        1. 初始化应用上下文
        2. 配置 Actuator 模块
        3. 集成 Web 框架
        4. 验证端点功能
        5. 启动监控服务
        """
        start_time = time.time()
        self.startup_stats["start_time"] = start_time

        try:
            logger.info("=" * 80)
            logger.info(f"🌟 启动 {self.app_name}")
            logger.info("=" * 80)

            # 第一阶段：初始化应用上下文
            await self._initialize_application_context()

            # 第二阶段：配置 Actuator 模块
            await self._configure_actuator_module()

            # 第三阶段：集成 Web 框架
            await self._integrate_web_framework()

            # 第四阶段：验证端点功能
            await self._verify_endpoints()

            # 第五阶段：启动监控服务
            await self._start_monitoring_services()

            # 计算总启动时间
            total_time = time.time() - start_time
            self.startup_stats["total_startup_time"] = total_time

            logger.info("=" * 80)
            logger.info(f"🎉 {self.app_name} 启动完成！")
            logger.info(f"⏱️  总启动时间: {total_time:.2f}s")
            logger.info("=" * 80)

            # 显示启动摘要
            await self._display_startup_summary()

        except Exception as e:
            logger.error(f"❌ 应用启动失败: {e}")
            raise

    async def _initialize_application_context(self) -> None:
        """第一阶段：初始化应用上下文

        创建和启动 Mini-Boot 应用上下文，包括：
        - Bean 工厂初始化
        - 环境配置加载
        - 组件扫描和注册
        - 依赖注入容器启动
        """
        phase_start = time.time()
        logger.info("📦 第一阶段：初始化应用上下文")

        # 创建应用上下文
        logger.info("  🔧 创建 DefaultApplicationContext...")
        self.application_context = create_application(
            packages_to_scan=["miniboot.starters.actuator"],
            auto_detect=True
        )

        # 启动应用上下文
        logger.info("  🚀 启动应用上下文...")
        await self.application_context.start()

        # 验证上下文状态
        if self.application_context.is_running():
            logger.info("  ✅ 应用上下文启动成功")
        else:
            raise RuntimeError("应用上下文启动失败")

        phase_time = time.time() - phase_start
        self.startup_stats["context_init_time"] = phase_time
        logger.info(f"  ⏱️  上下文初始化耗时: {phase_time:.2f}s")

    async def _configure_actuator_module(self) -> None:
        """第二阶段：配置 Actuator 模块

        执行 Actuator 模块的自动配置，包括：
        - ActuatorProperties 配置加载
        - 条件化配置验证
        - 自动配置类注册
        - Bean 定义注册
        """
        phase_start = time.time()
        logger.info("🔧 第二阶段：配置 Actuator 模块")

        # 创建 Actuator 配置属性
        logger.info("  📋 创建 ActuatorProperties 配置...")
        self.actuator_properties = ActuatorProperties()
        self.actuator_properties.enabled = True
        self.actuator_properties.auto_start = True

        # 配置 Web 集成
        self.actuator_properties.web.enabled = True
        self.actuator_properties.web.base_path = "/actuator"
        self.actuator_properties.web.endpoints.health = True
        self.actuator_properties.web.endpoints.info = True
        self.actuator_properties.web.endpoints.metrics = True
        self.actuator_properties.web.endpoints.beans = True

        # 配置指标收集
        self.actuator_properties.metrics.enabled = True
        self.actuator_properties.metrics.collection_interval = "30s"

        # 配置安全设置
        self.actuator_properties.security.enabled = False  # 演示环境禁用安全

        logger.info("  ✅ ActuatorProperties 配置完成")

        # 执行自动配置
        await self._execute_auto_configurations()

        phase_time = time.time() - phase_start
        self.startup_stats["actuator_config_time"] = phase_time
        logger.info(f"  ⏱️  Actuator 配置耗时: {phase_time:.2f}s")

    async def _execute_auto_configurations(self) -> None:
        """执行自动配置类

        按照依赖顺序执行各个自动配置类：
        1. ActuatorStarterAutoConfiguration - 主配置
        2. BeanMetricsAutoConfiguration - Bean 指标
        3. ContextMetricsAutoConfiguration - 上下文指标
        4. EnvMetricsAutoConfiguration - 环境指标
        5. SchedulerMetricsAutoConfiguration - 调度器指标
        6. WebAutoConfiguration - Web 集成
        """
        logger.info("  🔄 执行自动配置类...")

        # 配置执行顺序（按照优先级）
        auto_configs = [
            ("ActuatorStarterAutoConfiguration", ActuatorStarterAutoConfiguration),
            ("BeanMetricsAutoConfiguration", BeanMetricsAutoConfiguration),
            ("ContextMetricsAutoConfiguration", ContextMetricsAutoConfiguration),
            ("EnvMetricsAutoConfiguration", EnvMetricsAutoConfiguration),
            ("SchedulerMetricsAutoConfiguration", SchedulerMetricsAutoConfiguration),
            ("WebAutoConfiguration", WebAutoConfiguration),
        ]

        for config_name, config_class in auto_configs:
            try:
                logger.info(f"    📌 执行 {config_name}...")
                config_instance = config_class()

                # 检查配置条件
                if hasattr(config_instance, 'should_configure'):
                    if not config_instance.should_configure(self.application_context):
                        logger.info(f"    ⏭️  {config_name} 条件不满足，跳过")
                        continue

                # 执行配置
                if hasattr(config_instance, 'configure'):
                    config_instance.configure(self.application_context)

                logger.info(f"    ✅ {config_name} 配置完成")

            except Exception as e:
                logger.warning(f"    ⚠️  {config_name} 配置失败: {e}")
                # 继续执行其他配置，不中断整个流程

        logger.info("  ✅ 自动配置执行完成")

    async def _integrate_web_framework(self) -> None:
        """第三阶段：集成 Web 框架

        集成 FastAPI Web 框架，包括：
        - FastAPI 应用创建
        - Web 集成条件检查
        - 路由注册器创建
        - Actuator 端点路由注册
        """
        phase_start = time.time()
        logger.info("🌐 第三阶段：集成 Web 框架")

        if not FASTAPI_AVAILABLE:
            logger.warning("  ⚠️  FastAPI 不可用，跳过 Web 集成")
            return

        # 创建 FastAPI 应用
        logger.info("  🔧 创建 FastAPI 应用...")
        self.fastapi_app = FastAPI(
            title=self.app_name,
            description="Mini-Boot 应用，集成 Actuator 监控功能",
            version="1.0.0",
            docs_url="/docs",
            redoc_url="/redoc"
        )

        # 检查 Web 集成条件
        logger.info("  🔍 检查 Web 集成条件...")
        web_checker = WebIntegrationChecker(self.actuator_properties)

        if not web_checker.should_enable_web_integration():
            logger.warning("  ⚠️  Web 集成条件不满足")
            return

        integration_status = web_checker.get_integration_status()
        logger.info(f"    ✅ FastAPI 可用: {integration_status.fastapi_available}")
        logger.info(f"    ✅ Web 模块启用: {integration_status.web_module_enabled}")

        # 创建路由注册器
        logger.info("  🛣️  创建 Actuator 路由注册器...")
        self.route_registrar = ActuatorRouteRegistrar(
            app=self.fastapi_app,
            actuator_context=None,  # 简化演示，实际应用中需要 ActuatorContext
            base_path=self.actuator_properties.web.base_path
        )

        # 注册 Actuator 端点路由
        await self._register_actuator_routes()

        phase_time = time.time() - phase_start
        self.startup_stats["web_integration_time"] = phase_time
        logger.info(f"  ⏱️  Web 集成耗时: {phase_time:.2f}s")

    async def _register_actuator_routes(self) -> None:
        """注册 Actuator 端点路由

        为各个 Actuator 端点注册 HTTP 路由：
        - /actuator/health - 健康检查端点
        - /actuator/info - 应用信息端点
        - /actuator/metrics - 指标收集端点
        - /actuator/beans - Bean 信息端点
        """
        logger.info("  📍 注册 Actuator 端点路由...")

        base_path = self.actuator_properties.web.base_path

        # 注册健康检查端点
        if self.actuator_properties.web.endpoints.health:
            health_endpoint = HealthEndpoint()

            @self.fastapi_app.get(f"{base_path}/health")
            async def health_check():
                """健康检查端点"""
                return await health_endpoint.health(detailed=True)

            logger.info(f"    ✅ 注册健康检查端点: GET {base_path}/health")

        # 注册应用信息端点
        if self.actuator_properties.web.endpoints.info:
            info_endpoint = InfoEndpoint()

            @self.fastapi_app.get(f"{base_path}/info")
            async def app_info():
                """应用信息端点"""
                return await info_endpoint.get_info()

            logger.info(f"    ✅ 注册应用信息端点: GET {base_path}/info")

        # 注册指标收集端点
        if self.actuator_properties.web.endpoints.metrics:
            metrics_endpoint = MetricsEndpoint()

            @self.fastapi_app.get(f"{base_path}/metrics")
            async def metrics_info():
                """指标收集端点"""
                return await metrics_endpoint.metrics()

            logger.info(f"    ✅ 注册指标收集端点: GET {base_path}/metrics")

        # 注册 Bean 信息端点
        if self.actuator_properties.web.endpoints.beans:
            beans_endpoint = BeansEndpoint(self.application_context)

            @self.fastapi_app.get(f"{base_path}/beans")
            async def beans_info():
                """Bean 信息端点"""
                return beans_endpoint.get()

            logger.info(f"    ✅ 注册 Bean 信息端点: GET {base_path}/beans")

        # 注册根端点（端点发现）
        @self.fastapi_app.get(f"{base_path}")
        async def actuator_root():
            """Actuator 根端点 - 端点发现"""
            endpoints = {}
            if self.actuator_properties.web.endpoints.health:
                endpoints["health"] = {"href": f"{base_path}/health"}
            if self.actuator_properties.web.endpoints.info:
                endpoints["info"] = {"href": f"{base_path}/info"}
            if self.actuator_properties.web.endpoints.metrics:
                endpoints["metrics"] = {"href": f"{base_path}/metrics"}
            if self.actuator_properties.web.endpoints.beans:
                endpoints["beans"] = {"href": f"{base_path}/beans"}

            return {
                "_links": {
                    "self": {"href": base_path},
                    **endpoints
                }
            }

        logger.info(f"    ✅ 注册端点发现: GET {base_path}")
        logger.info("  ✅ Actuator 端点路由注册完成")

    async def _verify_endpoints(self) -> None:
        """第四阶段：验证端点功能

        验证各个 Actuator 端点的功能是否正常：
        - 健康检查端点验证
        - 应用信息端点验证
        - 指标收集端点验证
        - Bean 信息端点验证
        """
        logger.info("🔍 第四阶段：验证端点功能")

        # 验证健康检查端点
        await self._verify_health_endpoint()

        # 验证应用信息端点
        await self._verify_info_endpoint()

        # 验证指标收集端点
        await self._verify_metrics_endpoint()

        # 验证 Bean 信息端点
        await self._verify_beans_endpoint()

        logger.info("  ✅ 所有端点功能验证完成")

    async def _verify_health_endpoint(self) -> None:
        """验证健康检查端点"""
        logger.info("  🏥 验证健康检查端点...")

        try:
            health_endpoint = HealthEndpoint()
            health_data = await health_endpoint.health(detailed=True)

            # 验证响应结构
            assert "status" in health_data, "健康检查响应缺少 status 字段"
            assert "timestamp" in health_data, "健康检查响应缺少 timestamp 字段"

            logger.info(f"    ✅ 健康状态: {health_data['status']}")
            logger.info(f"    ✅ 检查时间: {health_data['timestamp']}")

        except Exception as e:
            logger.error(f"    ❌ 健康检查端点验证失败: {e}")
            raise

    async def _verify_info_endpoint(self) -> None:
        """验证应用信息端点"""
        logger.info("  ℹ️  验证应用信息端点...")

        try:
            info_endpoint = InfoEndpoint()
            info_data = await info_endpoint.get_info()

            # 验证响应结构
            assert "app" in info_data, "应用信息响应缺少 app 字段"
            assert "timestamp" in info_data, "应用信息响应缺少 timestamp 字段"

            logger.info(f"    ✅ 应用名称: {info_data['app'].get('name', 'Unknown')}")
            logger.info(f"    ✅ 应用版本: {info_data['app'].get('version', 'Unknown')}")

        except Exception as e:
            logger.error(f"    ❌ 应用信息端点验证失败: {e}")
            raise

    async def _verify_metrics_endpoint(self) -> None:
        """验证指标收集端点"""
        logger.info("  📊 验证指标收集端点...")

        try:
            metrics_endpoint = MetricsEndpoint()
            metrics_data = await metrics_endpoint.metrics()

            # 验证响应结构
            assert "timestamp" in metrics_data, "指标响应缺少 timestamp 字段"
            assert "metrics" in metrics_data, "指标响应缺少 metrics 字段"

            metrics = metrics_data["metrics"]
            logger.info(f"    ✅ 系统指标: {list(metrics.get('system', {}).keys())}")
            logger.info(f"    ✅ 应用指标: {list(metrics.get('application', {}).keys())}")

        except Exception as e:
            logger.error(f"    ❌ 指标收集端点验证失败: {e}")
            raise

    async def _verify_beans_endpoint(self) -> None:
        """验证 Bean 信息端点"""
        logger.info("  🫘 验证 Bean 信息端点...")

        try:
            beans_endpoint = BeansEndpoint(self.application_context)
            beans_data = beans_endpoint.get()

            # 验证响应结构
            assert "timestamp" in beans_data, "Bean 信息响应缺少 timestamp 字段"

            if "contexts" in beans_data:
                contexts = beans_data["contexts"]
                if "application" in contexts:
                    app_context = contexts["application"]
                    beans_count = len(app_context.get("beans", {}))
                    logger.info(f"    ✅ Bean 总数: {beans_count}")
                else:
                    logger.info("    ✅ Bean 信息结构正常（无应用上下文）")
            else:
                logger.info("    ✅ Bean 信息结构正常（简化模式）")

        except Exception as e:
            logger.error(f"    ❌ Bean 信息端点验证失败: {e}")
            raise

    async def _start_monitoring_services(self) -> None:
        """第五阶段：启动监控服务

        启动各种监控和管理服务：
        - 性能监控服务
        - 健康检查调度器
        - 指标收集器
        - 缓存管理器
        """
        logger.info("📈 第五阶段：启动监控服务")

        # 启动性能监控
        await self._start_performance_monitoring()

        # 启动健康检查调度
        await self._start_health_monitoring()

        # 启动指标收集
        await self._start_metrics_collection()

        # 启动缓存管理
        await self._start_cache_management()

        logger.info("  ✅ 所有监控服务启动完成")

    async def _start_performance_monitoring(self) -> None:
        """启动性能监控"""
        logger.info("  ⚡ 启动性能监控服务...")

        try:
            # 模拟性能监控启动
            logger.info("    📊 初始化性能指标收集器...")
            logger.info("    🔄 启动性能数据采集任务...")
            logger.info("    ✅ 性能监控服务启动成功")

        except Exception as e:
            logger.error(f"    ❌ 性能监控服务启动失败: {e}")

    async def _start_health_monitoring(self) -> None:
        """启动健康检查调度"""
        logger.info("  🏥 启动健康检查调度器...")

        try:
            # 模拟健康检查调度启动
            logger.info("    🔍 注册健康检查指标...")
            logger.info("    ⏰ 启动定时健康检查任务...")
            logger.info("    ✅ 健康检查调度器启动成功")

        except Exception as e:
            logger.error(f"    ❌ 健康检查调度器启动失败: {e}")

    async def _start_metrics_collection(self) -> None:
        """启动指标收集"""
        logger.info("  📊 启动指标收集服务...")

        try:
            # 模拟指标收集启动
            logger.info("    📈 初始化指标注册表...")
            logger.info("    🔄 启动指标收集任务...")
            logger.info("    ✅ 指标收集服务启动成功")

        except Exception as e:
            logger.error(f"    ❌ 指标收集服务启动失败: {e}")

    async def _start_cache_management(self) -> None:
        """启动缓存管理"""
        logger.info("  🗄️  启动缓存管理服务...")

        try:
            # 模拟缓存管理启动
            logger.info("    💾 初始化缓存管理器...")
            logger.info("    🔄 启动缓存清理任务...")
            logger.info("    ✅ 缓存管理服务启动成功")

        except Exception as e:
            logger.error(f"    ❌ 缓存管理服务启动失败: {e}")

    async def _display_startup_summary(self) -> None:
        """显示启动摘要信息"""
        logger.info("")
        logger.info("📋 启动摘要信息")
        logger.info("-" * 60)

        # 应用信息
        logger.info(f"🏷️  应用名称: {self.app_name}")
        logger.info(f"🔧 应用上下文: {type(self.application_context).__name__}")
        logger.info(f"🌐 Web 框架: {'FastAPI' if FASTAPI_AVAILABLE else '未集成'}")

        # 配置信息
        if self.actuator_properties:
            logger.info(f"📍 Actuator 基础路径: {self.actuator_properties.web.base_path}")
            logger.info(f"🔒 安全模式: {'启用' if self.actuator_properties.security.enabled else '禁用'}")
            logger.info(f"📊 指标收集: {'启用' if self.actuator_properties.metrics.enabled else '禁用'}")

        # 端点信息
        logger.info("🔗 可用端点:")
        if self.actuator_properties and FASTAPI_AVAILABLE:
            base_path = self.actuator_properties.web.base_path
            if self.actuator_properties.web.endpoints.health:
                logger.info(f"   🏥 健康检查: GET {base_path}/health")
            if self.actuator_properties.web.endpoints.info:
                logger.info(f"   ℹ️  应用信息: GET {base_path}/info")
            if self.actuator_properties.web.endpoints.metrics:
                logger.info(f"   📊 指标收集: GET {base_path}/metrics")
            if self.actuator_properties.web.endpoints.beans:
                logger.info(f"   🫘 Bean 信息: GET {base_path}/beans")
            logger.info(f"   🔍 端点发现: GET {base_path}")

        # 性能统计
        logger.info("⏱️  性能统计:")
        stats = self.startup_stats
        if stats["context_init_time"]:
            logger.info(f"   📦 上下文初始化: {stats['context_init_time']:.2f}s")
        if stats["actuator_config_time"]:
            logger.info(f"   🔧 Actuator 配置: {stats['actuator_config_time']:.2f}s")
        if stats["web_integration_time"]:
            logger.info(f"   🌐 Web 集成: {stats['web_integration_time']:.2f}s")
        if stats["total_startup_time"]:
            logger.info(f"   🚀 总启动时间: {stats['total_startup_time']:.2f}s")

        logger.info("-" * 60)
        logger.info("🎉 Mini-Boot 应用启动完成，Actuator 监控功能已就绪！")

        # 如果有 FastAPI 应用，显示访问信息
        if self.fastapi_app and FASTAPI_AVAILABLE:
            logger.info("")
            logger.info("🌐 Web 服务访问信息:")
            logger.info("   📖 API 文档: http://localhost:8000/docs")
            logger.info("   📚 ReDoc 文档: http://localhost:8000/redoc")
            logger.info(f"   🔍 Actuator 端点: http://localhost:8000{self.actuator_properties.web.base_path}")


async def main():
    """主函数 - 演示完整的 Mini-Boot 应用启动流程"""
    try:
        # 创建并运行 Mini-Boot 应用
        app = MiniBootApplication("Mini-Boot Actuator 集成演示")
        await app.run()

        # 保持应用运行（演示模式）
        logger.info("")
        logger.info("🔄 应用正在运行中...")
        logger.info("💡 提示: 在实际应用中，这里会启动 Web 服务器并保持运行")
        logger.info("🛑 演示模式: 应用将在 3 秒后自动退出")

        # 等待 3 秒后退出（演示目的）
        await asyncio.sleep(3)

        logger.info("👋 演示完成，应用正常退出")

    except Exception as e:
        logger.error(f"❌ 应用运行失败: {e}")
        raise


if __name__ == "__main__":
    """程序入口点"""
    logger.info("🚀 启动 Mini-Boot Actuator 完整集成演示")
    asyncio.run(main())

"""
Bean 管理模块

提供 Mini-Boot 框架的 Bean 定义、创建、注册和依赖解析功能。

主要功能:
- Bean 定义 (BeanDefinition) - 描述 Bean 的元数据
- Bean 工厂 (BeanFactory) - Bean 实例的创建和管理
- Bean 注册表 (BeanDefinitionRegistry) - Bean 定义的注册和管理
- 依赖注入器 (DependencyInjector) - 处理 Bean 之间的依赖关系
- Bean 作用域管理 (ScopeManager) - 管理不同作用域的 Bean
- Bean 生命周期管理 (LifecycleManager) - 管理 Bean 的完整生命周期
- Bean 代理系统 (BeanProxy) - 提供透明的同步/异步适配

新架构特性:
- 统一工厂：DefaultBeanFactory 替代所有现有工厂类
- 智能代理：自动适配同步/异步环境
- 三级缓存：Spring 式的循环依赖解决方案
- 性能优化：多级缓存和智能检测机制

推荐使用方式:
    # 基础用法 - 核心API
    from miniboot.bean import (
        BeanFactory, BeanDefinition, BeanScope,
        DefaultBeanFactory, create_bean_registry
    )

    # 生命周期管理
    from miniboot.bean import (
        BeanPostProcessor, InitializingBean, DisposableBean,
        BeanNameAware, BeanFactoryAware, ApplicationContextAware
    )

    # 作用域管理
    from miniboot.bean import (
        ScopeManager, ScopeRegistry
    )

    # 高级功能 - 从专门的子模块导入
    from miniboot.bean.factory import DefaultBeanFactory
    from miniboot.bean.proxy import BeanProxy
    from miniboot.bean.lifecycle import LifecycleManager
    from miniboot.bean.scopes import ScopeRegistry

注意: 本模块导出核心公共API，高级功能请从相应子模块导入。
"""

from typing import Optional

# 导入核心异常类
from ..errors import BeanCircularDependencyError as CircularDependencyError
from ..errors import BeanCreationError
from ..errors import BeanNotFoundError as NoSuchBeanDefinitionError
from ..errors import MultipleBeanFoundError as NoUniqueBeanDefinitionError
# 导入核心接口和基类
from .base import (ApplicationContextAware, BeanDefinitionRegistry,
                   BeanFactory, BeanFactoryAware, BeanNameAware,
                   BeanPostProcessor, DisposableBean, InitializingBean,
                   Lifecycle, SmartLifecycle)
# 导入核心实现类
from .definition import (BeanDefinition, BeanScope, ConstructorArgument,
                         PropertyValue)
from .factory import DefaultBeanFactory
from .graph import DependencyGraph
from .injector import DependencyInjector
from .lifecycle import LifecycleManager
from .proxy import BeanProxy
from .registry import DefaultBeanDefinitionRegistry
from .scopes import ScopeManager, ScopeRegistry
# 导入工具类
from .utils import BeanUtils, ReflectionUtils

__all__ = [
    # === 核心Bean定义 ===
    "BeanDefinition",  # Bean元数据定义
    "BeanScope",  # Bean作用域枚举
    "PropertyValue",  # Bean属性值
    "ConstructorArgument",  # 构造函数参数

    # === 核心接口 ===
    "BeanFactory",  # Bean工厂接口
    "BeanDefinitionRegistry",  # Bean定义注册表接口

    # === 生命周期接口 ===
    "BeanPostProcessor",  # Bean后置处理器
    "InitializingBean",  # 初始化Bean接口
    "DisposableBean",  # 可销毁Bean接口
    "BeanNameAware",  # Bean名称感知接口
    "BeanFactoryAware",  # Bean工厂感知接口
    "ApplicationContextAware",  # 应用上下文感知接口
    "Lifecycle",  # 生命周期接口
    "SmartLifecycle",  # 智能生命周期接口

    # === 核心实现类 ===
    "DefaultBeanFactory",  # 默认Bean工厂实现
    "DefaultBeanDefinitionRegistry",  # 默认Bean定义注册表实现
    "DependencyInjector",  # 依赖注入器
    "LifecycleManager",  # 生命周期管理器
    "BeanProxy",  # Bean代理

    # === 作用域管理 ===
    "ScopeManager",  # 作用域管理器接口
    "ScopeRegistry",  # 作用域注册表


    # === 工具类 ===
    "DependencyGraph",  # 依赖关系图
    "BeanUtils",  # Bean工具类
    "ReflectionUtils",  # 反射工具类

    # === 核心异常类 ===
    "BeanCreationError",  # Bean创建异常(最常见)
    "NoSuchBeanDefinitionError",  # Bean不存在异常(最常见)
    "NoUniqueBeanDefinitionError",  # Bean不唯一异常
    "CircularDependencyError",  # 循环依赖异常(重要)

    # === 工厂函数 ===
    "create_bean_registry",  # 创建Bean注册表(推荐使用)
    "create_bean_factory",  # 创建Bean工厂(推荐使用)
    "create_dependency_injector",  # 创建依赖注入器
]


# === 工厂函数实现 ===


def create_bean_registry() -> DefaultBeanDefinitionRegistry:
    """创建Bean注册表 - 推荐使用

    Returns:
        DefaultBeanDefinitionRegistry: Bean定义注册表实例
    """
    return DefaultBeanDefinitionRegistry()


def create_dependency_injector(bean_factory: BeanFactory) -> DependencyInjector:
    """创建依赖注入器

    Args:
        bean_factory: Bean工厂实例

    Returns:
        DependencyInjector: 依赖注入器实例
    """
    return DependencyInjector(bean_factory)


def create_bean_factory(registry: Optional[DefaultBeanDefinitionRegistry] = None) -> DefaultBeanFactory:
    """创建Bean工厂 - 推荐使用

    Args:
        registry: Bean定义注册表，如果为None则创建默认实现

    Returns:
        DefaultBeanFactory: Bean工厂实例
    """
    if registry is None:
        registry = create_bean_registry()
    return DefaultBeanFactory(registry)







# 子模块API说明
__submodules__ = {
    "base": "基础接口和抽象类 - 定义核心接口",
    "definition": "Bean定义相关 - Bean元数据和配置",
    "factory": "Bean工厂实现 - 核心Bean创建和管理",
    "registry": "Bean注册表实现 - Bean定义的注册和管理",
    "injector": "依赖注入器 - 处理Bean之间的依赖关系",
    "lifecycle": "生命周期管理 - Bean的完整生命周期管理",
    "scopes": "作用域管理 - 不同作用域的Bean管理",
    "proxy": "Bean代理系统 - 透明的同步/异步适配",
    "utils": "工具类和异常 - 辅助功能和错误处理",
}

__version__ = "2.0.0"


# API使用指导
def get_usage_guide():
    """获取API使用指导"""
    return """
    Bean模块API使用指导 (v2.0):

    1. 基础用法(推荐):
       from miniboot.bean import (
           BeanFactory, BeanDefinition, BeanScope,
           DefaultBeanFactory, create_bean_registry
       )

       # 创建Bean工厂
       factory = DefaultBeanFactory()

       # 注册Bean定义
       definition = BeanDefinition("myBean", MyClass, BeanScope.SINGLETON)
       factory.register_bean_definition("myBean", definition)

       # 获取Bean实例
       bean = factory.get_bean("myBean")

    2. 生命周期管理:
       from miniboot.bean import (
           BeanPostProcessor, InitializingBean, DisposableBean,
           LifecycleManager
       )

       # 创建生命周期管理器
       lifecycle_manager = LifecycleManager(factory)

    3. 作用域管理:
       from miniboot.bean import (
           ScopeManager, get_scope_registry
       )

       # 获取作用域注册表
       scope_registry = ScopeRegistry()

    4. 高级功能:
       from miniboot.bean.factory import DefaultBeanFactory
       from miniboot.bean.proxy import BeanProxy
       from miniboot.bean.utils import DependencyGraph, BeanUtils

    5. 工厂函数(推荐):
       建议使用工厂函数而不是直接实例化具体类:
       - DefaultBeanFactory() 直接使用构造函数
       - create_bean_registry() 而不是 DefaultBeanDefinitionRegistry()
       - BeanProxy() 直接使用构造函数
    """


# === 便捷API函数 ===

def get_bean_info():
    """获取Bean模块信息"""
    return {
        "version": __version__,
        "architecture": "Unified Bean Factory with Smart Proxy",
        "features": [
            "统一Bean工厂 (DefaultBeanFactory)",
            "智能代理系统 (BeanProxy)",
            "三级缓存机制",
            "完整生命周期管理",
            "多种作用域支持",
            "循环依赖检测",
            "性能优化缓存"
        ],
        "modules": list(__submodules__.keys())
    }


def validate_configuration():
    """验证Bean模块配置

    Returns:
        Dict[str, Any]: 验证结果
    """
    results = {
        "valid": True,
        "errors": [],
        "warnings": []
    }

    try:
        # 检查核心模块是否可导入
        from .base import BeanFactory
        from .definition import BeanDefinition
        from .factory import DefaultBeanFactory
        from .utils import DependencyGraph

        # 检查工厂函数是否正常工作
        factory = DefaultBeanFactory()
        registry = create_bean_registry()

        results["info"] = "Bean模块配置验证通过"

    except ImportError as e:
        results["valid"] = False
        results["errors"].append(f"模块导入失败: {str(e)}")
    except Exception as e:
        results["valid"] = False
        results["errors"].append(f"配置验证失败: {str(e)}")

    return results


# === 兼容性支持 ===

# 为了向后兼容，保留一些旧的别名
BeanDefinitionError = BeanCreationError  # 兼容旧版本
BeanError = BeanCreationError  # 兼容旧版本

# 添加到__all__中
__all__.extend([
    "get_bean_info",
    "validate_configuration",
    "get_usage_guide",
    # 兼容性别名
    "BeanDefinitionError",
    "BeanError",
])

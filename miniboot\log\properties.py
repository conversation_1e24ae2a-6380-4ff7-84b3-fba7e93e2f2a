#!/usr/bin/env python
"""
* @author: cz
* @description: Logging 模块配置属性

基于 application.yml 中现有的日志配置结构,提供统一的配置属性类.
"""

from dataclasses import dataclass, field
from typing import Any, Optional


@dataclass
class ConsoleConfig:
    """控制台日志配置"""

    enabled: bool = True
    level: str = "INFO"
    colorize: bool = True
    format: str = "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <blue>Thread-{thread.id}</blue> | <cyan>{name}</cyan>:<yellow>{function}</yellow>:<white>{line}</white> - <level>{message}</level>"


@dataclass
class FileConfig:
    """文件日志配置"""

    enabled: bool = False
    path: str = "logs/app.log"
    level: str = "DEBUG"
    rotation: str = "500 MB"
    retention: str = "10 days"
    compression: str = "zip"
    format: str = "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | Thread-{thread.id} | {name}:{function}:{line} - {message}"


@dataclass
class FormatConfig:
    """日志格式配置"""

    pattern: Optional[str] = None  # 自定义格式模式,如果设置则覆盖上面的format
    color: bool = True  # 是否启用颜色(控制台)


@dataclass
class RotationConfig:
    """日志轮转配置"""

    size: str = "500 MB"  # 按大小轮转
    time: str = "10 days"  # 按时间保留


@dataclass
class LoggingProperties:
    """Logging 框架配置属性类

    基于 application.yml 中的 miniboot.logging 配置结构
    """

    # 基础配置
    level: str = "INFO"  # 全局日志级别

    # 子配置对象
    console: ConsoleConfig = field(default_factory=ConsoleConfig)
    file: FileConfig = field(default_factory=FileConfig)
    format: FormatConfig = field(default_factory=FormatConfig)
    rotation: RotationConfig = field(default_factory=RotationConfig)

    @classmethod
    def from_environment(cls, environment) -> "LoggingProperties":
        """从环境配置创建Logging配置

        基于 application.yml 中的 miniboot.logging 配置结构
        """
        return cls(
            level=environment.get_property("miniboot.logging.level", "INFO"),
            console=ConsoleConfig(
                enabled=environment.get_property("miniboot.logging.console.enabled", True),
                level=environment.get_property("miniboot.logging.console.level", environment.get_property("miniboot.logging.level", "INFO")),
                colorize=environment.get_property("miniboot.logging.console.colorize", True),
                format=environment.get_property(
                    "miniboot.logging.console.format",
                    "<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level: <8}</level> | <blue>Thread-{thread.id}</blue> | <cyan>{name}</cyan>:<yellow>{function}</yellow>:<white>{line}</white> - <level>{message}</level>",
                ),
            ),
            file=FileConfig(
                enabled=environment.get_property("miniboot.logging.file.enabled", False),
                path=environment.get_property("miniboot.logging.file.path", "logs/app.log"),
                level=environment.get_property("miniboot.logging.file.level", environment.get_property("miniboot.logging.level", "DEBUG")),
                rotation=environment.get_property("miniboot.logging.file.rotation", "500 MB"),
                retention=environment.get_property("miniboot.logging.file.retention", "10 days"),
                compression=environment.get_property("miniboot.logging.file.compression", "zip"),
                format=environment.get_property(
                    "miniboot.logging.file.format",
                    "{time:YYYY-MM-DD HH:mm:ss} | {level: <8} | Thread-{thread.id} | {name}:{function}:{line} - {message}",
                ),
            ),
            format=FormatConfig(
                pattern=environment.get_property("miniboot.logging.format.pattern", None),
                color=environment.get_property("miniboot.logging.format.color", True),
            ),
            rotation=RotationConfig(
                size=environment.get_property("miniboot.logging.rotation.size", "500 MB"),
                time=environment.get_property("miniboot.logging.rotation.time", "10 days"),
            ),
        )

    def is_enabled(self) -> bool:
        """检查Logging模块是否启用"""
        return True  # 日志模块总是启用的

    def validate(self) -> None:
        """验证配置参数"""
        # 验证日志级别
        valid_levels = {"TRACE", "DEBUG", "INFO", "SUCCESS", "WARNING", "ERROR", "CRITICAL"}
        if self.level.upper() not in valid_levels:
            raise ValueError(f"Invalid logging level: {self.level}. Must be one of {valid_levels}")

        if self.console.level.upper() not in valid_levels:
            raise ValueError(f"Invalid console logging level: {self.console.level}")

        if self.file.level.upper() not in valid_levels:
            raise ValueError(f"Invalid file logging level: {self.file.level}")

        # 验证文件路径
        if self.file.enabled and not self.file.path:
            raise ValueError("File logging path cannot be empty when file logging is enabled")

    def to_dict(self) -> dict[str, Any]:
        """将配置转换为字典格式"""
        return {
            "level": self.level,
            "console": {
                "enabled": self.console.enabled,
                "level": self.console.level,
                "colorize": self.console.colorize,
                "format": self.console.format,
            },
            "file": {
                "enabled": self.file.enabled,
                "path": self.file.path,
                "level": self.file.level,
                "rotation": self.file.rotation,
                "retention": self.file.retention,
                "compression": self.file.compression,
                "format": self.file.format,
            },
            "format": {
                "pattern": self.format.pattern,
                "color": self.format.color,
            },
            "rotation": {
                "size": self.rotation.size,
                "time": self.rotation.time,
            },
        }

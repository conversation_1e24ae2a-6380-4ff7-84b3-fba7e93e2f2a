#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置异常测试
"""

import unittest

from miniboot.errors.autoconfigure import (
    AutoConfigurationException,
    CircularDependencyException,
    ConditionEvaluationException,
    ConfigurationConflictException,
)


class TestAutoConfigurationExceptions(unittest.TestCase):
    """自动配置异常测试"""

    def test_auto_configuration_exception_basic(self):
        """测试基础自动配置异常"""
        message = "Test auto configuration error"
        exception = AutoConfigurationException(message)

        self.assertEqual(str(exception), message)
        self.assertIsNone(exception.get_configuration_name())

    def test_auto_configuration_exception_with_config_name(self):
        """测试带配置名称的自动配置异常"""
        message = "Test error"
        config_name = "test-config"
        exception = AutoConfigurationException(message, configuration_name=config_name)

        self.assertEqual(str(exception), f"Configuration '{config_name}': {message}")
        self.assertEqual(exception.get_configuration_name(), config_name)

    def test_configuration_conflict_exception(self):
        """测试配置冲突异常"""
        config1 = "config-a"
        config2 = "config-b"
        exception = ConfigurationConflictException(config1, config2)

        self.assertIn(config1, str(exception))
        self.assertIn(config2, str(exception))
        self.assertEqual(exception.get_config1(), config1)
        self.assertEqual(exception.get_config2(), config2)
        self.assertIsInstance(exception, AutoConfigurationException)

    def test_configuration_conflict_exception_with_reason(self):
        """测试带原因的配置冲突异常"""
        config1 = "config-a"
        config2 = "config-b"
        reason = "Both define the same bean"

        exception = ConfigurationConflictException(config1, config2, reason=reason)

        self.assertIn(config1, str(exception))
        self.assertIn(config2, str(exception))
        self.assertIn(reason, str(exception))
        self.assertEqual(exception.get_config1(), config1)
        self.assertEqual(exception.get_config2(), config2)
        self.assertEqual(exception.get_reason(), reason)

    def test_circular_dependency_exception(self):
        """测试循环依赖异常"""
        dependency_chain = ["config-a", "config-b", "config-c", "config-a"]
        exception = CircularDependencyException(dependency_chain)

        self.assertEqual(exception.get_dependency_chain(), dependency_chain)
        self.assertIn("config-a", str(exception))
        self.assertIn("config-b", str(exception))
        self.assertIn("config-c", str(exception))
        self.assertIsInstance(exception, AutoConfigurationException)

    def test_circular_dependency_exception_empty_chain(self):
        """测试空依赖链的循环依赖异常"""
        dependency_chain = []
        exception = CircularDependencyException(dependency_chain)

        self.assertEqual(exception.get_dependency_chain(), dependency_chain)

    def test_condition_evaluation_exception(self):
        """测试条件评估异常"""
        condition = "property:test.enabled"
        reason = "Property not found"
        exception = ConditionEvaluationException(condition, reason)

        self.assertEqual(exception.get_condition(), condition)
        self.assertEqual(exception.get_reason(), reason)
        self.assertIn(condition, str(exception))
        self.assertIn(reason, str(exception))
        self.assertIsInstance(exception, AutoConfigurationException)

    def test_exception_inheritance(self):
        """测试异常继承关系"""
        # 所有自定义异常都应该继承自AutoConfigurationException
        self.assertTrue(issubclass(ConfigurationConflictException, AutoConfigurationException))
        self.assertTrue(issubclass(CircularDependencyException, AutoConfigurationException))
        self.assertTrue(issubclass(ConditionEvaluationException, AutoConfigurationException))

        # AutoConfigurationException应该继承自Exception
        self.assertTrue(issubclass(AutoConfigurationException, Exception))

    def test_exception_str_representation(self):
        """测试异常的字符串表示"""
        # 基础异常
        base_exception = AutoConfigurationException("Base error")
        self.assertEqual(str(base_exception), "Base error")

        # 带配置名称的异常
        config_exception = AutoConfigurationException("Config error", configuration_name="test-config")
        self.assertEqual(str(config_exception), "Configuration 'test-config': Config error")

        # 配置冲突异常
        conflict_exception = ConfigurationConflictException("config1", "config2")
        self.assertIn("config1", str(conflict_exception))
        self.assertIn("config2", str(conflict_exception))

        # 循环依赖异常
        circular_exception = CircularDependencyException(["a", "b"])
        self.assertIn("a", str(circular_exception))
        self.assertIn("b", str(circular_exception))

        # 条件评估异常
        condition_exception = ConditionEvaluationException("test.condition", "evaluation failed")
        self.assertIn("test.condition", str(condition_exception))
        self.assertIn("evaluation failed", str(condition_exception))

    def test_exception_raising(self):
        """测试异常抛出"""
        # 测试各种异常都可以正常抛出和捕获
        with self.assertRaises(AutoConfigurationException):
            raise AutoConfigurationException("Test error")

        with self.assertRaises(ConfigurationConflictException):
            raise ConfigurationConflictException("config1", "config2")

        with self.assertRaises(CircularDependencyException):
            raise CircularDependencyException(["a", "b"])

        with self.assertRaises(ConditionEvaluationException):
            raise ConditionEvaluationException("condition", "reason")


if __name__ == "__main__":
    unittest.main()

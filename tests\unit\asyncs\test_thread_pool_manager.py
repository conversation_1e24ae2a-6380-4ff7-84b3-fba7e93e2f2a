#!/usr/bin/env python
"""
* @author: cz
* @description: 线程池管理器单元测试
"""

import time
import unittest
from concurrent.futures import Future

from miniboot.asyncs.pool import (ManagedThreadPool, PoolState, PoolStrategy,
                                  ThreadPoolConfig, ThreadPoolManager)


class TestThreadPoolConfig(unittest.TestCase):
    """线程池配置测试"""

    def test_default_config(self):
        """测试默认配置"""
        config = ThreadPoolConfig()

        self.assertEqual(config.core_size, 2)
        self.assertEqual(config.max_size, 4)
        self.assertEqual(config.keep_alive, 60)
        self.assertEqual(config.queue_capacity, 100)
        self.assertFalse(config.allow_core_thread_timeout)
        self.assertEqual(config.thread_name_prefix, "miniboot-async-")
        self.assertEqual(config.rejection_policy, "caller_runs")

    def test_custom_config(self):
        """测试自定义配置"""
        config = ThreadPoolConfig(
            core_size=5,
            max_size=20,
            keep_alive=120,
            queue_capacity=200,
            allow_core_thread_timeout=True,
            thread_name_prefix="test-thread",
            rejection_policy="abort",
        )

        self.assertEqual(config.core_size, 5)
        self.assertEqual(config.max_size, 20)
        self.assertEqual(config.keep_alive, 120)
        self.assertEqual(config.queue_capacity, 200)
        self.assertTrue(config.allow_core_thread_timeout)
        self.assertEqual(config.thread_name_prefix, "test-thread")
        self.assertEqual(config.rejection_policy, "abort")

    def test_config_validation(self):
        """测试配置验证"""
        # 新的 ThreadPoolConfig 是简单的 dataclass，没有内置验证
        # 验证逻辑现在在使用配置的地方进行
        config = ThreadPoolConfig(core_size=10, max_size=5)
        self.assertEqual(config.core_size, 10)
        self.assertEqual(config.max_size, 5)


class TestManagedThreadPool(unittest.TestCase):
    """托管线程池测试"""

    def setUp(self):
        """设置测试"""
        self.config = ThreadPoolConfig(
            core_size=2,
            max_size=5,
        )
        self.pool = ManagedThreadPool(self.config, name="test-pool")

    def tearDown(self):
        """清理测试"""
        if not self.pool.is_shutdown():
            self.pool.shutdown()

    def test_pool_initialization(self):
        """测试线程池初始化"""
        self.assertEqual(self.pool.name, "test-pool")
        # 延迟初始化：创建时状态为 CREATED
        self.assertEqual(self.pool.metrics.state, PoolState.CREATED)

        # 提交任务后应该初始化并变为 RUNNING
        future = self.pool.submit(lambda: "test")
        result = future.result(timeout=5)
        self.assertEqual(result, "test")

        # 现在应该是 RUNNING 状态
        self.assertEqual(self.pool.metrics.state, PoolState.RUNNING)
        self.assertEqual(self.pool.metrics.core_pool_size, 2)
        self.assertEqual(self.pool.metrics.maximum_pool_size, 5)

    def test_submit_task(self):
        """测试提交任务"""

        def test_function(x, y):
            return x + y

        future = self.pool.submit(test_function, 1, 2)
        self.assertIsInstance(future, Future)

        result = future.result(timeout=5)
        self.assertEqual(result, 3)

        # 检查指标更新
        metrics = self.pool.get_metrics()
        self.assertEqual(metrics.task_count, 1)
        self.assertEqual(metrics.completed_task_count, 1)

    def test_submit_multiple_tasks(self):
        """测试提交多个任务"""

        def test_function(x):
            time.sleep(0.1)
            return x * 2

        futures = []
        for i in range(5):
            future = self.pool.submit(test_function, i)
            futures.append(future)

        results = [future.result(timeout=5) for future in futures]
        expected = [i * 2 for i in range(5)]

        self.assertEqual(results, expected)

        # 检查指标
        metrics = self.pool.get_metrics()
        self.assertEqual(metrics.task_count, 5)
        self.assertEqual(metrics.completed_task_count, 5)

    def test_submit_task_with_exception(self):
        """测试提交异常任务"""

        def failing_function():
            raise ValueError("Test exception")

        future = self.pool.submit(failing_function)

        with self.assertRaises(ValueError):
            future.result(timeout=5)

        # 任务计数应该仍然增加
        metrics = self.pool.get_metrics()
        self.assertEqual(metrics.task_count, 1)
        self.assertEqual(metrics.completed_task_count, 1)

    def test_shutdown(self):
        """测试关闭线程池"""
        self.assertFalse(self.pool.is_shutdown())

        self.pool.shutdown()

        self.assertTrue(self.pool.is_shutdown())
        self.assertEqual(self.pool.metrics.state, PoolState.TERMINATED)

    def test_submit_after_shutdown(self):
        """测试关闭后提交任务"""
        self.pool.shutdown()

        with self.assertRaises(RuntimeError):
            self.pool.submit(lambda: None)

    def test_context_manager(self):
        """测试上下文管理器"""
        config = ThreadPoolConfig()

        with ManagedThreadPool(config, name="context-pool") as pool:
            self.assertEqual(pool.metrics.state, PoolState.RUNNING)

            future = pool.submit(lambda: "test")
            result = future.result(timeout=5)
            self.assertEqual(result, "test")

        # 退出上下文后应该自动关闭
        self.assertTrue(pool.is_shutdown())


class TestThreadPoolManager(unittest.TestCase):
    """线程池管理器测试"""

    def setUp(self):
        """设置测试"""
        # 创建新的管理器实例用于测试
        self.manager = ThreadPoolManager()
        # 清理现有池
        for pool_name in list(self.manager.list_pools()):
            self.manager.remove_pool(pool_name)
        # 重置全局指标
        self.manager._global_metrics = {"total_pools": 0, "active_pools": 0, "total_tasks": 0, "completed_tasks": 0, "failed_tasks": 0}

    def tearDown(self):
        """清理测试"""
        self.manager.shutdown_all()

    def test_singleton_pattern(self):
        """测试单例模式"""
        manager1 = ThreadPoolManager()
        manager2 = ThreadPoolManager()

        self.assertIs(manager1, manager2)

    def test_create_pool(self):
        """测试创建线程池"""
        config = ThreadPoolConfig()
        pool = self.manager.create_pool("test-pool", config)

        self.assertIsInstance(pool, ManagedThreadPool)
        self.assertEqual(pool.name, "test-pool")
        self.assertIn("test-pool", self.manager.list_pools())

    def test_create_duplicate_pool(self):
        """测试创建重复线程池"""
        config = ThreadPoolConfig()
        self.manager.create_pool("test-pool", config)

        with self.assertRaises(ValueError):
            self.manager.create_pool("test-pool", config)

    def test_get_pool(self):
        """测试获取线程池"""
        config = ThreadPoolConfig()
        created_pool = self.manager.create_pool("test-pool", config)

        retrieved_pool = self.manager.get_pool("test-pool")
        self.assertIs(created_pool, retrieved_pool)

        # 测试获取不存在的池
        self.assertIsNone(self.manager.get_pool("nonexistent"))

    def test_ensure_pool(self):
        """测试获取或创建线程池"""
        # 测试创建新池
        pool1 = self.manager.ensure_pool("new-pool")
        self.assertIsInstance(pool1, ManagedThreadPool)
        self.assertEqual(pool1.name, "new-pool")

        # 测试获取现有池
        pool2 = self.manager.ensure_pool("new-pool")
        self.assertIs(pool1, pool2)

    def test_remove_pool(self):
        """测试移除线程池"""
        config = ThreadPoolConfig()
        self.manager.create_pool("test-pool", config)

        self.assertTrue(self.manager.remove_pool("test-pool"))
        self.assertNotIn("test-pool", self.manager.list_pools())

        # 测试移除不存在的池
        self.assertFalse(self.manager.remove_pool("nonexistent"))

    def test_submit_to_pool(self):
        """测试向指定池提交任务"""
        config = ThreadPoolConfig()
        self.manager.create_pool("test-pool", config)

        future = self.manager.submit("test-pool", lambda x: x * 2, 5)
        result = future.result(timeout=5)

        self.assertEqual(result, 10)

    def test_submit_to_nonexistent_pool(self):
        """测试向不存在的池提交任务"""
        with self.assertRaises(ValueError):
            self.manager.submit("nonexistent", lambda: None)

    def test_submit_to_default(self):
        """测试向默认池提交任务"""
        future = self.manager.submit_to_default(lambda x: x + 1, 5)
        result = future.result(timeout=5)

        self.assertEqual(result, 6)
        self.assertIn("default", self.manager.list_pools())

    def test_metrics(self):
        """测试获取所有指标"""
        config = ThreadPoolConfig()
        pool = self.manager.create_pool("test-pool", config)

        # 提交一些任务
        future = pool.submit(lambda: "test")
        future.result(timeout=5)

        metrics = self.manager.metrics()

        self.assertIn("global_metrics", metrics)
        self.assertIn("pool_metrics", metrics)
        self.assertIn("test-pool", metrics["pool_metrics"])

        global_metrics = metrics["global_metrics"]
        self.assertEqual(global_metrics["total_pools"], 1)
        self.assertEqual(global_metrics["active_pools"], 1)

    def test_health_check(self):
        """测试健康检查"""
        config = ThreadPoolConfig()
        self.manager.create_pool("test-pool", config)

        health = self.manager.health_check()

        self.assertEqual(health["status"], "healthy")
        self.assertEqual(health["total_pools"], 1)
        self.assertEqual(health["healthy_pools"], 1)
        self.assertEqual(health["unhealthy_pools"], 0)

    def test_shutdown_all(self):
        """测试关闭所有池"""
        config1 = ThreadPoolConfig()
        config2 = ThreadPoolConfig()

        pool1 = self.manager.create_pool("pool1", config1)
        pool2 = self.manager.create_pool("pool2", config2)

        self.manager.shutdown_all()

        self.assertTrue(pool1.is_shutdown())
        self.assertTrue(pool2.is_shutdown())


class TestGlobalFunctions(unittest.TestCase):
    """全局函数测试"""

    def tearDown(self):
        """测试清理"""
        # 重置全局线程池管理器单例状态
        from miniboot.utils.singleton import SingletonMeta
        SingletonMeta.reset_instance(ThreadPoolManager)

    def test_get_thread_pool_manager(self):
        """测试获取全局线程池管理器"""
        manager = ThreadPoolManager()
        self.assertIsInstance(manager, ThreadPoolManager)

    def test_get_thread_pool_manager_singleton(self):
        """测试线程池管理器单例行为"""
        manager1 = ThreadPoolManager()
        manager2 = ThreadPoolManager()

        # 应该返回同一个实例
        self.assertIs(manager1, manager2)
        self.assertIsInstance(manager1, ThreadPoolManager)

    def test_submit_task(self):
        """测试提交任务便捷函数"""
        # 测试提交到默认池
        manager = ThreadPoolManager()
        future = manager.submit_to_default(lambda x: x * 3, 4)
        result = future.result(timeout=5)
        self.assertEqual(result, 12)

        # 清理
        manager.shutdown_all()


if __name__ == "__main__":
    unittest.main()

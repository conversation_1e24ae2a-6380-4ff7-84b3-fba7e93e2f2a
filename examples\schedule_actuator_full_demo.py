#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Schedule Actuator 完整功能演示

展示 Schedule Actuator 集成的完整功能，包括：
- 调度任务的创建和管理
- 通过 Actuator 端点监控任务
- 任务的暂停、恢复、删除操作
- 调度器的启动、停止、重启操作
- 实时指标监控
"""

import asyncio
import json
import time

# DEPRECATED: from miniboot.actuator.autoconfigure.schedule_actuator_auto_configuration import ScheduleActuatorAutoConfiguration


class DemoApplicationContext:
    """演示应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = DemoEnvironment()

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class DemoEnvironment:
    """演示环境配置"""

    def get_property(self, key, default=None):
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.schedule.enabled": True,
            "actuator.security.enabled": False,
        }
        return config.get(key, default)


class ScheduleActuatorDemo:
    """Schedule Actuator 演示类"""

    def __init__(self):
        self.app_context = DemoApplicationContext()
        self.schedule_config = None
        self.scheduler = None
        self.task_ids = []

    async def initialize(self):
        """初始化演示环境"""
        print("🚀 Initializing Schedule Actuator Demo...")

        # 创建调度器和任务
        await self._create_scheduler_with_tasks()

        # 创建并配置 Schedule Actuator
        self.schedule_config = ScheduleActuatorAutoConfiguration(self.app_context)
        await self.schedule_config.configure()

        print("✅ Demo environment initialized successfully!")

    async def _create_scheduler_with_tasks(self):
        """创建调度器和演示任务"""
        try:
            from miniboot.annotations.schedule import ScheduledConfig
            from miniboot.schedule.scheduler import MiniBootScheduler
            from miniboot.schedule.tasks import TaskFactory

            # 创建调度器
            self.scheduler = MiniBootScheduler()
            self.app_context.register_singleton("scheduler", self.scheduler)

            # 创建演示任务
            def heartbeat_task():
                print(f"💓 Heartbeat at {time.strftime('%H:%M:%S')}")

            def data_sync_task():
                print(f"🔄 Data sync at {time.strftime('%H:%M:%S')}")

            def cleanup_task():
                print(f"🧹 Cleanup at {time.strftime('%H:%M:%S')}")

            async def async_report_task():
                print(f"📊 Generating report at {time.strftime('%H:%M:%S')}")
                await asyncio.sleep(0.5)  # 模拟异步操作
                print(f"📋 Report completed at {time.strftime('%H:%M:%S')}")

            # 创建任务配置
            configs = [
                ScheduledConfig(fixed_rate="2s"),      # 心跳任务，每2秒
                ScheduledConfig(fixed_delay="5s"),     # 数据同步，延迟5秒
                ScheduledConfig(cron="0 */1 * * * *"), # 清理任务，每分钟
                ScheduledConfig(fixed_rate="8s")       # 异步报告任务，每8秒
            ]

            # 创建任务
            tasks = [
                TaskFactory.create_lambda_task(heartbeat_task, configs[0], "heartbeat-task"),
                TaskFactory.create_lambda_task(data_sync_task, configs[1], "data-sync-task"),
                TaskFactory.create_lambda_task(cleanup_task, configs[2], "cleanup-task"),
                TaskFactory.create_lambda_task(async_report_task, configs[3], "async-report-task")
            ]

            # 启动调度器并调度任务
            self.scheduler.start()

            for task in tasks:
                task_id = self.scheduler.schedule_task(task)
                self.task_ids.append(task_id)
                print(f"📅 Scheduled task: {task.name} (ID: {task_id})")

            print(f"✅ Created scheduler with {len(tasks)} tasks")

        except ImportError as e:
            print(f"⚠️  Schedule module not available: {e}")
            self.scheduler = None

    async def demonstrate_endpoints(self):
        """演示端点功能"""
        print("\n🔌 Demonstrating Schedule Actuator Endpoints...")

        if not self.schedule_config:
            print("❌ Schedule configuration not available")
            return

        scheduler_manager = self.schedule_config.get_scheduler_manager()
        if not scheduler_manager:
            print("❌ Scheduler not available")
            return

        # 获取端点
        # DEPRECATED: from miniboot.actuator.autoconfigure.schedule_actuator_auto_configuration import (
        # ScheduledTasksEndpoint, SchedulerEndpoint)
        tasks_endpoint = ScheduledTasksEndpoint(scheduler_manager)
        scheduler_endpoint = SchedulerEndpoint(scheduler_manager)

        # 1. 查看调度任务
        print("\n📋 1. Viewing Scheduled Tasks:")
        tasks_info = tasks_endpoint.invoke(tasks_endpoint.operations()[0].operation_type)
        print(f"   Total tasks: {tasks_info.get('summary', {}).get('total', 0)}")
        print(f"   Running tasks: {tasks_info.get('summary', {}).get('running', 0)}")
        print(f"   Scheduler status: {tasks_info.get('scheduler_status', 'unknown')}")

        # 显示任务详情
        for task in tasks_info.get('tasks', [])[:3]:  # 只显示前3个任务
            print(f"   - {task['name']}: {task['status']} (Type: {task['type']})")

        # 2. 查看调度器状态
        print("\n⚙️  2. Viewing Scheduler Status:")
        scheduler_info = scheduler_endpoint.invoke(scheduler_endpoint.operations()[0].operation_type)
        print(f"   Status: {scheduler_info.get('status', 'unknown')}")
        print(f"   State: {scheduler_info.get('state', 'unknown')}")
        print(f"   Uptime: {scheduler_info.get('uptime', 0):.2f}s")
        print(f"   Total tasks: {scheduler_info.get('total_tasks', 0)}")

        # 3. 演示任务管理操作
        if self.task_ids:
            await self._demonstrate_task_management(tasks_endpoint)

        # 4. 演示调度器管理操作
        await self._demonstrate_scheduler_management(scheduler_endpoint)

    async def _demonstrate_task_management(self, tasks_endpoint):
        """演示任务管理操作"""
        print("\n🎛️  3. Demonstrating Task Management:")

        if not self.task_ids:
            print("   No tasks available for management")
            return

        task_id = self.task_ids[0]  # 使用第一个任务进行演示

        # 暂停任务
        print(f"   ⏸️  Pausing task: {task_id}")
        pause_result = tasks_endpoint._pause_task(task_id)
        print(f"   Result: {pause_result.get('success', False)}")

        # 等待一段时间
        await asyncio.sleep(2)

        # 恢复任务
        print(f"   ▶️  Resuming task: {task_id}")
        resume_result = tasks_endpoint._resume_task(task_id)
        print(f"   Result: {resume_result.get('success', False)}")

    async def _demonstrate_scheduler_management(self, scheduler_endpoint):
        """演示调度器管理操作"""
        print("\n🎛️  4. Demonstrating Scheduler Management:")

        # 注意：这里只演示获取状态，不实际停止调度器以免影响演示
        print("   📊 Getting scheduler status...")
        status = scheduler_endpoint._get_scheduler_info()
        print(f"   Current status: {status.get('status', 'unknown')}")
        print(f"   Configuration: {json.dumps(status.get('configuration', {}), indent=4)}")

    async def monitor_metrics(self, duration=15):
        """监控指标变化"""
        print(f"\n📊 Monitoring metrics for {duration} seconds...")

        start_time = time.time()
        while time.time() - start_time < duration:
            # 刷新指标
            await self.schedule_config.refresh_metrics()

            # 获取当前状态
            status = self.schedule_config.get_integration_status()
            metrics = status.get('metrics', {})

            print(f"   [{time.strftime('%H:%M:%S')}] "
                  f"Total: {metrics.get('total_tasks', 0)}, "
                  f"Running: {metrics.get('running_tasks', 0)}, "
                  f"Uptime: {metrics.get('scheduler_uptime', 0):.1f}s")

            await asyncio.sleep(3)

        print("📊 Metrics monitoring completed")

    async def run_demo(self):
        """运行完整演示"""
        try:
            # 初始化
            await self.initialize()

            # 等待任务开始执行
            print("\n⏰ Waiting for tasks to start executing...")
            await asyncio.sleep(3)

            # 演示端点功能
            await self.demonstrate_endpoints()

            # 监控指标
            await self.monitor_metrics(duration=12)

            print("\n🎉 Demo completed successfully!")

        except Exception as e:
            print(f"❌ Demo failed: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 清理资源
            if self.scheduler and self.scheduler.is_running():
                print("\n🧹 Cleaning up resources...")
                self.scheduler.stop()
                print("✅ Scheduler stopped")


async def main():
    """主函数"""
    print("🎯 Schedule Actuator Full Demo")
    print("=" * 50)

    demo = ScheduleActuatorDemo()
    await demo.run_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

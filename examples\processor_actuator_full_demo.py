#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Processor Actuator 完整功能演示

展示 Processor Actuator 集成的完整功能，包括：
- 处理器系统的创建和管理
- 通过 Actuator 端点监控处理器
- 处理器性能分析和配置管理
- 处理器状态管理和指标收集
- 实时指标监控
"""

import asyncio
import json
import time

# DEPRECATED: from miniboot.actuator.autoconfigure.processor_actuator_auto_configuration import ProcessorActuatorAutoConfiguration


class DemoApplicationContext:
    """演示应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = DemoEnvironment()
        self._processor_manager = None

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class DemoEnvironment:
    """演示环境配置"""

    def get_property(self, key, default=None):
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.processor.enabled": True,
            "actuator.security.enabled": False,
        }
        return config.get(key, default)


class ProcessorActuatorDemo:
    """Processor Actuator 演示类"""

    def __init__(self):
        self.app_context = DemoApplicationContext()
        self.processor_config = None
        self.processor_manager = None

    async def initialize(self):
        """初始化演示环境"""
        print("🚀 Initializing Processor Actuator Demo...")

        # 创建处理器管理器和处理器
        await self._create_processor_system()

        # 创建并配置 Processor Actuator
        self.processor_config = ProcessorActuatorAutoConfiguration(self.app_context)
        await self.processor_config.configure()

        print("✅ Demo environment initialized successfully!")

    async def _create_processor_system(self):
        """创建处理器系统"""
        try:
            from miniboot.processor.base import BeanPostProcessor
            from miniboot.processor.manager import (BeanPostProcessorManager,
                                                    ProcessorConfig)

            # 创建处理器管理器
            self.processor_manager = BeanPostProcessorManager()
            self.app_context._processor_manager = self.processor_manager

            # 定义演示处理器
            class SecurityProcessor(BeanPostProcessor):
                def get_order(self) -> int:
                    return 50

                def post_process_before_initialization(self, bean, bean_name: str):
                    print(f"🔒 Security check for: {bean_name}")
                    time.sleep(0.01)  # 模拟处理时间
                    return bean

                def post_process_after_initialization(self, bean, bean_name: str):
                    print(f"🛡️  Security validated: {bean_name}")
                    return bean

            class ValidationProcessor(BeanPostProcessor):
                def get_order(self) -> int:
                    return 100

                def post_process_before_initialization(self, bean, bean_name: str):
                    print(f"✅ Validating: {bean_name}")
                    time.sleep(0.02)  # 模拟处理时间
                    return bean

                def post_process_after_initialization(self, bean, bean_name: str):
                    print(f"✔️  Validation complete: {bean_name}")
                    return bean

            class AuditProcessor(BeanPostProcessor):
                def get_order(self) -> int:
                    return 200

                def post_process_before_initialization(self, bean, bean_name: str):
                    print(f"📝 Auditing: {bean_name}")
                    time.sleep(0.005)  # 模拟处理时间
                    return bean

                def post_process_after_initialization(self, bean, bean_name: str):
                    print(f"📋 Audit logged: {bean_name}")
                    return bean

            class PerformanceProcessor(BeanPostProcessor):
                def get_order(self) -> int:
                    return 300

                def post_process_before_initialization(self, bean, bean_name: str):
                    print(f"⚡ Performance monitoring: {bean_name}")
                    time.sleep(0.015)  # 模拟处理时间
                    return bean

                def post_process_after_initialization(self, bean, bean_name: str):
                    print(f"📊 Performance tracked: {bean_name}")
                    return bean

            # 创建处理器实例
            security_processor = SecurityProcessor()
            validation_processor = ValidationProcessor()
            audit_processor = AuditProcessor()
            performance_processor = PerformanceProcessor()

            # 配置处理器
            security_config = ProcessorConfig(
                enabled=True,
                timeout_seconds=2.0,
                retry_count=1,
                error_threshold=5,
                circuit_breaker_enabled=True,
                custom_properties={"priority": "high", "category": "security"}
            )

            validation_config = ProcessorConfig(
                enabled=True,
                timeout_seconds=5.0,
                retry_count=2,
                error_threshold=3,
                circuit_breaker_enabled=False,
                custom_properties={"priority": "medium", "category": "validation"}
            )

            audit_config = ProcessorConfig(
                enabled=True,
                timeout_seconds=1.0,
                retry_count=0,
                error_threshold=10,
                circuit_breaker_enabled=False,
                custom_properties={"priority": "low", "category": "audit"}
            )

            performance_config = ProcessorConfig(
                enabled=False,  # 演示禁用状态
                timeout_seconds=3.0,
                retry_count=1,
                error_threshold=5,
                circuit_breaker_enabled=True,
                custom_properties={"priority": "medium", "category": "monitoring"}
            )

            # 注册处理器
            self.processor_manager.register(security_processor, security_config)
            self.processor_manager.register(validation_processor, validation_config)
            self.processor_manager.register(audit_processor, audit_config)
            self.processor_manager.register(performance_processor, performance_config)

            # 模拟一些 Bean 处理来生成指标
            await self._simulate_bean_processing()

            print(f"✅ Created processor system with {self.processor_manager.get_registry().count()} processors")

        except ImportError as e:
            print(f"⚠️  Processor module not available: {e}")
            self.processor_manager = None
        except Exception as e:
            print(f"❌ Failed to create processor system: {e}")
            self.processor_manager = None

    async def _simulate_bean_processing(self):
        """模拟 Bean 处理以生成指标"""
        try:
            # 模拟处理一些 Bean
            demo_beans = [
                ("userService", object()),
                ("orderRepository", object()),
                ("paymentController", object()),
                ("configurationBean", object()),
                ("cacheManager", object())
            ]

            for bean_name, bean in demo_beans:
                # 获取所有处理器并按顺序执行
                processors = self.processor_manager.get_registry().processors()
                for processor in processors:
                    processor_name = processor.__class__.__name__
                    if self.processor_manager.get_processor_state(processor_name).name == "ACTIVE":
                        try:
                            processor.post_process_before_initialization(bean, bean_name)
                            processor.post_process_after_initialization(bean, bean_name)
                        except Exception as e:
                            print(f"❌ Error in processor {processor_name}: {e}")

            print("✅ Bean processing simulation completed")

        except Exception as e:
            print(f"Failed to simulate bean processing: {e}")

    async def demonstrate_endpoints(self):
        """演示端点功能"""
        print("\n🔌 Demonstrating Processor Actuator Endpoints...")

        if not self.processor_config:
            print("❌ Processor configuration not available")
            return

        # 获取端点
        # DEPRECATED: from miniboot.actuator.autoconfigure.processor_actuator_auto_configuration import (
        # ProcessorConfigEndpoint, ProcessorManagerEndpoint,
        # ProcessorMetricsEndpoint)

        manager_endpoint = ProcessorManagerEndpoint(self.processor_manager)
        metrics_endpoint = ProcessorMetricsEndpoint(self.processor_manager)
        config_endpoint = ProcessorConfigEndpoint(self.processor_manager)

        # 1. 查看处理器管理器信息
        print("\n⚙️  1. Viewing Processor Manager Information:")
        manager_info = manager_endpoint.invoke(manager_endpoint.operations()[0].operation_type)

        print(f"   Available: {manager_info.get('available', False)}")
        print(f"   Type: {manager_info.get('type', 'unknown')}")

        registry = manager_info.get('registry', {})
        print(f"   Registry: {registry.get('total_processors', 0)} processors")
        print(f"   Processor types: {registry.get('processor_types', [])}")

        stats = manager_info.get('statistics', {})
        print(f"   Statistics:")
        print(f"     - Active: {stats.get('active_processors', 0)}")
        print(f"     - Disabled: {stats.get('disabled_processors', 0)}")
        print(f"     - Total executions: {stats.get('total_executions', 0)}")
        print(f"     - Success rate: {stats.get('success_rate', 0):.1f}%")

        # 2. 查看处理器指标
        print("\n📊 2. Viewing Processor Metrics:")
        metrics_info = metrics_endpoint.invoke(metrics_endpoint.operations()[0].operation_type)

        summary = metrics_info.get('summary', {})
        print(f"   Summary:")
        print(f"     - Total processors: {summary.get('total_processors', 0)}")
        print(f"     - Total executions: {summary.get('total_executions', 0)}")
        print(f"     - Overall success rate: {summary.get('overall_success_rate', 0):.1f}%")

        metrics = metrics_info.get('metrics', {})
        print(f"   Individual Metrics:")
        for processor_name, metric_data in list(metrics.items())[:3]:  # 只显示前3个
            print(f"     📋 {processor_name}:")
            print(f"       - Executions: {metric_data['total_executions']}")
            print(f"       - Avg time: {metric_data['average_execution_time']:.3f}s")
            print(f"       - Success rate: {metric_data['success_rate']:.1f}%")

        # 3. 查看处理器配置
        print("\n🔧 3. Viewing Processor Configurations:")
        config_info = config_endpoint.invoke(config_endpoint.operations()[0].operation_type)

        config_summary = config_info.get('summary', {})
        print(f"   Summary:")
        print(f"     - Total processors: {config_summary.get('total_processors', 0)}")
        print(f"     - Enabled: {config_summary.get('enabled_processors', 0)}")
        print(f"     - Disabled: {config_summary.get('disabled_processors', 0)}")

        configs = config_info.get('configs', {})
        print(f"   Configuration Details:")
        for processor_name, config_data in list(configs.items())[:3]:  # 只显示前3个
            print(f"     🔧 {processor_name}:")
            print(f"       - Enabled: {config_data['enabled']}")
            print(f"       - Timeout: {config_data['timeout_seconds']}s")
            print(f"       - Order: {config_data['order']}")
            print(f"       - Category: {config_data['custom_properties'].get('category', 'unknown')}")

        # 4. 演示管理操作
        await self._demonstrate_management_operations(manager_endpoint, config_endpoint)

    async def _demonstrate_management_operations(self, manager_endpoint, config_endpoint):
        """演示管理操作"""
        print("\n🎛️  4. Demonstrating Management Operations:")

        # 启用被禁用的处理器
        print("   ▶️  Enabling PerformanceProcessor...")
        enable_result = manager_endpoint._enable_processor("PerformanceProcessor")
        print(f"   Result: {enable_result.get('success', False)}")

        # 更新处理器配置
        print("   🔧 Updating SecurityProcessor configuration...")
        new_config = {
            "enabled": True,
            "timeout_seconds": 3.0,
            "retry_count": 2,
            "error_threshold": 8,
            "circuit_breaker_enabled": True,
            "custom_properties": {"priority": "critical", "category": "security", "updated": True}
        }
        update_result = config_endpoint._update_processor_config("SecurityProcessor", new_config)
        print(f"   Result: {update_result.get('success', False)}")

        # 重置处理器指标
        print("   🔄 Resetting ValidationProcessor metrics...")
        reset_result = manager_endpoint._reset_processor_metrics("ValidationProcessor")
        print(f"   Result: {reset_result.get('success', False)}")

        # 禁用处理器
        print("   ⏸️  Disabling AuditProcessor...")
        disable_result = manager_endpoint._disable_processor("AuditProcessor")
        print(f"   Result: {disable_result.get('success', False)}")

    async def monitor_metrics(self, duration=12):
        """监控指标变化"""
        print(f"\n📊 Monitoring Processor metrics for {duration} seconds...")

        start_time = time.time()
        iteration = 0

        while time.time() - start_time < duration:
            # 刷新指标
            await self.processor_config.refresh_metrics()

            # 获取当前状态
            status = self.processor_config.get_integration_status()
            metrics = status.get('metrics', {})

            print(f"   [{time.strftime('%H:%M:%S')}] "
                  f"Processors: {metrics.get('total_processors', 0)}, "
                  f"Active: {metrics.get('active_processors', 0)}, "
                  f"Executions: {metrics.get('total_executions', 0)}, "
                  f"Success Rate: {metrics.get('success_rate', 0):.1f}%")

            # 每隔几秒模拟一些处理器执行
            if iteration % 2 == 0:
                await self._simulate_additional_processing()

            iteration += 1
            await asyncio.sleep(2)

        print("📊 Metrics monitoring completed")

    async def _simulate_additional_processing(self):
        """模拟额外的处理器执行"""
        try:
            if not self.processor_manager:
                return

            # 模拟处理一个新的 Bean
            demo_bean = object()
            bean_name = f"dynamicBean_{int(time.time())}"

            processors = self.processor_manager.get_registry().processors()
            for processor in processors[:2]:  # 只处理前2个处理器
                processor_name = processor.__class__.__name__
                if self.processor_manager.get_processor_state(processor_name).name == "ACTIVE":
                    try:
                        processor.post_process_before_initialization(demo_bean, bean_name)
                        processor.post_process_after_initialization(demo_bean, bean_name)
                    except Exception:
                        pass  # 忽略错误

        except Exception:
            pass  # 忽略所有错误

    async def run_demo(self):
        """运行完整演示"""
        try:
            # 初始化
            await self.initialize()

            # 演示端点功能
            await self.demonstrate_endpoints()

            # 监控指标
            await self.monitor_metrics(duration=10)

            print("\n🎉 Demo completed successfully!")

        except Exception as e:
            print(f"❌ Demo failed: {e}")
            import traceback
            traceback.print_exc()


async def main():
    """主函数"""
    print("🎯 Processor Actuator Full Demo")
    print("=" * 50)

    demo = ProcessorActuatorDemo()
    await demo.run_demo()


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

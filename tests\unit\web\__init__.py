#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Web模块单元测试包

包含Web模块所有组件的单元测试,确保Web功能的正确性和稳定性.

测试模块:
- test_annotations.py - Web注解系统测试
- test_application.py - WebApplication核心功能测试
- test_registry.py - 控制器注册管理测试
- test_middleware.py - 中间件系统测试
- test_params.py - 参数绑定测试
- test_router.py - 路由处理测试
- test_exceptions.py - 异常处理测试
- test_response.py - 响应管理测试
- test_integration.py - Web模块集成测试
- test_performance.py - Web模块性能测试
"""

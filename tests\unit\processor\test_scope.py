#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 作用域处理器单元测试

测试作用域 Bean 后置处理器的功能，包括：
- 作用域 Bean 处理
- 作用域上下文管理
- Bean 工厂集成
- 错误处理
"""

import unittest
from unittest.mock import Mock

from miniboot.annotations.scope import ApplicationScope, RequestScope, Scope, SessionScope, WebSocketScope
from miniboot.bean.bean_definition import BeanScope
from miniboot.bean.scopes import (
    ApplicationScopeManager,
    RequestScopeManager,
    ScopeContext,
    ScopeManagerRegistry,
    SessionScopeManager,
    WebSocketScopeManager,
)
from miniboot.errors import BeanProcessingError
from miniboot.processor.base import ProcessorOrder
from miniboot.processor.scope import ScopeBeanPostProcessor, ScopedBeanFactory, create_scope_processor, create_scoped_bean_factory


class TestScopeBeanPostProcessor(unittest.TestCase):
    """作用域 Bean 后置处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.processor = ScopeBeanPostProcessor()

        # 创建模拟的作用域管理器
        self.mock_request_manager = Mock(spec=RequestScopeManager)
        self.mock_session_manager = Mock(spec=SessionScopeManager)
        self.mock_application_manager = Mock(spec=ApplicationScopeManager)
        self.mock_websocket_manager = Mock(spec=WebSocketScopeManager)

        # 创建模拟的作用域上下文
        self.mock_context = Mock(spec=ScopeContext)

        # 设置模拟的作用域注册表
        self.processor._scope_registry = Mock(spec=ScopeManagerRegistry)

    def test_processor_creation(self):
        """测试处理器创建"""
        processor = create_scope_processor()
        self.assertIsInstance(processor, ScopeBeanPostProcessor)

    def test_processor_order(self):
        """测试处理器执行顺序"""
        order = self.processor.get_order()
        self.assertEqual(order, ProcessorOrder.LIFECYCLE_PROCESSOR + 10)

    def test_supports_web_scope_bean(self):
        """测试支持 Web 作用域 Bean"""

        @RequestScope()
        class RequestService:
            pass

        @SessionScope()
        class SessionService:
            pass

        @ApplicationScope()
        class ApplicationService:
            pass

        @WebSocketScope()
        class WebSocketService:
            pass

        # 测试支持 Web 作用域 Bean
        self.assertTrue(self.processor.supports(RequestService(), "requestService"))
        self.assertTrue(self.processor.supports(SessionService(), "sessionService"))
        self.assertTrue(self.processor.supports(ApplicationService(), "applicationService"))
        self.assertTrue(self.processor.supports(WebSocketService(), "websocketService"))

    def test_not_supports_non_web_scope_bean(self):
        """测试不支持非 Web 作用域 Bean"""

        @Scope("singleton")
        class SingletonService:
            pass

        @Scope("prototype")
        class PrototypeService:
            pass

        class RegularService:
            pass

        # 测试不支持非 Web 作用域 Bean
        self.assertFalse(self.processor.supports(SingletonService(), "singletonService"))
        self.assertFalse(self.processor.supports(PrototypeService(), "prototypeService"))
        self.assertFalse(self.processor.supports(RegularService(), "regularService"))

    def test_supports_none_bean(self):
        """测试不支持 None Bean"""
        self.assertFalse(self.processor.supports(None, "nullBean"))

    def test_post_process_request_scope_bean(self):
        """测试处理请求作用域 Bean"""

        @RequestScope()
        class RequestService:
            pass

        bean = RequestService()

        # 设置模拟
        self.processor._scope_registry.get_scope_manager.return_value = self.mock_request_manager
        self.mock_request_manager.get_scope_context.return_value = self.mock_context
        self.mock_context.get_bean.return_value = None  # Bean 不存在

        # 处理 Bean
        result = self.processor.post_process_before_initialization(bean, "requestService")

        # 验证结果
        self.assertEqual(result, bean)
        self.mock_context.put_bean.assert_called_once_with("requestService", bean)

    def test_post_process_existing_bean_in_scope(self):
        """测试处理作用域中已存在的 Bean"""

        @RequestScope()
        class RequestService:
            pass

        bean = RequestService()
        existing_bean = RequestService()

        # 设置模拟
        self.processor._scope_registry.get_scope_manager.return_value = self.mock_request_manager
        self.mock_request_manager.get_scope_context.return_value = self.mock_context
        self.mock_context.get_bean.return_value = existing_bean  # Bean 已存在

        # 处理 Bean
        result = self.processor.post_process_before_initialization(bean, "requestService")

        # 验证返回已存在的 Bean
        self.assertEqual(result, existing_bean)
        self.mock_context.put_bean.assert_not_called()

    def test_post_process_no_scope_manager(self):
        """测试没有作用域管理器的情况"""

        @RequestScope()
        class RequestService:
            pass

        bean = RequestService()

        # 设置模拟：没有作用域管理器
        self.processor._scope_registry.get_scope_manager.return_value = None

        # 处理 Bean
        result = self.processor.post_process_before_initialization(bean, "requestService")

        # 验证返回原始 Bean
        self.assertEqual(result, bean)

    def test_post_process_no_scope_context(self):
        """测试没有作用域上下文的情况"""

        @RequestScope()
        class RequestService:
            pass

        bean = RequestService()

        # 设置模拟：有管理器但没有上下文
        self.processor._scope_registry.get_scope_manager.return_value = self.mock_request_manager
        self.mock_request_manager.get_scope_context.return_value = None

        # 处理 Bean
        result = self.processor.post_process_before_initialization(bean, "requestService")

        # 验证返回原始 Bean
        self.assertEqual(result, bean)

    def test_post_process_non_scoped_bean(self):
        """测试处理非作用域 Bean"""

        class RegularService:
            pass

        bean = RegularService()

        # 处理 Bean
        result = self.processor.post_process_before_initialization(bean, "regularService")

        # 验证返回原始 Bean
        self.assertEqual(result, bean)

    def test_post_process_none_bean(self):
        """测试处理 None Bean"""
        result = self.processor.post_process_before_initialization(None, "nullBean")
        self.assertIsNone(result)

    def test_post_process_already_processed_bean(self):
        """测试处理已处理的 Bean"""

        @RequestScope()
        class RequestService:
            pass

        bean = RequestService()

        # 标记为已处理
        self.processor._processed_beans.add("requestService")

        # 处理 Bean
        result = self.processor.post_process_before_initialization(bean, "requestService")

        # 验证返回原始 Bean，不进行处理
        self.assertEqual(result, bean)

    def test_post_process_after_initialization(self):
        """测试初始化后处理"""

        class TestService:
            pass

        bean = TestService()
        result = self.processor.post_process_after_initialization(bean, "testService")

        # 验证返回原始 Bean
        self.assertEqual(result, bean)

    def test_post_process_error_handling(self):
        """测试处理过程中的错误处理"""

        @RequestScope()
        class RequestService:
            pass

        bean = RequestService()

        # 设置模拟抛出异常
        self.processor._scope_registry.get_scope_manager.side_effect = Exception("Test error")

        # 处理 Bean 应该抛出 BeanProcessingError
        with self.assertRaises(BeanProcessingError) as context:
            self.processor.post_process_before_initialization(bean, "requestService")

        self.assertIn("Failed to process scope", str(context.exception))
        self.assertEqual(context.exception.get_bean_name(), "requestService")

    def test_get_scoped_bean(self):
        """测试获取作用域 Bean"""
        # 设置模拟
        test_bean = "test_instance"
        self.processor._scope_registry.get_scope_manager.return_value = self.mock_request_manager
        self.mock_request_manager.get_scope_context.return_value = self.mock_context
        self.mock_context.get_bean.return_value = test_bean

        # 获取 Bean
        result = self.processor.get_scoped_bean("testBean", BeanScope.REQUEST)

        # 验证结果
        self.assertEqual(result, test_bean)

    def test_get_scoped_bean_no_manager(self):
        """测试获取作用域 Bean - 没有管理器"""
        self.processor._scope_registry.get_scope_manager.return_value = None

        result = self.processor.get_scoped_bean("testBean", BeanScope.REQUEST)
        self.assertIsNone(result)

    def test_remove_scoped_bean(self):
        """测试移除作用域 Bean"""
        # 设置模拟
        removed_bean = "removed_instance"
        self.processor._scope_registry.get_scope_manager.return_value = self.mock_request_manager
        self.mock_request_manager.get_scope_context.return_value = self.mock_context
        self.mock_context.remove_bean.return_value = removed_bean

        # 移除 Bean
        result = self.processor.remove_scoped_bean("testBean", BeanScope.REQUEST)

        # 验证结果
        self.assertEqual(result, removed_bean)
        self.mock_context.remove_bean.assert_called_once_with("testBean")


class TestScopedBeanFactory(unittest.TestCase):
    """作用域 Bean 工厂测试类"""

    def setUp(self):
        """测试前置设置"""
        self.factory = ScopedBeanFactory()

        # 创建模拟的作用域管理器和上下文
        self.mock_manager = Mock(spec=RequestScopeManager)
        self.mock_context = Mock(spec=ScopeContext)

        # 设置模拟的作用域注册表
        self.factory._scope_registry = Mock(spec=ScopeManagerRegistry)

    def test_factory_creation(self):
        """测试工厂创建"""
        factory = create_scoped_bean_factory()
        self.assertIsInstance(factory, ScopedBeanFactory)

    def test_get_scoped_bean_existing(self):
        """测试获取已存在的作用域 Bean"""
        existing_bean = "existing_instance"

        # 设置模拟
        self.factory._scope_registry.get_scope_manager.return_value = self.mock_manager
        self.mock_manager.get_scope_context.return_value = self.mock_context
        self.mock_context.get_bean.return_value = existing_bean

        # 获取 Bean
        result = self.factory.get_scoped_bean("testBean", BeanScope.REQUEST, lambda: "new_instance")

        # 验证返回已存在的 Bean
        self.assertEqual(result, existing_bean)
        self.mock_context.put_bean.assert_not_called()

    def test_get_scoped_bean_create_new(self):
        """测试创建新的作用域 Bean"""
        new_bean = "new_instance"

        # 设置模拟
        self.factory._scope_registry.get_scope_manager.return_value = self.mock_manager
        self.mock_manager.get_scope_context.return_value = self.mock_context
        self.mock_context.get_bean.return_value = None  # Bean 不存在

        # Bean 工厂函数
        bean_factory = Mock(return_value=new_bean)

        # 获取 Bean
        result = self.factory.get_scoped_bean("testBean", BeanScope.REQUEST, bean_factory)

        # 验证创建新 Bean
        self.assertEqual(result, new_bean)
        bean_factory.assert_called_once()
        self.mock_context.put_bean.assert_called_once_with("testBean", new_bean)

    def test_get_scoped_bean_no_manager(self):
        """测试获取作用域 Bean - 没有管理器"""
        self.factory._scope_registry.get_scope_manager.return_value = None

        with self.assertRaises(Exception) as context:
            self.factory.get_scoped_bean("testBean", BeanScope.REQUEST, lambda: "new_instance")

        self.assertIn("Failed to get scoped bean", str(context.exception))

    def test_get_scoped_bean_no_context(self):
        """测试获取作用域 Bean - 没有上下文"""
        self.factory._scope_registry.get_scope_manager.return_value = self.mock_manager
        self.mock_manager.get_scope_context.return_value = None

        with self.assertRaises(Exception) as context:
            self.factory.get_scoped_bean("testBean", BeanScope.REQUEST, lambda: "new_instance")

        self.assertIn("Failed to get scoped bean", str(context.exception))

    def test_remove_scoped_bean(self):
        """测试移除作用域 Bean"""
        removed_bean = "removed_instance"

        # 设置模拟
        self.factory._scope_registry.get_scope_manager.return_value = self.mock_manager
        self.mock_manager.get_scope_context.return_value = self.mock_context
        self.mock_context.remove_bean.return_value = removed_bean

        # 移除 Bean
        result = self.factory.remove_scoped_bean("testBean", BeanScope.REQUEST)

        # 验证结果
        self.assertEqual(result, removed_bean)

    def test_clear_scope(self):
        """测试清空作用域"""
        # 设置模拟
        self.factory._scope_registry.get_scope_manager.return_value = self.mock_manager
        self.mock_manager.get_scope_context.return_value = self.mock_context
        self.mock_context.get_bean_names.return_value = {"bean1", "bean2", "bean3"}

        # 清空作用域
        self.factory.clear_scope(BeanScope.REQUEST)

        # 验证所有 Bean 都被移除
        self.assertEqual(self.mock_context.remove_bean.call_count, 3)

    def test_clear_scope_no_manager(self):
        """测试清空作用域 - 没有管理器"""
        self.factory._scope_registry.get_scope_manager.return_value = None

        # 清空作用域不应该抛出异常
        self.factory.clear_scope(BeanScope.REQUEST)

    def test_clear_scope_no_context(self):
        """测试清空作用域 - 没有上下文"""
        self.factory._scope_registry.get_scope_manager.return_value = self.mock_manager
        self.mock_manager.get_scope_context.return_value = None

        # 清空作用域不应该抛出异常
        self.factory.clear_scope(BeanScope.REQUEST)


if __name__ == "__main__":
    unittest.main()

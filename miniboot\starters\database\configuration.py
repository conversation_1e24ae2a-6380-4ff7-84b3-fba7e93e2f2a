#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: Database功能自动配置
"""

from miniboot.annotations import Bean, ConditionalOnProperty
from miniboot.autoconfigure.base import StarterAutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata

from .properties import DatabaseProperties
from .service import DatabaseService


@ConditionalOnProperty(prefix="miniboot.starters.database", name="enabled", having_value="true", match_if_missing=True)
class DatabaseAutoConfiguration(StarterAutoConfiguration):
    """Database功能自动配置

    当满足以下条件时自动配置Database功能:
    - miniboot.starters.database.enabled=true (默认为true)
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取自动配置元数据

        Returns:
            AutoConfigurationMetadata: 配置元数据
        """
        return AutoConfigurationMetadata(
            name="database-auto-configuration",
            description="Database功能自动配置",
            priority=100,
            depends_on=[],
            auto_configure_after=[],
            auto_configure_before=[],
            conditions=["miniboot.starters.database.enabled=true"],
        )

    def get_starter_name(self) -> str:
        """获取Starter名称"""
        return "miniboot-starter-database"

    def get_starter_version(self) -> str:
        """获取Starter版本"""
        return "1.0.0"

    def get_starter_description(self) -> str:
        """获取Starter描述"""
        return "Mini-Boot 数据库操作Starter,提供简单的数据库连接和操作功能"

    def get_configuration_properties_classes(self) -> list[type]:
        """获取配置属性类列表"""
        return [DatabaseProperties]

    @Bean
    def database_properties(self) -> DatabaseProperties:
        """创建Database配置属性Bean

        Returns:
            DatabaseProperties: Database配置属性实例
        """
        return DatabaseProperties()

    @Bean
    def database_service(self, database_properties: DatabaseProperties) -> DatabaseService:
        """创建Database服务Bean

        Args:
            database_properties: Database配置属性

        Returns:
            DatabaseService: Database服务实例
        """
        service = DatabaseService(database_properties)

        # 如果启用自动连接,则在创建时连接数据库
        if database_properties.auto_connect:
            service.connect()

        return service

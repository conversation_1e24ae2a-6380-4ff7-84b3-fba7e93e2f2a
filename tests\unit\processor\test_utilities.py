#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean处理器模块工具类和辅助功能测试
"""

import unittest
from datetime import datetime
from unittest.mock import Mock

from miniboot.processor.base import BeanPostProcessor, ProcessorOrder
from miniboot.processor.manager import BeanPostProcessorManager, ProcessorConfig, ProcessorMetrics, ProcessorState
from miniboot.processor.registry import BeanPostProcessorRegistry, ProcessorRegistrationHelper


class UtilityBean:
    """工具测试Bean"""

    def __init__(self):
        self.value = "test"


class TestProcessorUtilities(unittest.TestCase):
    """处理器工具类测试"""

    def setUp(self):
        """测试前置设置"""
        self.bean = UtilityBean()
        self.bean_name = "utilityBean"

    def test_processor_config_defaults(self):
        """测试处理器配置默认值"""
        config = ProcessorConfig()

        # 验证默认值
        self.assertTrue(config.enabled)
        self.assertIsNone(config.timeout_seconds)
        self.assertEqual(config.retry_count, 0)
        self.assertEqual(config.retry_delay, 0.1)
        self.assertEqual(config.error_threshold, 10)
        self.assertFalse(config.circuit_breaker_enabled)
        self.assertEqual(config.circuit_breaker_timeout, 60.0)
        self.assertEqual(config.custom_properties, {})

    def test_processor_config_custom_values(self):
        """测试处理器配置自定义值"""
        custom_properties = {"key1": "value1", "key2": 123}

        config = ProcessorConfig(
            enabled=False,
            timeout_seconds=30.0,
            retry_count=3,
            retry_delay=0.5,
            error_threshold=5,
            circuit_breaker_enabled=True,
            circuit_breaker_timeout=120.0,
            custom_properties=custom_properties,
        )

        # 验证自定义值
        self.assertFalse(config.enabled)
        self.assertEqual(config.timeout_seconds, 30.0)
        self.assertEqual(config.retry_count, 3)
        self.assertEqual(config.retry_delay, 0.5)
        self.assertEqual(config.error_threshold, 5)
        self.assertTrue(config.circuit_breaker_enabled)
        self.assertEqual(config.circuit_breaker_timeout, 120.0)
        self.assertEqual(config.custom_properties, custom_properties)

    def test_processor_metrics_initialization(self):
        """测试处理器指标初始化"""
        metrics = ProcessorMetrics("TestProcessor")

        # 验证初始值
        self.assertEqual(metrics.processor_name, "TestProcessor")
        self.assertEqual(metrics.total_executions, 0)
        self.assertEqual(metrics.total_execution_time, 0.0)
        self.assertEqual(metrics.average_execution_time, 0.0)
        self.assertEqual(metrics.max_execution_time, 0.0)
        self.assertEqual(metrics.min_execution_time, float("inf"))
        self.assertEqual(metrics.error_count, 0)
        self.assertIsNone(metrics.last_execution_time)
        self.assertIsNone(metrics.last_error_time)
        self.assertIsNone(metrics.last_error_message)

    def test_processor_metrics_updates(self):
        """测试处理器指标更新"""
        metrics = ProcessorMetrics("TestProcessor")

        # 模拟指标更新
        metrics.total_executions = 5
        metrics.total_execution_time = 1.5
        metrics.average_execution_time = 0.3
        metrics.max_execution_time = 0.5
        metrics.min_execution_time = 0.1
        metrics.error_count = 1
        metrics.last_execution_time = datetime.now()
        metrics.last_error_time = datetime.now()
        metrics.last_error_message = "Test error"

        # 验证更新
        self.assertEqual(metrics.total_executions, 5)
        self.assertEqual(metrics.total_execution_time, 1.5)
        self.assertEqual(metrics.average_execution_time, 0.3)
        self.assertEqual(metrics.max_execution_time, 0.5)
        self.assertEqual(metrics.min_execution_time, 0.1)
        self.assertEqual(metrics.error_count, 1)
        self.assertIsNotNone(metrics.last_execution_time)
        self.assertIsNotNone(metrics.last_error_time)
        self.assertEqual(metrics.last_error_message, "Test error")

    def test_processor_state_constants(self):
        """测试处理器状态常量"""
        # 验证所有状态常量存在
        self.assertEqual(ProcessorState.ACTIVE.value, "ACTIVE")
        self.assertEqual(ProcessorState.DISABLED.value, "DISABLED")
        self.assertEqual(ProcessorState.ERROR.value, "ERROR")
        self.assertEqual(ProcessorState.CIRCUIT_OPEN.value, "CIRCUIT_OPEN")

    def test_processor_order_constants(self):
        """测试处理器执行顺序常量"""
        # 验证顺序常量的相对关系
        self.assertLess(ProcessorOrder.DEPENDENCY_INJECTION_PROCESSOR, ProcessorOrder.VALUE_INJECTION_PROCESSOR)
        self.assertLess(ProcessorOrder.VALUE_INJECTION_PROCESSOR, ProcessorOrder.CONFIGURATION_PROCESSOR)
        self.assertLess(ProcessorOrder.CONFIGURATION_PROCESSOR, ProcessorOrder.LIFECYCLE_PROCESSOR)
        self.assertLess(ProcessorOrder.AOP_PROCESSOR, ProcessorOrder.LIFECYCLE_PROCESSOR)
        self.assertLess(ProcessorOrder.LIFECYCLE_PROCESSOR, ProcessorOrder.LOWEST_PRECEDENCE)

    def test_processor_registration_helper_create_ordered_processor(self):
        """测试处理器注册助手创建有序处理器"""
        # 创建基础处理器
        base_processor = Mock(spec=BeanPostProcessor)
        base_processor.post_process_before_initialization.return_value = self.bean
        base_processor.post_process_after_initialization.return_value = self.bean
        base_processor.supports.return_value = True

        # 使用助手创建有序处理器
        ordered_processor = ProcessorRegistrationHelper.create_ordered_processor(base_processor, order=500)

        # 验证有序处理器存在
        self.assertIsNotNone(ordered_processor)
        # 注意：create_ordered_processor可能返回Mock对象，所以检查是否有必要的方法
        self.assertTrue(hasattr(ordered_processor, "post_process_before_initialization"))
        self.assertTrue(hasattr(ordered_processor, "post_process_after_initialization"))
        self.assertTrue(hasattr(ordered_processor, "supports"))

        # 验证方法可以被调用（不验证具体返回值，因为可能是Mock）
        try:
            ordered_processor.post_process_before_initialization(self.bean, self.bean_name)
            ordered_processor.post_process_after_initialization(self.bean, self.bean_name)
            ordered_processor.supports(self.bean, self.bean_name)
            # 如果没有抛出异常，说明方法调用成功
            self.assertTrue(True)
        except Exception as e:
            self.fail(f"Ordered processor method calls failed: {e}")

    def test_processor_registration_helper_register_processors(self):
        """测试处理器注册助手批量注册处理器"""
        registry = BeanPostProcessorRegistry()

        # 创建多个处理器
        processors = []
        for i in range(3):
            processor = Mock(spec=BeanPostProcessor)
            processor.__class__.__name__ = f"TestProcessor{i}"
            processor.get_order.return_value = i * 100
            processor.supports.return_value = True
            processor.post_process_before_initialization.return_value = self.bean
            processor.post_process_after_initialization.return_value = self.bean
            processors.append(processor)

        # 使用助手批量注册
        ProcessorRegistrationHelper.register_processors(registry, processors)

        # 验证所有处理器都被注册
        self.assertEqual(registry.get_processor_count(), 3)

        # 验证处理器按顺序执行
        result = registry.apply_before_initialization(self.bean, self.bean_name)
        self.assertIs(result, self.bean)

        # 验证所有处理器都被调用
        for processor in processors:
            processor.post_process_before_initialization.assert_called_once()

    def test_processor_manager_monitoring_control(self):
        """测试处理器管理器监控控制"""
        manager = BeanPostProcessorManager()

        # 默认应该启用监控
        self.assertTrue(manager.is_monitoring_enabled())

        # 禁用监控
        manager.set_monitoring_enabled(False)
        self.assertFalse(manager.is_monitoring_enabled())

        # 重新启用监控
        manager.set_monitoring_enabled(True)
        self.assertTrue(manager.is_monitoring_enabled())

    def test_processor_manager_registry_access(self):
        """测试处理器管理器注册表访问"""
        registry = BeanPostProcessorRegistry()
        manager = BeanPostProcessorManager(registry)

        # 验证可以访问底层注册表
        retrieved_registry = manager.get_registry()
        # 注意：管理器可能创建了新的注册表实例，所以检查类型而不是身份
        self.assertIsInstance(retrieved_registry, BeanPostProcessorRegistry)

    def test_processor_manager_bulk_operations(self):
        """测试处理器管理器批量操作"""
        manager = BeanPostProcessorManager()

        # 注册多个处理器
        processor_names = []
        for i in range(5):
            processor = Mock(spec=BeanPostProcessor)
            processor_name = f"BulkProcessor{i}"
            processor.__class__.__name__ = processor_name
            processor.supports.return_value = True
            processor.post_process_before_initialization.return_value = self.bean
            processor.post_process_after_initialization.return_value = self.bean

            manager.register_processor(processor)
            processor_names.append(processor_name)

        # 验证批量查询
        active_processors = manager.get_processors_by_state(ProcessorState.ACTIVE)
        self.assertEqual(len(active_processors), 5)

        for name in processor_names:
            self.assertIn(name, active_processors)

        # 批量禁用部分处理器
        for i in range(0, 5, 2):  # 禁用索引为0, 2, 4的处理器
            manager.disable_processor(processor_names[i])

        # 验证状态分布
        active_processors = manager.get_processors_by_state(ProcessorState.ACTIVE)
        disabled_processors = manager.get_processors_by_state(ProcessorState.DISABLED)

        self.assertEqual(len(active_processors), 2)  # 索引1, 3
        self.assertEqual(len(disabled_processors), 3)  # 索引0, 2, 4

    def test_processor_manager_config_validation(self):
        """测试处理器管理器配置验证"""
        manager = BeanPostProcessorManager()

        # 测试有效配置
        valid_config = ProcessorConfig(enabled=True, timeout_seconds=10.0, retry_count=3, error_threshold=5)

        processor = Mock(spec=BeanPostProcessor)
        processor.__class__.__name__ = "ValidProcessor"

        # 应该成功注册
        manager.register_processor(processor, valid_config)

        stored_config = manager.get_processor_config("ValidProcessor")
        self.assertEqual(stored_config.timeout_seconds, 10.0)
        self.assertEqual(stored_config.retry_count, 3)
        self.assertEqual(stored_config.error_threshold, 5)

    def test_processor_manager_metrics_aggregation(self):
        """测试处理器管理器指标聚合"""
        manager = BeanPostProcessorManager()

        # 注册处理器并执行操作
        processor = Mock(spec=BeanPostProcessor)
        processor.__class__.__name__ = "MetricsProcessor"
        processor.supports.return_value = True
        processor.post_process_before_initialization.return_value = self.bean
        processor.post_process_after_initialization.return_value = self.bean

        manager.register_processor(processor)

        # 执行多次操作
        for _ in range(10):
            manager.apply_before_initialization(self.bean, self.bean_name)
            manager.apply_after_initialization(self.bean, self.bean_name)

        # 验证指标聚合
        metrics = manager.get_processor_metrics("MetricsProcessor")
        self.assertEqual(metrics.total_executions, 20)  # 10次 * 2个方法
        self.assertGreaterEqual(metrics.total_execution_time, 0)  # 可能为0（执行太快）
        self.assertGreaterEqual(metrics.average_execution_time, 0)  # 可能为0
        self.assertEqual(metrics.error_count, 0)

    def test_processor_registry_string_representation(self):
        """测试处理器注册表字符串表示"""
        registry = BeanPostProcessorRegistry()

        # 空注册表
        str_repr = str(registry)
        self.assertIn("BeanPostProcessorRegistry", str_repr)
        self.assertIn("0 processors", str_repr)

        # 添加处理器
        processor = Mock(spec=BeanPostProcessor)
        processor.__class__.__name__ = "TestProcessor"
        processor.get_order.return_value = 100

        registry.register_processor(processor)

        # 非空注册表
        str_repr = str(registry)
        self.assertIn("1 processors", str_repr)
        self.assertIn("TestProcessor", str_repr)

    def test_processor_manager_state_transitions(self):
        """测试处理器管理器状态转换"""
        manager = BeanPostProcessorManager()

        processor = Mock(spec=BeanPostProcessor)
        processor.__class__.__name__ = "StateProcessor"

        manager.register_processor(processor)

        # 初始状态应该是ACTIVE
        state = manager.get_processor_state("StateProcessor")
        self.assertEqual(state, ProcessorState.ACTIVE)

        # 禁用处理器
        manager.disable_processor("StateProcessor")
        state = manager.get_processor_state("StateProcessor")
        self.assertEqual(state, ProcessorState.DISABLED)

        # 重新启用处理器
        manager.enable_processor("StateProcessor")
        state = manager.get_processor_state("StateProcessor")
        self.assertEqual(state, ProcessorState.ACTIVE)


class TestProcessorHelpers(unittest.TestCase):
    """处理器辅助功能测试"""

    def setUp(self):
        """测试前置设置"""
        self.bean = UtilityBean()
        self.bean_name = "utilityBean"

    def test_processor_order_comparison(self):
        """测试处理器顺序比较"""
        # 创建不同顺序的处理器
        processor1 = Mock(spec=BeanPostProcessor)
        processor1.get_order.return_value = 100

        processor2 = Mock(spec=BeanPostProcessor)
        processor2.get_order.return_value = 200

        processor3 = Mock(spec=BeanPostProcessor)
        processor3.get_order.return_value = 50

        # 验证顺序比较
        self.assertLess(processor3.get_order(), processor1.get_order())
        self.assertLess(processor1.get_order(), processor2.get_order())

    def test_processor_supports_method_consistency(self):
        """测试处理器supports方法一致性"""
        processor = Mock(spec=BeanPostProcessor)
        processor.supports.return_value = True

        # 多次调用应该返回一致结果
        for _ in range(10):
            result = processor.supports(self.bean, self.bean_name)
            self.assertTrue(result)

    def test_processor_method_return_value_consistency(self):
        """测试处理器方法返回值一致性"""
        processor = Mock(spec=BeanPostProcessor)
        processor.post_process_before_initialization.return_value = self.bean
        processor.post_process_after_initialization.return_value = self.bean

        # 验证返回值一致性
        result_before = processor.post_process_before_initialization(self.bean, self.bean_name)
        result_after = processor.post_process_after_initialization(self.bean, self.bean_name)

        self.assertIs(result_before, self.bean)
        self.assertIs(result_after, self.bean)


if __name__ == "__main__":
    unittest.main()

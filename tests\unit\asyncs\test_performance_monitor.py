#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 可配置性能监控器测试 - 验证FIX-2.2.1修复的性能优化
"""

import time
import unittest

from miniboot.asyncs.metrics import (ConfigurablePerformanceMonitor,
                                     LightweightMetrics, PerformanceConfig)

# 注意：default_config, dev_config, prod_config 已被删除，使用 PerformanceConfig 直接创建


class PerformanceMonitorTestCase(unittest.TestCase):
    """可配置性能监控器测试"""

    def setUp(self):
        """设置测试环境"""
        # 重置单例监控器
        from miniboot.asyncs.base import AsyncSingletonMeta
        from miniboot.asyncs.metrics import ConfigurablePerformanceMonitor

        AsyncSingletonMeta.reset_instance(ConfigurablePerformanceMonitor)

    def tearDown(self):
        """清理测试环境"""
        # 重置单例监控器
        from miniboot.asyncs.base import AsyncSingletonMeta
        from miniboot.asyncs.metrics import ConfigurablePerformanceMonitor

        AsyncSingletonMeta.reset_instance(ConfigurablePerformanceMonitor)

    def test_performance_config_creation(self):
        """测试性能配置创建"""
        # 默认配置
        default_cfg = PerformanceConfig(enabled=False)
        self.assertFalse(default_cfg.enabled)
        self.assertTrue(default_cfg.collect_execution_time)
        self.assertTrue(default_cfg.batch_update)

        # 开发环境配置
        dev_cfg = PerformanceConfig(
            enabled=True,
            enable_detailed_history=True,
            batch_update=False
        )
        self.assertTrue(dev_cfg.enabled)
        self.assertTrue(dev_cfg.enable_detailed_history)
        self.assertFalse(dev_cfg.batch_update)

        # 生产环境配置
        prod_cfg = PerformanceConfig(
            enabled=True,
            sampling_enabled=True,
            sampling_rate=0.05,
            batch_update=True
        )
        self.assertTrue(prod_cfg.enabled)
        self.assertTrue(prod_cfg.sampling_enabled)
        self.assertEqual(prod_cfg.sampling_rate, 0.05)
        self.assertTrue(prod_cfg.batch_update)

    def test_lightweight_metrics(self):
        """测试轻量级指标"""
        metrics = LightweightMetrics()

        # 初始状态
        self.assertEqual(metrics.total_executions, 0)
        self.assertEqual(metrics.successful_executions, 0)
        self.assertEqual(metrics.failed_executions, 0)

        # 更新指标
        metrics.total_executions = 10
        metrics.successful_executions = 8
        metrics.failed_executions = 2
        metrics.total_execution_time = 5.0

        # 测试缓存的计算
        avg_time = metrics.avg_time()
        self.assertEqual(avg_time, 5.0 / 8)

        success_rate = metrics.success_rate()
        self.assertEqual(success_rate, 0.8)

        # 测试缓存命中
        avg_time2 = metrics.avg_time()
        self.assertEqual(avg_time, avg_time2)

    def test_monitor_disabled_by_default(self):
        """测试监控器默认禁用"""
        monitor = ConfigurablePerformanceMonitor()
        # 重置为默认配置
        monitor.config_update(PerformanceConfig())
        self.assertFalse(monitor.is_enabled())

        # 禁用时不应该收集样本
        self.assertFalse(monitor.should_collect_sample())

        # 禁用时获取指标应该返回None
        metrics = monitor.executor_metrics("test")
        self.assertIsNone(metrics)

    def test_monitor_enabled_configuration(self):
        """测试启用监控器配置"""
        monitor = ConfigurablePerformanceMonitor()
        config = PerformanceConfig(enabled=True)
        monitor.config_update(config)

        self.assertTrue(monitor.is_enabled())
        self.assertTrue(monitor.should_collect_sample())

    def test_sampling_configuration(self):
        """测试采样配置"""
        monitor = ConfigurablePerformanceMonitor()
        config = PerformanceConfig(
            enabled=True,
            sampling_enabled=True,
            sampling_rate=0.5,  # 50% 采样率
        )
        monitor.config_update(config)

        # 测试采样逻辑
        samples = []
        for _ in range(100):
            samples.append(monitor.should_collect_sample())

        # 应该有大约50%的样本被收集
        collected_count = sum(samples)
        self.assertGreater(collected_count, 30)  # 至少30%
        self.assertLess(collected_count, 70)  # 最多70%

    def test_execution_measurement(self):
        """测试执行时间测量"""
        monitor = ConfigurablePerformanceMonitor()
        config = PerformanceConfig(
            enabled=True,
            collect_execution_time=True,
            collect_task_count=True,
            batch_update=False,  # 禁用批量更新以立即看到结果
        )
        monitor.config_update(config)

        # 测量执行时间
        with monitor.measure("test_executor", "test_operation"):
            time.sleep(0.01)  # 模拟执行时间

        # 获取指标
        metrics = monitor.executor_metrics("test_executor")
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics["total_executions"], 1)
        self.assertEqual(metrics["successful_executions"], 1)
        self.assertGreater(metrics["total_execution_time"], 0)

    def test_execution_measurement_with_exception(self):
        """测试异常情况下的执行测量"""
        monitor = ConfigurablePerformanceMonitor()
        config = PerformanceConfig(
            enabled=True,
            collect_error_rate=True,
            collect_task_count=True,
            batch_update=False,  # 禁用批量更新以立即看到结果
        )
        monitor.config_update(config)

        # 测量异常执行
        try:
            with monitor.measure("test_executor", "test_operation"):
                raise ValueError("Test exception")
        except ValueError:
            pass

        # 获取指标
        metrics = monitor.executor_metrics("test_executor")
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics["total_executions"], 1)
        self.assertEqual(metrics["successful_executions"], 0)
        self.assertEqual(metrics["failed_executions"], 1)
        self.assertEqual(metrics["success_rate"], 0.0)

    def test_batch_update_mode(self):
        """测试批量更新模式"""
        monitor = ConfigurablePerformanceMonitor()
        config = PerformanceConfig(enabled=True, batch_update=True, batch_size=3)
        monitor.config_update(config)

        # 执行多次操作
        for i in range(5):
            with monitor.measure("test_executor", f"operation_{i}"):
                pass

        # 获取指标
        metrics = monitor.executor_metrics("test_executor")
        self.assertIsNotNone(metrics)
        # 批量更新可能还有待处理的更新
        self.assertGreaterEqual(metrics["total_executions"], 3)

    def test_cache_functionality(self):
        """测试缓存功能"""
        monitor = ConfigurablePerformanceMonitor()
        config = PerformanceConfig(
            enabled=True,
            cache_metrics=True,
            cache_duration=0.1,
            collect_execution_time=True,
            collect_task_count=True,
            batch_update=False,  # 禁用批量更新以立即看到结果
        )
        monitor.config_update(config)

        # 添加一些指标
        with monitor.measure("test_executor", "test_operation"):
            time.sleep(0.001)  # 确保有执行时间

        # 获取指标（应该被缓存）
        metrics1 = monitor.executor_metrics("test_executor")
        metrics2 = monitor.executor_metrics("test_executor")

        # 验证指标存在
        self.assertIsNotNone(metrics1)
        self.assertIsNotNone(metrics2)

        # 验证缓存工作
        if "average_execution_time" in metrics1:
            self.assertEqual(metrics1["average_execution_time"], metrics2["average_execution_time"])

        # 等待缓存过期
        time.sleep(0.2)

        # 再次获取指标（缓存应该过期）
        metrics3 = monitor.executor_metrics("test_executor")
        self.assertIsNotNone(metrics3)

    def test_global_monitor_instance(self):
        """测试全局监控器实例"""
        # 获取全局监控器实例
        monitor1 = ConfigurablePerformanceMonitor()
        monitor2 = ConfigurablePerformanceMonitor()

        # 验证是同一个实例
        self.assertIs(monitor1, monitor2)

        # 测试配置更新
        config = PerformanceConfig(enabled=True)
        monitor1.config_update(config)

        self.assertTrue(monitor1.config.enabled)

    def test_config_update(self):
        """测试配置更新"""
        # 获取监控器实例并设置初始配置（禁用）
        monitor = ConfigurablePerformanceMonitor()
        config1 = PerformanceConfig(enabled=False)
        monitor.config_update(config1)

        self.assertFalse(monitor.is_enabled())

        # 更新配置（启用）
        config2 = PerformanceConfig(enabled=True)
        monitor.config_update(config2)

        self.assertTrue(monitor.is_enabled())

        # 更新配置（再次禁用）
        config3 = PerformanceConfig(enabled=False)
        monitor.config_update(config3)

        self.assertFalse(monitor.is_enabled())

    def test_metrics_clearing(self):
        """测试指标清理"""
        monitor = ConfigurablePerformanceMonitor()
        config = PerformanceConfig(
            enabled=True,
            collect_task_count=True,
            batch_update=False,  # 禁用批量更新以立即看到结果
        )
        monitor.config_update(config)

        # 添加一些指标
        with monitor.measure("test_executor", "test_operation"):
            pass

        # 验证指标存在
        metrics = monitor.executor_metrics("test_executor")
        self.assertIsNotNone(metrics)
        self.assertGreater(metrics["total_executions"], 0)

        # 清理指标
        monitor.clear()

        # 验证指标已清理
        metrics = monitor.executor_metrics("test_executor")
        self.assertIsNone(metrics)

    def test_all_metrics_collection(self):
        """测试所有指标收集"""
        monitor = ConfigurablePerformanceMonitor()
        config = PerformanceConfig(enabled=True, batch_update=True)
        monitor.config_update(config)

        # 添加多个执行器的指标
        with monitor.measure("executor1", "operation1"):
            pass

        with monitor.measure("executor2", "operation2"):
            pass

        # 获取所有指标
        all_metrics = monitor.all_metrics()

        self.assertTrue(all_metrics["enabled"])
        self.assertIn("executors", all_metrics)
        self.assertIn("config", all_metrics)
        self.assertIn("stats", all_metrics)

        # 验证执行器指标
        executors = all_metrics["executors"]
        self.assertGreaterEqual(len(executors), 1)

    def test_performance_overhead(self):
        """测试性能开销"""
        # 禁用监控的情况
        monitor_disabled = ConfigurablePerformanceMonitor()
        config_disabled = PerformanceConfig(enabled=False)
        monitor_disabled.config_update(config_disabled)

        start_time = time.time()
        for _ in range(1000):
            with monitor_disabled.measure("test", "op"):
                pass
        disabled_time = time.time() - start_time

        # 启用监控的情况
        monitor_enabled = ConfigurablePerformanceMonitor()
        config_enabled = PerformanceConfig(enabled=True)
        monitor_enabled.config_update(config_enabled)

        start_time = time.time()
        for _ in range(1000):
            with monitor_enabled.measure("test", "op"):
                pass
        enabled_time = time.time() - start_time

        # 验证禁用时的开销很小
        self.assertLess(disabled_time, 0.1)  # 应该很快

        # 启用时的开销应该是可接受的
        overhead_ratio = enabled_time / disabled_time if disabled_time > 0 else 1
        self.assertLess(overhead_ratio, 10)  # 开销不应该超过10倍


if __name__ == "__main__":
    unittest.main()

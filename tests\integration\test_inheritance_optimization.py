#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 继承层次优化集成测试 - 验证重构后的组合模式实现

全面测试重构后的组合模式实现，确保功能完整性和性能不受影响。
包括Bean工厂、生命周期接口、事件系统和智能代理的集成测试。
"""

import asyncio
import time
import unittest
from typing import Any
from unittest.mock import Mock

from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.proxy import BeanProxy
# Bean工厂相关
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.bean.utils import DependencyGraph
# 事件系统
from miniboot.events.event_types import (ApplicationStartedEvent,
                                         BeanCreatedEvent, EventTypes)
# 生命周期接口
from miniboot.processor.base import (BeanPostProcessor, InitializingBean,
                                     LifecycleProcessor, SmartLifecycle)


class TestBean(InitializingBean):
    """测试用的Bean类"""

    def __init__(self, name: str = "testBean"):
        self.name = name
        self.initialized = False
        self.value = "initial"
        self.call_count = 0

    def after_properties_set(self) -> None:
        """初始化回调"""
        self.initialized = True

    def business_method(self) -> str:
        """业务方法"""
        self.call_count += 1
        return f"business_result_{self.call_count}"

    def get_value(self) -> str:
        """获取值"""
        return self.value

    def set_value(self, value: str) -> None:
        """设置值"""
        self.value = value


class TestAsyncBean:
    """测试用的异步Bean类"""

    def __init__(self, name: str = "testAsyncBean"):
        self.name = name
        self.call_count = 0

    async def async_business_method(self) -> str:
        """异步业务方法"""
        self.call_count += 1
        await asyncio.sleep(0.01)  # 模拟异步操作
        return f"async_business_result_{self.call_count}"

    def sync_method_in_async_bean(self) -> str:
        """异步Bean中的同步方法"""
        return "sync_in_async"


class TestLifecycleBean(SmartLifecycle):
    """测试用的生命周期Bean"""

    def __init__(self, phase: int = 0):
        self._phase = phase
        self._running = False
        self._auto_startup = True

    def start(self) -> None:
        """启动Bean"""
        self._running = True

    def stop(self, callback: Any = None) -> None:
        """停止Bean"""
        self._running = False
        if callback:
            callback()

    def is_running(self) -> bool:
        """检查是否运行"""
        return self._running

    def get_phase(self) -> int:
        """获取阶段"""
        return self._phase

    def is_auto_startup(self) -> bool:
        """是否自动启动"""
        return self._auto_startup


class IntegrationTestBeanPostProcessor(BeanPostProcessor):
    """测试用的Bean后置处理器"""

    def __init__(self):
        self.before_calls = []
        self.after_calls = []

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """初始化前处理"""
        self.before_calls.append((bean, bean_name))
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """初始化后处理"""
        self.after_calls.append((bean, bean_name))
        return bean

    def get_order(self) -> int:
        """获取执行顺序"""
        return 100


@unittest.skip("依赖模块不存在")
class InheritanceOptimizationIntegrationTestCase(unittest.TestCase):
    """继承层次优化集成测试用例"""

    def setUp(self):
        """测试前置设置"""
        self.test_bean = TestBean("integrationTestBean")
        self.test_async_bean = TestAsyncBean("integrationTestAsyncBean")
        self.test_lifecycle_bean = TestLifecycleBean(100)
        self.test_processor = IntegrationTestBeanPostProcessor()

    def test_composite_bean_factory_integration(self):
        """测试组合式Bean工厂集成"""
        # 创建统一Bean工厂
        registry = DefaultBeanDefinitionRegistry()
        dependency_graph = DependencyGraph()
        factory = DefaultBeanFactory()

        # 注册Bean
        factory.register_singleton("testBean", self.test_bean)
        factory.register_singleton("lifecycleBean", self.test_lifecycle_bean)

        # 添加Bean后置处理器
        factory.add_bean_post_processor(self.test_processor)

        # 获取Bean
        retrieved_bean = factory.get_bean("testBean")
        self.assertIsInstance(retrieved_bean, TestBean)
        self.assertEqual(retrieved_bean.name, "integrationTestBean")

        # 验证后置处理器被调用
        self.assertEqual(len(self.test_processor.before_calls), 1)
        self.assertEqual(len(self.test_processor.after_calls), 1)

        # 验证生命周期Bean
        lifecycle_bean = factory.get_bean("lifecycleBean")
        self.assertIsInstance(lifecycle_bean, TestLifecycleBean)

        # 清理
        factory.shutdown()

    def test_async_composite_bean_factory_integration(self):
        """测试异步组合式Bean工厂集成"""

        async def async_test():
            # 创建异步组合式Bean工厂
            registry = DefaultBeanDefinitionRegistry()
            dependency_graph = DependencyGraph()
            factory = AsyncCompositeBeanFactory(registry, dependency_graph)

            # 注册Bean
            await factory.register_singleton("testBean", self.test_bean)
            await factory.register_singleton("asyncBean", self.test_async_bean)

            # 获取Bean
            retrieved_bean = await factory.get_bean("testBean")
            self.assertIsInstance(retrieved_bean, TestBean)

            async_bean = await factory.get_bean("asyncBean")
            self.assertIsInstance(async_bean, TestAsyncBean)

            # 清理
            await factory.shutdown()

        # 运行异步测试
        asyncio.run(async_test())

    def test_lifecycle_processor_integration(self):
        """测试生命周期处理器集成"""
        # 创建生命周期处理器
        processor = LifecycleProcessor()

        # 添加生命周期Bean
        processor.add_lifecycle_bean("lifecycleBean1", self.test_lifecycle_bean)

        # 创建另一个生命周期Bean
        lifecycle_bean2 = TestLifecycleBean(200)
        processor.add_lifecycle_bean("lifecycleBean2", lifecycle_bean2)

        # 启动所有生命周期Bean
        processor.start()

        # 验证Bean已启动
        self.assertTrue(self.test_lifecycle_bean.is_running())
        self.assertTrue(lifecycle_bean2.is_running())
        self.assertTrue(processor.is_running())

        # 停止所有生命周期Bean
        processor.stop()

        # 验证Bean已停止
        self.assertFalse(self.test_lifecycle_bean.is_running())
        self.assertFalse(lifecycle_bean2.is_running())
        self.assertFalse(processor.is_running())

    def test_composite_event_system_integration(self):
        """测试组合式事件系统集成"""
        # 创建应用启动事件
        app_started_event = ApplicationStartedEvent(application=Mock(), startup_time=2.5)

        # 验证事件属性
        self.assertEqual(app_started_event.event_type, EventTypes.APPLICATION_STARTED)
        self.assertEqual(app_started_event.get_startup_time(), 2.5)
        self.assertFalse(app_started_event.is_processed())

        # 创建Bean创建事件
        bean_created_event = BeanCreatedEvent(bean_name="testBean", bean_class="TestBean", source=self.test_bean)

        # 验证Bean事件
        self.assertEqual(bean_created_event.event_type, EventTypes.BEAN_CREATED)
        self.assertEqual(bean_created_event.get_bean_name(), "testBean")
        self.assertEqual(bean_created_event.get_bean_class(), "TestBean")

        # 验证Bean事件注册
        self.assertEqual(bean_created_event.get_bean_name(), "testBean")

        # 标记事件已处理
        app_started_event.mark_processed()
        bean_created_event.mark_processed()

        self.assertTrue(app_started_event.is_processed())
        self.assertTrue(bean_created_event.is_processed())

        # 清理事件
        app_started_event.cleanup()
        bean_created_event.cleanup()

    def test_composite_proxy_integration(self):
        """测试组合式代理集成"""
        # 创建组合式代理
        proxy = CompositeProxy(sync_bean=self.test_bean, async_bean=self.test_async_bean, bean_name="integrationProxy")

        # 测试同步方法访问
        result = proxy.business_method()
        self.assertEqual(result, "business_result_1")
        self.assertEqual(self.test_bean.call_count, 1)

        # 测试属性访问
        value = proxy.get_value()
        self.assertEqual(value, "initial")

        # 设置属性
        proxy.set_value("updated")
        updated_value = proxy.get_value()
        self.assertEqual(updated_value, "updated")

        # 验证代理统计
        stats = proxy.get_stats()
        self.assertEqual(stats["bean_name"], "integrationProxy")
        self.assertGreater(stats["access_count"], 0)
        self.assertTrue(stats["has_sync_bean"])
        self.assertTrue(stats["has_async_bean"])

        # 清理代理
        proxy.shutdown()

    def test_full_system_integration(self):
        """测试完整系统集成"""
        # 1. 创建组合式Bean工厂
        registry = DefaultBeanDefinitionRegistry()
        dependency_graph = DependencyGraph()
        factory = CompositeBeanFactory(registry, dependency_graph)

        # 2. 注册Bean和处理器
        factory.register_singleton("testBean", self.test_bean)
        factory.register_singleton("lifecycleBean", self.test_lifecycle_bean)
        factory.add_bean_post_processor(self.test_processor)

        # 3. 创建生命周期处理器
        lifecycle_processor = LifecycleProcessor()
        lifecycle_processor.add_lifecycle_bean("lifecycleBean", self.test_lifecycle_bean)

        # 4. 创建组合式代理
        proxy = CompositeProxy(sync_bean=self.test_bean, async_bean=self.test_async_bean, bean_name="systemIntegrationProxy")

        # 5. 创建事件
        bean_created_event = BeanCreatedEvent(bean_name="testBean", bean_class="TestBean", source=self.test_bean)

        # 6. 执行完整流程

        # 启动生命周期
        lifecycle_processor.start()
        self.assertTrue(self.test_lifecycle_bean.is_running())

        # 通过工厂获取Bean
        factory_bean = factory.get_bean("testBean")
        self.assertIsInstance(factory_bean, TestBean)

        # 通过代理访问Bean
        proxy_result = proxy.business_method()
        self.assertEqual(proxy_result, "business_result_1")

        # 处理事件
        self.assertEqual(bean_created_event.get_bean_name(), "testBean")
        bean_created_event.mark_processed()
        self.assertTrue(bean_created_event.is_processed())

        # 验证后置处理器
        self.assertGreater(len(self.test_processor.before_calls), 0)
        self.assertGreater(len(self.test_processor.after_calls), 0)

        # 7. 清理资源
        lifecycle_processor.stop()
        self.assertFalse(self.test_lifecycle_bean.is_running())

        proxy.shutdown()
        factory.shutdown()
        bean_created_event.cleanup()

    def test_performance_comparison(self):
        """测试性能对比"""
        # 测试组合式Bean工厂性能
        registry = DefaultBeanDefinitionRegistry()
        dependency_graph = DependencyGraph()
        factory = CompositeBeanFactory(registry, dependency_graph)

        # 注册多个Bean
        for i in range(100):
            bean = TestBean(f"perfTestBean_{i}")
            factory.register_singleton(f"perfTestBean_{i}", bean)

        # 测试获取Bean的性能
        start_time = time.time()
        for i in range(100):
            bean = factory.get_bean(f"perfTestBean_{i}")
            self.assertIsInstance(bean, TestBean)
        end_time = time.time()

        factory_time = end_time - start_time
        self.assertLess(factory_time, 1.0)  # 应该在1秒内完成

        # 测试组合式代理性能
        proxy = CompositeProxy(sync_bean=self.test_bean, async_bean=self.test_async_bean, bean_name="perfTestProxy")

        # 测试方法访问性能
        start_time = time.time()
        for i in range(1000):
            result = proxy.business_method()
            self.assertIsNotNone(result)
        end_time = time.time()

        proxy_time = end_time - start_time
        self.assertLess(proxy_time, 1.0)  # 应该在1秒内完成

        # 验证缓存效果
        stats = proxy.get_stats()
        cache_stats = stats.get("cache_stats", {})
        if cache_stats:
            hit_rate = cache_stats.get("hits", 0) / (cache_stats.get("hits", 0) + cache_stats.get("misses", 1))
            self.assertGreater(hit_rate, 0.5)  # 缓存命中率应该大于50%

        # 清理
        proxy.shutdown()
        factory.shutdown()

    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 测试Bean工厂错误处理
        registry = DefaultBeanDefinitionRegistry()
        dependency_graph = DependencyGraph()
        factory = CompositeBeanFactory(registry, dependency_graph)

        with self.assertRaises(Exception):
            factory.get_bean("nonexistentBean")

        # 测试代理错误处理
        proxy = CompositeProxy(sync_bean=self.test_bean, bean_name="errorTestProxy")

        with self.assertRaises(AttributeError):
            proxy.nonexistent_method()

        # 清理
        proxy.shutdown()
        factory.shutdown()


if __name__ == "__main__":
    unittest.main()

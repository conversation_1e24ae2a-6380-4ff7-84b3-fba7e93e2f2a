#!/usr/bin/env python
"""
* @author: cz
* @description: 注解系统性能分析工具

提供性能监控、分析和优化建议功能.
"""

import functools
import inspect
import threading
import time
from collections import deque
from dataclasses import dataclass, field
from typing import Any, Callable, Dict, List, Optional, Set, Tuple, Type

from ..utils.singleton import SingletonMeta


@dataclass
class PerformanceConfig:
    """性能优化配置"""

    # 反射优化配置
    enable_reflection_optimization: bool = True
    enable_batch_processing: bool = True
    cache_reflection_results: bool = True
    max_reflection_cache_size: int = 1000

    # 注解检查优化
    enable_annotation_caching: bool = True
    batch_annotation_checks: bool = True

    # 性能监控配置
    enable_performance_monitoring: bool = False
    monitor_reflection_calls: bool = False


@dataclass
class PerformanceMetrics:
    """性能指标"""

    operation_name: str
    total_calls: int = 0
    total_time: float = 0.0
    min_time: float = float("inf")
    max_time: float = 0.0
    avg_time: float = 0.0
    recent_times: deque = field(default_factory=lambda: deque(maxlen=100))

    def add_measurement(self, duration: float):
        """添加测量结果"""
        self.total_calls += 1
        self.total_time += duration
        self.min_time = min(self.min_time, duration)
        self.max_time = max(self.max_time, duration)
        self.avg_time = self.total_time / self.total_calls
        self.recent_times.append(duration)

    def get_recent_avg(self) -> float:
        """获取最近的平均时间"""
        if not self.recent_times:
            return 0.0
        return sum(self.recent_times) / len(self.recent_times)


class PerformanceMonitor:
    """性能监控器"""

    def __init__(self):
        self._metrics: dict[str, PerformanceMetrics] = {}
        self._lock = threading.Lock()
        self._enabled = True

    def enable(self, enabled: bool = True):
        """启用/禁用性能监控"""
        self._enabled = enabled

    def record_operation(self, operation_name: str, duration: float):
        """记录操作性能"""
        if not self._enabled:
            return

        with self._lock:
            if operation_name not in self._metrics:
                self._metrics[operation_name] = PerformanceMetrics(operation_name)

            self._metrics[operation_name].add_measurement(duration)

    def get_metrics(self, operation_name: str = None) -> dict[str, PerformanceMetrics]:
        """获取性能指标"""
        with self._lock:
            if operation_name:
                return {operation_name: self._metrics.get(operation_name)}
            return self._metrics.copy()

    def clear_metrics(self):
        """清空性能指标"""
        with self._lock:
            self._metrics.clear()

    def get_summary(self) -> dict[str, Any]:
        """获取性能摘要"""
        with self._lock:
            summary = {"total_operations": len(self._metrics), "operations": {}}

            for name, metrics in self._metrics.items():
                summary["operations"][name] = {
                    "total_calls": metrics.total_calls,
                    "total_time": round(metrics.total_time, 4),
                    "avg_time": round(metrics.avg_time, 4),
                    "min_time": round(metrics.min_time, 4),
                    "max_time": round(metrics.max_time, 4),
                    "recent_avg": round(metrics.get_recent_avg(), 4),
                }

            return summary


# 全局性能监控器
_global_monitor = PerformanceMonitor()


def get_performance_monitor() -> PerformanceMonitor:
    """获取全局性能监控器"""
    return _global_monitor


def performance_monitor(operation_name: str = None):
    """性能监控装饰器"""

    def decorator(func: Callable) -> Callable:
        nonlocal operation_name
        if operation_name is None:
            operation_name = f"{func.__module__}.{func.__name__}"

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                _global_monitor.record_operation(operation_name, duration)

        return wrapper

    return decorator


class PerformanceAnalyzer:
    """性能分析器"""

    def __init__(self, monitor: PerformanceMonitor = None):
        self.monitor = monitor or get_performance_monitor()

    def analyze_bottlenecks(self, threshold: float = 0.1) -> list[dict[str, Any]]:
        """分析性能瓶颈"""
        metrics = self.monitor.get_metrics()
        bottlenecks = []

        for name, metric in metrics.items():
            if metric.avg_time > threshold:
                bottlenecks.append(
                    {
                        "operation": name,
                        "avg_time": metric.avg_time,
                        "total_calls": metric.total_calls,
                        "total_time": metric.total_time,
                        "severity": self._calculate_severity(metric, threshold),
                    }
                )

        # 按严重程度排序
        bottlenecks.sort(key=lambda x: x["severity"], reverse=True)
        return bottlenecks

    def _calculate_severity(self, metric: PerformanceMetrics, threshold: float) -> float:
        """计算瓶颈严重程度"""
        time_factor = metric.avg_time / threshold
        frequency_factor = min(metric.total_calls / 100, 10)  # 最多10倍权重
        return time_factor * frequency_factor

    def generate_optimization_suggestions(self) -> list[str]:
        """生成优化建议"""
        suggestions = []
        bottlenecks = self.analyze_bottlenecks()

        for bottleneck in bottlenecks[:5]:  # 只分析前5个瓶颈
            operation = bottleneck["operation"]
            avg_time = bottleneck["avg_time"]

            if "scan" in operation.lower():
                if avg_time > 1.0:
                    suggestions.append(f"考虑为 {operation} 启用缓存机制")
                if avg_time > 2.0:
                    suggestions.append(f"考虑为 {operation} 实现增量扫描")

            elif "metadata" in operation.lower():
                if avg_time > 0.1:
                    suggestions.append(f"考虑优化 {operation} 的数据结构")
                if bottleneck["total_calls"] > 1000:
                    suggestions.append(f"考虑为 {operation} 添加内存缓存")

            elif avg_time > 0.5:
                suggestions.append(f"考虑优化 {operation} 的算法复杂度")

        if not suggestions:
            suggestions.append("当前性能表现良好,无需特别优化")

        return suggestions


class ReflectionOptimizer(metaclass=SingletonMeta):
    """反射调用优化器

    通过缓存反射结果和批量操作来优化性能
    """

    def __init__(self, config: Optional[PerformanceConfig] = None):
        if not hasattr(self, '_initialized'):
            self.config = config or PerformanceConfig()

            # 缓存锁
            self._lock = threading.RLock()

            # 类成员缓存
            self._class_members_cache: Dict[Type, Dict[str, Any]] = {}
            self._class_methods_cache: Dict[Type, List[Tuple[str, Callable]]] = {}
            self._class_attributes_cache: Dict[Type, List[str]] = {}

            # 注解检查缓存
            self._annotation_cache: Dict[Tuple[Type, str], bool] = {}
            self._method_annotation_cache: Dict[Tuple[Callable, str], bool] = {}

            # 预定义的注解属性列表
            self._class_annotation_attrs = [
                "__is_component__", "__is_service__", "__is_repository__",
                "__is_configuration__", "__is_miniboot_application__"
            ]

            self._method_annotation_attrs = [
                "__is_bean__", "__is_autowired__", "__is_post_construct__",
                "__is_pre_destroy__", "__is_async__", "__is_scheduled__",
                "__is_event_listener__"
            ]

            self._initialized = True

    def get_class_members(self, cls: Type) -> Dict[str, Any]:
        """获取类的所有成员（缓存版本）"""
        if not self.config.cache_reflection_results:
            return dict(inspect.getmembers(cls))

        with self._lock:
            if cls not in self._class_members_cache:
                members = {}
                for name, obj in inspect.getmembers(cls):
                    members[name] = obj

                # 检查缓存大小
                if len(self._class_members_cache) >= self.config.max_reflection_cache_size:
                    # 简单的清理策略：删除最旧的条目
                    oldest_key = next(iter(self._class_members_cache))
                    del self._class_members_cache[oldest_key]

                self._class_members_cache[cls] = members

            return self._class_members_cache[cls].copy()

    def get_class_methods(self, cls: Type) -> List[Tuple[str, Callable]]:
        """获取类的所有方法（缓存版本）"""
        if not self.config.cache_reflection_results:
            return list(inspect.getmembers(cls, inspect.isfunction))

        with self._lock:
            if cls not in self._class_methods_cache:
                methods = []
                for name, obj in inspect.getmembers(cls, inspect.isfunction):
                    methods.append((name, obj))

                # 检查缓存大小
                if len(self._class_methods_cache) >= self.config.max_reflection_cache_size:
                    oldest_key = next(iter(self._class_methods_cache))
                    del self._class_methods_cache[oldest_key]

                self._class_methods_cache[cls] = methods

            return self._class_methods_cache[cls].copy()

    def get_class_attributes(self, cls: Type) -> List[str]:
        """获取类的所有属性名（缓存版本）"""
        if not self.config.cache_reflection_results:
            return [name for name in dir(cls) if not name.startswith('_')]

        with self._lock:
            if cls not in self._class_attributes_cache:
                attributes = []
                for name in dir(cls):
                    if not name.startswith('_'):
                        attributes.append(name)

                # 检查缓存大小
                if len(self._class_attributes_cache) >= self.config.max_reflection_cache_size:
                    oldest_key = next(iter(self._class_attributes_cache))
                    del self._class_attributes_cache[oldest_key]

                self._class_attributes_cache[cls] = attributes

            return self._class_attributes_cache[cls].copy()

    def has_class_annotation(self, cls: Type, annotation_attr: str) -> bool:
        """检查类是否有指定注解（缓存版本）"""
        if not self.config.enable_annotation_caching:
            return hasattr(cls, annotation_attr) and getattr(cls, annotation_attr, False)

        cache_key = (cls, annotation_attr)

        with self._lock:
            if cache_key not in self._annotation_cache:
                result = hasattr(cls, annotation_attr) and getattr(cls, annotation_attr, False)
                self._annotation_cache[cache_key] = result

            return self._annotation_cache[cache_key]

    def has_method_annotation(self, method: Callable, annotation_attr: str) -> bool:
        """检查方法是否有指定注解（缓存版本）"""
        if not self.config.enable_annotation_caching:
            return hasattr(method, annotation_attr) and getattr(method, annotation_attr, False)

        cache_key = (method, annotation_attr)

        with self._lock:
            if cache_key not in self._method_annotation_cache:
                result = hasattr(method, annotation_attr) and getattr(method, annotation_attr, False)
                self._method_annotation_cache[cache_key] = result

            return self._method_annotation_cache[cache_key]

    def batch_check_class_annotations(self, cls: Type) -> Dict[str, bool]:
        """批量检查类的所有注解"""
        if not self.config.batch_annotation_checks:
            return {attr: self.has_class_annotation(cls, attr) for attr in self._class_annotation_attrs}

        results = {}
        for attr in self._class_annotation_attrs:
            results[attr] = self.has_class_annotation(cls, attr)
        return results

    def batch_check_method_annotations(self, method: Callable) -> Dict[str, bool]:
        """批量检查方法的所有注解"""
        if not self.config.batch_annotation_checks:
            return {attr: self.has_method_annotation(method, attr) for attr in self._method_annotation_attrs}

        results = {}
        for attr in self._method_annotation_attrs:
            results[attr] = self.has_method_annotation(method, attr)
        return results

    def has_any_class_annotation(self, cls: Type) -> bool:
        """快速检查类是否有任何注解"""
        for attr in self._class_annotation_attrs:
            if self.has_class_annotation(cls, attr):
                return True
        return False

    def has_any_method_annotation(self, method: Callable) -> bool:
        """快速检查方法是否有任何注解"""
        for attr in self._method_annotation_attrs:
            if self.has_method_annotation(method, attr):
                return True
        return False

    def get_annotated_methods(self, cls: Type) -> List[Tuple[str, Callable, Dict[str, bool]]]:
        """获取类中所有有注解的方法"""
        annotated_methods = []

        for name, method in self.get_class_methods(cls):
            annotations = self.batch_check_method_annotations(method)
            if any(annotations.values()):
                annotated_methods.append((name, method, annotations))

        return annotated_methods

    def get_class_annotation_summary(self, cls: Type) -> Dict[str, Any]:
        """获取类的注解摘要"""
        annotations = self.batch_check_class_annotations(cls)

        # 确定类的主要类型
        class_type = None
        if annotations.get("__is_component__"):
            class_type = "component"
        elif annotations.get("__is_service__"):
            class_type = "service"
        elif annotations.get("__is_repository__"):
            class_type = "repository"
        elif annotations.get("__is_configuration__"):
            class_type = "configuration"
        elif annotations.get("__is_miniboot_application__"):
            class_type = "application"

        return {
            'class_type': class_type,
            'has_annotations': any(annotations.values()),
            'annotations': annotations,
            'module': cls.__module__,
            'name': cls.__name__
        }

    def clear_cache(self) -> None:
        """清空所有缓存"""
        with self._lock:
            self._class_members_cache.clear()
            self._class_methods_cache.clear()
            self._class_attributes_cache.clear()
            self._annotation_cache.clear()
            self._method_annotation_cache.clear()

    def get_cache_stats(self) -> Dict[str, int]:
        """获取缓存统计信息"""
        with self._lock:
            return {
                'class_members_cached': len(self._class_members_cache),
                'class_methods_cached': len(self._class_methods_cache),
                'class_attributes_cached': len(self._class_attributes_cache),
                'class_annotations_cached': len([k for k in self._annotation_cache.keys() if isinstance(k[0], type)]),
                'method_annotations_cached': len([k for k in self._method_annotation_cache.keys()]),
                'total_annotation_checks': len(self._annotation_cache) + len(self._method_annotation_cache)
            }


# 全局优化器实例
_reflection_optimizer = None


def get_reflection_optimizer(config: Optional[PerformanceConfig] = None) -> ReflectionOptimizer:
    """获取反射优化器实例"""
    global _reflection_optimizer
    if _reflection_optimizer is None:
        _reflection_optimizer = ReflectionOptimizer(config)
    return _reflection_optimizer

    def generate_report(self) -> str:
        """生成性能报告"""
        summary = self.monitor.get_summary()
        bottlenecks = self.analyze_bottlenecks()
        suggestions = self.generate_optimization_suggestions()

        report = []
        report.append("=" * 60)
        report.append("Mini-Boot 注解系统性能报告")
        report.append("=" * 60)

        # 总体统计
        report.append("\n📊 总体统计:")
        report.append(f"   监控操作数: {summary['total_operations']}")

        # 性能指标
        if summary["operations"]:
            report.append("\n⏱️  性能指标:")
            for name, metrics in summary["operations"].items():
                report.append(f"   {name}:")
                report.append(f"     调用次数: {metrics['total_calls']}")
                report.append(f"     平均耗时: {metrics['avg_time']}s")
                report.append(f"     最小耗时: {metrics['min_time']}s")
                report.append(f"     最大耗时: {metrics['max_time']}s")
                report.append(f"     最近平均: {metrics['recent_avg']}s")

        # 性能瓶颈
        if bottlenecks:
            report.append("\n🚨 性能瓶颈:")
            for i, bottleneck in enumerate(bottlenecks[:3], 1):
                report.append(f"   {i}. {bottleneck['operation']}")
                report.append(f"      平均耗时: {bottleneck['avg_time']:.4f}s")
                report.append(f"      调用次数: {bottleneck['total_calls']}")
                report.append(f"      严重程度: {bottleneck['severity']:.2f}")
        else:
            report.append("\n✅ 未发现明显的性能瓶颈")

        # 优化建议
        report.append("\n💡 优化建议:")
        for i, suggestion in enumerate(suggestions, 1):
            report.append(f"   {i}. {suggestion}")

        report.append("\n" + "=" * 60)

        return "\n".join(report)


class MemoryProfiler:
    """内存分析器"""

    @staticmethod
    def get_memory_usage() -> dict[str, Any]:
        """获取内存使用情况"""
        import os

        import psutil

        process = psutil.Process(os.getpid())
        memory_info = process.memory_info()

        return {
            "rss_mb": round(memory_info.rss / 1024 / 1024, 2),  # 物理内存
            "vms_mb": round(memory_info.vms / 1024 / 1024, 2),  # 虚拟内存
            "percent": round(process.memory_percent(), 2),  # 内存占用百分比
        }

    @staticmethod
    def profile_function(func: Callable) -> Callable:
        """内存分析装饰器"""

        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            import tracemalloc

            tracemalloc.start()
            try:
                result = func(*args, **kwargs)
                current, peak = tracemalloc.get_traced_memory()
                print(f"{func.__name__} 内存使用: 当前 {current / 1024 / 1024:.2f}MB, 峰值 {peak / 1024 / 1024:.2f}MB")
                return result
            finally:
                tracemalloc.stop()

        return wrapper


# 便捷函数 - 保持向后兼容
def enable_performance_monitoring(enabled: bool = True):
    """启用/禁用性能监控

    Note: 这是一个简化的便捷函数，用于向后兼容。
    推荐使用 miniboot.actuator.performance 模块中的完整功能。
    """
    get_performance_monitor().enable(enabled)


def get_performance_report() -> str:
    """获取性能报告

    Note: 这是一个简化的便捷函数，用于向后兼容。
    推荐使用 miniboot.actuator.performance 模块中的完整功能。
    """
    analyzer = PerformanceAnalyzer()
    return analyzer.generate_report()


def clear_performance_data():
    """清空性能数据

    Note: 这是一个简化的便捷函数，用于向后兼容。
    推荐使用 miniboot.actuator.performance 模块中的完整功能。
    """
    get_performance_monitor().clear_metrics()


def analyze_performance() -> dict[str, Any]:
    """分析性能

    Note: 这是一个简化的便捷函数，用于向后兼容。
    推荐使用 miniboot.actuator.performance 模块中的完整功能。
    """
    analyzer = PerformanceAnalyzer()
    return {
        "summary": get_performance_monitor().get_summary(),
        "bottlenecks": analyzer.analyze_bottlenecks(),
        "suggestions": analyzer.generate_optimization_suggestions(),
    }

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
监控组件接口定义

定义了 Mini-Boot 监控体系的核心接口，实现接口驱动的架构设计。
所有监控组件都应该实现这些接口，以便框架能够自动发现和集成。

设计原则：
- 接口隔离：每个接口职责单一，功能明确
- 依赖倒置：框架依赖接口而不是具体实现
- 开闭原则：可扩展新的监控组件而无需修改框架代码
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, List, Optional, Union
from dataclasses import dataclass


@dataclass
class EndpointInfo:
    """端点信息"""
    name: str
    path: str
    methods: List[str]
    description: str
    enabled: bool = True
    sensitive: bool = False


@dataclass
class MetricsData:
    """指标数据"""
    name: str
    value: Union[int, float, str]
    unit: Optional[str] = None
    tags: Optional[Dict[str, str]] = None
    timestamp: Optional[float] = None


@dataclass
class HealthStatus:
    """健康状态"""
    status: str  # UP, DOWN, OUT_OF_SERVICE, UNKNOWN
    details: Optional[Dict[str, Any]] = None
    description: Optional[str] = None


class MonitoringContext(ABC):
    """监控上下文接口
    
    监控上下文是监控体系的核心组件，负责：
    - 监控组件的生命周期管理
    - 端点和指标的统一管理
    - 监控数据的聚合和暴露
    """
    
    @abstractmethod
    async def start(self) -> bool:
        """启动监控上下文
        
        Returns:
            bool: 启动是否成功
        """
        pass
    
    @abstractmethod
    async def stop(self) -> bool:
        """停止监控上下文
        
        Returns:
            bool: 停止是否成功
        """
        pass
    
    @abstractmethod
    def get_endpoints(self) -> Dict[str, EndpointInfo]:
        """获取所有端点信息
        
        Returns:
            Dict[str, EndpointInfo]: 端点名称到端点信息的映射
        """
        pass
    
    @abstractmethod
    def get_metrics(self) -> Dict[str, MetricsData]:
        """获取所有指标数据
        
        Returns:
            Dict[str, MetricsData]: 指标名称到指标数据的映射
        """
        pass
    
    @abstractmethod
    def is_started(self) -> bool:
        """检查监控上下文是否已启动
        
        Returns:
            bool: 是否已启动
        """
        pass


class EndpointProvider(ABC):
    """端点提供者接口
    
    端点提供者负责：
    - 提供特定功能的监控端点
    - 处理端点请求
    - 返回端点元数据
    """
    
    @abstractmethod
    def get_endpoint_info(self) -> EndpointInfo:
        """获取端点信息
        
        Returns:
            EndpointInfo: 端点元数据
        """
        pass
    
    @abstractmethod
    async def handle_request(self, request: Any) -> Any:
        """处理端点请求
        
        Args:
            request: 请求对象
            
        Returns:
            Any: 响应数据
        """
        pass
    
    @abstractmethod
    def is_enabled(self) -> bool:
        """检查端点是否启用
        
        Returns:
            bool: 是否启用
        """
        pass


class MetricsCollector(ABC):
    """指标收集器接口
    
    指标收集器负责：
    - 收集特定模块的指标数据
    - 提供指标元数据
    - 支持指标的实时更新
    """
    
    @abstractmethod
    def collect_metrics(self) -> List[MetricsData]:
        """收集指标数据
        
        Returns:
            List[MetricsData]: 指标数据列表
        """
        pass
    
    @abstractmethod
    def get_collector_name(self) -> str:
        """获取收集器名称
        
        Returns:
            str: 收集器名称
        """
        pass
    
    @abstractmethod
    def get_supported_metrics(self) -> List[str]:
        """获取支持的指标名称列表
        
        Returns:
            List[str]: 支持的指标名称
        """
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查收集器是否可用
        
        Returns:
            bool: 是否可用
        """
        pass


class HealthIndicator(ABC):
    """健康检查指示器接口
    
    健康检查指示器负责：
    - 检查特定组件的健康状态
    - 提供健康检查详细信息
    - 支持异步健康检查
    """
    
    @abstractmethod
    async def health(self) -> HealthStatus:
        """执行健康检查
        
        Returns:
            HealthStatus: 健康状态信息
        """
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取健康检查指示器名称
        
        Returns:
            str: 指示器名称
        """
        pass
    
    @abstractmethod
    def get_order(self) -> int:
        """获取执行顺序
        
        Returns:
            int: 执行顺序，数值越小优先级越高
        """
        pass


class MonitoringEventListener(ABC):
    """监控事件监听器接口
    
    监听监控体系中的各种事件：
    - 监控上下文启动/停止事件
    - 端点注册/注销事件
    - 指标更新事件
    - 健康状态变化事件
    """
    
    @abstractmethod
    async def on_monitoring_started(self, context: MonitoringContext) -> None:
        """监控上下文启动事件
        
        Args:
            context: 监控上下文
        """
        pass
    
    @abstractmethod
    async def on_monitoring_stopped(self, context: MonitoringContext) -> None:
        """监控上下文停止事件
        
        Args:
            context: 监控上下文
        """
        pass
    
    @abstractmethod
    async def on_endpoint_registered(self, endpoint_info: EndpointInfo) -> None:
        """端点注册事件
        
        Args:
            endpoint_info: 端点信息
        """
        pass
    
    @abstractmethod
    async def on_health_status_changed(self, indicator_name: str, old_status: HealthStatus, new_status: HealthStatus) -> None:
        """健康状态变化事件
        
        Args:
            indicator_name: 指示器名称
            old_status: 旧状态
            new_status: 新状态
        """
        pass

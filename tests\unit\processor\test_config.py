#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 配置属性处理器单元测试 - 简化版本
"""

import unittest
from unittest.mock import Mock

from miniboot.processor.base import ProcessorOrder
from miniboot.processor.configuration import ConfigurationPropertiesProcessor


class TestBeanWithConfigurationProperties:
    """测试Bean - 有@ConfigurationProperties注解"""

    def __init__(self):
        self.host = "localhost"
        self.port = 3306
        self.username = ""
        self.password = ""

    # 添加类型注解
    __annotations__ = {"host": str, "port": int, "username": str, "password": str}


# 为测试Bean添加@ConfigurationProperties注解
TestBeanWithConfigurationProperties.__configuration_properties_metadata__ = type(
    "ConfigurationPropertiesMetadata", (), {"prefix": "database", "ignore_unknown_fields": True, "ignore_invalid_fields": False, "validate": True}
)()


class TestBeanWithoutConfigurationProperties:
    """测试Bean - 无@ConfigurationProperties注解"""

    def __init__(self):
        self.value = "test"


class TestBeanWithEmptyPrefix:
    """测试Bean - 空前缀的@ConfigurationProperties注解"""

    def __init__(self):
        self.app_name = "default"
        self.debug = False

    __annotations__ = {"app_name": str, "debug": bool}


# 为空前缀测试Bean添加注解
TestBeanWithEmptyPrefix.__configuration_properties_metadata__ = type(
    "ConfigurationPropertiesMetadata", (), {"prefix": "", "ignore_unknown_fields": True, "ignore_invalid_fields": False, "validate": True}
)()


class TestConfigProcessor(unittest.TestCase):
    """配置属性处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.processor = ConfigurationPropertiesProcessor()
        self.mock_environment = Mock()
        self.processor.set_environment(self.mock_environment)

        # 设置模拟环境的返回值
        def mock_get_property(key, default=None):
            properties = {
                "database.host": "mysql.example.com",
                "database.port": "5432",
                "database.username": "admin",
                "database.password": "secret123",
                "app_name": "MyApp",
                "debug": "true",
            }
            return properties.get(key, default)

        self.mock_environment.get_property.side_effect = mock_get_property

    def test_processor_order(self):
        """测试处理器执行顺序"""
        self.assertEqual(self.processor.get_order(), ProcessorOrder.CONFIGURATION_PROCESSOR)

    def test_supports_bean_with_configuration_properties(self):
        """测试支持有@ConfigurationProperties注解的Bean"""
        bean = TestBeanWithConfigurationProperties()
        self.assertTrue(self.processor.supports(bean, "testBean"))

    def test_supports_bean_without_configuration_properties(self):
        """测试不支持无@ConfigurationProperties注解的Bean"""
        bean = TestBeanWithoutConfigurationProperties()
        self.assertFalse(self.processor.supports(bean, "testBean"))

    def test_supports_none_bean(self):
        """测试不支持None Bean"""
        self.assertFalse(self.processor.supports(None, "testBean"))

    def test_configuration_properties_binding(self):
        """测试配置属性绑定"""
        bean = TestBeanWithConfigurationProperties()

        # 执行处理
        result = self.processor.post_process_before_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)
        self.assertEqual(bean.host, "mysql.example.com")
        self.assertEqual(bean.port, 5432)  # 应该被转换为int类型
        self.assertEqual(bean.username, "admin")
        self.assertEqual(bean.password, "secret123")

        # 验证环境调用
        self.mock_environment.get_property.assert_any_call("database.host")
        self.mock_environment.get_property.assert_any_call("database.port")
        self.mock_environment.get_property.assert_any_call("database.username")
        self.mock_environment.get_property.assert_any_call("database.password")

    def test_empty_prefix_binding(self):
        """测试空前缀的配置绑定"""
        bean = TestBeanWithEmptyPrefix()

        # 执行处理
        result = self.processor.post_process_before_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)
        self.assertEqual(bean.app_name, "MyApp")
        self.assertEqual(bean.debug, True)  # 应该被转换为bool类型

        # 验证环境调用
        self.mock_environment.get_property.assert_any_call("app_name")
        self.mock_environment.get_property.assert_any_call("debug")

    def test_configuration_not_found(self):
        """测试配置未找到的情况"""
        bean = TestBeanWithConfigurationProperties()

        # 设置环境返回None
        self.mock_environment.get_property.side_effect = lambda _key, _default=None: None

        # 执行处理（不应该抛出异常）
        result = self.processor.post_process_before_initialization(bean, "testBean")

        # 验证结果（应该保持默认值）
        self.assertIs(result, bean)
        self.assertEqual(bean.host, "localhost")  # 保持默认值
        self.assertEqual(bean.port, 3306)  # 保持默认值

    def test_no_environment(self):
        """测试没有环境配置的情况"""
        processor = ConfigurationPropertiesProcessor()  # 没有设置环境
        bean = TestBeanWithConfigurationProperties()

        # 执行处理（不应该抛出异常，因为有前缀但会在内部处理）
        try:
            result = processor.post_process_before_initialization(bean, "testBean")
            # 如果没有抛出异常，验证Bean保持原状
            self.assertIs(result, bean)
        except Exception:  # noqa: BLE001
            # 如果抛出异常，这也是可以接受的行为
            pass

    def test_already_processed_bean(self):
        """测试已处理的Bean不会重复处理"""
        bean = TestBeanWithConfigurationProperties()

        # 第一次处理
        self.processor.post_process_before_initialization(bean, "testBean")

        # 重置mock调用记录
        self.mock_environment.reset_mock()

        # 第二次处理
        result = self.processor.post_process_before_initialization(bean, "testBean")

        # 验证没有再次调用环境
        self.mock_environment.get_property.assert_not_called()
        self.assertIs(result, bean)

    def test_post_process_after_initialization(self):
        """测试初始化后处理（应该直接返回原Bean）"""
        bean = TestBeanWithConfigurationProperties()

        result = self.processor.post_process_after_initialization(bean, "testBean")

        self.assertIs(result, bean)

    def test_none_bean_handling(self):
        """测试None Bean的处理"""
        result = self.processor.post_process_before_initialization(None, "testBean")
        self.assertIsNone(result)

    def test_type_conversion(self):
        """测试类型转换"""
        bean = TestBeanWithConfigurationProperties()

        # 执行处理
        self.processor.post_process_before_initialization(bean, "testBean")

        # 验证类型转换
        self.assertIsInstance(bean.port, int)
        self.assertEqual(bean.port, 5432)
        self.assertIsInstance(bean.host, str)
        self.assertEqual(bean.host, "mysql.example.com")

    def test_processed_beans_count(self):
        """测试已处理的Bean数量"""
        initial_count = self.processor.get_processed_beans_count()

        bean1 = TestBeanWithConfigurationProperties()
        bean2 = TestBeanWithConfigurationProperties()

        self.processor.post_process_before_initialization(bean1, "testBean1")
        self.processor.post_process_before_initialization(bean2, "testBean2")

        self.assertEqual(self.processor.get_processed_beans_count(), initial_count + 2)

    def test_bean_without_configuration_properties_not_processed(self):
        """测试没有@ConfigurationProperties注解的Bean不被处理"""
        bean = TestBeanWithoutConfigurationProperties()

        # 执行处理
        result = self.processor.post_process_before_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)
        # 验证环境没有被调用
        self.mock_environment.get_property.assert_not_called()


if __name__ == "__main__":
    unittest.main()

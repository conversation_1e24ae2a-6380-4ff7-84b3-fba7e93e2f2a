#!/usr/bin/env python
# encoding: utf-8
"""
中间件选择策略模块

使用策略模式优化复杂的中间件选择逻辑,提高代码可读性和可维护性.
"""

from abc import ABC, abstractmethod
from typing import List, Set
from dataclasses import dataclass
from enum import Enum

from loguru import logger


class RequestType(Enum):
    """请求类型"""

    STATIC = "static"
    API = "api"
    UPLOAD = "upload"
    ADMIN = "admin"
    UNKNOWN = "unknown"


class SecurityLevel(Enum):
    """安全级别"""

    LOW = "low"
    NORMAL = "normal"
    HIGH = "high"


@dataclass
class RequestContext:
    """请求上下文"""

    request_type: RequestType = RequestType.UNKNOWN
    security_level: SecurityLevel = SecurityLevel.NORMAL
    method: str = "GET"
    cache_eligible: bool = False
    compression_eligible: bool = False
    complexity_score: float = 0.0


class MiddlewareSelectionStrategy(ABC):
    """中间件选择策略接口"""

    @abstractmethod
    def select_middlewares(self, context: RequestContext) -> List[str]:
        """选择中间件列表

        Args:
            context: 请求上下文

        Returns:
            中间件名称列表
        """
        pass

    @abstractmethod
    def get_priority(self) -> int:
        """获取策略优先级

        Returns:
            优先级数字,越小优先级越高
        """
        pass

    @abstractmethod
    def supports(self, context: RequestContext) -> bool:
        """检查是否支持该请求上下文

        Args:
            context: 请求上下文

        Returns:
            是否支持
        """
        pass


class BaseMiddlewareStrategy(MiddlewareSelectionStrategy):
    """基础中间件策略 - 总是包含的中间件"""

    def select_middlewares(self, context: RequestContext) -> List[str]:
        """选择基础中间件"""
        return ["logging", "cors"]

    def get_priority(self) -> int:
        return 1000  # 最低优先级,总是执行

    def supports(self, context: RequestContext) -> bool:
        return True  # 总是支持


class SecurityMiddlewareStrategy(MiddlewareSelectionStrategy):
    """安全中间件策略"""

    def select_middlewares(self, context: RequestContext) -> List[str]:
        """根据安全级别选择中间件"""
        middlewares = []

        if context.security_level in [SecurityLevel.NORMAL, SecurityLevel.HIGH]:
            middlewares.append("security")

        if context.security_level == SecurityLevel.HIGH:
            middlewares.extend(["auth", "rate_limit"])

        return middlewares

    def get_priority(self) -> int:
        return 100  # 高优先级

    def supports(self, context: RequestContext) -> bool:
        return context.security_level != SecurityLevel.LOW


class StaticResourceStrategy(MiddlewareSelectionStrategy):
    """静态资源策略"""

    def select_middlewares(self, context: RequestContext) -> List[str]:
        """为静态资源选择中间件"""
        return ["cache", "compression"]

    def get_priority(self) -> int:
        return 200

    def supports(self, context: RequestContext) -> bool:
        return context.request_type == RequestType.STATIC


class ApiRequestStrategy(MiddlewareSelectionStrategy):
    """API请求策略"""

    def select_middlewares(self, context: RequestContext) -> List[str]:
        """为API请求选择中间件"""
        middlewares = []

        if context.cache_eligible:
            middlewares.append("cache")

        if context.compression_eligible:
            middlewares.append("compression")

        middlewares.append("rate_limit")

        return middlewares

    def get_priority(self) -> int:
        return 300

    def supports(self, context: RequestContext) -> bool:
        return context.request_type == RequestType.API


class UploadRequestStrategy(MiddlewareSelectionStrategy):
    """文件上传策略"""

    def select_middlewares(self, context: RequestContext) -> List[str]:
        """为文件上传选择中间件"""
        return ["security", "rate_limit"]

    def get_priority(self) -> int:
        return 400

    def supports(self, context: RequestContext) -> bool:
        return context.request_type == RequestType.UPLOAD


class AdminRequestStrategy(MiddlewareSelectionStrategy):
    """管理员请求策略"""

    def select_middlewares(self, context: RequestContext) -> List[str]:
        """为管理员请求选择中间件"""
        return ["security", "auth", "rate_limit"]

    def get_priority(self) -> int:
        return 500

    def supports(self, context: RequestContext) -> bool:
        return context.request_type == RequestType.ADMIN


class ComplexityBasedStrategy(MiddlewareSelectionStrategy):
    """基于复杂度的策略"""

    def select_middlewares(self, context: RequestContext) -> List[str]:
        """根据复杂度选择中间件"""
        middlewares = []

        if context.complexity_score > 0.7:
            middlewares.append("timeout")

        if context.complexity_score > 0.9:
            middlewares.append("circuit_breaker")

        return middlewares

    def get_priority(self) -> int:
        return 600

    def supports(self, context: RequestContext) -> bool:
        return context.complexity_score > 0.7


class MiddlewareSelector:
    """中间件选择器 - 使用策略模式"""

    def __init__(self):
        """初始化中间件选择器"""
        self._strategies: List[MiddlewareSelectionStrategy] = []
        self._register_default_strategies()

    def _register_default_strategies(self) -> None:
        """注册默认策略"""
        self.register_strategy(BaseMiddlewareStrategy())
        self.register_strategy(SecurityMiddlewareStrategy())
        self.register_strategy(StaticResourceStrategy())
        self.register_strategy(ApiRequestStrategy())
        self.register_strategy(UploadRequestStrategy())
        self.register_strategy(AdminRequestStrategy())
        self.register_strategy(ComplexityBasedStrategy())

    def register_strategy(self, strategy: MiddlewareSelectionStrategy) -> None:
        """注册策略

        Args:
            strategy: 中间件选择策略
        """
        self._strategies.append(strategy)
        # 按优先级排序
        self._strategies.sort(key=lambda s: s.get_priority())
        logger.debug(f"Registered middleware strategy: {strategy.__class__.__name__}")

    def select_middlewares(self, context: RequestContext, available_middlewares: Set[str]) -> List[str]:
        """选择中间件列表

        Args:
            context: 请求上下文
            available_middlewares: 可用的中间件集合

        Returns:
            选择的中间件名称列表
        """
        selected_middlewares = []

        # 应用所有支持的策略
        for strategy in self._strategies:
            if strategy.supports(context):
                strategy_middlewares = strategy.select_middlewares(context)
                selected_middlewares.extend(strategy_middlewares)
                logger.debug(f"Strategy {strategy.__class__.__name__} selected: {strategy_middlewares}")

        # 去重并保持顺序,只保留可用的中间件
        unique_middlewares = []
        seen = set()

        for middleware_name in selected_middlewares:
            if middleware_name not in seen and middleware_name in available_middlewares:
                unique_middlewares.append(middleware_name)
                seen.add(middleware_name)

        logger.debug(f"Final middleware chain: {unique_middlewares}")
        return unique_middlewares

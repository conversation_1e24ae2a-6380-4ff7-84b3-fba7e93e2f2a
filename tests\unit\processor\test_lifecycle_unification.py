#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 生命周期接口统一测试 - 验证接口统一后的兼容性和功能
"""

import unittest
from typing import Any

from miniboot.processor.base import (
    ApplicationContextAware,
    BeanFactoryAware,
    BeanNameAware,
    BeanPostProcessor,
    DisposableBean,
    InitializingBean,
    Lifecycle,
    OrderedBeanPostProcessor,
    ProcessorOrder,
    SmartLifecycle,
)


class MockBeanPostProcessor(BeanPostProcessor):
    """测试用的Bean后置处理器"""

    def __init__(self):
        self.before_calls = []
        self.after_calls = []

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        self.before_calls.append((bean, bean_name))
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        self.after_calls.append((bean, bean_name))
        return bean

    def get_order(self) -> int:
        return 100


class MockOrderedBeanPostProcessor(OrderedBeanPostProcessor):
    """测试用的有序Bean后置处理器"""

    def __init__(self, order: int = 0):
        super().__init__(order)
        self.before_calls = []
        self.after_calls = []

    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        self.before_calls.append((bean, bean_name))
        return bean

    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        self.after_calls.append((bean, bean_name))
        return bean


class MockLifecycleBean(InitializingBean, DisposableBean, BeanNameAware, BeanFactoryAware, ApplicationContextAware, Lifecycle):
    """测试用的生命周期Bean"""

    def __init__(self):
        self.bean_name = None
        self.bean_factory = None
        self.application_context = None
        self.initialized = False
        self.destroyed = False
        self.running = False
        self.properties_set = False

    def set_bean_name(self, bean_name: str) -> None:
        self.bean_name = bean_name

    def set_bean_factory(self, bean_factory: Any) -> None:
        self.bean_factory = bean_factory

    def set_application_context(self, application_context: Any) -> None:
        self.application_context = application_context

    def after_properties_set(self) -> None:
        self.properties_set = True
        self.initialized = True

    def destroy(self) -> None:
        self.destroyed = True

    def start(self) -> None:
        self.running = True

    def stop(self) -> None:
        self.running = False

    def is_running(self) -> bool:
        return self.running


class MockSmartLifecycleBean(SmartLifecycle):
    """测试用的智能生命周期Bean"""

    def __init__(self, phase: int = 0, auto_startup: bool = True):
        self._phase = phase
        self._auto_startup = auto_startup
        self._running = False

    def start(self) -> None:
        self._running = True

    def stop(self, callback: Any = None) -> None:
        self._running = False
        if callback:
            callback()

    def is_running(self) -> bool:
        return self._running

    def get_phase(self) -> int:
        return self._phase

    def is_auto_startup(self) -> bool:
        return self._auto_startup


class LifecycleUnificationTestCase(unittest.TestCase):
    """生命周期接口统一测试用例"""

    def test_bean_post_processor_interface(self):
        """测试BeanPostProcessor接口"""
        processor = MockBeanPostProcessor()

        # 测试接口方法
        bean = object()
        bean_name = "testBean"

        result_before = processor.post_process_before_initialization(bean, bean_name)
        self.assertIs(result_before, bean)
        self.assertEqual(processor.before_calls, [(bean, bean_name)])

        result_after = processor.post_process_after_initialization(bean, bean_name)
        self.assertIs(result_after, bean)
        self.assertEqual(processor.after_calls, [(bean, bean_name)])

        # 测试执行顺序
        self.assertEqual(processor.get_order(), 100)

        # 测试支持检查
        self.assertTrue(processor.supports(bean, bean_name))
        self.assertFalse(processor.supports(None, bean_name))
        self.assertFalse(processor.supports(bean, ""))

    def test_ordered_bean_post_processor(self):
        """测试OrderedBeanPostProcessor"""
        processor = MockOrderedBeanPostProcessor(50)

        # 测试执行顺序
        self.assertEqual(processor.get_order(), 50)

        # 测试默认顺序
        default_processor = MockOrderedBeanPostProcessor()
        self.assertEqual(default_processor.get_order(), 0)

    def test_processor_order_constants(self):
        """测试处理器顺序常量"""
        # 验证常量存在且有合理的值
        self.assertIsInstance(ProcessorOrder.DEPENDENCY_INJECTION_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.VALUE_INJECTION_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.AWARE_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.LIFECYCLE_PROCESSOR, int)
        self.assertIsInstance(ProcessorOrder.AOP_PROCESSOR, int)

        # 验证顺序关系
        self.assertLess(ProcessorOrder.DEPENDENCY_INJECTION_PROCESSOR, ProcessorOrder.VALUE_INJECTION_PROCESSOR)
        self.assertLess(ProcessorOrder.VALUE_INJECTION_PROCESSOR, ProcessorOrder.AWARE_PROCESSOR)
        self.assertLess(ProcessorOrder.AWARE_PROCESSOR, ProcessorOrder.LIFECYCLE_PROCESSOR)

    def test_lifecycle_interfaces(self):
        """测试生命周期接口"""
        bean = MockLifecycleBean()

        # 测试BeanNameAware
        bean.set_bean_name("testBean")
        self.assertEqual(bean.bean_name, "testBean")

        # 测试BeanFactoryAware
        mock_factory = object()
        bean.set_bean_factory(mock_factory)
        self.assertIs(bean.bean_factory, mock_factory)

        # 测试ApplicationContextAware
        mock_context = object()
        bean.set_application_context(mock_context)
        self.assertIs(bean.application_context, mock_context)

        # 测试InitializingBean
        bean.after_properties_set()
        self.assertTrue(bean.properties_set)
        self.assertTrue(bean.initialized)

        # 测试Lifecycle
        self.assertFalse(bean.is_running())
        bean.start()
        self.assertTrue(bean.is_running())
        bean.stop()
        self.assertFalse(bean.is_running())

        # 测试DisposableBean
        bean.destroy()
        self.assertTrue(bean.destroyed)

    def test_smart_lifecycle_interface(self):
        """测试SmartLifecycle接口"""
        bean = MockSmartLifecycleBean(phase=100, auto_startup=True)

        # 测试基本生命周期
        self.assertFalse(bean.is_running())
        bean.start()
        self.assertTrue(bean.is_running())
        bean.stop()
        self.assertFalse(bean.is_running())

        # 测试智能生命周期特性
        self.assertEqual(bean.get_phase(), 100)
        self.assertTrue(bean.is_auto_startup())

        # 测试带回调的停止
        callback_called = False

        def stop_callback():
            nonlocal callback_called
            callback_called = True

        bean.start()
        bean.stop(stop_callback)
        self.assertFalse(bean.is_running())
        self.assertTrue(callback_called)

    def test_backward_compatibility_imports(self):
        """测试向后兼容性导入"""
        # 测试从bean.lifecycle导入
        from miniboot.bean.lifecycle import BeanPostProcessor as BeanBeanPostProcessor
        from miniboot.bean.lifecycle import InitializingBean as BeanInitializingBean

        # 验证导入的是同一个类（都来自processor.base）
        self.assertIs(BeanBeanPostProcessor, BeanPostProcessor)
        self.assertIs(BeanInitializingBean, InitializingBean)

    def test_interface_inheritance_hierarchy(self):
        """测试接口继承层次"""
        # 验证SmartLifecycle继承自Lifecycle
        self.assertTrue(issubclass(SmartLifecycle, Lifecycle))

        # 验证OrderedBeanPostProcessor继承自BeanPostProcessor
        self.assertTrue(issubclass(OrderedBeanPostProcessor, BeanPostProcessor))

        # 验证MockLifecycleBean实现了所有接口
        bean = MockLifecycleBean()
        self.assertIsInstance(bean, InitializingBean)
        self.assertIsInstance(bean, DisposableBean)
        self.assertIsInstance(bean, BeanNameAware)
        self.assertIsInstance(bean, BeanFactoryAware)
        self.assertIsInstance(bean, ApplicationContextAware)
        self.assertIsInstance(bean, Lifecycle)


if __name__ == "__main__":
    unittest.main()

"""
监控和诊断功能集成器

将所有监控和诊断组件整合在一起,提供统一的接口:
- 统一的监控数据收集
- 集成的健康检查
- 综合的诊断信息
- 性能分析报告
- 与 Actuator 模块的集成
"""

import asyncio
from typing import Any, Dict, List, Optional

from loguru import logger

from .analyzer import PerformanceAnalyzer
from .backpressure import BackpressureMetrics
from .diagnostics import DiagnosticEndpoint
from .health import BackpressureHealthCheck, HealthIndicator, SchedulingHealthCheck
from .scheduling import SchedulingMetrics
from .web import WebMetricsCollector


class MonitoringManager:
    """监控和诊断功能管理器"""

    def __init__(self, app_context=None):
        """
        初始化监控管理器

        Args:
            app_context: 应用上下文
        """
        self.app_context = app_context

        # 初始化各个监控组件
        self.web_metrics = WebMetricsCollector()
        self.backpressure_metrics = BackpressureMetrics()
        self.scheduling_metrics = SchedulingMetrics()

        # 初始化健康检查
        self.health_indicator = HealthIndicator()
        self._setup_health_checks()

        # 初始化诊断端点
        self.diagnostic_endpoint = DiagnosticEndpoint(
            app_context=app_context,
            web_metrics=self.web_metrics,
            backpressure_metrics=self.backpressure_metrics,
            scheduling_metrics=self.scheduling_metrics,
            health_indicator=self.health_indicator,
        )

        # 初始化性能分析器
        self.performance_analyzer = PerformanceAnalyzer(
            web_metrics=self.web_metrics, backpressure_metrics=self.backpressure_metrics, scheduling_metrics=self.scheduling_metrics
        )

        logger.info("MonitoringManager initialized")

    def _setup_health_checks(self) -> None:
        """设置健康检查"""
        # 注册背压控制健康检查
        backpressure_health_check = BackpressureHealthCheck(self.backpressure_metrics)
        self.health_indicator.register_health_check(backpressure_health_check)

        # 注册智能调度健康检查
        scheduling_health_check = SchedulingHealthCheck(self.scheduling_metrics)
        self.health_indicator.register_health_check(scheduling_health_check)

        logger.debug("Health checks configured")

    def get_web_metrics_collector(self) -> WebMetricsCollector:
        """获取 Web 指标收集器"""
        return self.web_metrics

    def get_backpressure_metrics_collector(self) -> BackpressureMetrics:
        """获取背压指标收集器"""
        return self.backpressure_metrics

    def get_scheduling_metrics_collector(self) -> SchedulingMetrics:
        """获取调度指标收集器"""
        return self.scheduling_metrics

    def get_health_indicator(self) -> HealthIndicator:
        """获取健康指示器"""
        return self.health_indicator

    def get_diagnostic_endpoint(self) -> DiagnosticEndpoint:
        """获取诊断端点"""
        return self.diagnostic_endpoint

    def get_performance_analyzer(self) -> PerformanceAnalyzer:
        """获取性能分析器"""
        return self.performance_analyzer

    async def get_comprehensive_metrics(self, period_seconds: int = 300) -> Dict[str, Any]:
        """获取综合指标"""
        try:
            # 并行获取各种指标
            web_metrics_task = asyncio.create_task(asyncio.to_thread(self.web_metrics.to_dict))
            backpressure_metrics_task = asyncio.create_task(asyncio.to_thread(self.backpressure_metrics.to_dict))
            scheduling_metrics_task = asyncio.create_task(asyncio.to_thread(self.scheduling_metrics.to_dict))
            health_check_task = asyncio.create_task(self.health_indicator.check_health())

            # 等待所有任务完成
            web_metrics_result = await web_metrics_task
            backpressure_metrics_result = await backpressure_metrics_task
            scheduling_metrics_result = await scheduling_metrics_task
            health_check_result = await health_check_task

            return {
                "timestamp": asyncio.get_event_loop().time(),
                "web_metrics": web_metrics_result,
                "backpressure_metrics": backpressure_metrics_result,
                "scheduling_metrics": scheduling_metrics_result,
                "health_status": self.health_indicator.to_dict(),
                "overall_status": self.health_indicator.get_overall_status().value,
            }

        except Exception as e:
            logger.error(f"Failed to get comprehensive metrics: {e}")
            return {"error": str(e), "timestamp": asyncio.get_event_loop().time()}

    async def get_health_status(self) -> Dict[str, Any]:
        """获取健康状态"""
        try:
            await self.health_indicator.check_health()
            return self.health_indicator.to_dict()
        except Exception as e:
            logger.error(f"Health check failed: {e}")
            return {"status": "DOWN", "error": str(e), "timestamp": asyncio.get_event_loop().time()}

    async def get_diagnostic_info(self) -> Dict[str, Any]:
        """获取诊断信息"""
        try:
            return await self.diagnostic_endpoint.get_full_diagnostic_info()
        except Exception as e:
            logger.error(f"Failed to get diagnostic info: {e}")
            return {"error": str(e), "timestamp": asyncio.get_event_loop().time()}

    async def get_performance_report(self, period_seconds: int = 3600) -> Dict[str, Any]:
        """获取性能报告"""
        try:
            return await asyncio.to_thread(self.performance_analyzer.to_dict, period_seconds)
        except Exception as e:
            logger.error(f"Failed to generate performance report: {e}")
            return {"error": str(e), "timestamp": asyncio.get_event_loop().time()}

    def reset_all_metrics(self) -> None:
        """重置所有指标"""
        try:
            self.web_metrics.reset_metrics()
            self.backpressure_metrics.reset_metrics()
            self.scheduling_metrics.reset_metrics()
            logger.info("All metrics reset successfully")
        except Exception as e:
            logger.error(f"Failed to reset metrics: {e}")

    def configure_thresholds(self, thresholds: Dict[str, Any]) -> None:
        """配置监控阈值"""
        try:
            # 这里可以添加阈值配置逻辑
            # 例如:更新性能分析器的阈值
            if hasattr(self.performance_analyzer, "performance_thresholds"):
                self.performance_analyzer.performance_thresholds.update(thresholds)

            logger.info("Monitoring thresholds configured")
        except Exception as e:
            logger.error(f"Failed to configure thresholds: {e}")

    async def start_monitoring(self) -> None:
        """启动监控"""
        try:
            # 这里可以添加定期监控任务
            logger.info("Monitoring started")
        except Exception as e:
            logger.error(f"Failed to start monitoring: {e}")

    async def stop_monitoring(self) -> None:
        """停止监控"""
        try:
            # 这里可以添加清理逻辑
            logger.info("Monitoring stopped")
        except Exception as e:
            logger.error(f"Failed to stop monitoring: {e}")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            "monitoring_integration": {
                "status": "UP",
                "components": {
                    "web_metrics": "available",
                    "backpressure_metrics": "available",
                    "scheduling_metrics": "available",
                    "health_indicator": "available",
                    "diagnostic_endpoint": "available",
                    "performance_analyzer": "available",
                },
                "health_checks_count": len(self.health_indicator.health_checks),
                "overall_health": self.health_indicator.get_overall_status().value,
            }
        }


# 全局监控管理实例
_monitoring_integration: Optional[MonitoringManager] = None


def get_monitoring_integration(app_context=None) -> MonitoringManager:
    """获取监控管理实例(单例模式)"""
    global _monitoring_integration

    if _monitoring_integration is None:
        _monitoring_integration = MonitoringManager(app_context)

    return _monitoring_integration


def initialize_monitoring(app_context=None) -> MonitoringManager:
    """初始化监控系统"""
    global _monitoring_integration

    _monitoring_integration = MonitoringManager(app_context)
    logger.info("Monitoring system initialized")

    return _monitoring_integration


async def get_monitoring_summary() -> Dict[str, Any]:
    """获取监控摘要"""
    integration = get_monitoring_integration()

    try:
        # 获取基本指标
        web_summary = integration.web_metrics.get_metrics_summary()
        backpressure_summary = integration.backpressure_metrics.get_metrics_summary()
        scheduling_summary = integration.scheduling_metrics.get_metrics_summary()

        # 获取健康状态
        overall_health = integration.health_indicator.get_overall_status()

        return {
            "timestamp": asyncio.get_event_loop().time(),
            "overall_health": overall_health.value,
            "summary": {
                "web": {
                    "total_requests": web_summary.total_requests,
                    "avg_response_time": web_summary.avg_response_time,
                    "error_rate": web_summary.error_rate,
                    "active_connections": web_summary.active_connections,
                },
                "backpressure": {
                    "stability_score": backpressure_summary.system_stability_score,
                    "protection_effectiveness": backpressure_summary.protection_effectiveness,
                    "circuit_breaker_triggers": backpressure_summary.circuit_breaker_triggers,
                },
                "scheduling": {
                    "total_tasks": scheduling_summary.total_tasks,
                    "classification_accuracy": scheduling_summary.classification_accuracy,
                    "strategy_effectiveness": scheduling_summary.strategy_effectiveness,
                },
            },
        }

    except Exception as e:
        logger.error(f"Failed to get monitoring summary: {e}")
        return {"error": str(e), "timestamp": asyncio.get_event_loop().time()}

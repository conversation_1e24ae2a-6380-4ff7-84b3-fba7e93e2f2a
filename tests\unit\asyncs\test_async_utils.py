#!/usr/bin/env python
"""
* @author: cz
* @description: 异步工具函数单元测试
"""

import asyncio
import time
import unittest

from miniboot.asyncs.tools import (AsyncBatch, filter, gather, map, retry,
                                   run_sync, timeout, timer)


class TestRunInThread(unittest.TestCase):
    """run_sync函数测试"""

    def setUp(self):
        """设置测试"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        """清理测试"""
        self.loop.close()

    def test_run_sync_function_in_thread(self):
        """测试在线程中运行同步函数"""

        def sync_function(x, y):
            time.sleep(0.1)  # 模拟耗时操作
            return x + y

        async def test_async():
            result = await run_sync(sync_function, 1, 2)
            return result

        result = self.loop.run_until_complete(test_async())
        self.assertEqual(result, 3)

    def test_run_with_kwargs(self):
        """测试使用关键字参数"""

        def sync_function(x, y=10):
            return x * y

        async def test_async():
            result = await run_sync(sync_function, 5, y=3)
            return result

        result = self.loop.run_until_complete(test_async())
        self.assertEqual(result, 15)

    def test_run_with_exception(self):
        """测试异常处理"""

        def failing_function():
            raise ValueError("Test error")

        async def test_async():
            with self.assertRaises(ValueError):
                await run_sync(failing_function)

        self.loop.run_until_complete(test_async())


class TestGatherWithConcurrency(unittest.TestCase):
    """gather函数测试"""

    def setUp(self):
        """设置测试"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        """清理测试"""
        self.loop.close()

    def test_basic_concurrency_control(self):
        """测试基本并发控制"""

        async def slow_task(value, delay=0.1):
            await asyncio.sleep(delay)
            return value * 2

        async def test_async():
            tasks = [slow_task(i) for i in range(5)]
            results = await gather(2, *tasks)
            return results

        start_time = time.time()
        results = self.loop.run_until_complete(test_async())
        elapsed = time.time() - start_time

        self.assertEqual(results, [0, 2, 4, 6, 8])
        # 由于并发限制为2，应该比完全并发慢
        self.assertGreater(elapsed, 0.2)  # 至少需要3轮执行

    def test_empty_awaitables(self):
        """测试空协程列表"""

        async def test_async():
            results = await gather(5)
            return results

        results = self.loop.run_until_complete(test_async())
        self.assertEqual(results, [])

    def test_invalid_limit(self):
        """测试无效的并发限制"""

        async def test_async():
            with self.assertRaises(ValueError):
                await gather(0, asyncio.sleep(0.1))

        self.loop.run_until_complete(test_async())


class TestTimeoutWrapper(unittest.TestCase):
    """timeout装饰器测试"""

    def setUp(self):
        """设置测试"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        """清理测试"""
        self.loop.close()

    def test_function_completes_within_timeout(self):
        """测试函数在超时时间内完成"""

        @timeout(1.0)
        async def fast_function():
            await asyncio.sleep(0.1)
            return "completed"

        result = self.loop.run_until_complete(fast_function())
        self.assertEqual(result, "completed")

    def test_function_times_out(self):
        """测试函数超时"""

        @timeout(0.1)
        async def slow_function():
            await asyncio.sleep(0.2)
            return "should not reach here"

        with self.assertRaises(asyncio.TimeoutError):
            self.loop.run_until_complete(slow_function())


class TestRetryAsync(unittest.TestCase):
    """retry函数测试"""

    def setUp(self):
        """设置测试"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        """清理测试"""
        self.loop.close()

    def test_successful_retry(self):
        """测试成功重试"""
        call_count = 0

        async def flaky_function():
            nonlocal call_count
            call_count += 1
            if call_count < 3:
                raise ValueError("Temporary error")
            return "success"

        async def test_async():
            result = await retry(flaky_function, max_retries=5, delay=0.01, exceptions=(ValueError,))
            return result

        result = self.loop.run_until_complete(test_async())
        self.assertEqual(result, "success")
        self.assertEqual(call_count, 3)

    def test_max_retries_exceeded(self):
        """测试超过最大重试次数"""

        async def always_failing_function():
            raise ValueError("Always fails")

        async def test_async():
            with self.assertRaises(ValueError):
                await retry(always_failing_function, max_retries=2, delay=0.01, exceptions=(ValueError,))

        self.loop.run_until_complete(test_async())

    def test_non_retryable_exception(self):
        """测试不可重试的异常"""

        async def function_with_non_retryable_error():
            raise TypeError("Non-retryable error")

        async def test_async():
            with self.assertRaises(TypeError):
                await retry(
                    function_with_non_retryable_error,
                    max_retries=3,
                    delay=0.01,
                    exceptions=(ValueError,),  # 只重试ValueError
                )

        self.loop.run_until_complete(test_async())


class TestAsyncBatch(unittest.TestCase):
    """AsyncBatch类测试"""

    def setUp(self):
        """设置测试"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        """清理测试"""
        self.loop.close()

    def test_batch_processing(self):
        """测试批处理"""

        async def process_item(item):
            await asyncio.sleep(0.01)
            return item * 2

        async def test_async():
            batch_processor = AsyncBatch(batch_size=3, concurrency=2)
            items = list(range(10))
            results = await batch_processor.process(items, process_item)
            return results

        results = self.loop.run_until_complete(test_async())
        expected = [i * 2 for i in range(10)]
        self.assertEqual(results, expected)

    def test_empty_items(self):
        """测试空项目列表"""

        async def process_item(item):
            return item

        async def test_async():
            batch_processor = AsyncBatch()
            results = await batch_processor.process([], process_item)
            return results

        results = self.loop.run_until_complete(test_async())
        self.assertEqual(results, [])


class TestAsyncTimer(unittest.TestCase):
    """timer上下文管理器测试"""

    def setUp(self):
        """设置测试"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        """清理测试"""
        self.loop.close()

    def test_timer_measurement(self):
        """测试计时功能"""

        async def test_async():
            async with timer() as timer_ctx:
                await asyncio.sleep(0.1)
            return timer_ctx.elapsed

        elapsed = self.loop.run_until_complete(test_async())
        self.assertGreater(elapsed, 0.09)  # 应该至少0.1秒
        self.assertLess(elapsed, 0.2)  # 但不应该太长


class TestAsyncMapAndFilter(unittest.TestCase):
    """map和filter函数测试"""

    def setUp(self):
        """设置测试"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)

    def tearDown(self):
        """清理测试"""
        self.loop.close()

    def test_async_map(self):
        """测试异步映射"""

        async def double(x):
            await asyncio.sleep(0.01)
            return x * 2

        async def test_async():
            items = [1, 2, 3, 4, 5]
            results = await map(double, items, concurrency=3)
            return results

        results = self.loop.run_until_complete(test_async())
        self.assertEqual(results, [2, 4, 6, 8, 10])

    def test_async_filter(self):
        """测试异步过滤"""

        async def is_even(x):
            await asyncio.sleep(0.01)
            return x % 2 == 0

        async def test_async():
            items = [1, 2, 3, 4, 5, 6]
            results = await filter(is_even, items, concurrency=3)
            return results

        results = self.loop.run_until_complete(test_async())
        self.assertEqual(results, [2, 4, 6])

    def test_empty_lists(self):
        """测试空列表"""

        async def identity(x):
            return x

        async def test_async():
            map_results = await map(identity, [])
            filter_results = await filter(identity, [])
            return map_results, filter_results

        map_results, filter_results = self.loop.run_until_complete(test_async())
        self.assertEqual(map_results, [])
        self.assertEqual(filter_results, [])


# 别名测试已移除，因为函数已重命名为更简洁的名称


if __name__ == "__main__":
    unittest.main()

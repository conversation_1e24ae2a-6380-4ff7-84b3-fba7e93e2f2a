# Mini-Boot Actuator 架构重构方案

## 总体设计理念

**核心原则：** 将 Actuator 从框架核心功能降级为可选的 Starter 扩展，基于 Python 生态特点实现模块化监控解决方案。

**设计目标：**

-   零依赖的核心监控功能
-   基于 mini.factories 的自动配置机制
-   条件化的 Web 集成支持
-   可配置的指标采集策略
-   符合 Python 生态的模块化特点

## 新的目录结构设计

```
miniboot/starters/actuator/
├── __init__.py                    # Starter 主入口
├── properties.py                  # 配置属性定义
├── configuration.py               # 主自动配置类
├── META-INF/
│   └── mini.factories            # 自动配置注册
├── context.py                    # 重构后的 ActuatorContext（移除 FastAPI 依赖）
├── endpoint.py                   # 端点抽象接口
├── registry.py                   # 端点注册器
├── endpoints/                    # 端点实现层：HTTP/API 暴露接口（从 miniboot/actuator 迁移）
│   ├── __init__.py
│   ├── health.py                 # 健康检查端点：/actuator/health
│   ├── info.py                   # 应用信息端点：/actuator/info
│   ├── metrics.py                # 指标端点：/actuator/metrics
│   ├── beans.py                  # Bean 信息端点：/actuator/beans
│   ├── env.py                    # 环境信息端点：/actuator/env
│   ├── loggers.py                # 日志配置端点：/actuator/loggers
│   ├── threaddump.py             # 线程转储端点：/actuator/threaddump
│   ├── custom.py                 # 自定义端点：用户扩展端点
│   └── aggregator.py             # 端点聚合器：统一管理所有端点
├── collectors/                   # 数据收集层：原始数据采集和预处理（从 miniboot/actuator 迁移）
│   ├── __init__.py
│   ├── application.py            # 应用级数据收集：Bean统计、启动信息、配置数据
│   ├── system.py                 # 系统级数据收集：CPU、内存、磁盘、网络
│   ├── health.py                 # 健康状态收集：组件健康检查、依赖服务状态
│   └── aggregator.py             # 收集器聚合：数据汇总和格式化
├── monitoring/                   # 监控业务层：智能分析和告警逻辑（从 miniboot/actuator 迁移）
│   ├── __init__.py
│   ├── alerts.py                 # 告警机制：阈值监控、异常检测、通知发送
│   ├── endpoint.py               # 监控端点：监控状态查询、告警历史
│   └── health.py                 # 健康监控：健康评分、趋势分析、故障预测
├── cache/                        # 缓存优化层：性能提升和数据缓存（从 miniboot/actuator 迁移）
│   ├── __init__.py
│   ├── cache.py                  # 缓存实现：指标数据缓存、查询结果缓存
│   ├── decorators.py             # 缓存装饰器：方法级缓存、TTL控制
│   └── endpoint.py               # 缓存端点：缓存统计、缓存管理
├── performance/                  # 性能分析层：深度性能监控和优化建议（从 miniboot/actuator 迁移）
│   ├── __init__.py
│   ├── decorators.py             # 性能装饰器：方法耗时统计、调用链追踪
│   ├── endpoint.py               # 性能端点：性能报告、瓶颈分析
│   ├── metrics.py                # 性能指标：响应时间、吞吐量、资源利用率
│   └── monitor.py                # 性能监控器：实时监控、性能基线、异常检测
├── security/                     # 安全功能（从 miniboot/actuator 迁移）
│   ├── __init__.py
│   ├── integration.py            # 安全集成
│   ├── manager.py                # 安全管理器
│   ├── middleware.py             # 安全中间件
│   └── security.py               # 安全核心
├── autoconfigure/                # 各个核心模块的指标采集配置
│   ├── __init__.py
│   ├── base.py                   # 基础自动配置类
│   ├── bean.py                   # Bean 模块指标采集配置
│   ├── scheduler.py              # 异步调度模块指标采集配置
│   ├── context.py                # Context 模块指标采集配置
│   ├── env.py                    # Environment 模块指标采集配置
│   └── web.py                    # Web 集成自动配置
└── web/                          # Web 集成模块（条件化）
    ├── __init__.py
    ├── integration.py            # Web 框架集成
    ├── routes.py                 # 路由注册
    └── middleware.py             # 中间件
```

## 配置文件设计

### application.yml 配置结构

```yaml
starters:
    actuator:
        enabled: true # Actuator 总开关
        auto-start: true # 自动启动

        # 指标采集配置
        metrics:
            enabled: true # 指标采集总开关
            collection-interval: 30s # 采集间隔
            cache-enabled: true # 缓存启用
            async-collection: true # 异步采集
            batch-size: 100 # 批处理大小

            # 核心模块指标采集配置（对应 autoconfigure 中的配置类）
            core-modules:
                bean: true # Bean 工厂指标采集
                scheduler: true # 异步调度指标采集
                context: true # 应用上下文指标采集
                env: true # 环境配置指标采集

            # 自定义收集器
            custom-collectors: []

        # Web 集成配置（条件化）
        web:
            enabled: true # Web 集成开关
            base-path: "/actuator" # 基础路径
            port: null # null 表示使用主应用端口
            cors-enabled: true # CORS 支持
            cors-origins: ["*"] # CORS 允许的源

            # 端点配置
            endpoints:
                health: true # 健康检查端点
                info: true # 应用信息端点
                metrics: true # 指标端点
                beans: false # Bean 信息端点
                env: false # 环境信息端点
                loggers: false # 日志配置端点
                threaddump: false # 线程转储端点
                custom: [] # 自定义端点列表

            # 安全配置
            security-enabled: false
            allowed-ips: []

        # 安全配置
        security:
            enabled: false
            authentication-required: false
            api-key: null
            allowed-roles: []
            rate-limiting: true
            max-requests-per-minute: 100

        # 全局配置
        graceful-shutdown: true
        shutdown-timeout: 30
        debug: false
        performance-monitoring: true
```

## 自动配置机制设计

### mini.factories 注册机制

```properties
# miniboot/starters/actuator/META-INF/mini.factories
miniboot.autoconfigure.EnableAutoConfiguration=\
miniboot.starters.actuator.configuration.ActuatorStarterAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.bean.BeanMetricsAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.scheduler.SchedulerMetricsAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.context.ContextMetricsAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.env.EnvMetricsAutoConfiguration,\
miniboot.starters.actuator.autoconfigure.web.WebAutoConfiguration
```

### 条件化配置类设计

````python
# 主自动配置类
@Configuration
@ConditionalOnProperty(name="starters.actuator.enabled", match_if_missing=True)
class ActuatorStarterAutoConfiguration:
    """Actuator Starter 主自动配置类"""

# Bean 模块指标采集配置
@Configuration
@ConditionalOnProperty(name="starters.actuator.metrics.core-modules.bean", match_if_missing=True)
@ConditionalOnBean(ActuatorStarterAutoConfiguration)
class BeanMetricsAutoConfiguration:
    """Bean 模块指标采集自动配置"""

# 异步调度模块指标采集配置
@Configuration
@ConditionalOnProperty(name="starters.actuator.metrics.core-modules.scheduler", match_if_missing=True)
@ConditionalOnBean(ActuatorStarterAutoConfiguration)
class SchedulerMetricsAutoConfiguration:
    """异步调度模块指标采集自动配置"""

# Web 集成自动配置（条件化）
@Configuration
@ConditionalOnClass(name="fastapi.FastAPI")
@ConditionalOnProperty(name="starters.web.enabled", match_if_missing=False)
@ConditionalOnProperty(name="starters.actuator.web.enabled", match_if_missing=True)
@ConditionalOnBean(ActuatorStarterAutoConfiguration)
class WebAutoConfiguration:
    """Web 集成自动配置（条件化）"""

## 各核心模块指标采集内容

### Bean 模块指标采集 (bean.py)
- **Bean 创建统计**：Bean 实例化数量、耗时分析
- **依赖注入性能**：依赖解析时间、循环依赖检测
- **三级缓存监控**：singleton_objects、early_singleton_objects、singleton_factories 命中率
- **Bean 生命周期**：初始化、销毁事件统计
- **Post Processor 性能**：各个 BeanPostProcessor 执行耗时

### 异步调度模块指标采集 (scheduler.py)
- **任务执行统计**：任务总数、成功率、失败率
- **线程池状态**：活跃线程数、队列长度、完成任务数
- **调度延迟监控**：任务调度延迟、执行延迟分析
- **APScheduler 集成**：作业状态、触发器统计
- **异步任务性能**：async/await 任务执行时间分布

### 应用上下文模块指标采集 (context.py)
- **启动性能统计**：14步启动序列各阶段耗时
- **模块加载状态**：各核心模块初始化状态和耗时
- **生命周期事件**：启动、停止、刷新事件统计
- **智能异步支持**：异步环境检测和切换统计
- **应用健康状态**：上下文状态、错误统计

### 环境配置模块指标采集 (env.py)
- **配置源统计**：各配置源（文件、环境变量等）加载状态
- **Profile 管理**：当前激活的 Profile、切换统计
- **属性解析性能**：配置属性解析耗时、缓存命中率
- **配置绑定统计**：@ConfigurationProperties 绑定性能
- **环境变量监控**：环境变量变更检测

## 迁移实施计划

### 阶段1：创建 Starter 目录结构 (30分钟)

#### 任务1.1：创建基础目录结构
- **文件路径**：`miniboot/starters/actuator/`
- **工作量**：15分钟
- **风险等级**：低
- **实现内容**：
  - 创建主目录和子目录结构
  - 创建 `__init__.py` 文件
  - 建立 `META-INF/mini.factories` 文件

#### 任务1.2：创建配置属性类
- **文件路径**：`miniboot/starters/actuator/properties.py`
- **工作量**：15分钟
- **风险等级**：低
- **实现内容**：
  - 定义 `ActuatorStarterProperties` 类
  - 实现配置属性的数据类结构
  - 添加配置验证和默认值

### 阶段2：迁移核心功能模块 (2小时)

#### 任务2.1：迁移端点模块
- **文件路径**：`miniboot/actuator/endpoints/` → `miniboot/starters/actuator/endpoints/`
- **工作量**：30分钟
- **风险等级**：中
- **依赖关系**：依赖任务1.1
- **实现内容**：
  - 复制所有端点实现文件
  - 更新导入路径：`from miniboot.actuator` → `from miniboot.starters.actuator`
  - 保持端点接口不变

#### 任务2.2：迁移数据收集器
- **文件路径**：`miniboot/actuator/collectors/` → `miniboot/starters/actuator/collectors/`
- **工作量**：30分钟
- **风险等级**：中
- **依赖关系**：依赖任务1.1
- **实现内容**：
  - 复制所有收集器文件
  - 更新导入路径
  - 保持收集器接口不变

#### 任务2.3：迁移监控模块
- **文件路径**：`miniboot/actuator/monitoring/` → `miniboot/starters/actuator/monitoring/`
- **工作量**：20分钟
- **风险等级**：低
- **依赖关系**：依赖任务1.1
- **实现内容**：
  - 复制监控相关文件
  - 更新导入路径

#### 任务2.4：迁移缓存和性能模块
- **文件路径**：`miniboot/actuator/{cache,performance}/` → `miniboot/starters/actuator/{cache,performance}/`
- **工作量**：20分钟
- **风险等级**：低
- **依赖关系**：依赖任务1.1
- **实现内容**：
  - 复制缓存和性能监控文件
  - 更新导入路径

#### 任务2.5：迁移安全模块
- **文件路径**：`miniboot/actuator/security/` → `miniboot/starters/actuator/security/`
- **工作量**：20分钟
- **风险等级**：低
- **依赖关系**：依赖任务1.1
- **实现内容**：
  - 复制安全相关文件
  - 更新导入路径

### 阶段3：重构自动配置机制 (1.5小时)

#### 任务3.1：重构 ActuatorContext
- **文件路径**：`miniboot/starters/actuator/context.py`
- **工作量**：45分钟
- **风险等级**：高
- **依赖关系**：依赖任务2.1-2.5
- **实现内容**：
  - 移除 FastAPI 强依赖：删除 `self.sub_app: Optional[FastAPI]`
  - 重构为纯监控上下文，不包含 Web 集成逻辑
  - 保留端点注册和管理功能
  - 添加条件化初始化逻辑

#### 任务3.2：创建主自动配置类
- **文件路径**：`miniboot/starters/actuator/configuration.py`
- **工作量**：30分钟
- **风险等级**：中
- **依赖关系**：依赖任务1.2、3.1
- **实现内容**：
  - 实现 `ActuatorStarterAutoConfiguration` 类
  - 添加 `@ConditionalOnProperty` 注解
  - 注册核心 Bean：ActuatorContext、EndpointRegistry

#### 任务3.3：创建 Bean 模块指标采集配置
- **文件路径**：`miniboot/starters/actuator/autoconfigure/bean.py`
- **工作量**：15分钟
- **风险等级**：中
- **依赖关系**：依赖任务3.2
- **实现内容**：
  - 实现 `BeanMetricsAutoConfiguration` 类
  - 添加条件化注解：`@ConditionalOnProperty(name="starters.actuator.metrics.core-modules.bean")`
  - 注册 `BeanMetricsCollector` Bean
  - 配置 Bean 工厂指标采集：创建统计、依赖注入性能、三级缓存监控

#### 任务3.4：创建异步调度模块指标采集配置
- **文件路径**：`miniboot/starters/actuator/autoconfigure/scheduler.py`
- **工作量**：15分钟
- **风险等级**：中
- **依赖关系**：依赖任务3.2
- **实现内容**：
  - 实现 `SchedulerMetricsAutoConfiguration` 类
  - 添加条件化注解：`@ConditionalOnProperty(name="starters.actuator.metrics.core-modules.scheduler")`
  - 注册 `SchedulerMetricsCollector` Bean
  - 配置异步调度指标采集：任务执行统计、线程池状态、调度延迟监控

#### 任务3.5：创建应用上下文模块指标采集配置
- **文件路径**：`miniboot/starters/actuator/autoconfigure/context.py`
- **工作量**：15分钟
- **风险等级**：中
- **依赖关系**：依赖任务3.2
- **实现内容**：
  - 实现 `ContextMetricsAutoConfiguration` 类
  - 添加条件化注解：`@ConditionalOnProperty(name="starters.actuator.metrics.core-modules.context")`
  - 注册 `ContextMetricsCollector` Bean
  - 配置上下文指标采集：启动性能统计、模块加载状态、生命周期事件

#### 任务3.6：创建环境配置模块指标采集配置
- **文件路径**：`miniboot/starters/actuator/autoconfigure/env.py`
- **工作量**：15分钟
- **风险等级**：中
- **依赖关系**：依赖任务3.2
- **实现内容**：
  - 实现 `EnvMetricsAutoConfiguration` 类
  - 添加条件化注解：`@ConditionalOnProperty(name="starters.actuator.metrics.core-modules.env")`
  - 注册 `EnvMetricsCollector` Bean
  - 配置环境指标采集：配置源统计、Profile 管理、属性解析性能

### 阶段4：实现条件化 Web 集成 (1小时)

#### 任务4.1：创建 Web 集成检查机制
- **文件路径**：`miniboot/starters/actuator/web/integration.py`
- **工作量**：20分钟
- **风险等级**：中
- **依赖关系**：依赖任务3.1
- **实现内容**：
  - 实现 `WebIntegrationChecker` 类
  - 检查 FastAPI 可用性：`is_fastapi_available()`
  - 检查 Web 模块启用状态：`is_web_module_enabled()`
  - 综合判断 Web 集成条件：`should_enable_web_integration()`

#### 任务4.2：实现动态路由注册
- **文件路径**：`miniboot/starters/actuator/web/routes.py`
- **工作量**：25分钟
- **风险等级**：中
- **依赖关系**：依赖任务4.1
- **实现内容**：
  - 实现 `ActuatorRouteRegistrar` 类
  - 动态注册启用的端点路由：`register_routes()`
  - 支持基础路径配置：`/actuator/*`
  - 处理端点路径映射

#### 任务4.3：创建 Web 自动配置类
- **文件路径**：`miniboot/starters/actuator/autoconfigure/web.py`
- **工作量**：15分钟
- **风险等级**：高
- **依赖关系**：依赖任务4.1、4.2
- **实现内容**：
  - 实现 `WebAutoConfiguration` 类
  - 添加多重条件化注解：
    - `@ConditionalOnClass(name="fastapi.FastAPI")`
    - `@ConditionalOnProperty(name="starters.web.enabled", match_if_missing=False)`
    - `@ConditionalOnProperty(name="starters.actuator.web.enabled", match_if_missing=True)`
  - 注册 Web 集成相关 Bean

### 阶段5：更新配置和测试 (1小时)

#### 任务5.1：更新 application.yml 配置
- **文件路径**：`resources/application.yml`
- **工作量**：15分钟
- **风险等级**：低
- **依赖关系**：依赖任务1.2
- **实现内容**：
  - 添加 `starters.actuator` 配置节点
  - 配置各核心模块指标采集开关
  - 配置 Web 集成选项
  - 移除原有 `actuator` 配置节点

#### 任务5.2：更新框架核心模块导入
- **文件路径**：`miniboot/context/application.py`, `miniboot/bean/factory.py` 等
- **工作量**：20分钟
- **风险等级**：中
- **依赖关系**：依赖所有前置任务
- **实现内容**：
  - 更新导入路径：`from miniboot.actuator` → `from miniboot.starters.actuator`
  - 检查并修复所有引用
  - 确保条件化加载正常工作

#### 任务5.3：删除原 Actuator 目录
- **文件路径**：`miniboot/actuator/`
- **工作量**：5分钟
- **风险等级**：高
- **依赖关系**：依赖任务5.2 完成并验证
- **实现内容**：
  - 备份原目录（可选）
  - 删除 `miniboot/actuator/` 整个目录
  - 确认无残留引用

#### 任务5.4：集成测试验证
- **文件路径**：测试用例
- **工作量**：20分钟
- **风险等级**：中
- **依赖关系**：依赖任务5.3
- **实现内容**：
  - 测试 Actuator 在无 Web 环境下的工作状态
  - 测试 Web 集成的条件化加载
  - 测试各核心模块指标采集功能
  - 验证配置文件的正确性

## 技术细节说明

### 迁移策略
1. **完整迁移**：将 `miniboot/actuator/` 下所有文件迁移到 `miniboot/starters/actuator/`
2. **路径更新**：所有导入路径从 `miniboot.actuator` 更新为 `miniboot.starters.actuator`
3. **结构保持**：保持原有的功能模块结构，仅改变位置
4. **零破坏**：不考虑向后兼容性，完全重构

### 条件化注解使用
```python
# 基础条件：Actuator 启用
@ConditionalOnProperty(name="starters.actuator.enabled", match_if_missing=True)

# 指标采集条件：特定模块启用
@ConditionalOnProperty(name="starters.actuator.metrics.core-modules.bean", match_if_missing=True)

# Web 集成条件：多重条件组合
@ConditionalOnClass(name="fastapi.FastAPI")
@ConditionalOnProperty(name="starters.web.enabled", match_if_missing=False)
@ConditionalOnProperty(name="starters.actuator.web.enabled", match_if_missing=True)
````

### 依赖关系图

```
任务1.1 (基础结构)
├── 任务1.2 (配置属性)
├── 任务2.1-2.5 (功能迁移)
│   └── 任务3.1 (重构Context)
│       ├── 任务3.2 (主配置)
│       │   ├── 任务3.3-3.6 (各模块配置)
│       │   └── 任务4.3 (Web配置)
│       └── 任务4.1-4.2 (Web集成)
└── 任务5.1-5.4 (配置更新和测试)
```

## 核心术语详解

### 🔌 Endpoints（端点实现层）

**职责**：对外暴露的 HTTP/API 接口，负责接收请求并返回格式化的监控数据

**特点**：

-   **面向用户**：直接响应外部 HTTP 请求（如 `/actuator/health`）
-   **数据展示**：将内部数据转换为用户友好的 JSON/XML 格式
-   **路由管理**：定义 URL 路径和请求方法（GET/POST）
-   **权限控制**：处理访问权限和安全验证
-   **协议适配**：支持不同的传输协议（HTTP、WebSocket）

**示例**：

```python
# health.py - 健康检查端点
@endpoint("/health")
def health_endpoint():
    return {"status": "UP", "components": {...}}
```

### 📊 Collectors（数据收集层）

**职责**：从各个系统组件中采集原始监控数据，进行初步处理和标准化

**特点**：

-   **数据源对接**：直接与系统组件交互（Bean 工厂、线程池、数据库连接池）
-   **原始采集**：获取最底层的运行时数据（内存使用、CPU 占用、请求计数）
-   **数据清洗**：过滤无效数据、格式标准化、单位转换
-   **采集策略**：支持定时采集、事件触发采集、按需采集
-   **性能优化**：异步采集、批量处理、采集频率控制

**示例**：

```python
# system.py - 系统级数据收集
class SystemCollector:
    def collect_memory_usage(self):
        return psutil.virtual_memory()._asdict()

    def collect_cpu_usage(self):
        return psutil.cpu_percent(interval=1)
```

### 🔍 Monitoring（监控业务层）

**职责**：基于收集的数据进行智能分析、异常检测和告警处理

**特点**：

-   **智能分析**：趋势分析、异常检测、模式识别
-   **告警逻辑**：阈值监控、规则引擎、告警去重
-   **业务理解**：理解应用业务逻辑，提供有意义的监控指标
-   **预测能力**：基于历史数据预测潜在问题
-   **决策支持**：为运维决策提供数据支撑

**示例**：

```python
# alerts.py - 告警机制
class AlertManager:
    def check_memory_threshold(self, usage_percent):
        if usage_percent > 85:
            self.send_alert("HIGH_MEMORY_USAGE", usage_percent)

    def detect_anomaly(self, metrics_history):
        # 基于历史数据检测异常模式
        return self.ml_model.predict(metrics_history)
```

### ⚡ Performance（性能分析层）

**职责**：专注于应用性能的深度分析、瓶颈识别和优化建议

**特点**：

-   **深度剖析**：方法级性能分析、调用链追踪、热点识别
-   **性能基线**：建立性能基准、对比分析、回归检测
-   **瓶颈定位**：精确定位性能瓶颈的根本原因
-   **优化建议**：基于分析结果提供具体的优化建议
-   **实时监控**：实时性能监控、性能事件捕获

**示例**：

```python
# monitor.py - 性能监控器
class PerformanceMonitor:
    @performance_trace
    def trace_method_execution(self, method_name, execution_time):
        if execution_time > self.baseline[method_name] * 1.5:
            self.report_performance_degradation(method_name, execution_time)

    def analyze_bottleneck(self, call_stack):
        # 分析调用栈找出性能瓶颈
        return self.identify_slowest_components(call_stack)
```

## 四层架构的协作关系

```
┌─────────────────┐    HTTP请求     ┌─────────────────┐
│   外部用户/工具   │ ──────────────→ │   Endpoints     │ 端点实现层
└─────────────────┘                 └─────────────────┘
                                            │
                                            ▼ 数据请求
                                    ┌─────────────────┐
                                    │   Monitoring    │ 监控业务层
                                    └─────────────────┘
                                            │
                                            ▼ 原始数据需求
                                    ┌─────────────────┐
                                    │   Collectors    │ 数据收集层
                                    └─────────────────┘
                                            │
                                            ▼ 系统调用
                                    ┌─────────────────┐
                                    │  系统组件/资源   │ 底层数据源
                                    └─────────────────┘

                    ┌─────────────────┐
                    │   Performance   │ 性能分析层（横切关注点）
                    └─────────────────┘
                            │
                            ▼ 性能数据注入到各层
```

## 预期效果

1. **模块化**：Actuator 成为可选的 Starter 扩展，不再是框架核心依赖
2. **零依赖**：核心监控功能不强制依赖 Web 框架，支持纯后台应用
3. **可配置**：通过 application.yml 灵活控制指标采集和端点暴露
4. **条件化**：Web 集成仅在 FastAPI 可用且启用时生效
5. **Python 化**：符合 Python 生态的模块化和配置管理特点
6. **分层清晰**：四层架构职责明确，便于扩展和维护

## 自定义指标系统设计

### 🎯 设计目标

提供类似 Spring Boot Micrometer 的自定义指标能力，让业务开发者能够轻松添加自定义监控指标。

### 📊 指标类型支持

#### 1. Counter（计数器）

**用途**：单调递增的计数，如请求总数、错误次数

```python
from miniboot.starters.actuator.metrics import Counter, metrics_registry

# 编程式使用
request_counter = metrics_registry.counter("http.requests.total",
                                         tags={"method": "GET", "endpoint": "/api/users"})
request_counter.increment()

# 注解式使用
@Component
class UserService:
    @Counted(name="user.operations", description="用户操作计数")
    def create_user(self, user_data):
        # 业务逻辑
        pass
```

#### 2. Gauge（仪表盘）

**用途**：可增可减的数值，如当前活跃连接数、队列长度

```python
from miniboot.starters.actuator.metrics import Gauge

# 编程式使用
active_connections = metrics_registry.gauge("connections.active",
                                           lambda: get_active_connection_count())

# 注解式使用（自动管理）
@Component
class ConnectionPool:
    def __init__(self):
        self._active_count = 0
        metrics_registry.gauge("pool.connections.active", lambda: self._active_count)

    @Gauged(name="pool.size", description="连接池大小")
    def get_pool_size(self):
        return len(self.connections)
```

#### 3. Timer（计时器）

**用途**：测量操作耗时，如方法执行时间、请求响应时间

```python
from miniboot.starters.actuator.metrics import Timer
import time

# 编程式使用
with metrics_registry.timer("database.query.duration").time():
    # 数据库查询操作
    result = database.query("SELECT * FROM users")

# 注解式使用
@Component
class UserRepository:
    @Timed(name="user.query.duration", description="用户查询耗时")
    def find_user_by_id(self, user_id):
        return self.db.query(f"SELECT * FROM users WHERE id = {user_id}")

    @Timed(name="user.save.duration", description="用户保存耗时",
           tags={"operation": "create"})
    def save_user(self, user):
        return self.db.save(user)
```

#### 4. Histogram（直方图）

**用途**：统计数值分布，如请求大小分布、响应时间分布

```python
from miniboot.starters.actuator.metrics import Histogram

# 编程式使用
request_size_histogram = metrics_registry.histogram("http.request.size")
request_size_histogram.record(len(request_body))

# 注解式使用
@Component
class FileService:
    @Histogram(name="file.upload.size", description="文件上传大小分布")
    def upload_file(self, file_data):
        # 自动记录 file_data 的大小
        return self.storage.save(file_data)
```

### 🏗️ 核心组件架构

#### MetricsRegistry（指标注册中心）

```python
# miniboot/starters/actuator/metrics/registry.py
from typing import Dict, List, Callable, Optional
from dataclasses import dataclass
import threading
import time

@dataclass
class MetricTags:
    """指标标签"""
    tags: Dict[str, str]

    def to_string(self) -> str:
        return ",".join([f"{k}={v}" for k, v in sorted(self.tags.items())])

class MetricsRegistry:
    """指标注册中心 - 类似 Spring Boot 的 MeterRegistry"""

    def __init__(self):
        self._counters: Dict[str, Counter] = {}
        self._gauges: Dict[str, Gauge] = {}
        self._timers: Dict[str, Timer] = {}
        self._histograms: Dict[str, Histogram] = {}
        self._lock = threading.RLock()

    def counter(self, name: str, description: str = "", tags: Dict[str, str] = None) -> 'Counter':
        """创建或获取计数器"""
        key = self._build_key(name, tags)
        with self._lock:
            if key not in self._counters:
                self._counters[key] = Counter(name, description, tags or {})
            return self._counters[key]

    def gauge(self, name: str, supplier: Callable[[], float],
              description: str = "", tags: Dict[str, str] = None) -> 'Gauge':
        """创建或获取仪表盘"""
        key = self._build_key(name, tags)
        with self._lock:
            if key not in self._gauges:
                self._gauges[key] = Gauge(name, description, tags or {}, supplier)
            return self._gauges[key]

    def timer(self, name: str, description: str = "", tags: Dict[str, str] = None) -> 'Timer':
        """创建或获取计时器"""
        key = self._build_key(name, tags)
        with self._lock:
            if key not in self._timers:
                self._timers[key] = Timer(name, description, tags or {})
            return self._timers[key]

    def histogram(self, name: str, description: str = "", tags: Dict[str, str] = None) -> 'Histogram':
        """创建或获取直方图"""
        key = self._build_key(name, tags)
        with self._lock:
            if key not in self._histograms:
                self._histograms[key] = Histogram(name, description, tags or {})
            return self._histograms[key]

    def get_all_metrics(self) -> Dict[str, any]:
        """获取所有指标数据"""
        with self._lock:
            metrics = {}

            # 收集计数器数据
            for counter in self._counters.values():
                metrics[counter.get_metric_name()] = counter.get_value()

            # 收集仪表盘数据
            for gauge in self._gauges.values():
                metrics[gauge.get_metric_name()] = gauge.get_value()

            # 收集计时器数据
            for timer in self._timers.values():
                timer_data = timer.get_statistics()
                for key, value in timer_data.items():
                    metrics[f"{timer.get_metric_name()}.{key}"] = value

            # 收集直方图数据
            for histogram in self._histograms.values():
                hist_data = histogram.get_statistics()
                for key, value in hist_data.items():
                    metrics[f"{histogram.get_metric_name()}.{key}"] = value

            return metrics

    def _build_key(self, name: str, tags: Dict[str, str]) -> str:
        """构建指标唯一键"""
        if not tags:
            return name
        tag_str = ",".join([f"{k}={v}" for k, v in sorted(tags.items())])
        return f"{name}[{tag_str}]"

# 全局指标注册中心实例
metrics_registry = MetricsRegistry()
```

### 🎨 注解支持

#### 指标注解定义

```python
# miniboot/starters/actuator/metrics/annotations.py
from functools import wraps
from typing import Dict, Callable, Optional
import time
import inspect

def Counted(name: str, description: str = "", tags: Dict[str, str] = None):
    """计数器注解 - 类似 Spring Boot 的 @Counted"""
    def decorator(func: Callable):
        counter = metrics_registry.counter(name, description, tags)

        @wraps(func)
        def wrapper(*args, **kwargs):
            counter.increment()
            return func(*args, **kwargs)
        return wrapper
    return decorator

def Timed(name: str, description: str = "", tags: Dict[str, str] = None):
    """计时器注解 - 类似 Spring Boot 的 @Timed"""
    def decorator(func: Callable):
        timer = metrics_registry.timer(name, description, tags)

        @wraps(func)
        def wrapper(*args, **kwargs):
            start_time = time.time()
            try:
                result = func(*args, **kwargs)
                return result
            finally:
                duration = time.time() - start_time
                timer.record(duration)
        return wrapper
    return decorator

def Gauged(name: str, description: str = "", tags: Dict[str, str] = None):
    """仪表盘注解 - 自动将返回值作为 Gauge 值"""
    def decorator(func: Callable):
        def supplier():
            # 这里需要获取实例来调用方法，实际实现会更复杂
            return func()

        gauge = metrics_registry.gauge(name, supplier, description, tags)
        return func
    return decorator

# 装饰器风格（更 Pythonic）
def metrics_counter(name: str, description: str = "", tags: Dict[str, str] = None):
    """计数器装饰器"""
    return Counted(name, description, tags)

def metrics_timer(name: str, description: str = "", tags: Dict[str, str] = None):
    """计时器装饰器"""
    return Timed(name, description, tags)

def metrics_gauge(name: str, description: str = "", tags: Dict[str, str] = None):
    """仪表盘装饰器"""
    return Gauged(name, description, tags)
```

### 🔧 业务代码使用示例

#### 完整的业务服务示例

```python
# 业务代码示例：用户服务
from miniboot.annotations import Component, Service, Autowired
from miniboot.starters.actuator.metrics import metrics_registry, Counted, Timed, metrics_counter

@Service
class UserService:
    """用户服务 - 展示各种自定义指标的使用"""

    def __init__(self):
        # 编程式创建指标
        self.user_cache_hits = metrics_registry.counter(
            "user.cache.hits",
            description="用户缓存命中次数",
            tags={"service": "user", "type": "cache"}
        )

        self.user_cache_misses = metrics_registry.counter(
            "user.cache.misses",
            description="用户缓存未命中次数",
            tags={"service": "user", "type": "cache"}
        )

        # 活跃用户数量（Gauge）
        metrics_registry.gauge(
            "user.active.count",
            lambda: self.get_active_user_count(),
            description="当前活跃用户数量"
        )

    @Counted(name="user.operations.total", description="用户操作总数",
             tags={"operation": "create"})
    @Timed(name="user.create.duration", description="用户创建耗时")
    def create_user(self, user_data: dict) -> dict:
        """创建用户 - 自动统计次数和耗时"""
        # 模拟业务逻辑
        user = self._validate_and_save_user(user_data)

        # 手动记录业务指标
        if self._is_premium_user(user):
            metrics_registry.counter("user.premium.created").increment()

        return user

    @metrics_counter(name="user.queries.total", description="用户查询总数",
                    tags={"operation": "query"})
    def find_user_by_id(self, user_id: str) -> dict:
        """查询用户 - 使用装饰器风格"""
        # 缓存逻辑
        user = self._get_from_cache(user_id)
        if user:
            self.user_cache_hits.increment()
            return user

        self.user_cache_misses.increment()
        user = self._load_from_database(user_id)
        self._put_to_cache(user_id, user)
        return user

    @Timed(name="user.batch.process.duration", description="批量处理用户耗时")
    def batch_process_users(self, user_ids: list) -> dict:
        """批量处理用户"""
        # 记录批量大小分布
        batch_size_histogram = metrics_registry.histogram("user.batch.size")
        batch_size_histogram.record(len(user_ids))

        results = []
        for user_id in user_ids:
            result = self.process_single_user(user_id)
            results.append(result)

        return {"processed": len(results), "results": results}

    def get_active_user_count(self) -> int:
        """获取活跃用户数量 - 用于 Gauge"""
        # 实际业务逻辑
        return len(self._get_active_sessions())

@Component
class OrderService:
    """订单服务 - 展示更多指标使用场景"""

    def __init__(self):
        # 订单状态分布（多个 Counter）
        self.order_counters = {
            'created': metrics_registry.counter("order.status.created"),
            'paid': metrics_registry.counter("order.status.paid"),
            'shipped': metrics_registry.counter("order.status.shipped"),
            'delivered': metrics_registry.counter("order.status.delivered"),
            'cancelled': metrics_registry.counter("order.status.cancelled")
        }

        # 订单金额分布（Histogram）
        self.order_amount_histogram = metrics_registry.histogram(
            "order.amount.distribution",
            description="订单金额分布"
        )

    @Timed(name="order.create.duration", tags={"service": "order"})
    def create_order(self, order_data: dict) -> dict:
        """创建订单"""
        order = self._process_order(order_data)

        # 记录订单状态
        self.order_counters['created'].increment()

        # 记录订单金额
        self.order_amount_histogram.record(order['amount'])

        # 根据订单类型添加标签
        order_type_counter = metrics_registry.counter(
            "order.type.total",
            tags={"type": order['type'], "payment_method": order['payment_method']}
        )
        order_type_counter.increment()

        return order

    def update_order_status(self, order_id: str, new_status: str):
        """更新订单状态"""
        if new_status in self.order_counters:
            self.order_counters[new_status].increment()

        # 记录状态变更
        status_change_counter = metrics_registry.counter(
            "order.status.changes",
            tags={"to_status": new_status}
        )
        status_change_counter.increment()
```

### 📈 指标数据查看

#### 通过 Actuator 端点查看指标

```bash
# 查看所有自定义指标
curl http://localhost:8080/actuator/metrics

# 查看特定指标
curl http://localhost:8080/actuator/metrics/user.operations.total
curl http://localhost:8080/actuator/metrics/order.amount.distribution

# 带标签过滤的指标查询
curl http://localhost:8080/actuator/metrics/order.type.total?tag=type:premium
```

#### 指标数据格式示例

```json
{
    "name": "user.operations.total",
    "description": "用户操作总数",
    "baseUnit": null,
    "measurements": [
        {
            "statistic": "COUNT",
            "value": 1247.0
        }
    ],
    "availableTags": [
        {
            "tag": "operation",
            "values": ["create", "update", "delete"]
        },
        {
            "tag": "service",
            "values": ["user"]
        }
    ]
}
```

### ⚙️ 配置支持

#### application.yml 配置

```yaml
starters:
    actuator:
        metrics:
            # 自定义指标配置
            custom:
                enabled: true # 启用自定义指标
                auto-time-requests: true # 自动为 HTTP 请求计时
                auto-count-requests: true # 自动统计 HTTP 请求次数

                # 指标命名配置
                naming:
                    prefix: "miniboot" # 指标名称前缀
                    separator: "." # 分隔符

                # 标签配置
                common-tags: # 所有指标的公共标签
                    application: "my-app"
                    environment: "production"
                    version: "1.0.0"

                # 导出配置
                export:
                    prometheus:
                        enabled: true # 启用 Prometheus 格式导出
                        endpoint: "/actuator/prometheus"
                    json:
                        enabled: true # 启用 JSON 格式导出
                        pretty-print: true # JSON 格式化输出

                # 性能配置
                performance:
                    collection-interval: 10s # 指标收集间隔
                    max-metrics: 10000 # 最大指标数量
                    cleanup-interval: 300s # 清理过期指标间隔
```

### 🔌 与 Actuator 架构集成

#### 自定义指标收集器

```python
# miniboot/starters/actuator/collectors/custom_metrics.py
from miniboot.starters.actuator.collectors.base import BaseCollector
from miniboot.starters.actuator.metrics import metrics_registry

class CustomMetricsCollector(BaseCollector):
    """自定义指标收集器"""

    def collect(self) -> dict:
        """收集所有自定义指标数据"""
        return {
            "custom_metrics": metrics_registry.get_all_metrics(),
            "metrics_count": len(metrics_registry.get_all_metrics()),
            "collection_timestamp": self.get_current_timestamp()
        }

    def get_health_status(self) -> dict:
        """获取指标系统健康状态"""
        metrics_count = len(metrics_registry.get_all_metrics())
        return {
            "status": "UP" if metrics_count < 10000 else "WARN",
            "details": {
                "total_metrics": metrics_count,
                "max_allowed": 10000
            }
        }
```

#### 自定义指标端点

```python
# miniboot/starters/actuator/endpoints/custom_metrics.py
from miniboot.starters.actuator.endpoints.base import BaseEndpoint
from miniboot.starters.actuator.metrics import metrics_registry

class CustomMetricsEndpoint(BaseEndpoint):
    """自定义指标端点"""

    def __init__(self):
        super().__init__(endpoint_id="custom-metrics", enabled=True)

    def invoke(self, **kwargs) -> dict:
        """返回自定义指标数据"""
        return {
            "metrics": metrics_registry.get_all_metrics(),
            "summary": self._get_metrics_summary()
        }

    def _get_metrics_summary(self) -> dict:
        """获取指标摘要"""
        all_metrics = metrics_registry.get_all_metrics()
        return {
            "total_count": len(all_metrics),
            "counters": len([k for k in all_metrics.keys() if not any(suffix in k for suffix in ['.mean', '.max', '.count'])]),
            "timers": len([k for k in all_metrics.keys() if '.mean' in k]),
            "gauges": len([k for k in all_metrics.keys() if k.endswith('.value')])
        }
```

这样，Mini-Boot 就提供了完整的自定义指标能力，开发者可以像使用 Spring Boot 一样轻松地添加业务监控指标！

#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean生命周期管理单元测试
"""

import unittest
from typing import Any
from unittest.mock import Mock, patch

from miniboot.bean.base import InitializingBean, DisposableBean, Lifecycle, SmartLifecycle
from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import DefaultBeanFactory


class SimpleLifecycleBean:
    """简单生命周期Bean"""
    
    def __init__(self, name: str = "simple"):
        self.name = name
        self.initialized = False
        self.destroyed = False
        self.init_order = []
        self.destroy_order = []
    
    def init_method(self):
        """初始化方法"""
        self.initialized = True
        self.init_order.append("init_method")
    
    def destroy_method(self):
        """销毁方法"""
        self.destroyed = True
        self.destroy_order.append("destroy_method")


class InterfaceLifecycleBean(InitializingBean, DisposableBean):
    """实现生命周期接口的Bean"""
    
    def __init__(self, name: str = "interface"):
        self.name = name
        self.initialized = False
        self.destroyed = False
        self.init_order = []
        self.destroy_order = []
    
    def after_properties_set(self):
        """InitializingBean接口方法"""
        self.initialized = True
        self.init_order.append("after_properties_set")
    
    def destroy(self):
        """DisposableBean接口方法"""
        self.destroyed = True
        self.destroy_order.append("destroy")


class ComplexLifecycleBean(InitializingBean, DisposableBean):
    """复杂生命周期Bean，同时有接口和方法"""
    
    def __init__(self, name: str = "complex"):
        self.name = name
        self.initialized = False
        self.destroyed = False
        self.init_order = []
        self.destroy_order = []
    
    def after_properties_set(self):
        """InitializingBean接口方法"""
        self.init_order.append("after_properties_set")
    
    def custom_init(self):
        """自定义初始化方法"""
        self.initialized = True
        self.init_order.append("custom_init")
    
    def destroy(self):
        """DisposableBean接口方法"""
        self.destroy_order.append("destroy")
    
    def custom_destroy(self):
        """自定义销毁方法"""
        self.destroyed = True
        self.destroy_order.append("custom_destroy")


class SmartLifecycleBean(SmartLifecycle):
    """智能生命周期Bean"""
    
    def __init__(self, name: str = "smart"):
        self.name = name
        self.running = False
        self.auto_startup = True
        self.phase = 0
        self.lifecycle_order = []
    
    def start(self):
        """启动方法"""
        if not self.running:
            self.running = True
            self.lifecycle_order.append("start")
    
    def stop(self):
        """停止方法"""
        if self.running:
            self.running = False
            self.lifecycle_order.append("stop")
    
    def is_running(self) -> bool:
        """检查是否运行中"""
        return self.running
    
    def is_auto_startup(self) -> bool:
        """是否自动启动"""
        return self.auto_startup
    
    def get_phase(self) -> int:
        """获取阶段"""
        return self.phase
    
    def stop_with_callback(self, callback):
        """带回调的停止方法"""
        self.stop()
        self.lifecycle_order.append("stop_with_callback")
        if callback:
            callback()


class TestBeanLifecycle(unittest.TestCase):
    """Bean生命周期测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.factory = DefaultBeanFactory()
    
    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.factory, 'destroy_singletons'):
            self.factory.destroy_singletons()
    
    def test_simple_lifecycle_methods(self):
        """测试简单生命周期方法"""
        bean_def = BeanDefinition(
            bean_name="simpleBean",
            bean_class=SimpleLifecycleBean,
            scope=BeanScope.SINGLETON,
            init_method_name="init_method",
            destroy_method_name="destroy_method"
        )
        
        self.factory._registry.register_bean_definition("simpleBean", bean_def)
        
        # 获取Bean，应该自动调用初始化方法
        bean = self.factory.get_bean("simpleBean")
        self.assertTrue(bean.initialized)
        self.assertIn("init_method", bean.init_order)
        
        # 销毁Bean
        self.factory.destroy_singletons()
        self.assertTrue(bean.destroyed)
        self.assertIn("destroy_method", bean.destroy_order)
    
    def test_interface_lifecycle(self):
        """测试生命周期接口"""
        bean_def = BeanDefinition(
            bean_name="interfaceBean",
            bean_class=InterfaceLifecycleBean,
            scope=BeanScope.SINGLETON
        )
        
        self.factory._registry.register_bean_definition("interfaceBean", bean_def)
        
        # 获取Bean，应该自动调用接口方法
        bean = self.factory.get_bean("interfaceBean")
        self.assertTrue(bean.initialized)
        self.assertIn("after_properties_set", bean.init_order)
        
        # 销毁Bean
        self.factory.destroy_singletons()
        self.assertTrue(bean.destroyed)
        self.assertIn("destroy", bean.destroy_order)
    
    def test_complex_lifecycle_order(self):
        """测试复杂生命周期顺序"""
        bean_def = BeanDefinition(
            bean_name="complexBean",
            bean_class=ComplexLifecycleBean,
            scope=BeanScope.SINGLETON,
            init_method_name="custom_init",
            destroy_method_name="custom_destroy"
        )
        
        self.factory._registry.register_bean_definition("complexBean", bean_def)
        
        # 获取Bean
        bean = self.factory.get_bean("complexBean")
        
        # 验证初始化顺序：接口方法 -> 自定义方法
        self.assertEqual(len(bean.init_order), 2)
        self.assertEqual(bean.init_order[0], "after_properties_set")
        self.assertEqual(bean.init_order[1], "custom_init")
        self.assertTrue(bean.initialized)
        
        # 销毁Bean
        self.factory.destroy_singletons()
        
        # 验证销毁顺序：自定义方法 -> 接口方法
        self.assertEqual(len(bean.destroy_order), 2)
        self.assertEqual(bean.destroy_order[0], "custom_destroy")
        self.assertEqual(bean.destroy_order[1], "destroy")
        self.assertTrue(bean.destroyed)
    
    def test_prototype_lifecycle(self):
        """测试原型Bean生命周期"""
        bean_def = BeanDefinition(
            bean_name="prototypeBean",
            bean_class=SimpleLifecycleBean,
            scope=BeanScope.PROTOTYPE,
            init_method_name="init_method"
            # 注意：原型Bean通常不调用销毁方法
        )
        
        self.factory._registry.register_bean_definition("prototypeBean", bean_def)
        
        # 获取多个实例
        bean1 = self.factory.get_bean("prototypeBean")
        bean2 = self.factory.get_bean("prototypeBean")
        
        # 验证都是不同实例且都被初始化
        self.assertIsNot(bean1, bean2)
        self.assertTrue(bean1.initialized)
        self.assertTrue(bean2.initialized)
        
        # 原型Bean不会被工厂销毁
        self.factory.destroy_singletons()
        self.assertFalse(bean1.destroyed)
        self.assertFalse(bean2.destroyed)
    
    def test_smart_lifecycle(self):
        """测试智能生命周期"""
        bean_def = BeanDefinition(
            bean_name="smartBean",
            bean_class=SmartLifecycleBean,
            scope=BeanScope.SINGLETON
        )
        
        self.factory._registry.register_bean_definition("smartBean", bean_def)
        
        # 获取Bean
        bean = self.factory.get_bean("smartBean")
        
        # 验证SmartLifecycle接口
        self.assertIsInstance(bean, SmartLifecycle)
        self.assertTrue(bean.is_auto_startup())
        self.assertEqual(bean.get_phase(), 0)
        
        # 手动启动和停止
        bean.start()
        self.assertTrue(bean.is_running())
        self.assertIn("start", bean.lifecycle_order)
        
        bean.stop()
        self.assertFalse(bean.is_running())
        self.assertIn("stop", bean.lifecycle_order)
    
    def test_lifecycle_error_handling(self):
        """测试生命周期错误处理"""
        class ErrorBean:
            def __init__(self):
                self.name = "error"
            
            def bad_init(self):
                raise ValueError("Init error")
            
            def bad_destroy(self):
                raise ValueError("Destroy error")
        
        bean_def = BeanDefinition(
            bean_name="errorBean",
            bean_class=ErrorBean,
            scope=BeanScope.SINGLETON,
            init_method_name="bad_init",
            destroy_method_name="bad_destroy"
        )
        
        self.factory._registry.register_bean_definition("errorBean", bean_def)
        
        # 初始化错误应该被处理
        with self.assertRaises(Exception):
            self.factory.get_bean("errorBean")
    
    def test_lifecycle_with_dependencies(self):
        """测试带依赖的生命周期"""
        # 创建依赖Bean
        dependency_def = BeanDefinition(
            bean_name="dependencyBean",
            bean_class=SimpleLifecycleBean,
            scope=BeanScope.SINGLETON,
            init_method_name="init_method"
        )
        self.factory._registry.register_bean_definition("dependencyBean", dependency_def)
        
        # 创建主Bean
        class DependentBean(InitializingBean):
            def __init__(self):
                self.dependency = None
                self.initialized = False
            
            def set_dependency(self, dep):
                self.dependency = dep
            
            def after_properties_set(self):
                if self.dependency and self.dependency.initialized:
                    self.initialized = True
        
        main_def = BeanDefinition(
            bean_name="mainBean",
            bean_class=DependentBean,
            scope=BeanScope.SINGLETON
        )
        main_def.add_property_value("dependency", ref="dependencyBean")
        self.factory._registry.register_bean_definition("mainBean", main_def)
        
        # 获取主Bean
        main_bean = self.factory.get_bean("mainBean")
        
        # 验证依赖注入和生命周期
        self.assertIsNotNone(main_bean.dependency)
        self.assertTrue(main_bean.dependency.initialized)
        self.assertTrue(main_bean.initialized)
    
    def test_multiple_beans_lifecycle_order(self):
        """测试多个Bean的生命周期顺序"""
        beans_created = []
        beans_destroyed = []
        
        class OrderedBean:
            def __init__(self, name: str):
                self.name = name
                beans_created.append(name)
            
            def init_method(self):
                pass
            
            def destroy_method(self):
                beans_destroyed.append(self.name)
        
        # 注册多个Bean
        for i in range(3):
            bean_def = BeanDefinition(
                bean_name=f"bean{i}",
                bean_class=OrderedBean,
                scope=BeanScope.SINGLETON,
                destroy_method_name="destroy_method"
            )
            bean_def.add_constructor_arg(0, value=f"bean{i}")
            self.factory._registry.register_bean_definition(f"bean{i}", bean_def)
        
        # 获取所有Bean
        for i in range(3):
            self.factory.get_bean(f"bean{i}")
        
        # 验证创建顺序
        self.assertEqual(len(beans_created), 3)
        
        # 销毁所有Bean
        self.factory.destroy_singletons()
        
        # 验证销毁顺序（通常与创建顺序相反）
        self.assertEqual(len(beans_destroyed), 3)


if __name__ == '__main__':
    unittest.main()

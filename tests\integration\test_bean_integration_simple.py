#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean模块简化集成测试
"""

import unittest
import time
from typing import Any, Dict

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import DefaultBeanFactory


class SimpleService:
    """简单服务类"""
    
    def __init__(self, name: str = "simple-service"):
        self.name = name
        self.initialized = False
        self.repository = None
    
    def init(self):
        """初始化方法"""
        self.initialized = True
    
    def set_repository(self, repository):
        """设置仓储依赖"""
        self.repository = repository
    
    def get_data(self) -> Dict[str, Any]:
        """获取数据"""
        return {
            "service_name": self.name,
            "initialized": self.initialized,
            "has_repository": self.repository is not None,
            "repository_data": self.repository.get_data() if self.repository else None
        }


class SimpleRepository:
    """简单仓储类"""
    
    def __init__(self, database_url: str = "memory://"):
        self.database_url = database_url
        self.connected = False
        self.data = []
    
    def connect(self):
        """连接数据库"""
        self.connected = True
        self.data = ["item1", "item2", "item3"]
    
    def get_data(self) -> list:
        """获取数据"""
        if not self.connected:
            raise RuntimeError("Database not connected")
        return self.data.copy()


class TestBeanIntegrationSimple(unittest.TestCase):
    """Bean模块简化集成测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.factory = DefaultBeanFactory()
    
    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.factory, 'destroy_singletons'):
            self.factory.destroy_singletons()
    
    def test_basic_bean_integration(self):
        """测试基本Bean集成"""
        # 注册仓储Bean
        repository_def = BeanDefinition(
            bean_name="simpleRepository",
            bean_class=SimpleRepository,
            scope=BeanScope.SINGLETON,
            init_method_name="connect"
        )
        repository_def.add_constructor_arg(0, value="sqlite:///:memory:")
        
        self.factory._registry.register_bean_definition("simpleRepository", repository_def)
        
        # 注册服务Bean，注入仓储依赖
        service_def = BeanDefinition(
            bean_name="simpleService",
            bean_class=SimpleService,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        service_def.add_constructor_arg(0, value="integration-service")
        service_def.add_property_value("repository", ref="simpleRepository")
        
        self.factory._registry.register_bean_definition("simpleService", service_def)
        
        # 获取服务Bean
        service = self.factory.get_bean("simpleService")
        
        # 验证Bean创建和依赖注入
        self.assertIsInstance(service, SimpleService)
        self.assertEqual(service.name, "integration-service")
        self.assertTrue(service.initialized)
        self.assertIsNotNone(service.repository)
        self.assertIsInstance(service.repository, SimpleRepository)
        
        # 验证仓储Bean已初始化
        self.assertTrue(service.repository.connected)
        self.assertEqual(service.repository.database_url, "sqlite:///:memory:")
        
        # 测试业务功能
        data = service.get_data()
        self.assertTrue(data["initialized"])
        self.assertTrue(data["has_repository"])
        self.assertEqual(len(data["repository_data"]), 3)
        
        print("✅ Basic bean integration test passed!")
    
    def test_multiple_beans_integration(self):
        """测试多个Bean集成"""
        # 注册多个Bean
        for i in range(5):
            bean_def = BeanDefinition(
                bean_name=f"service{i}",
                bean_class=SimpleService,
                scope=BeanScope.SINGLETON,
                init_method_name="init"
            )
            bean_def.add_constructor_arg(0, value=f"service-{i}")
            
            self.factory._registry.register_bean_definition(f"service{i}", bean_def)
        
        # 获取所有Bean
        services = []
        for i in range(5):
            service = self.factory.get_bean(f"service{i}")
            services.append(service)
        
        # 验证所有Bean都正确创建
        self.assertEqual(len(services), 5)
        for i, service in enumerate(services):
            self.assertEqual(service.name, f"service-{i}")
            self.assertTrue(service.initialized)
        
        print("✅ Multiple beans integration test passed!")
    
    def test_bean_scopes_integration(self):
        """测试Bean作用域集成"""
        # 注册单例Bean
        singleton_def = BeanDefinition(
            bean_name="singletonService",
            bean_class=SimpleService,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("singletonService", singleton_def)
        
        # 注册原型Bean
        prototype_def = BeanDefinition(
            bean_name="prototypeService",
            bean_class=SimpleService,
            scope=BeanScope.PROTOTYPE
        )
        self.factory._registry.register_bean_definition("prototypeService", prototype_def)
        
        # 测试单例作用域
        singleton1 = self.factory.get_bean("singletonService")
        singleton2 = self.factory.get_bean("singletonService")
        self.assertIs(singleton1, singleton2)
        
        # 测试原型作用域
        prototype1 = self.factory.get_bean("prototypeService")
        prototype2 = self.factory.get_bean("prototypeService")
        self.assertIsNot(prototype1, prototype2)
        
        print("✅ Bean scopes integration test passed!")
    
    def test_bean_lifecycle_integration(self):
        """测试Bean生命周期集成"""
        # 创建带销毁方法的Bean
        class LifecycleService(SimpleService):
            def __init__(self, name: str = "lifecycle"):
                super().__init__(name)
                self.destroyed = False
            
            def destroy(self):
                self.destroyed = True
        
        # 注册Bean
        lifecycle_def = BeanDefinition(
            bean_name="lifecycleService",
            bean_class=LifecycleService,
            scope=BeanScope.SINGLETON,
            init_method_name="init",
            destroy_method_name="destroy"
        )
        
        self.factory._registry.register_bean_definition("lifecycleService", lifecycle_def)
        
        # 获取Bean并验证初始化
        service = self.factory.get_bean("lifecycleService")
        self.assertTrue(service.initialized)
        self.assertFalse(service.destroyed)
        
        # 销毁Bean
        self.factory.destroy_singletons()
        self.assertTrue(service.destroyed)
        
        print("✅ Bean lifecycle integration test passed!")
    
    def test_performance_integration(self):
        """测试性能集成"""
        # 注册Bean
        service_def = BeanDefinition(
            bean_name="perfService",
            bean_class=SimpleService,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("perfService", service_def)
        
        # 测试Bean获取性能
        start_time = time.time()
        
        # 获取Bean 1000次
        for _ in range(1000):
            service = self.factory.get_bean("perfService")
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 验证性能（应该在0.1秒内完成）
        self.assertLess(execution_time, 0.1)
        
        avg_time = execution_time / 1000
        print(f"✅ Performance test passed! Average access time: {avg_time*1000:.2f}ms")
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 测试获取不存在的Bean
        with self.assertRaises(KeyError):
            self.factory.get_bean("nonexistentBean")
        
        # 测试Bean创建错误
        class ErrorBean:
            def __init__(self):
                raise ValueError("Creation error")
        
        error_def = BeanDefinition(
            bean_name="errorBean",
            bean_class=ErrorBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("errorBean", error_def)
        
        with self.assertRaises(Exception):
            self.factory.get_bean("errorBean")
        
        print("✅ Error handling integration test passed!")
    
    def test_complex_dependency_integration(self):
        """测试复杂依赖集成"""
        # 创建依赖链：ServiceA -> ServiceB -> Repository
        
        # 注册Repository
        repo_def = BeanDefinition(
            bean_name="repository",
            bean_class=SimpleRepository,
            scope=BeanScope.SINGLETON,
            init_method_name="connect"
        )
        self.factory._registry.register_bean_definition("repository", repo_def)
        
        # 注册ServiceB，依赖Repository
        service_b_def = BeanDefinition(
            bean_name="serviceB",
            bean_class=SimpleService,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        service_b_def.add_constructor_arg(0, value="service-b")
        service_b_def.add_property_value("repository", ref="repository")
        self.factory._registry.register_bean_definition("serviceB", service_b_def)
        
        # 注册ServiceA，依赖ServiceB
        service_a_def = BeanDefinition(
            bean_name="serviceA",
            bean_class=SimpleService,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        service_a_def.add_constructor_arg(0, value="service-a")
        service_a_def.add_property_value("repository", ref="serviceB")  # 将ServiceB作为repository注入
        self.factory._registry.register_bean_definition("serviceA", service_a_def)
        
        # 获取ServiceA
        service_a = self.factory.get_bean("serviceA")
        
        # 验证依赖链
        self.assertIsInstance(service_a, SimpleService)
        self.assertEqual(service_a.name, "service-a")
        self.assertTrue(service_a.initialized)
        
        # ServiceA的repository实际上是ServiceB
        service_b = service_a.repository
        self.assertIsInstance(service_b, SimpleService)
        self.assertEqual(service_b.name, "service-b")
        self.assertTrue(service_b.initialized)
        
        # ServiceB的repository是真正的Repository
        repository = service_b.repository
        self.assertIsInstance(repository, SimpleRepository)
        self.assertTrue(repository.connected)
        
        print("✅ Complex dependency integration test passed!")


if __name__ == '__main__':
    unittest.main()

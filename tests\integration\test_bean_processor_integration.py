#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean模块与Processor模块集成测试
"""

import unittest
import asyncio
from typing import Any, Dict, List
from unittest.mock import Mock, patch

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import Default<PERSON>eanFactory
from miniboot.bean.base import BeanPostProcessor
from miniboot.processor.base import BaseProcessor


class TestDataProcessor(BaseProcessor):
    """测试数据处理器"""
    
    def __init__(self, name: str = "data-processor"):
        super().__init__()
        self.name = name
        self.processed_items = []
        self.dependencies = {}
    
    def set_dependency(self, name: str, dependency: Any):
        """设置依赖"""
        self.dependencies[name] = dependency
    
    async def process(self, data: Any) -> Any:
        """处理数据"""
        processed_data = {
            "original": data,
            "processor": self.name,
            "timestamp": self.get_current_timestamp(),
            "dependencies_count": len(self.dependencies)
        }
        self.processed_items.append(processed_data)
        return processed_data
    
    def get_current_timestamp(self) -> float:
        """获取当前时间戳"""
        import time
        return time.time()
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            "name": self.name,
            "processed_count": len(self.processed_items),
            "dependencies": list(self.dependencies.keys())
        }


class TestValidationProcessor(BaseProcessor):
    """测试验证处理器"""
    
    def __init__(self, rules: List[str] = None):
        super().__init__()
        self.rules = rules or ["not_empty", "valid_format"]
        self.validation_results = []
    
    async def process(self, data: Any) -> Any:
        """验证数据"""
        result = {
            "data": data,
            "valid": True,
            "errors": []
        }
        
        # 应用验证规则
        for rule in self.rules:
            if rule == "not_empty" and not data:
                result["valid"] = False
                result["errors"].append("Data cannot be empty")
            elif rule == "valid_format" and not isinstance(data, (str, dict)):
                result["valid"] = False
                result["errors"].append("Invalid data format")
        
        self.validation_results.append(result)
        return result


class TestProcessorBeanPostProcessor(BeanPostProcessor):
    """测试处理器Bean后处理器"""
    
    def __init__(self):
        self.processed_processors = []
    
    def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        """初始化前处理"""
        if isinstance(bean, BaseProcessor):
            # 为处理器添加通用配置
            if hasattr(bean, 'name') and not bean.name:
                bean.name = f"auto_{bean_name}"
        return bean
    
    def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        """初始化后处理"""
        if isinstance(bean, BaseProcessor):
            self.processed_processors.append(bean_name)
            # 为处理器添加监控
            if hasattr(bean, 'set_dependency'):
                bean.set_dependency("monitor", f"monitor_for_{bean_name}")
        return bean


class TestBeanProcessorIntegration(unittest.TestCase):
    """Bean模块与Processor模块集成测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.factory = DefaultBeanFactory()
    
    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.factory, 'destroy_singletons'):
            self.factory.destroy_singletons()
    
    def test_processor_bean_registration(self):
        """测试处理器Bean注册"""
        # 注册数据处理器Bean
        processor_def = BeanDefinition(
            bean_name="dataProcessor",
            bean_class=TestDataProcessor,
            scope=BeanScope.SINGLETON
        )
        processor_def.add_constructor_arg(0, value="test-data-processor")
        
        self.factory._registry.register_bean_definition("dataProcessor", processor_def)
        
        # 获取处理器Bean
        processor = self.factory.get_bean("dataProcessor")
        self.assertIsInstance(processor, TestDataProcessor)
        self.assertEqual(processor.name, "test-data-processor")
    
    def test_processor_dependency_injection(self):
        """测试处理器依赖注入"""
        # 注册依赖Bean
        validation_def = BeanDefinition(
            bean_name="validationProcessor",
            bean_class=TestValidationProcessor,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("validationProcessor", validation_def)
        
        # 注册主处理器Bean，注入依赖
        data_def = BeanDefinition(
            bean_name="dataProcessor",
            bean_class=TestDataProcessor,
            scope=BeanScope.SINGLETON
        )
        data_def.add_property_value("validator", ref="validationProcessor")
        
        self.factory._registry.register_bean_definition("dataProcessor", data_def)
        
        # 获取处理器并验证依赖注入
        processor = self.factory.get_bean("dataProcessor")
        self.assertIsInstance(processor, TestDataProcessor)
        self.assertIn("validator", processor.dependencies)
        self.assertIsInstance(processor.dependencies["validator"], TestValidationProcessor)
    
    def test_processor_bean_post_processing(self):
        """测试处理器Bean后处理"""
        # 注册Bean后处理器
        post_processor = TestProcessorBeanPostProcessor()
        self.factory.add_bean_post_processor(post_processor)
        
        # 注册处理器Bean
        processor_def = BeanDefinition(
            bean_name="autoProcessor",
            bean_class=TestDataProcessor,
            scope=BeanScope.SINGLETON
        )
        
        self.factory._registry.register_bean_definition("autoProcessor", processor_def)
        
        # 获取处理器并验证后处理
        processor = self.factory.get_bean("autoProcessor")
        self.assertIn("autoProcessor", post_processor.processed_processors)
        self.assertIn("monitor", processor.dependencies)
        self.assertEqual(processor.dependencies["monitor"], "monitor_for_autoProcessor")
    
    def test_processor_chain_integration(self):
        """测试处理器链集成"""
        # 注册验证处理器
        validation_def = BeanDefinition(
            bean_name="validator",
            bean_class=TestValidationProcessor,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("validator", validation_def)
        
        # 注册数据处理器
        data_def = BeanDefinition(
            bean_name="dataProcessor",
            bean_class=TestDataProcessor,
            scope=BeanScope.SINGLETON
        )
        data_def.add_property_value("validator", ref="validator")
        
        self.factory._registry.register_bean_definition("dataProcessor", data_def)
        
        # 获取处理器
        validator = self.factory.get_bean("validator")
        data_processor = self.factory.get_bean("dataProcessor")
        
        # 测试处理器链
        async def test_chain():
            # 验证数据
            validation_result = await validator.process("test_data")
            self.assertTrue(validation_result["valid"])
            
            # 处理数据
            if validation_result["valid"]:
                process_result = await data_processor.process(validation_result["data"])
                self.assertEqual(process_result["original"], "test_data")
                self.assertIn("timestamp", process_result)
        
        # 运行异步测试
        asyncio.run(test_chain())
    
    def test_processor_lifecycle_integration(self):
        """测试处理器生命周期集成"""
        # 创建带生命周期的处理器
        class LifecycleProcessor(TestDataProcessor):
            def __init__(self, name: str = "lifecycle-processor"):
                super().__init__(name)
                self.started = False
                self.stopped = False
            
            def start(self):
                """启动处理器"""
                self.started = True
            
            def stop(self):
                """停止处理器"""
                self.stopped = True
        
        # 注册处理器Bean
        processor_def = BeanDefinition(
            bean_name="lifecycleProcessor",
            bean_class=LifecycleProcessor,
            scope=BeanScope.SINGLETON,
            init_method_name="start",
            destroy_method_name="stop"
        )
        
        self.factory._registry.register_bean_definition("lifecycleProcessor", processor_def)
        
        # 获取处理器并验证生命周期
        processor = self.factory.get_bean("lifecycleProcessor")
        self.assertTrue(processor.started)
        self.assertFalse(processor.stopped)
        
        # 销毁Bean
        self.factory.destroy_singletons()
        self.assertTrue(processor.stopped)
    
    def test_processor_configuration_integration(self):
        """测试处理器配置集成"""
        # 注册配置Bean
        config = {
            "batch_size": 100,
            "timeout": 30,
            "retry_count": 3
        }
        self.factory.register_singleton("processorConfig", config)
        
        # 创建配置化处理器
        class ConfigurableProcessor(TestDataProcessor):
            def __init__(self, name: str = "configurable"):
                super().__init__(name)
                self.config = None
            
            def set_config(self, config: Dict[str, Any]):
                self.config = config
            
            def get_batch_size(self) -> int:
                return self.config.get("batch_size", 10) if self.config else 10
        
        # 注册处理器Bean
        processor_def = BeanDefinition(
            bean_name="configurableProcessor",
            bean_class=ConfigurableProcessor,
            scope=BeanScope.SINGLETON
        )
        processor_def.add_property_value("config", ref="processorConfig")
        
        self.factory._registry.register_bean_definition("configurableProcessor", processor_def)
        
        # 获取处理器并验证配置
        processor = self.factory.get_bean("configurableProcessor")
        self.assertIsNotNone(processor.config)
        self.assertEqual(processor.get_batch_size(), 100)
    
    def test_processor_error_handling_integration(self):
        """测试处理器错误处理集成"""
        # 创建会出错的处理器
        class ErrorProcessor(TestDataProcessor):
            def __init__(self, name: str = "error-processor"):
                super().__init__(name)
                self.error_handler = None
            
            def set_error_handler(self, handler):
                self.error_handler = handler
            
            async def process(self, data: Any) -> Any:
                if data == "error":
                    if self.error_handler:
                        return self.error_handler.handle_error("Processing error", data)
                    else:
                        raise ValueError("Processing error")
                return await super().process(data)
        
        # 创建错误处理器
        class ErrorHandler:
            def __init__(self):
                self.handled_errors = []
            
            def handle_error(self, error_msg: str, data: Any) -> Dict[str, Any]:
                error_info = {
                    "error": error_msg,
                    "data": data,
                    "handled": True
                }
                self.handled_errors.append(error_info)
                return error_info
        
        # 注册错误处理器Bean
        error_handler_def = BeanDefinition(
            bean_name="errorHandler",
            bean_class=ErrorHandler,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("errorHandler", error_handler_def)
        
        # 注册错误处理器Bean
        processor_def = BeanDefinition(
            bean_name="errorProcessor",
            bean_class=ErrorProcessor,
            scope=BeanScope.SINGLETON
        )
        processor_def.add_property_value("error_handler", ref="errorHandler")
        
        self.factory._registry.register_bean_definition("errorProcessor", processor_def)
        
        # 测试错误处理
        processor = self.factory.get_bean("errorProcessor")
        error_handler = self.factory.get_bean("errorHandler")
        
        async def test_error_handling():
            # 正常处理
            normal_result = await processor.process("normal_data")
            self.assertEqual(normal_result["original"], "normal_data")
            
            # 错误处理
            error_result = await processor.process("error")
            self.assertTrue(error_result["handled"])
            self.assertEqual(len(error_handler.handled_errors), 1)
        
        asyncio.run(test_error_handling())
    
    def test_processor_performance_monitoring(self):
        """测试处理器性能监控集成"""
        # 创建性能监控器
        class PerformanceMonitor:
            def __init__(self):
                self.metrics = {}
            
            def record_execution_time(self, processor_name: str, execution_time: float):
                if processor_name not in self.metrics:
                    self.metrics[processor_name] = []
                self.metrics[processor_name].append(execution_time)
            
            def get_average_time(self, processor_name: str) -> float:
                if processor_name not in self.metrics:
                    return 0.0
                times = self.metrics[processor_name]
                return sum(times) / len(times) if times else 0.0
        
        # 创建带监控的处理器
        class MonitoredProcessor(TestDataProcessor):
            def __init__(self, name: str = "monitored"):
                super().__init__(name)
                self.monitor = None
            
            def set_monitor(self, monitor: PerformanceMonitor):
                self.monitor = monitor
            
            async def process(self, data: Any) -> Any:
                import time
                start_time = time.time()
                
                result = await super().process(data)
                
                end_time = time.time()
                execution_time = end_time - start_time
                
                if self.monitor:
                    self.monitor.record_execution_time(self.name, execution_time)
                
                return result
        
        # 注册监控器Bean
        monitor_def = BeanDefinition(
            bean_name="performanceMonitor",
            bean_class=PerformanceMonitor,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("performanceMonitor", monitor_def)
        
        # 注册处理器Bean
        processor_def = BeanDefinition(
            bean_name="monitoredProcessor",
            bean_class=MonitoredProcessor,
            scope=BeanScope.SINGLETON
        )
        processor_def.add_property_value("monitor", ref="performanceMonitor")
        
        self.factory._registry.register_bean_definition("monitoredProcessor", processor_def)
        
        # 测试性能监控
        processor = self.factory.get_bean("monitoredProcessor")
        monitor = self.factory.get_bean("performanceMonitor")
        
        async def test_monitoring():
            # 处理一些数据
            await processor.process("data1")
            await processor.process("data2")
            await processor.process("data3")
            
            # 验证监控数据
            avg_time = monitor.get_average_time("monitored")
            self.assertGreater(avg_time, 0)
            self.assertEqual(len(monitor.metrics["monitored"]), 3)
        
        asyncio.run(test_monitoring())


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean依赖注入单元测试
"""

import unittest
from typing import Any
from unittest.mock import Mock, patch

from miniboot.bean.definition import BeanDefinition, BeanScope, PropertyValue, ConstructorArgument
from miniboot.bean.factory import Default<PERSON>eanFactory
from miniboot.bean.utils import BeanCreationError, CircularDependencyError


class ServiceBean:
    """服务Bean测试类"""
    
    def __init__(self, name: str = "service"):
        self.name = name
        self.initialized = False
    
    def init(self):
        self.initialized = True
    
    def get_data(self) -> str:
        return f"data from {self.name}"


class RepositoryBean:
    """仓储Bean测试类"""
    
    def __init__(self, database_url: str = "memory://"):
        self.database_url = database_url
        self.connected = False
    
    def connect(self):
        self.connected = True
    
    def find_all(self) -> list:
        return ["item1", "item2", "item3"]


class ControllerBean:
    """控制器Bean测试类"""
    
    def __init__(self):
        self.service = None
        self.repository = None
        self.config_value = None
    
    def set_service(self, service: ServiceBean):
        self.service = service
    
    def set_repository(self, repository: RepositoryBean):
        self.repository = repository
    
    def set_config(self, value: str):
        self.config_value = value
    
    def process_request(self) -> dict:
        if not self.service or not self.repository:
            raise ValueError("Dependencies not injected")
        
        return {
            "service_data": self.service.get_data(),
            "repository_data": self.repository.find_all(),
            "config": self.config_value
        }


class ComplexBean:
    """复杂依赖Bean测试类"""
    
    def __init__(self, service: ServiceBean, repository: RepositoryBean, timeout: int = 30):
        self.service = service
        self.repository = repository
        self.timeout = timeout
        self.ready = False
    
    def init(self):
        if self.service and self.repository:
            self.ready = True


class TestDependencyInjection(unittest.TestCase):
    """依赖注入测试"""
    
    def setUp(self):
        """测试前置设置"""
        self.factory = DefaultBeanFactory()
    
    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.factory, 'destroy_singletons'):
            self.factory.destroy_singletons()
    
    def test_constructor_injection(self):
        """测试构造函数注入"""
        # 注册依赖Bean
        service_def = BeanDefinition(
            bean_name="serviceBean",
            bean_class=ServiceBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("serviceBean", service_def)
        
        repository_def = BeanDefinition(
            bean_name="repositoryBean",
            bean_class=RepositoryBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("repositoryBean", repository_def)
        
        # 注册复杂Bean，使用构造函数注入
        complex_def = BeanDefinition(
            bean_name="complexBean",
            bean_class=ComplexBean,
            scope=BeanScope.SINGLETON
        )
        complex_def.add_constructor_arg(0, ref="serviceBean")
        complex_def.add_constructor_arg(1, ref="repositoryBean")
        complex_def.add_constructor_arg(2, value=60)  # timeout参数
        
        self.factory._registry.register_bean_definition("complexBean", complex_def)
        
        # 获取Bean并验证注入
        complex_bean = self.factory.get_bean("complexBean")
        self.assertIsInstance(complex_bean, ComplexBean)
        self.assertIsInstance(complex_bean.service, ServiceBean)
        self.assertIsInstance(complex_bean.repository, RepositoryBean)
        self.assertEqual(complex_bean.timeout, 60)
    
    def test_property_injection(self):
        """测试属性注入"""
        # 注册依赖Bean
        service_def = BeanDefinition(
            bean_name="serviceBean",
            bean_class=ServiceBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("serviceBean", service_def)
        
        repository_def = BeanDefinition(
            bean_name="repositoryBean",
            bean_class=RepositoryBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("repositoryBean", repository_def)
        
        # 注册控制器Bean，使用属性注入
        controller_def = BeanDefinition(
            bean_name="controllerBean",
            bean_class=ControllerBean,
            scope=BeanScope.SINGLETON
        )
        controller_def.add_property_value("service", ref="serviceBean")
        controller_def.add_property_value("repository", ref="repositoryBean")
        controller_def.add_property_value("config_value", value="production")
        
        self.factory._registry.register_bean_definition("controllerBean", controller_def)
        
        # 获取Bean并验证注入
        controller_bean = self.factory.get_bean("controllerBean")
        self.assertIsInstance(controller_bean, ControllerBean)
        self.assertIsInstance(controller_bean.service, ServiceBean)
        self.assertIsInstance(controller_bean.repository, RepositoryBean)
        self.assertEqual(controller_bean.config_value, "production")
        
        # 验证功能正常
        result = controller_bean.process_request()
        self.assertIn("service_data", result)
        self.assertIn("repository_data", result)
        self.assertEqual(result["config"], "production")
    
    def test_mixed_injection(self):
        """测试混合注入（构造函数+属性）"""
        # 注册基础Bean
        service_def = BeanDefinition(
            bean_name="serviceBean",
            bean_class=ServiceBean,
            scope=BeanScope.SINGLETON
        )
        service_def.add_constructor_arg(0, value="mixed-service")
        self.factory._registry.register_bean_definition("serviceBean", service_def)
        
        # 注册控制器Bean
        controller_def = BeanDefinition(
            bean_name="controllerBean",
            bean_class=ControllerBean,
            scope=BeanScope.SINGLETON
        )
        controller_def.add_property_value("service", ref="serviceBean")
        controller_def.add_property_value("config_value", value="mixed-config")
        
        self.factory._registry.register_bean_definition("controllerBean", controller_def)
        
        # 验证注入结果
        controller = self.factory.get_bean("controllerBean")
        self.assertEqual(controller.service.name, "mixed-service")
        self.assertEqual(controller.config_value, "mixed-config")
    
    def test_injection_with_lifecycle(self):
        """测试注入与生命周期方法结合"""
        # 注册带生命周期的Bean
        service_def = BeanDefinition(
            bean_name="serviceBean",
            bean_class=ServiceBean,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        self.factory._registry.register_bean_definition("serviceBean", service_def)
        
        repository_def = BeanDefinition(
            bean_name="repositoryBean",
            bean_class=RepositoryBean,
            scope=BeanScope.SINGLETON,
            init_method_name="connect"
        )
        self.factory._registry.register_bean_definition("repositoryBean", repository_def)
        
        # 注册复杂Bean
        complex_def = BeanDefinition(
            bean_name="complexBean",
            bean_class=ComplexBean,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        complex_def.add_constructor_arg(0, ref="serviceBean")
        complex_def.add_constructor_arg(1, ref="repositoryBean")
        
        self.factory._registry.register_bean_definition("complexBean", complex_def)
        
        # 验证Bean创建和初始化
        complex_bean = self.factory.get_bean("complexBean")
        self.assertTrue(complex_bean.service.initialized)
        self.assertTrue(complex_bean.repository.connected)
        self.assertTrue(complex_bean.ready)
    
    def test_injection_error_handling(self):
        """测试注入错误处理"""
        # 注册引用不存在Bean的定义
        controller_def = BeanDefinition(
            bean_name="controllerBean",
            bean_class=ControllerBean,
            scope=BeanScope.SINGLETON
        )
        controller_def.add_property_value("service", ref="nonexistentService")
        
        self.factory._registry.register_bean_definition("controllerBean", controller_def)
        
        # 获取Bean应该抛出异常
        with self.assertRaises(Exception):  # 可能是KeyError或其他异常
            self.factory.get_bean("controllerBean")
    
    def test_prototype_injection(self):
        """测试原型Bean注入"""
        # 注册原型Bean
        service_def = BeanDefinition(
            bean_name="prototypeService",
            bean_class=ServiceBean,
            scope=BeanScope.PROTOTYPE
        )
        self.factory._registry.register_bean_definition("prototypeService", service_def)
        
        # 注册两个控制器Bean，都依赖原型服务
        controller1_def = BeanDefinition(
            bean_name="controller1",
            bean_class=ControllerBean,
            scope=BeanScope.SINGLETON
        )
        controller1_def.add_property_value("service", ref="prototypeService")
        self.factory._registry.register_bean_definition("controller1", controller1_def)
        
        controller2_def = BeanDefinition(
            bean_name="controller2",
            bean_class=ControllerBean,
            scope=BeanScope.SINGLETON
        )
        controller2_def.add_property_value("service", ref="prototypeService")
        self.factory._registry.register_bean_definition("controller2", controller2_def)
        
        # 验证每个控制器都有不同的服务实例
        controller1 = self.factory.get_bean("controller1")
        controller2 = self.factory.get_bean("controller2")
        
        self.assertIsNot(controller1.service, controller2.service)
        self.assertIsInstance(controller1.service, ServiceBean)
        self.assertIsInstance(controller2.service, ServiceBean)


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python
"""
可配置背压控制器

统一管理所有背压控制组件,提供系统稳定性保障的核心控制逻辑.

主要功能:
- 统一管理负载监控、熔断器、降级管理器等组件
- 基于配置的策略选择和参数调整
- 实时决策和保护动作执行
- 组件间协调和状态同步
- 性能指标收集和分析
"""

import asyncio
import time
from typing import Dict, List, Optional, Any, Callable
from dataclasses import dataclass
from enum import Enum
from loguru import logger

from ..properties import BackpressureConfig, BackpressureStrategy
from .monitor import LoadMonitor, LoadStatus
from .breaker import CircuitBreaker, CircuitBreakerState


class ProtectionAction(Enum):
    """保护动作类型"""

    NONE = "none"
    THROTTLE = "throttle"  # 限流
    REJECT = "reject"  # 拒绝请求
    DEGRADE = "degrade"  # 降级服务
    CIRCUIT_BREAK = "circuit_break"  # 熔断


@dataclass
class ProtectionDecision:
    """保护决策"""

    action: ProtectionAction
    reason: str
    component: str
    severity: str  # LOW, MEDIUM, HIGH, CRITICAL
    metadata: Dict[str, Any]


class BackpressureController:
    """可配置背压控制器

    作为背压控制系统的核心组件,统一管理和协调各个子组件,
    根据配置策略做出保护决策并执行相应的保护动作.
    """

    def __init__(self, config: BackpressureConfig):
        """初始化背压控制器

        Args:
            config: 背压控制配置
        """
        self.config = config

        # 组件管理
        self.load_monitor: Optional[LoadMonitor] = None
        self.circuit_breakers: Dict[str, CircuitBreaker] = {}

        # 状态管理
        self._is_initialized = False
        self._is_running = False
        self._protection_enabled = True

        # 决策历史
        self._recent_decisions: List[ProtectionDecision] = []
        self._max_decision_history = 100

        # 统计信息
        self._total_requests = 0
        self._protected_requests = 0
        self._rejected_requests = 0
        self._throttled_requests = 0

        # 回调函数
        self._protection_callbacks: List[Callable[[ProtectionDecision], None]] = []

        logger.debug(f"BackpressureController initialized with strategy: {config.strategy}")

    async def initialize_async(self) -> None:
        """异步初始化控制器"""
        if self._is_initialized:
            logger.warning("BackpressureController is already initialized")
            return

        try:
            # 初始化负载监控器
            if self.config.load_monitoring_enabled:
                self.load_monitor = LoadMonitor(self.config)
                self.load_monitor.add_load_change_callback(self._on_load_change)
                await self.load_monitor.start()
                logger.debug("LoadMonitor initialized and started")

            # 初始化默认熔断器
            if self.config.circuit_breaker_enabled:
                default_cb = CircuitBreaker("default", backpressure_config=self.config)
                default_cb.add_state_change_callback(self._on_circuit_breaker_state_change)
                self.circuit_breakers["default"] = default_cb
                logger.debug("Default CircuitBreaker initialized")

            self._is_initialized = True
            self._is_running = True

            logger.info("✅ BackpressureController initialized successfully")

        except Exception as e:
            logger.error(f"Failed to initialize BackpressureController: {e}")
            raise

    async def cleanup_async(self) -> None:
        """异步清理资源"""
        if not self._is_initialized:
            return

        try:
            self._is_running = False

            # 停止负载监控器
            if self.load_monitor:
                await self.load_monitor.stop()
                logger.debug("LoadMonitor stopped")

            # 清理熔断器
            self.circuit_breakers.clear()

            self._is_initialized = False
            logger.debug("BackpressureController cleaned up")

        except Exception as e:
            logger.error(f"Error during BackpressureController cleanup: {e}")

    async def should_protect_request(self, request_context: Optional[Dict[str, Any]] = None) -> ProtectionDecision:
        """判断是否需要保护请求

        Args:
            request_context: 请求上下文信息

        Returns:
            保护决策
        """
        if not self._is_running or not self._protection_enabled:
            return ProtectionDecision(action=ProtectionAction.NONE, reason="Protection disabled", component="controller", severity="LOW", metadata={})

        self._total_requests += 1

        try:
            # 根据策略进行决策
            if self.config.strategy == BackpressureStrategy.INTELLIGENT:
                decision = await self._intelligent_decision(request_context)
            elif self.config.strategy == BackpressureStrategy.AGGRESSIVE:
                decision = await self._aggressive_decision(request_context)
            elif self.config.strategy == BackpressureStrategy.CONSERVATIVE:
                decision = await self._conservative_decision(request_context)
            else:
                decision = await self._default_decision(request_context)

            # 记录决策
            self._record_decision(decision)

            # 更新统计
            if decision.action != ProtectionAction.NONE:
                self._protected_requests += 1
                if decision.action == ProtectionAction.REJECT:
                    self._rejected_requests += 1
                elif decision.action == ProtectionAction.THROTTLE:
                    self._throttled_requests += 1

            return decision

        except Exception as e:
            logger.error(f"Error in protection decision: {e}")
            return ProtectionDecision(
                action=ProtectionAction.NONE, reason=f"Decision error: {e}", component="controller", severity="LOW", metadata={"error": str(e)}
            )

    async def _intelligent_decision(self, request_context: Optional[Dict[str, Any]]) -> ProtectionDecision:
        """智能决策策略"""
        # 检查负载状态
        if self.load_monitor:
            load_status = self.load_monitor.get_current_load_status()

            if load_status.is_overloaded:
                if load_status.load_level == "CRITICAL":
                    return ProtectionDecision(
                        action=ProtectionAction.REJECT,
                        reason=f"System critically overloaded: {load_status.system_metrics.cpu_percent:.1f}% CPU",
                        component="load_monitor",
                        severity="CRITICAL",
                        metadata={"load_status": load_status},
                    )
                elif load_status.load_level == "WARNING":
                    return ProtectionDecision(
                        action=ProtectionAction.THROTTLE,
                        reason=f"System under high load: {load_status.system_metrics.cpu_percent:.1f}% CPU",
                        component="load_monitor",
                        severity="MEDIUM",
                        metadata={"load_status": load_status},
                    )

        # 检查熔断器状态
        for name, cb in self.circuit_breakers.items():
            if cb.get_state() == CircuitBreakerState.OPEN:
                return ProtectionDecision(
                    action=ProtectionAction.CIRCUIT_BREAK,
                    reason=f"Circuit breaker '{name}' is open",
                    component=f"circuit_breaker_{name}",
                    severity="HIGH",
                    metadata={"circuit_breaker": name, "metrics": cb.get_metrics()},
                )

        return ProtectionDecision(
            action=ProtectionAction.NONE, reason="System operating normally", component="controller", severity="LOW", metadata={}
        )

    async def _aggressive_decision(self, request_context: Optional[Dict[str, Any]]) -> ProtectionDecision:
        """激进决策策略 - 更早触发保护"""
        if self.load_monitor:
            load_status = self.load_monitor.get_current_load_status()

            # 更低的阈值触发保护
            if load_status.system_metrics.cpu_percent > 60:  # 降低CPU阈值
                return ProtectionDecision(
                    action=ProtectionAction.THROTTLE,
                    reason=f"Aggressive protection: CPU {load_status.system_metrics.cpu_percent:.1f}%",
                    component="load_monitor",
                    severity="MEDIUM",
                    metadata={"strategy": "aggressive", "load_status": load_status},
                )

            if load_status.app_metrics.error_rate > 5:  # 降低错误率阈值
                return ProtectionDecision(
                    action=ProtectionAction.REJECT,
                    reason=f"Aggressive protection: Error rate {load_status.app_metrics.error_rate:.1f}%",
                    component="load_monitor",
                    severity="HIGH",
                    metadata={"strategy": "aggressive", "load_status": load_status},
                )

        return ProtectionDecision(
            action=ProtectionAction.NONE,
            reason="Aggressive strategy - system OK",
            component="controller",
            severity="LOW",
            metadata={"strategy": "aggressive"},
        )

    async def _conservative_decision(self, request_context: Optional[Dict[str, Any]]) -> ProtectionDecision:
        """保守决策策略 - 更晚触发保护"""
        if self.load_monitor:
            load_status = self.load_monitor.get_current_load_status()

            # 只在极端情况下触发保护
            if load_status.system_metrics.cpu_percent > 90:  # 提高CPU阈值
                return ProtectionDecision(
                    action=ProtectionAction.THROTTLE,
                    reason=f"Conservative protection: CPU {load_status.system_metrics.cpu_percent:.1f}%",
                    component="load_monitor",
                    severity="HIGH",
                    metadata={"strategy": "conservative", "load_status": load_status},
                )

            if load_status.app_metrics.error_rate > 20:  # 提高错误率阈值
                return ProtectionDecision(
                    action=ProtectionAction.REJECT,
                    reason=f"Conservative protection: Error rate {load_status.app_metrics.error_rate:.1f}%",
                    component="load_monitor",
                    severity="CRITICAL",
                    metadata={"strategy": "conservative", "load_status": load_status},
                )

        return ProtectionDecision(
            action=ProtectionAction.NONE,
            reason="Conservative strategy - allowing request",
            component="controller",
            severity="LOW",
            metadata={"strategy": "conservative"},
        )

    async def _default_decision(self, request_context: Optional[Dict[str, Any]]) -> ProtectionDecision:
        """默认决策策略"""
        return await self._intelligent_decision(request_context)

    def _record_decision(self, decision: ProtectionDecision) -> None:
        """记录决策历史"""
        self._recent_decisions.append(decision)
        if len(self._recent_decisions) > self._max_decision_history:
            self._recent_decisions.pop(0)

        # 通知回调
        for callback in self._protection_callbacks:
            try:
                callback(decision)
            except Exception as e:
                logger.error(f"Error in protection callback: {e}")

    async def _on_load_change(self, load_status: LoadStatus) -> None:
        """负载状态变化回调"""
        logger.debug(f"Load status changed: {load_status.load_level}, overloaded: {load_status.is_overloaded}")

    def _on_circuit_breaker_state_change(self, old_state: CircuitBreakerState, new_state: CircuitBreakerState) -> None:
        """熔断器状态变化回调"""
        logger.debug(f"Circuit breaker state changed: {old_state.value} -> {new_state.value}")

    def get_circuit_breaker(self, name: str = "default") -> Optional[CircuitBreaker]:
        """获取熔断器"""
        return self.circuit_breakers.get(name)

    def create_circuit_breaker(self, name: str) -> CircuitBreaker:
        """创建新的熔断器"""
        if name in self.circuit_breakers:
            logger.warning(f"Circuit breaker '{name}' already exists")
            return self.circuit_breakers[name]

        cb = CircuitBreaker(name, backpressure_config=self.config)
        cb.add_state_change_callback(self._on_circuit_breaker_state_change)
        self.circuit_breakers[name] = cb

        logger.debug(f"Created circuit breaker: {name}")
        return cb

    def add_protection_callback(self, callback: Callable[[ProtectionDecision], None]) -> None:
        """添加保护决策回调"""
        self._protection_callbacks.append(callback)

    def remove_protection_callback(self, callback: Callable[[ProtectionDecision], None]) -> None:
        """移除保护决策回调"""
        if callback in self._protection_callbacks:
            self._protection_callbacks.remove(callback)

    def enable_protection(self) -> None:
        """启用保护"""
        self._protection_enabled = True
        logger.debug("Backpressure protection enabled")

    def disable_protection(self) -> None:
        """禁用保护"""
        self._protection_enabled = False
        logger.debug("Backpressure protection disabled")

    def is_protection_enabled(self) -> bool:
        """检查保护是否启用"""
        return self._protection_enabled

    def get_status_summary(self) -> Dict[str, Any]:
        """获取状态摘要"""
        protection_rate = 0.0
        if self._total_requests > 0:
            protection_rate = (self._protected_requests / self._total_requests) * 100

        circuit_breaker_status = {}
        for name, cb in self.circuit_breakers.items():
            circuit_breaker_status[name] = cb.get_status_summary()

        load_status = None
        if self.load_monitor:
            load_status = self.load_monitor.get_current_load_status()

        return {
            "initialized": self._is_initialized,
            "running": self._is_running,
            "protection_enabled": self._protection_enabled,
            "strategy": self.config.strategy.value,
            "statistics": {
                "total_requests": self._total_requests,
                "protected_requests": self._protected_requests,
                "rejected_requests": self._rejected_requests,
                "throttled_requests": self._throttled_requests,
                "protection_rate": round(protection_rate, 2),
            },
            "circuit_breakers": circuit_breaker_status,
            "load_status": load_status,
            "recent_decisions_count": len(self._recent_decisions),
        }

    def get_recent_decisions(self, limit: int = 10) -> List[ProtectionDecision]:
        """获取最近的决策记录"""
        return self._recent_decisions[-limit:] if self._recent_decisions else []

    def reset_statistics(self) -> None:
        """重置统计信息"""
        self._total_requests = 0
        self._protected_requests = 0
        self._rejected_requests = 0
        self._throttled_requests = 0
        self._recent_decisions.clear()

        logger.debug("BackpressureController statistics reset")

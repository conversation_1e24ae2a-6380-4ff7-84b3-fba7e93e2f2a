#!/usr/bin/env python
"""
负载监控器模块

提供系统资源和应用性能的实时监控功能,为背压控制提供决策数据.

主要功能:
- 系统资源监控 (CPU、内存使用率)
- 应用性能监控 (响应时间、吞吐量、错误率)
- 实时指标收集和统计分析
- 阈值检测和告警触发
- 历史数据维护和趋势分析
"""

import asyncio
import time
from collections import deque
from dataclasses import dataclass, field
from threading import Lock
from typing import Any, Callable, Dict, List, Optional

import psutil
from loguru import logger

from ..properties import BackpressureConfig


@dataclass
class SystemMetrics:
    """系统资源指标"""

    cpu_percent: float = 0.0
    memory_percent: float = 0.0
    memory_available: int = 0
    disk_io_read: int = 0
    disk_io_write: int = 0
    network_io_sent: int = 0
    network_io_recv: int = 0
    timestamp: float = field(default_factory=time.time)


@dataclass
class ApplicationMetrics:
    """应用性能指标"""

    request_count: int = 0
    error_count: int = 0
    avg_response_time: float = 0.0
    max_response_time: float = 0.0
    min_response_time: float = float("inf")
    throughput: float = 0.0  # requests per second
    error_rate: float = 0.0  # percentage
    active_connections: int = 0
    timestamp: float = field(default_factory=time.time)


@dataclass
class LoadStatus:
    """负载状态"""

    is_overloaded: bool = False
    load_level: str = "NORMAL"  # NORMAL, WARNING, CRITICAL
    system_metrics: SystemMetrics = field(default_factory=SystemMetrics)
    app_metrics: ApplicationMetrics = field(default_factory=ApplicationMetrics)
    recommendations: List[str] = field(default_factory=list)


class LoadMonitor:
    """负载监控器

    实时监控系统资源和应用性能,提供负载状态评估和预警功能.
    支持可配置的监控间隔、历史数据保留和阈值设置.
    """

    def __init__(self, config: BackpressureConfig):
        """初始化负载监控器

        Args:
            config: 背压控制配置
        """
        self.config = config

        # 监控状态
        self._is_running = False
        self._monitor_task: Optional[asyncio.Task] = None

        # 指标存储 (使用双端队列保持固定大小的历史数据)
        self._max_history = 100  # 保留最近100个数据点
        self._system_history: deque = deque(maxlen=self._max_history)
        self._app_history: deque = deque(maxlen=self._max_history)

        # 当前指标
        self._current_system_metrics = SystemMetrics()
        self._current_app_metrics = ApplicationMetrics()
        self._current_load_status = LoadStatus()

        # 线程安全锁
        self._metrics_lock = Lock()

        # 回调函数
        self._load_change_callbacks: List[Callable[[LoadStatus], None]] = []

        # 性能统计
        self._request_times: deque = deque(maxlen=1000)  # 保留最近1000个请求时间
        self._last_request_count = 0
        self._last_error_count = 0
        self._last_throughput_time = time.time()

        logger.info("LoadMonitor initialized with config")

    async def start(self) -> None:
        """启动负载监控"""
        if self._is_running:
            logger.warning("LoadMonitor is already running")
            return

        self._is_running = True
        self._monitor_task = asyncio.create_task(self._monitor_loop())
        logger.info("LoadMonitor started")

    async def stop(self) -> None:
        """停止负载监控"""
        if not self._is_running:
            return

        self._is_running = False
        if self._monitor_task:
            self._monitor_task.cancel()
            try:
                await self._monitor_task
            except asyncio.CancelledError:
                pass
            self._monitor_task = None

        logger.info("LoadMonitor stopped")

    def get_current_load_status(self) -> LoadStatus:
        """获取当前负载状态"""
        with self._metrics_lock:
            return LoadStatus(
                is_overloaded=self._current_load_status.is_overloaded,
                load_level=self._current_load_status.load_level,
                system_metrics=SystemMetrics(**self._current_system_metrics.__dict__),
                app_metrics=ApplicationMetrics(**self._current_app_metrics.__dict__),
                recommendations=self._current_load_status.recommendations.copy(),
            )

    def get_system_metrics_history(self) -> List[SystemMetrics]:
        """获取系统指标历史数据"""
        with self._metrics_lock:
            return list(self._system_history)

    def get_app_metrics_history(self) -> List[ApplicationMetrics]:
        """获取应用指标历史数据"""
        with self._metrics_lock:
            return list(self._app_history)

    def record_request(self, response_time: float, is_error: bool = False) -> None:
        """记录请求指标

        Args:
            response_time: 响应时间 (秒)
            is_error: 是否为错误请求
        """
        with self._metrics_lock:
            self._request_times.append(response_time)
            self._current_app_metrics.request_count += 1
            if is_error:
                self._current_app_metrics.error_count += 1

    def add_load_change_callback(self, callback: Callable[[LoadStatus], None]) -> None:
        """添加负载状态变化回调

        Args:
            callback: 回调函数,接收 LoadStatus 参数
        """
        self._load_change_callbacks.append(callback)

    def remove_load_change_callback(self, callback: Callable[[LoadStatus], None]) -> None:
        """移除负载状态变化回调

        Args:
            callback: 要移除的回调函数
        """
        if callback in self._load_change_callbacks:
            self._load_change_callbacks.remove(callback)

    async def _monitor_loop(self) -> None:
        """监控主循环"""
        logger.debug("LoadMonitor monitoring loop started")

        while self._is_running:
            try:
                # 收集系统指标
                await self._collect_system_metrics()

                # 更新应用指标
                await self._update_app_metrics()

                # 评估负载状态
                await self._evaluate_load_status()

                # 等待下一次监控
                await asyncio.sleep(self.config.monitor_interval)

            except asyncio.CancelledError:
                break
            except Exception as e:
                logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(1.0)  # 错误时短暂等待

    async def _collect_system_metrics(self) -> None:
        """收集系统资源指标"""
        try:
            # CPU 使用率
            cpu_percent = psutil.cpu_percent(interval=0.1)

            # 内存使用情况
            memory = psutil.virtual_memory()

            # 磁盘 I/O
            disk_io = psutil.disk_io_counters()

            # 网络 I/O
            network_io = psutil.net_io_counters()

            metrics = SystemMetrics(
                cpu_percent=cpu_percent,
                memory_percent=memory.percent,
                memory_available=memory.available,
                disk_io_read=disk_io.read_bytes if disk_io else 0,
                disk_io_write=disk_io.write_bytes if disk_io else 0,
                network_io_sent=network_io.bytes_sent if network_io else 0,
                network_io_recv=network_io.bytes_recv if network_io else 0,
                timestamp=time.time(),
            )

            with self._metrics_lock:
                self._current_system_metrics = metrics
                self._system_history.append(metrics)

        except Exception as e:
            logger.error(f"Error collecting system metrics: {e}")

    async def _update_app_metrics(self) -> None:
        """更新应用性能指标"""
        try:
            current_time = time.time()

            with self._metrics_lock:
                # 计算响应时间统计
                if self._request_times:
                    response_times = list(self._request_times)
                    self._current_app_metrics.avg_response_time = sum(response_times) / len(response_times)
                    self._current_app_metrics.max_response_time = max(response_times)
                    self._current_app_metrics.min_response_time = min(response_times)

                # 计算吞吐量 (requests per second)
                time_diff = current_time - self._last_throughput_time
                if time_diff >= 1.0:  # 每秒更新一次吞吐量
                    request_diff = self._current_app_metrics.request_count - self._last_request_count
                    self._current_app_metrics.throughput = request_diff / time_diff

                    self._last_request_count = self._current_app_metrics.request_count
                    self._last_throughput_time = current_time

                # 计算错误率
                if self._current_app_metrics.request_count > 0:
                    self._current_app_metrics.error_rate = self._current_app_metrics.error_count / self._current_app_metrics.request_count * 100

                # 更新时间戳
                self._current_app_metrics.timestamp = current_time

                # 保存到历史记录
                self._app_history.append(ApplicationMetrics(**self._current_app_metrics.__dict__))

        except Exception as e:
            logger.error(f"Error updating app metrics: {e}")

    async def _evaluate_load_status(self) -> None:
        """评估负载状态"""
        try:
            with self._metrics_lock:
                system = self._current_system_metrics
                app = self._current_app_metrics

                # 评估负载级别
                load_level = "NORMAL"
                is_overloaded = False
                recommendations = []

                # CPU 负载检查
                if system.cpu_percent > self.config.cpu_threshold_critical:
                    load_level = "CRITICAL"
                    is_overloaded = True
                    recommendations.append("CPU usage is critical, consider scaling or load balancing")
                elif system.cpu_percent > self.config.cpu_threshold_warning:
                    load_level = "WARNING"
                    recommendations.append("CPU usage is high, monitor closely")

                # 内存负载检查
                if system.memory_percent > self.config.memory_threshold_critical:
                    load_level = "CRITICAL"
                    is_overloaded = True
                    recommendations.append("Memory usage is critical, check for memory leaks")
                elif system.memory_percent > self.config.memory_threshold_warning:
                    if load_level != "CRITICAL":
                        load_level = "WARNING"
                    recommendations.append("Memory usage is high, consider optimization")

                # 响应时间检查
                if app.avg_response_time > self.config.response_time_threshold_critical:
                    load_level = "CRITICAL"
                    is_overloaded = True
                    recommendations.append("Response time is critical, check application performance")
                elif app.avg_response_time > self.config.response_time_threshold_warning:
                    if load_level != "CRITICAL":
                        load_level = "WARNING"
                    recommendations.append("Response time is high, consider optimization")

                # 错误率检查
                if app.error_rate > self.config.error_rate_threshold_critical:
                    load_level = "CRITICAL"
                    is_overloaded = True
                    recommendations.append("Error rate is critical, check application health")
                elif app.error_rate > self.config.error_rate_threshold_warning:
                    if load_level != "CRITICAL":
                        load_level = "WARNING"
                    recommendations.append("Error rate is high, investigate errors")

                # 更新负载状态
                old_status = self._current_load_status
                self._current_load_status = LoadStatus(
                    is_overloaded=is_overloaded, load_level=load_level, system_metrics=system, app_metrics=app, recommendations=recommendations
                )

                # 如果负载状态发生变化,触发回调
                if old_status.is_overloaded != is_overloaded or old_status.load_level != load_level:
                    await self._notify_load_change()

        except Exception as e:
            logger.error(f"Error evaluating load status: {e}")

    async def _notify_load_change(self) -> None:
        """通知负载状态变化"""
        try:
            current_status = self.get_current_load_status()
            logger.info(f"Load status changed: {current_status.load_level}, overloaded: {current_status.is_overloaded}")

            for callback in self._load_change_callbacks:
                try:
                    if asyncio.iscoroutinefunction(callback):
                        await callback(current_status)
                    else:
                        callback(current_status)
                except Exception as e:
                    logger.error(f"Error in load change callback: {e}")

        except Exception as e:
            logger.error(f"Error notifying load change: {e}")

    def is_running(self) -> bool:
        """检查监控器是否正在运行"""
        return self._is_running

    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        with self._metrics_lock:
            return {
                "system": {
                    "cpu_percent": self._current_system_metrics.cpu_percent,
                    "memory_percent": self._current_system_metrics.memory_percent,
                    "memory_available_mb": self._current_system_metrics.memory_available // (1024 * 1024),
                },
                "application": {
                    "request_count": self._current_app_metrics.request_count,
                    "error_count": self._current_app_metrics.error_count,
                    "avg_response_time": round(self._current_app_metrics.avg_response_time, 3),
                    "throughput": round(self._current_app_metrics.throughput, 2),
                    "error_rate": round(self._current_app_metrics.error_rate, 2),
                },
                "load_status": {
                    "level": self._current_load_status.load_level,
                    "is_overloaded": self._current_load_status.is_overloaded,
                    "recommendations_count": len(self._current_load_status.recommendations),
                },
            }

"""
智能调度性能监控

监控智能调度系统的性能和效果:
- 任务分类准确性
- 执行策略选择效果
- 异步收益评估
- 性能分析结果
- 自适应学习效果
"""

import statistics
import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from threading import Lock
from typing import Any, Dict, List, Optional

from loguru import logger


@dataclass
class SchedulingEvent:
    """调度事件记录"""

    timestamp: float
    task_id: str
    task_type: str
    classification: str  # 'cpu_intensive', 'io_intensive', 'mixed', 'unknown'
    strategy: str  # 'sync', 'async', 'thread_pool'
    execution_time: float
    predicted_time: Optional[float] = None
    async_benefit: Optional[float] = None
    success: bool = True
    error: Optional[str] = None


@dataclass
class SchedulingMetricsSummary:
    """调度指标汇总"""

    total_tasks: int = 0
    successful_tasks: int = 0
    failed_tasks: int = 0
    classification_accuracy: float = 0.0
    strategy_effectiveness: float = 0.0
    avg_execution_time: float = 0.0
    async_adoption_rate: float = 0.0
    performance_improvement: float = 0.0
    learning_convergence: float = 0.0
    prediction_accuracy: float = 0.0


class ScheduleMetrics:
    """智能调度性能监控器"""

    def __init__(self, max_history: int = 10000, window_size: int = 300):
        """
        初始化调度指标监控器

        Args:
            max_history: 最大历史记录数
            window_size: 时间窗口大小(秒)
        """
        self.max_history = max_history
        self.window_size = window_size

        # 事件历史记录
        self.event_history: deque = deque(maxlen=max_history)
        self.lock = Lock()

        # 分类统计
        self.classification_stats: Dict[str, Dict[str, int]] = defaultdict(lambda: defaultdict(int))
        self.strategy_stats: Dict[str, Dict[str, float]] = defaultdict(lambda: defaultdict(float))

        # 性能基线
        self.baseline_performance: Dict[str, float] = {}
        self.performance_history: deque = deque(maxlen=1000)

        # 学习效果跟踪
        self.learning_iterations: deque = deque(maxlen=500)
        self.prediction_errors: deque = deque(maxlen=1000)

        logger.info("SchedulingMetrics initialized")

    def record_task_execution(self, event: SchedulingEvent) -> None:
        """记录任务执行事件"""
        with self.lock:
            self.event_history.append(event)

            # 更新分类统计
            self.classification_stats[event.task_type][event.classification] += 1

            # 更新策略统计
            if event.success:
                self.strategy_stats[event.strategy]["total_time"] += event.execution_time
                self.strategy_stats[event.strategy]["count"] += 1

                # 记录性能数据
                self.performance_history.append(
                    {"timestamp": event.timestamp, "strategy": event.strategy, "execution_time": event.execution_time, "task_type": event.task_type}
                )

            # 记录预测误差
            if event.predicted_time is not None:
                prediction_error = abs(event.execution_time - event.predicted_time) / event.execution_time
                self.prediction_errors.append(prediction_error)

        logger.debug(f"Task {event.task_id} executed with {event.strategy} strategy in {event.execution_time:.3f}s")

    def record_learning_iteration(self, iteration: int, accuracy: float, convergence: float) -> None:
        """记录学习迭代结果"""
        with self.lock:
            self.learning_iterations.append({"timestamp": time.time(), "iteration": iteration, "accuracy": accuracy, "convergence": convergence})

        logger.debug(f"Learning iteration {iteration}: accuracy={accuracy:.3f}, convergence={convergence:.3f}")

    def set_baseline_performance(self, task_type: str, baseline_time: float) -> None:
        """设置性能基线"""
        self.baseline_performance[task_type] = baseline_time
        logger.debug(f"Baseline performance for {task_type}: {baseline_time:.3f}s")

    def get_recent_events(self, seconds: int = None) -> List[SchedulingEvent]:
        """获取最近的事件记录"""
        if seconds is None:
            seconds = self.window_size

        cutoff_time = time.time() - seconds

        with self.lock:
            return [event for event in self.event_history if event.timestamp >= cutoff_time]

    def calculate_classification_accuracy(self, seconds: int = None) -> float:
        """计算分类准确性"""
        recent_events = self.get_recent_events(seconds)

        if not recent_events:
            return 0.0

        # 基于执行时间和策略效果评估分类准确性
        correct_classifications = 0
        total_classifications = len(recent_events)

        for event in recent_events:
            # 简化的准确性评估:如果选择的策略执行效果好,认为分类正确
            if event.success and event.execution_time > 0:
                # 根据任务类型和执行时间判断分类是否合理
                if self._is_classification_reasonable(event):
                    correct_classifications += 1

        return (correct_classifications / total_classifications) * 100 if total_classifications > 0 else 0.0

    def _is_classification_reasonable(self, event: SchedulingEvent) -> bool:
        """判断分类是否合理"""
        # 简化的分类合理性判断
        if event.classification == "cpu_intensive" and event.strategy in ["sync", "thread_pool"]:
            return True
        elif event.classification == "io_intensive" and event.strategy == "async":
            return True
        elif event.classification == "mixed" and event.strategy in ["async", "thread_pool"]:
            return True
        elif event.classification == "unknown":
            return True  # 未知类型不判断对错

        return False

    def calculate_strategy_effectiveness(self, seconds: int = None) -> float:
        """计算策略有效性"""
        recent_events = self.get_recent_events(seconds)

        if not recent_events:
            return 0.0

        successful_events = [event for event in recent_events if event.success]

        if not successful_events:
            return 0.0

        # 计算各策略的平均执行时间
        strategy_performance = defaultdict(list)
        for event in successful_events:
            strategy_performance[event.strategy].append(event.execution_time)

        # 计算相对于基线的改进
        total_improvement = 0.0
        total_tasks = 0

        for strategy, times in strategy_performance.items():
            avg_time = statistics.mean(times)

            # 与基线比较(如果有基线的话)
            baseline = self.baseline_performance.get("default", avg_time * 1.2)  # 假设基线比当前慢20%
            improvement = max(0, (baseline - avg_time) / baseline * 100)

            total_improvement += improvement * len(times)
            total_tasks += len(times)

        return total_improvement / total_tasks if total_tasks > 0 else 0.0

    def calculate_async_adoption_rate(self, seconds: int = None) -> float:
        """计算异步采用率"""
        recent_events = self.get_recent_events(seconds)

        if not recent_events:
            return 0.0

        async_tasks = sum(1 for event in recent_events if event.strategy == "async")
        total_tasks = len(recent_events)

        return (async_tasks / total_tasks) * 100 if total_tasks > 0 else 0.0

    def calculate_performance_improvement(self, seconds: int = None) -> float:
        """计算性能改进"""
        if seconds is None:
            seconds = self.window_size

        cutoff_time = time.time() - seconds

        with self.lock:
            recent_performance = [entry for entry in self.performance_history if entry["timestamp"] >= cutoff_time]

        if not recent_performance:
            return 0.0

        # 按任务类型分组计算改进
        type_performance = defaultdict(list)
        for entry in recent_performance:
            type_performance[entry["task_type"]].append(entry["execution_time"])

        total_improvement = 0.0
        total_weight = 0

        for task_type, times in type_performance.items():
            avg_time = statistics.mean(times)
            baseline = self.baseline_performance.get(task_type, avg_time * 1.1)

            improvement = max(0, (baseline - avg_time) / baseline * 100)
            weight = len(times)

            total_improvement += improvement * weight
            total_weight += weight

        return total_improvement / total_weight if total_weight > 0 else 0.0

    def calculate_learning_convergence(self) -> float:
        """计算学习收敛度"""
        with self.lock:
            if len(self.learning_iterations) < 1:
                return 0.0

            recent_iterations = list(self.learning_iterations)[-10:]  # 最近10次迭代

            if len(recent_iterations) < 1:
                return 0.0

            # 计算收敛趋势
            convergence_values = [iteration["convergence"] for iteration in recent_iterations]

            return statistics.mean(convergence_values) * 100

    def calculate_prediction_accuracy(self) -> float:
        """计算预测准确性"""
        with self.lock:
            if not self.prediction_errors:
                return 0.0

            # 计算平均预测误差
            avg_error = statistics.mean(self.prediction_errors)

            # 转换为准确性百分比
            accuracy = max(0, (1 - avg_error) * 100)

            return accuracy

    def get_strategy_distribution(self, seconds: int = None) -> Dict[str, int]:
        """获取策略分布"""
        recent_events = self.get_recent_events(seconds)

        strategy_counts = defaultdict(int)
        for event in recent_events:
            strategy_counts[event.strategy] += 1

        return dict(strategy_counts)

    def get_task_type_distribution(self, seconds: int = None) -> Dict[str, int]:
        """获取任务类型分布"""
        recent_events = self.get_recent_events(seconds)

        type_counts = defaultdict(int)
        for event in recent_events:
            type_counts[event.task_type] += 1

        return dict(type_counts)

    def get_metrics_summary(self, seconds: int = None) -> SchedulingMetricsSummary:
        """获取指标汇总"""
        recent_events = self.get_recent_events(seconds)

        total_tasks = len(recent_events)
        successful_tasks = sum(1 for event in recent_events if event.success)
        failed_tasks = total_tasks - successful_tasks

        if total_tasks == 0:
            return SchedulingMetricsSummary()

        avg_execution_time = statistics.mean([event.execution_time for event in recent_events if event.success]) if successful_tasks > 0 else 0.0

        return SchedulingMetricsSummary(
            total_tasks=total_tasks,
            successful_tasks=successful_tasks,
            failed_tasks=failed_tasks,
            classification_accuracy=self.calculate_classification_accuracy(seconds),
            strategy_effectiveness=self.calculate_strategy_effectiveness(seconds),
            avg_execution_time=avg_execution_time,
            async_adoption_rate=self.calculate_async_adoption_rate(seconds),
            performance_improvement=self.calculate_performance_improvement(seconds),
            learning_convergence=self.calculate_learning_convergence(),
            prediction_accuracy=self.calculate_prediction_accuracy(),
        )

    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self.lock:
            self.event_history.clear()
            self.classification_stats.clear()
            self.strategy_stats.clear()
            self.performance_history.clear()
            self.learning_iterations.clear()
            self.prediction_errors.clear()

        logger.info("SchedulingMetrics reset")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        summary = self.get_metrics_summary()

        return {
            "summary": {
                "total_tasks": summary.total_tasks,
                "successful_tasks": summary.successful_tasks,
                "failed_tasks": summary.failed_tasks,
                "classification_accuracy": summary.classification_accuracy,
                "strategy_effectiveness": summary.strategy_effectiveness,
                "avg_execution_time": summary.avg_execution_time,
                "async_adoption_rate": summary.async_adoption_rate,
                "performance_improvement": summary.performance_improvement,
                "learning_convergence": summary.learning_convergence,
                "prediction_accuracy": summary.prediction_accuracy,
            },
            "strategy_distribution": self.get_strategy_distribution(),
            "task_type_distribution": self.get_task_type_distribution(),
            "classification_stats": {k: dict(v) for k, v in self.classification_stats.items()},
            "baseline_performance": dict(self.baseline_performance),
        }

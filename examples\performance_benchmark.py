#!/usr/bin/env python3
"""
Mini-Boot 性能基准测试
测试应用上下文的启动性能、内存使用、并发处理等关键指标
"""

import asyncio
import time
import psutil
import gc
import tracemalloc
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from contextlib import asynccontextmanager

from miniboot.context.application import DefaultApplicationContext


@dataclass
class PerformanceMetrics:
    """性能指标数据类"""
    startup_time: float = 0.0
    shutdown_time: float = 0.0
    memory_usage_mb: float = 0.0
    peak_memory_mb: float = 0.0
    cpu_usage_percent: float = 0.0
    module_init_times: Dict[str, float] = None
    config_load_time: float = 0.0
    event_processing_time: float = 0.0
    concurrent_requests_per_second: float = 0.0

    def __post_init__(self):
        if self.module_init_times is None:
            self.module_init_times = {}


class PerformanceBenchmark:
    """性能基准测试类"""

    def __init__(self):
        self.process = psutil.Process()
        self.baseline_memory = 0.0

    async def run_all_benchmarks(self) -> Dict[str, PerformanceMetrics]:
        """运行所有性能基准测试"""
        print("🚀 Starting Mini-Boot Performance Benchmark Suite")
        print("=" * 60)

        results = {}

        # 1. 基础启动性能测试
        print("\n📊 1. Basic Startup Performance Test")
        results["basic_startup"] = await self.test_basic_startup_performance()

        # 2. 内存使用测试
        print("\n💾 2. Memory Usage Test")
        results["memory_usage"] = await self.test_memory_usage()

        # 3. 配置加载性能测试
        print("\n⚙️ 3. Configuration Loading Performance Test")
        results["config_loading"] = await self.test_config_loading_performance()

        # 4. 模块初始化性能测试
        print("\n🔧 4. Module Initialization Performance Test")
        results["module_init"] = await self.test_module_initialization_performance()

        # 5. 事件处理性能测试
        print("\n📡 5. Event Processing Performance Test")
        results["event_processing"] = await self.test_event_processing_performance()

        # 6. 并发处理性能测试
        print("\n🔄 6. Concurrent Processing Performance Test")
        results["concurrent_processing"] = await self.test_concurrent_performance()

        # 7. 压力测试
        print("\n💪 7. Stress Test")
        results["stress_test"] = await self.test_stress_performance()

        # 8. 优化性能测试
        results["optimized_performance"] = await self.test_optimized_performance()

        # 生成性能报告
        self.generate_performance_report(results)

        return results

    async def test_basic_startup_performance(self) -> PerformanceMetrics:
        """测试基础启动性能"""
        metrics = PerformanceMetrics()

        # 记录基线内存
        self.baseline_memory = self.get_memory_usage()

        # 测试启动时间
        start_time = time.perf_counter()

        context = DefaultApplicationContext(
            config_path="examples/config/demo.properties",
            packages_to_scan=["examples.demo"],
            auto_detect=True
        )

        await context.start()

        startup_end_time = time.perf_counter()
        metrics.startup_time = startup_end_time - start_time

        # 记录启动后的内存使用
        metrics.memory_usage_mb = self.get_memory_usage() - self.baseline_memory

        # 测试关闭时间
        shutdown_start_time = time.perf_counter()
        await context.stop()
        shutdown_end_time = time.perf_counter()

        metrics.shutdown_time = shutdown_end_time - shutdown_start_time

        print(f"  ✅ Startup time: {metrics.startup_time:.3f}s")
        print(f"  ✅ Shutdown time: {metrics.shutdown_time:.3f}s")
        print(f"  ✅ Memory usage: {metrics.memory_usage_mb:.2f}MB")

        return metrics

    async def test_memory_usage(self) -> PerformanceMetrics:
        """测试内存使用情况"""
        metrics = PerformanceMetrics()

        # 启用内存跟踪
        tracemalloc.start()

        initial_memory = self.get_memory_usage()
        peak_memory = initial_memory

        # 创建多个应用上下文实例测试内存泄漏
        contexts = []

        for i in range(5):
            context = DefaultApplicationContext(
                config_path="examples/config/demo.properties",
                packages_to_scan=["examples.demo"],
                auto_detect=True
            )

            await context.start()
            contexts.append(context)

            current_memory = self.get_memory_usage()
            peak_memory = max(peak_memory, current_memory)

            print(f"  📊 Instance {i+1} memory: {current_memory - initial_memory:.2f}MB")

        # 关闭所有上下文
        for context in contexts:
            await context.stop()

        # 强制垃圾回收
        gc.collect()

        final_memory = self.get_memory_usage()

        metrics.memory_usage_mb = final_memory - initial_memory
        metrics.peak_memory_mb = peak_memory - initial_memory

        # 获取内存跟踪信息
        current, peak = tracemalloc.get_traced_memory()
        tracemalloc.stop()

        print(f"  ✅ Peak memory usage: {metrics.peak_memory_mb:.2f}MB")
        print(f"  ✅ Final memory usage: {metrics.memory_usage_mb:.2f}MB")
        print(f"  ✅ Traced peak: {peak / 1024 / 1024:.2f}MB")

        return metrics

    async def test_config_loading_performance(self) -> PerformanceMetrics:
        """测试配置加载性能"""
        metrics = PerformanceMetrics()

        # 测试配置加载时间
        start_time = time.perf_counter()

        context = DefaultApplicationContext(
            config_path="examples/config/demo.properties",
            packages_to_scan=["examples.demo"],
            auto_detect=True
        )

        # 只初始化环境，不启动完整应用
        await context._initialize_environment()

        end_time = time.perf_counter()
        metrics.config_load_time = end_time - start_time

        print(f"  ✅ Config loading time: {metrics.config_load_time:.3f}s")

        await context.stop()
        return metrics

    async def test_module_initialization_performance(self) -> PerformanceMetrics:
        """测试模块初始化性能"""
        metrics = PerformanceMetrics()

        context = DefaultApplicationContext(
            config_path="examples/config/demo.properties",
            packages_to_scan=["examples.demo"],
            auto_detect=True
        )

        # 记录每个模块的初始化时间
        module_times = {}

        start_time = time.perf_counter()
        await context.start()
        total_time = time.perf_counter() - start_time

        # 获取模块状态
        if hasattr(context, '_unified_initializer'):
            module_status = context._unified_initializer.get_detailed_status()
            print(f"  📊 Module initialization details:")
            for module_name, status in module_status.items():
                if isinstance(status, dict) and 'initialized' in status:
                    init_status = "✅" if status['initialized'] else "❌"
                    print(f"    {init_status} {module_name}: {status.get('description', 'N/A')}")

        metrics.module_init_times = module_times

        print(f"  ✅ Total module init time: {total_time:.3f}s")

        await context.stop()
        return metrics

    async def test_event_processing_performance(self) -> PerformanceMetrics:
        """测试事件处理性能"""
        metrics = PerformanceMetrics()

        context = DefaultApplicationContext(
            config_path="examples/config/demo.properties",
            packages_to_scan=["examples.demo"],
            auto_detect=True
        )

        await context.start()

        # 测试事件发布性能
        event_count = 1000
        start_time = time.perf_counter()

        for i in range(event_count):
            # 创建简单的测试事件
            test_event = {"type": "test", "data": f"event_{i}"}
            if hasattr(context, '_event_publisher'):
                await context._event_publisher.publish_async(test_event)

        end_time = time.perf_counter()
        total_time = end_time - start_time

        metrics.event_processing_time = total_time
        events_per_second = event_count / total_time if total_time > 0 else 0

        print(f"  ✅ Processed {event_count} events in {total_time:.3f}s")
        print(f"  ✅ Events per second: {events_per_second:.0f}")

        await context.stop()
        return metrics

    async def test_concurrent_performance(self) -> PerformanceMetrics:
        """测试并发处理性能"""
        metrics = PerformanceMetrics()

        context = DefaultApplicationContext(
            config_path="examples/config/demo.properties",
            packages_to_scan=["examples.demo"],
            auto_detect=True
        )

        await context.start()

        # 并发启动多个任务
        concurrent_tasks = 50

        async def dummy_task(task_id: int):
            """模拟任务处理"""
            await asyncio.sleep(0.01)  # 模拟IO操作
            return f"task_{task_id}_completed"

        start_time = time.perf_counter()

        # 创建并发任务
        tasks = [dummy_task(i) for i in range(concurrent_tasks)]
        results = await asyncio.gather(*tasks)

        end_time = time.perf_counter()
        total_time = end_time - start_time

        metrics.concurrent_requests_per_second = concurrent_tasks / total_time if total_time > 0 else 0

        print(f"  ✅ Processed {concurrent_tasks} concurrent tasks in {total_time:.3f}s")
        print(f"  ✅ Tasks per second: {metrics.concurrent_requests_per_second:.0f}")

        await context.stop()
        return metrics

    async def test_stress_performance(self) -> PerformanceMetrics:
        """压力测试"""
        metrics = PerformanceMetrics()

        print("  🔥 Running stress test (multiple start/stop cycles)...")

        cycles = 10
        total_startup_time = 0.0
        total_shutdown_time = 0.0

        for i in range(cycles):
            # 启动测试
            start_time = time.perf_counter()

            context = DefaultApplicationContext(
                config_path="examples/config/demo.properties",
                packages_to_scan=["examples.demo"],
                auto_detect=True
            )

            await context.start()
            startup_time = time.perf_counter() - start_time
            total_startup_time += startup_time

            # 关闭测试
            shutdown_start = time.perf_counter()
            await context.stop()
            shutdown_time = time.perf_counter() - shutdown_start
            total_shutdown_time += shutdown_time

            print(f"    Cycle {i+1}: startup={startup_time:.3f}s, shutdown={shutdown_time:.3f}s")

        metrics.startup_time = total_startup_time / cycles
        metrics.shutdown_time = total_shutdown_time / cycles

        print(f"  ✅ Average startup time: {metrics.startup_time:.3f}s")
        print(f"  ✅ Average shutdown time: {metrics.shutdown_time:.3f}s")

        return metrics

    def get_memory_usage(self) -> float:
        """获取当前内存使用量(MB)"""
        return self.process.memory_info().rss / 1024 / 1024

    def get_cpu_usage(self) -> float:
        """获取CPU使用率"""
        return self.process.cpu_percent()

    async def test_optimized_performance(self) -> PerformanceMetrics:
        """测试优化后的性能"""
        print("\n🚀 8. Optimized Performance Test")
        metrics = PerformanceMetrics()

        # 启用性能优化
        try:
            from miniboot.env.performance_optimizer import configure_optimizer
            configure_optimizer(enable_cache=True, enable_parallel=True, max_workers=4)
            print("  ✅ Performance optimizations enabled")
        except ImportError:
            print("  ⚠️ Performance optimizer not available")

        # 测试优化后的启动性能
        cycles = 5
        total_startup_time = 0.0

        for i in range(cycles):
            start_time = time.perf_counter()

            context = DefaultApplicationContext(
                config_path="examples/config/demo.properties",
                packages_to_scan=["examples.demo"],
                auto_detect=True
            )

            await context.start()
            startup_time = time.perf_counter() - start_time
            total_startup_time += startup_time

            await context.stop()

            print(f"    Optimized cycle {i+1}: {startup_time:.3f}s")

        metrics.startup_time = total_startup_time / cycles
        print(f"  ✅ Average optimized startup time: {metrics.startup_time:.3f}s")

        return metrics

    def generate_performance_report(self, results: Dict[str, PerformanceMetrics]):
        """生成性能报告"""
        print("\n" + "=" * 60)
        print("📊 PERFORMANCE BENCHMARK REPORT")
        print("=" * 60)

        for test_name, metrics in results.items():
            print(f"\n🔍 {test_name.replace('_', ' ').title()}:")

            if metrics.startup_time > 0:
                print(f"  ⏱️  Startup Time: {metrics.startup_time:.3f}s")
            if metrics.shutdown_time > 0:
                print(f"  ⏱️  Shutdown Time: {metrics.shutdown_time:.3f}s")
            if metrics.memory_usage_mb > 0:
                print(f"  💾 Memory Usage: {metrics.memory_usage_mb:.2f}MB")
            if metrics.peak_memory_mb > 0:
                print(f"  📈 Peak Memory: {metrics.peak_memory_mb:.2f}MB")
            if metrics.config_load_time > 0:
                print(f"  ⚙️  Config Load Time: {metrics.config_load_time:.3f}s")
            if metrics.event_processing_time > 0:
                print(f"  📡 Event Processing Time: {metrics.event_processing_time:.3f}s")
            if metrics.concurrent_requests_per_second > 0:
                print(f"  🔄 Concurrent RPS: {metrics.concurrent_requests_per_second:.0f}")

        # 性能对比分析
        self._analyze_performance_improvements(results)

        # 性能建议
        print(f"\n💡 PERFORMANCE RECOMMENDATIONS:")

        basic_metrics = results.get("basic_startup")
        if basic_metrics and basic_metrics.startup_time > 2.0:
            print("  ⚠️  Startup time is slow (>2s). Consider optimizing module initialization.")

        memory_metrics = results.get("memory_usage")
        if memory_metrics and memory_metrics.peak_memory_mb > 100:
            print("  ⚠️  High memory usage detected. Consider memory optimization.")

        config_metrics = results.get("config_loading")
        if config_metrics and config_metrics.config_load_time > 0.1:
            print("  ⚠️  Config loading is slow (>100ms). Consider enabling caching.")

        event_metrics = results.get("event_processing")
        if event_metrics and event_metrics.event_processing_time > 0.05:
            print("  ⚠️  Event processing is slow. Consider optimizing event handlers.")

        print("  ✅ Benchmark completed successfully!")

    def _analyze_performance_improvements(self, results: Dict[str, PerformanceMetrics]):
        """分析性能改进"""
        print(f"\n📈 PERFORMANCE ANALYSIS:")

        # 对比基础性能和优化性能
        basic = results.get("basic_startup")
        stress = results.get("stress_test")
        optimized = results.get("optimized_performance")

        if basic and stress:
            improvement = ((basic.startup_time - stress.startup_time) / basic.startup_time) * 100
            if improvement > 0:
                print(f"  🚀 Startup time improved by {improvement:.1f}% under stress")
            else:
                print(f"  ⚠️  Startup time degraded by {abs(improvement):.1f}% under stress")

        if optimized and basic:
            improvement = ((basic.startup_time - optimized.startup_time) / basic.startup_time) * 100
            if improvement > 0:
                print(f"  ⚡ Optimizations improved startup time by {improvement:.1f}%")
            else:
                print(f"  ⚠️  Optimizations had minimal impact on startup time")

        # 内存效率分析
        memory = results.get("memory_usage")
        if memory and memory.peak_memory_mb > 0:
            efficiency = memory.memory_usage_mb / memory.peak_memory_mb
            if efficiency < 0.7:
                print(f"  💾 Good memory efficiency: {efficiency:.1%} final/peak ratio")
            else:
                print(f"  ⚠️  Memory efficiency could be improved: {efficiency:.1%} final/peak ratio")

        # 并发性能分析
        concurrent = results.get("concurrent_processing")
        if concurrent and concurrent.concurrent_requests_per_second > 0:
            if concurrent.concurrent_requests_per_second > 3000:
                print(f"  🔄 Excellent concurrent performance: {concurrent.concurrent_requests_per_second:.0f} RPS")
            elif concurrent.concurrent_requests_per_second > 1000:
                print(f"  ✅ Good concurrent performance: {concurrent.concurrent_requests_per_second:.0f} RPS")
            else:
                print(f"  ⚠️  Concurrent performance needs improvement: {concurrent.concurrent_requests_per_second:.0f} RPS")


async def main():
    """主函数"""
    benchmark = PerformanceBenchmark()
    await benchmark.run_all_benchmarks()


if __name__ == "__main__":
    asyncio.run(main())

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 调度配置系统单元测试
"""

import unittest

from miniboot.schedule import (
    CoalescePolicy,
    ConcurrencyConfig,
    ExecutorConfig,
    ExecutorType,
    JobStoreConfig,
    JobStoreType,
    MisfireGracePolicy,
    ScheduleConfigurationError,
    SchedulerConfigFactory,
    SchedulerProperties,
    SchedulerPropertiesBuilder,
    TriggerConfig,
)


class TestConcurrencyConfig(unittest.TestCase):
    """测试ConcurrencyConfig"""

    def test_default_config(self):
        """测试默认配置"""
        config = ConcurrencyConfig()
        self.assertEqual(config.max_workers, 10)
        self.assertEqual(config.max_instances, 3)
        self.assertFalse(config.coalesce)
        self.assertEqual(config.coalesce_policy, CoalescePolicy.LATEST)
        self.assertEqual(config.misfire_grace_time, 30)
        self.assertEqual(config.misfire_grace_policy, MisfireGracePolicy.IMMEDIATE)

    def test_custom_config(self):
        """测试自定义配置"""
        config = ConcurrencyConfig(
            max_workers=20,
            max_instances=5,
            coalesce=True,
            coalesce_policy=CoalescePolicy.EARLIEST,
            misfire_grace_time=60,
            misfire_grace_policy=MisfireGracePolicy.DELAYED,
        )
        self.assertEqual(config.max_workers, 20)
        self.assertEqual(config.max_instances, 5)
        self.assertTrue(config.coalesce)
        self.assertEqual(config.coalesce_policy, CoalescePolicy.EARLIEST)
        self.assertEqual(config.misfire_grace_time, 60)
        self.assertEqual(config.misfire_grace_policy, MisfireGracePolicy.DELAYED)

    def test_invalid_config(self):
        """测试无效配置"""
        with self.assertRaises(ScheduleConfigurationError):
            ConcurrencyConfig(max_workers=0)

        with self.assertRaises(ScheduleConfigurationError):
            ConcurrencyConfig(max_instances=-1)

        with self.assertRaises(ScheduleConfigurationError):
            ConcurrencyConfig(misfire_grace_time=-5)

    def test_to_dict(self):
        """测试转换为字典"""
        config = ConcurrencyConfig(max_workers=15, coalesce=True)
        config_dict = config.to_dict()

        self.assertEqual(config_dict["max_workers"], 15)
        self.assertTrue(config_dict["coalesce"])
        self.assertEqual(config_dict["coalesce_policy"], "latest")
        self.assertEqual(config_dict["misfire_grace_policy"], "immediate")


class TestJobStoreConfig(unittest.TestCase):
    """测试JobStoreConfig"""

    def test_memory_config(self):
        """测试内存存储配置"""
        config = JobStoreConfig()
        self.assertEqual(config.type, JobStoreType.MEMORY)
        self.assertIsNone(config.url)

    def test_database_config(self):
        """测试数据库存储配置"""
        config = JobStoreConfig(type=JobStoreType.DATABASE, url="sqlite:///test.db", table_prefix="test_", create_tables=True)
        self.assertEqual(config.type, JobStoreType.DATABASE)
        self.assertEqual(config.url, "sqlite:///test.db")
        self.assertEqual(config.table_prefix, "test_")
        self.assertTrue(config.create_tables)

    def test_database_validation(self):
        """测试数据库配置验证"""
        with self.assertRaises(ScheduleConfigurationError):
            JobStoreConfig(type=JobStoreType.DATABASE)  # 缺少URL

    def test_to_dict(self):
        """测试转换为字典"""
        config = JobStoreConfig(type=JobStoreType.DATABASE, url="sqlite:///test.db", table_prefix="test_", create_tables=True)
        config_dict = config.to_dict()

        self.assertEqual(config_dict["type"], "database")
        self.assertEqual(config_dict["url"], "sqlite:///test.db")
        self.assertEqual(config_dict["table_prefix"], "test_")
        self.assertTrue(config_dict["create_tables"])


class TestExecutorConfig(unittest.TestCase):
    """测试ExecutorConfig"""

    def test_default_config(self):
        """测试默认配置"""
        config = ExecutorConfig()
        self.assertEqual(config.type, ExecutorType.THREAD_POOL)
        self.assertEqual(config.max_workers, 10)
        self.assertEqual(config.pool_kwargs, {})

    def test_custom_config(self):
        """测试自定义配置"""
        config = ExecutorConfig(type=ExecutorType.PROCESS_POOL, max_workers=4, pool_kwargs={"initializer": None})
        self.assertEqual(config.type, ExecutorType.PROCESS_POOL)
        self.assertEqual(config.max_workers, 4)
        self.assertEqual(config.pool_kwargs, {"initializer": None})

    def test_invalid_config(self):
        """测试无效配置"""
        with self.assertRaises(ScheduleConfigurationError):
            ExecutorConfig(max_workers=0)

        with self.assertRaises(ScheduleConfigurationError):
            ExecutorConfig(type=ExecutorType.PROCESS_POOL, max_workers=100)  # 超过限制

    def test_to_dict(self):
        """测试转换为字典"""
        config = ExecutorConfig(type=ExecutorType.ASYNCIO, max_workers=5)
        config_dict = config.to_dict()

        self.assertEqual(config_dict["type"], "asyncio")
        self.assertEqual(config_dict["max_workers"], 5)


class TestTriggerConfig(unittest.TestCase):
    """测试TriggerConfig"""

    def test_default_config(self):
        """测试默认配置"""
        config = TriggerConfig()
        self.assertIsNone(config.timezone)
        self.assertIsNone(config.start_date)
        self.assertIsNone(config.end_date)
        self.assertIsNone(config.jitter)

    def test_custom_config(self):
        """测试自定义配置"""
        config = TriggerConfig(timezone="Asia/Shanghai", start_date="2024-01-01", end_date="2024-12-31", jitter=10)
        self.assertEqual(config.timezone, "Asia/Shanghai")
        self.assertEqual(config.start_date, "2024-01-01")
        self.assertEqual(config.end_date, "2024-12-31")
        self.assertEqual(config.jitter, 10)

    def test_invalid_config(self):
        """测试无效配置"""
        with self.assertRaises(ScheduleConfigurationError):
            TriggerConfig(jitter=-5)

    def test_to_dict(self):
        """测试转换为字典"""
        config = TriggerConfig(timezone="UTC", jitter=5)
        config_dict = config.to_dict()

        self.assertEqual(config_dict["timezone"], "UTC")
        self.assertEqual(config_dict["jitter"], 5)
        self.assertNotIn("start_date", config_dict)  # None值不应包含


class TestSchedulerProperties(unittest.TestCase):
    """测试SchedulerProperties"""

    def test_default_properties(self):
        """测试默认属性"""
        props = SchedulerProperties()
        self.assertIsNone(props.timezone)
        self.assertTrue(props.daemon)
        self.assertIsInstance(props.concurrency, ConcurrencyConfig)
        self.assertIsInstance(props.job_store, JobStoreConfig)
        self.assertIn("default", props.executors)
        self.assertIn("asyncio", props.executors)
        self.assertIsInstance(props.trigger, TriggerConfig)
        self.assertEqual(props.logging_level, "INFO")

    def test_custom_properties(self):
        """测试自定义属性"""
        concurrency = ConcurrencyConfig(max_workers=20)
        job_store = JobStoreConfig(type=JobStoreType.DATABASE, url="sqlite:///test.db")

        props = SchedulerProperties(timezone="UTC", daemon=False, concurrency=concurrency, job_store=job_store, logging_level="DEBUG")

        self.assertEqual(props.timezone, "UTC")
        self.assertFalse(props.daemon)
        self.assertEqual(props.concurrency.max_workers, 20)
        self.assertEqual(props.job_store.type, JobStoreType.DATABASE)
        self.assertEqual(props.logging_level, "DEBUG")

    def test_validation(self):
        """测试验证"""
        # 测试缺少默认执行器
        with self.assertRaises(ScheduleConfigurationError):
            SchedulerProperties(executors={})

    def test_to_dict(self):
        """测试转换为字典"""
        props = SchedulerProperties()
        props_dict = props.to_dict()

        self.assertIn("timezone", props_dict)
        self.assertIn("daemon", props_dict)
        self.assertIn("concurrency", props_dict)
        self.assertIn("job_store", props_dict)
        self.assertIn("executors", props_dict)
        self.assertIn("trigger", props_dict)
        self.assertIn("job_defaults", props_dict)
        self.assertIn("logging_level", props_dict)

    def test_get_apscheduler_config(self):
        """测试获取APScheduler配置"""
        props = SchedulerProperties()
        config = props.get_apscheduler_config()

        self.assertIn("jobstores", config)
        self.assertIn("executors", config)
        self.assertIn("job_defaults", config)
        self.assertIn("timezone", config)

        # 验证作业存储
        self.assertIn("default", config["jobstores"])

        # 验证执行器
        self.assertIn("default", config["executors"])
        self.assertIn("asyncio", config["executors"])


class TestSchedulerPropertiesBuilder(unittest.TestCase):
    """测试SchedulerPropertiesBuilder"""

    def test_builder_pattern(self):
        """测试构建器模式"""
        props = (
            SchedulerPropertiesBuilder()
            .timezone("Asia/Shanghai")
            .max_workers(15)
            .max_instances(5)
            .coalesce(True)
            .misfire_grace_time(60)
            .job_store_type(JobStoreType.MEMORY)
            .logging_level("DEBUG")
            .build()
        )

        self.assertEqual(props.timezone, "Asia/Shanghai")
        self.assertEqual(props.concurrency.max_workers, 15)
        self.assertEqual(props.concurrency.max_instances, 5)
        self.assertTrue(props.concurrency.coalesce)
        self.assertEqual(props.concurrency.misfire_grace_time, 60)
        self.assertEqual(props.job_store.type, JobStoreType.MEMORY)
        self.assertEqual(props.logging_level, "DEBUG")

    def test_database_config(self):
        """测试数据库配置"""
        props = SchedulerPropertiesBuilder().database_config(url="sqlite:///test.db", table_prefix="test_", create_tables=True).build()

        self.assertEqual(props.job_store.type, JobStoreType.DATABASE)
        self.assertEqual(props.job_store.url, "sqlite:///test.db")
        self.assertEqual(props.job_store.table_prefix, "test_")
        self.assertTrue(props.job_store.create_tables)

    def test_add_executor(self):
        """测试添加执行器"""
        props = SchedulerPropertiesBuilder().add_executor("custom", ExecutorType.THREAD_POOL, 8, thread_name_prefix="custom").build()

        self.assertIn("custom", props.executors)
        custom_executor = props.executors["custom"]
        self.assertEqual(custom_executor.type, ExecutorType.THREAD_POOL)
        self.assertEqual(custom_executor.max_workers, 8)
        self.assertEqual(custom_executor.pool_kwargs["thread_name_prefix"], "custom")


class TestSchedulerConfigFactory(unittest.TestCase):
    """测试SchedulerConfigFactory"""

    def test_create_default(self):
        """测试创建默认配置"""
        config = SchedulerConfigFactory.create_default()
        self.assertIsInstance(config, SchedulerProperties)
        self.assertEqual(config.job_store.type, JobStoreType.MEMORY)

    def test_create_memory_config(self):
        """测试创建内存配置"""
        config = SchedulerConfigFactory.create_memory_config(max_workers=8)
        self.assertEqual(config.job_store.type, JobStoreType.MEMORY)
        self.assertEqual(config.concurrency.max_workers, 8)

    def test_create_high_concurrency_config(self):
        """测试创建高并发配置"""
        config = SchedulerConfigFactory.create_high_concurrency_config(max_workers=30, max_instances=8)
        self.assertEqual(config.concurrency.max_workers, 30)
        self.assertEqual(config.concurrency.max_instances, 8)
        self.assertTrue(config.concurrency.coalesce)
        self.assertEqual(config.concurrency.misfire_grace_time, 60)
        self.assertIn("high_concurrency", config.executors)

    def test_create_async_config(self):
        """测试创建异步配置"""
        config = SchedulerConfigFactory.create_async_config(max_workers=12, asyncio_workers=6)
        self.assertEqual(config.concurrency.max_workers, 12)
        self.assertIn("asyncio", config.executors)
        self.assertEqual(config.executors["asyncio"].max_workers, 6)

    def test_create_from_dict(self):
        """测试从字典创建配置"""
        config_dict = {
            "timezone": "UTC",
            "concurrency": {"max_workers": 16, "coalesce": True},
            "job_store": {"type": "memory"},
            "logging_level": "WARNING",
        }

        config = SchedulerConfigFactory.create_from_dict(config_dict)
        self.assertEqual(config.timezone, "UTC")
        self.assertEqual(config.concurrency.max_workers, 16)
        self.assertTrue(config.concurrency.coalesce)
        self.assertEqual(config.job_store.type, JobStoreType.MEMORY)
        self.assertEqual(config.logging_level, "WARNING")


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Bean 模块指标采集自动配置

实现 Bean 工厂的指标采集功能，包括：
- Bean 创建统计：Bean 实例化数量、耗时分析
- 依赖注入性能：依赖解析时间、循环依赖检测
- 三级缓存监控：singleton_objects、early_singleton_objects、singleton_factories 命中率
- Bean 生命周期：初始化、销毁事件统计
- Post Processor 性能：各个 BeanPostProcessor 执行耗时

配置条件：
- miniboot.starters.actuator.metrics.core-modules.bean=true (默认启用)
- 依赖 ActuatorStarterAutoConfiguration 已配置

使用示例：
    # application.yml
    miniboot:
        starters:
            actuator:
                metrics:
                    core-modules:
                        bean: true  # 启用 Bean 模块指标采集
"""

import threading
import time
from dataclasses import dataclass, field
from typing import Any, Dict, List, Optional

from loguru import logger

from miniboot.annotations.conditional import (ConditionalOnBean,
                                              ConditionalOnProperty)
from miniboot.annotations.core import Bean, Configuration
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.bean.cache import ThreeLevelCache
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.monitoring.interfaces import MetricsCollector, MetricsData


@dataclass
class BeanMetrics:
    """Bean 模块指标数据"""

    # Bean 创建统计
    total_beans_created: int = 0
    total_creation_time: float = 0.0
    avg_creation_time: float = 0.0

    # 依赖注入性能
    total_injections: int = 0
    total_injection_time: float = 0.0
    avg_injection_time: float = 0.0
    circular_dependencies_resolved: int = 0

    # 三级缓存统计
    cache_hits_level1: int = 0  # 一级缓存命中
    cache_hits_level2: int = 0  # 二级缓存命中
    cache_hits_level3: int = 0  # 三级缓存命中
    cache_misses: int = 0       # 缓存未命中
    cache_hit_rate: float = 0.0

    # Bean 生命周期统计
    beans_initialized: int = 0
    beans_destroyed: int = 0
    initialization_failures: int = 0

    # Post Processor 性能
    post_processor_executions: int = 0
    total_post_processor_time: float = 0.0
    avg_post_processor_time: float = 0.0

    # Bean 注册表统计
    registered_bean_definitions: int = 0
    removed_bean_definitions: int = 0

    def calculate_derived_metrics(self):
        """计算派生指标"""
        # 计算平均创建时间
        if self.total_beans_created > 0:
            self.avg_creation_time = self.total_creation_time / self.total_beans_created

        # 计算平均注入时间
        if self.total_injections > 0:
            self.avg_injection_time = self.total_injection_time / self.total_injections

        # 计算缓存命中率
        total_cache_requests = (self.cache_hits_level1 + self.cache_hits_level2 +
                               self.cache_hits_level3 + self.cache_misses)
        if total_cache_requests > 0:
            total_hits = self.cache_hits_level1 + self.cache_hits_level2 + self.cache_hits_level3
            self.cache_hit_rate = total_hits / total_cache_requests

        # 计算平均 Post Processor 时间
        if self.post_processor_executions > 0:
            self.avg_post_processor_time = self.total_post_processor_time / self.post_processor_executions


class BeanMetricsCollector(MetricsCollector):
    """Bean 模块指标采集器

    负责收集 Bean 工厂的各种性能指标和统计信息。
    通过监控 Bean 的创建、依赖注入、缓存使用等过程，
    提供详细的性能分析数据。
    """

    def __init__(self):
        """初始化 Bean 指标采集器"""
        self._metrics = BeanMetrics()
        self._lock = threading.RLock()
        self._start_time = time.time()

        # 监控的 Bean 工厂实例
        self._monitored_factories: List[DefaultBeanFactory] = []

        # 监控的缓存实例
        self._monitored_caches: List[ThreeLevelCache] = []

        # 监控的注册表实例
        self._monitored_registries: List[DefaultBeanDefinitionRegistry] = []

        logger.info("BeanMetricsCollector initialized")

    def register_bean_factory(self, factory: DefaultBeanFactory) -> None:
        """注册要监控的 Bean 工厂

        Args:
            factory: Bean 工厂实例
        """
        with self._lock:
            if factory not in self._monitored_factories:
                self._monitored_factories.append(factory)
                logger.debug(f"Registered Bean factory for monitoring: {factory}")

    def register_cache(self, cache: ThreeLevelCache) -> None:
        """注册要监控的三级缓存

        Args:
            cache: 三级缓存实例
        """
        with self._lock:
            if cache not in self._monitored_caches:
                self._monitored_caches.append(cache)
                logger.debug(f"Registered cache for monitoring: {cache}")

    def register_registry(self, registry: DefaultBeanDefinitionRegistry) -> None:
        """注册要监控的 Bean 注册表

        Args:
            registry: Bean 注册表实例
        """
        with self._lock:
            if registry not in self._monitored_registries:
                self._monitored_registries.append(registry)
                logger.debug(f"Registered registry for monitoring: {registry}")

    async def collect_metrics(self) -> BeanMetrics:
        """收集当前的 Bean 模块指标

        Returns:
            BeanMetrics: 当前的指标数据
        """
        import asyncio

        # 使用同步锁保护的内部函数
        def _collect_with_lock():
            with self._lock:
                # 重置指标
                metrics = BeanMetrics()

                # 收集 Bean 工厂指标
                for factory in self._monitored_factories:
                    factory_stats = factory.get_performance_stats()
                    metrics.total_beans_created += factory_stats.get('total_beans_created', 0)
                    metrics.total_creation_time += factory_stats.get('total_creation_time', 0.0)

                    # 从工厂的基本统计中收集缓存信息
                    metrics.cache_misses += factory_stats.get('cache_misses', 0)

                # 收集三级缓存指标
                for cache in self._monitored_caches:
                    cache_stats = cache.stats()
                    metrics.cache_hits_level1 += cache_stats.get('hits_level1', 0)
                    metrics.cache_hits_level2 += cache_stats.get('hits_level2', 0)
                    metrics.cache_hits_level3 += cache_stats.get('hits_level3', 0)
                    metrics.cache_misses += cache_stats.get('misses', 0)

                # 收集注册表指标
                for registry in self._monitored_registries:
                    registry_stats = registry.stats()
                    metrics.registered_bean_definitions += registry_stats.get('registration_count', 0)
                    metrics.removed_bean_definitions += registry_stats.get('removal_count', 0)

                # 计算派生指标
                metrics.calculate_derived_metrics()

                # 更新内部指标
                self._metrics = metrics

                logger.debug(f"Collected Bean metrics: {metrics}")
                return metrics

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _collect_with_lock)

    async def get_metrics_dict(self) -> Dict[str, Any]:
        """获取指标的字典表示

        Returns:
            Dict[str, Any]: 指标字典
        """
        metrics = await self.collect_metrics_async()
        return {
            'bean_creation': {
                'total_beans_created': metrics.total_beans_created,
                'total_creation_time': metrics.total_creation_time,
                'avg_creation_time': metrics.avg_creation_time,
            },
            'dependency_injection': {
                'total_injections': metrics.total_injections,
                'total_injection_time': metrics.total_injection_time,
                'avg_injection_time': metrics.avg_injection_time,
                'circular_dependencies_resolved': metrics.circular_dependencies_resolved,
            },
            'cache_performance': {
                'hits_level1': metrics.cache_hits_level1,
                'hits_level2': metrics.cache_hits_level2,
                'hits_level3': metrics.cache_hits_level3,
                'misses': metrics.cache_misses,
                'hit_rate': metrics.cache_hit_rate,
            },
            'lifecycle': {
                'beans_initialized': metrics.beans_initialized,
                'beans_destroyed': metrics.beans_destroyed,
                'initialization_failures': metrics.initialization_failures,
            },
            'post_processors': {
                'executions': metrics.post_processor_executions,
                'total_time': metrics.total_post_processor_time,
                'avg_time': metrics.avg_post_processor_time,
            },
            'registry': {
                'registered_definitions': metrics.registered_bean_definitions,
                'removed_definitions': metrics.removed_bean_definitions,
            }
        }

    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self._lock:
            self._metrics = BeanMetrics()
            self._start_time = time.time()
            logger.info("Bean metrics reset")

    # MetricsCollector 接口实现
    def collect_metrics(self) -> List[MetricsData]:
        """收集指标数据 - MetricsCollector 接口实现

        Returns:
            List[MetricsData]: 指标数据列表
        """
        import asyncio
        import time

        # 简化的方法：直接调用异步方法并处理事件循环
        try:
            # 检查是否有运行中的事件循环
            loop = asyncio.get_running_loop()
            # 如果有，我们需要在新线程中运行
            import concurrent.futures
            import threading

            def run_in_thread():
                return asyncio.run(self.collect_metrics_async())

            with concurrent.futures.ThreadPoolExecutor() as executor:
                future = executor.submit(run_in_thread)
                metrics = future.result(timeout=5.0)

        except RuntimeError:
            # 没有运行中的事件循环，直接运行
            metrics = asyncio.run(self.collect_metrics_async())
        except Exception as e:
            # 如果还是失败，使用同步回退
            import logging
            logging.warning(f"Failed to collect bean metrics async: {e}, using fallback")
            metrics = self._get_fallback_metrics()

        # 将 BeanMetrics 转换为 MetricsData 列表
        metrics_data = []

        # Bean 创建指标
        metrics_data.append(MetricsData(
            name="bean.total_created",
            value=metrics.total_beans_created,
            unit="count",
            tags={"module": "bean"},
            timestamp=time.time()
        ))

        metrics_data.append(MetricsData(
            name="bean.avg_creation_time",
            value=metrics.avg_creation_time,
            unit="seconds",
            tags={"module": "bean"},
            timestamp=time.time()
        ))

        # 缓存指标
        metrics_data.append(MetricsData(
            name="bean.cache_hit_rate",
            value=metrics.cache_hit_rate,
            unit="percentage",
            tags={"module": "bean", "type": "cache"},
            timestamp=time.time()
        ))

        # 依赖注入指标
        metrics_data.append(MetricsData(
            name="bean.total_injections",
            value=metrics.total_injections,
            unit="count",
            tags={"module": "bean", "type": "injection"},
            timestamp=time.time()
        ))

        return metrics_data

    def _get_fallback_metrics(self):
        """获取回退指标数据"""
        return BeanMetrics(
            total_beans_created=0,
            total_creation_time=0.0,
            avg_creation_time=0.0,
            total_dependency_injections=0,
            total_injection_time=0.0,
            avg_injection_time=0.0,
            circular_dependencies_resolved=0,
            cache_hits_level1=0,
            cache_hits_level2=0,
            cache_hits_level3=0,
            cache_misses=0,
            cache_hit_rate=0.0,
            beans_initialized=0,
            beans_destroyed=0,
            initialization_failures=0,
            post_processor_executions=0,
            total_post_processor_time=0.0,
            avg_post_processor_time=0.0,
            registered_bean_definitions=0,
            removed_bean_definitions=0
        )

    def get_collector_name(self) -> str:
        """获取收集器名称 - MetricsCollector 接口实现

        Returns:
            str: 收集器名称
        """
        return "bean-metrics-collector"

    def get_supported_metrics(self) -> List[str]:
        """获取支持的指标名称列表 - MetricsCollector 接口实现

        Returns:
            List[str]: 支持的指标名称
        """
        return [
            "bean.total_created",
            "bean.avg_creation_time",
            "bean.cache_hit_rate",
            "bean.total_injections",
            "bean.singleton_count",
            "bean.prototype_count",
            "bean.failed_creations",
            "bean.circular_dependencies"
        ]

    def is_available(self) -> bool:
        """检查收集器是否可用 - MetricsCollector 接口实现

        Returns:
            bool: 是否可用
        """
        # Bean 指标收集器总是可用的，即使没有监控特定组件
        # 它可以提供基本的 Bean 统计信息
        return True

    async def collect_metrics_async(self) -> BeanMetrics:
        """异步收集指标的内部方法（重命名原来的 collect_metrics）"""
        import asyncio

        def _collect_with_lock() -> BeanMetrics:
            with self._lock:
                metrics = BeanMetrics()

                # 收集 Bean 工厂指标
                for factory in self._monitored_factories:
                    factory_stats = factory.stats() if hasattr(factory, 'stats') else {}
                    metrics.total_beans_created += factory_stats.get('beans_created', 0)
                    metrics.total_creation_time += factory_stats.get('total_creation_time', 0.0)
                    metrics.singleton_beans += factory_stats.get('singleton_count', 0)
                    metrics.prototype_beans += factory_stats.get('prototype_count', 0)
                    metrics.failed_bean_creations += factory_stats.get('creation_failures', 0)
                    metrics.circular_dependencies_detected += factory_stats.get('circular_dependencies', 0)

                # 收集缓存指标
                for cache in self._monitored_caches:
                    cache_stats = cache.stats() if hasattr(cache, 'stats') else {}
                    metrics.cache_hits += cache_stats.get('hits', 0)
                    metrics.cache_misses += cache_stats.get('misses', 0)
                    metrics.singleton_cache_size += cache_stats.get('singleton_cache_size', 0)
                    metrics.early_singleton_cache_size += cache_stats.get('early_singleton_cache_size', 0)
                    metrics.factory_cache_size += cache_stats.get('factory_cache_size', 0)

                # 收集注册表指标
                for registry in self._monitored_registries:
                    registry_stats = registry.stats()
                    metrics.registered_bean_definitions += registry_stats.get('registration_count', 0)
                    metrics.removed_bean_definitions += registry_stats.get('removal_count', 0)

                # 计算派生指标
                metrics.calculate_derived_metrics()

                # 更新内部指标
                self._metrics = metrics

                logger.debug(f"Collected Bean metrics: {metrics}")
                return metrics

        # 在线程池中执行同步操作
        loop = asyncio.get_event_loop()
        return await loop.run_in_executor(None, _collect_with_lock)


@ConditionalOnProperty(name="miniboot.starters.actuator.metrics.core-modules.bean", match_if_missing=True)
@ConditionalOnBean(name="actuator_context")
class BeanMetricsAutoConfiguration(AutoConfiguration):
    """Bean 模块指标采集自动配置类

    当满足以下条件时自动配置 Bean 指标采集：
    1. miniboot.starters.actuator.metrics.core-modules.bean=true (默认启用)
    2. ActuatorStarterAutoConfiguration 已配置

    注册的 Bean：
    - bean_metrics_collector: Bean 指标采集器
    """

    def get_metadata(self) -> AutoConfigurationMetadata:
        """获取配置元数据"""
        return AutoConfigurationMetadata(
            name="bean-metrics-auto-configuration",
            description="Bean 模块指标采集自动配置",
            priority=200,  # 中等优先级
            auto_configure_after=["actuator-starter-auto-configuration"],
        )

    @Bean
    def bean_metrics_collector(self) -> BeanMetricsCollector:
        """创建 Bean 指标采集器 Bean

        Returns:
            BeanMetricsCollector: Bean 指标采集器实例
        """
        collector = BeanMetricsCollector()
        logger.debug("Created BeanMetricsCollector bean")
        return collector

#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 高级缓存装饰器 - 为端点提供便捷的缓存功能

提供多种缓存装饰器,支持:
- 方法级缓存
- 条件缓存
- 缓存键自定义
- 缓存预热
- 缓存失效
- 缓存统计
"""

import asyncio
import functools
import hashlib
import inspect
import json
from typing import Any, Awaitable, Callable, Dict, List, Optional, Union

from loguru import logger

from .cache import AdvancedEndpointCache, CacheManager, CacheStrategy


def cache_key_generator(func_name: str, args: tuple, kwargs: dict, include_self: bool = True) -> str:
    """生成缓存键 - 优化版本

    Args:
        func_name: 函数名
        args: 位置参数
        kwargs: 关键字参数
        include_self: 是否包含self参数

    Returns:
        str: 缓存键
    """
    # 过滤掉self参数
    if include_self and args:
        filtered_args = args[1:] if hasattr(args[0], func_name) else args
    else:
        filtered_args = args

    # 优化:使用更高效的键生成策略
    key_parts = [func_name]

    # 处理位置参数 - 避免完整序列化
    if filtered_args:
        args_hash = hash(filtered_args) if len(filtered_args) < 5 else hash(str(filtered_args))
        key_parts.append(str(args_hash))

    # 处理关键字参数 - 只对小字典进行完整序列化
    if kwargs:
        if len(kwargs) < 3:
            kwargs_str = json.dumps(kwargs, sort_keys=True, default=str)
        else:
            kwargs_str = str(hash(frozenset(kwargs.items())))
        key_parts.append(kwargs_str)

    # 生成最终键
    content = ":".join(key_parts)
    return hashlib.md5(content.encode()).hexdigest()[:16]  # 使用较短的哈希


def advanced_cache(
    strategy: str = "lru",
    max_size: int = 1000,
    ttl: Optional[float] = None,
    key_generator: Optional[Callable] = None,
    condition: Optional[Callable] = None,
    cache_name: Optional[str] = None,
):
    """高级缓存装饰器

    Args:
        strategy: 缓存策略 (lru, lfu, fifo, ttl, adaptive)
        max_size: 最大缓存大小
        ttl: 缓存TTL(秒)
        key_generator: 自定义键生成器
        condition: 缓存条件函数
        cache_name: 自定义缓存名称
    """

    def decorator(func: Callable) -> Callable:
        # 获取或创建缓存实例
        endpoint_name = cache_name or func.__name__
        cache = CacheManager().get(f"endpoint_{endpoint_name}", max_size, strategy)

        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                # 检查缓存条件
                if condition and not condition(*args, **kwargs):
                    return await func(*args, **kwargs)

                # 生成缓存键
                if key_generator:
                    cache_key = key_generator(*args, **kwargs)
                else:
                    cache_key = cache_key_generator(func.__name__, args, kwargs)

                # 尝试从缓存获取
                cached_result = await cache.get(cache_key)
                if cached_result is not None:
                    logger.debug(f"Cache hit for {func.__name__}: {cache_key}")
                    return cached_result

                # 执行函数并缓存结果
                result = await func(*args, **kwargs)
                if result is not None:
                    await cache.put(cache_key, result)
                    logger.debug(f"Cached result for {func.__name__}: {cache_key}")

                return result

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                # 检查缓存条件
                if condition and not condition(*args, **kwargs):
                    return func(*args, **kwargs)

                # 生成缓存键
                if key_generator:
                    cache_key = key_generator(*args, **kwargs)
                else:
                    cache_key = cache_key_generator(func.__name__, args, kwargs)

                # 尝试从缓存获取(同步方式)
                try:
                    loop = asyncio.get_event_loop()
                    cached_result = loop.run_until_complete(cache.get(cache_key))
                    if cached_result is not None:
                        logger.debug(f"Cache hit for {func.__name__}: {cache_key}")
                        return cached_result
                except RuntimeError:
                    # 没有事件循环,跳过缓存
                    return func(*args, **kwargs)

                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                if result is not None:
                    try:
                        loop.run_until_complete(cache.put(cache_key, result))
                        logger.debug(f"Cached result for {func.__name__}: {cache_key}")
                    except RuntimeError:
                        pass  # 忽略缓存错误

                return result

            return sync_wrapper

    return decorator


def cache_evict(cache_name: Optional[str] = None, key: Optional[str] = None, all_entries: bool = False):
    """缓存失效装饰器

    Args:
        cache_name: 缓存名称
        key: 要失效的键
        all_entries: 是否清空所有条目
    """

    def decorator(func: Callable) -> Callable:
        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                result = await func(*args, **kwargs)

                # 执行缓存失效
                endpoint_name = cache_name or func.__name__
                cache = CacheManager().get(f"endpoint_{endpoint_name}")

                if all_entries:
                    await cache.clear()
                    logger.debug(f"Cleared all cache entries for {endpoint_name}")
                elif key:
                    await cache.remove(key)
                    logger.debug(f"Evicted cache key {key} for {endpoint_name}")

                return result

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                result = func(*args, **kwargs)

                # 执行缓存失效
                endpoint_name = cache_name or func.__name__
                cache = CacheManager().get(f"endpoint_{endpoint_name}")

                try:
                    loop = asyncio.get_event_loop()
                    if all_entries:
                        loop.run_until_complete(cache.clear())
                        logger.debug(f"Cleared all cache entries for {endpoint_name}")
                    elif key:
                        loop.run_until_complete(cache.remove(key))
                        logger.debug(f"Evicted cache key {key} for {endpoint_name}")
                except RuntimeError:
                    pass  # 忽略缓存错误

                return result

            return sync_wrapper

    return decorator


def cache_put(cache_name: Optional[str] = None, key_generator: Optional[Callable] = None):
    """缓存更新装饰器

    Args:
        cache_name: 缓存名称
        key_generator: 自定义键生成器
    """

    def decorator(func: Callable) -> Callable:
        if asyncio.iscoroutinefunction(func):

            @functools.wraps(func)
            async def async_wrapper(*args, **kwargs):
                result = await func(*args, **kwargs)

                # 更新缓存
                endpoint_name = cache_name or func.__name__
                cache = CacheManager().get(f"endpoint_{endpoint_name}")

                if key_generator:
                    cache_key = key_generator(*args, **kwargs)
                else:
                    cache_key = cache_key_generator(func.__name__, args, kwargs)

                if result is not None:
                    await cache.put(cache_key, result)
                    logger.debug(f"Updated cache for {func.__name__}: {cache_key}")

                return result

            return async_wrapper
        else:

            @functools.wraps(func)
            def sync_wrapper(*args, **kwargs):
                result = func(*args, **kwargs)

                # 更新缓存
                endpoint_name = cache_name or func.__name__
                cache = CacheManager().get(f"endpoint_{endpoint_name}")

                if key_generator:
                    cache_key = key_generator(*args, **kwargs)
                else:
                    cache_key = cache_key_generator(func.__name__, args, kwargs)

                if result is not None:
                    try:
                        loop = asyncio.get_event_loop()
                        loop.run_until_complete(cache.put(cache_key, result))
                        logger.debug(f"Updated cache for {func.__name__}: {cache_key}")
                    except RuntimeError:
                        pass  # 忽略缓存错误

                return result

            return sync_wrapper

    return decorator


class CacheableEndpoint:
    """可缓存端点基类"""

    def __init__(self, cache_strategy: str = "lru", cache_size: int = 1000, cache_ttl: Optional[float] = None):
        """初始化可缓存端点

        Args:
            cache_strategy: 缓存策略
            cache_size: 缓存大小
            cache_ttl: 缓存TTL
        """
        self.cache_strategy = cache_strategy
        self.cache_size = cache_size
        self.cache_ttl = cache_ttl
        self._cache: Optional[AdvancedEndpointCache] = None

    @property
    def cache(self) -> AdvancedEndpointCache:
        """获取缓存实例"""
        if self._cache is None:
            endpoint_name = self.__class__.__name__.lower()
            self._cache = CacheManager().get(f"endpoint_{endpoint_name}", self.cache_size, self.cache_strategy)
        return self._cache

    async def get_cached(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        return await self.cache.get(key)

    async def put_cached(self, key: str, value: Any, ttl: Optional[float] = None) -> None:
        """设置缓存值"""
        await self.cache.put(key, value, ttl)

    async def evict_cached(self, key: str) -> bool:
        """移除缓存值"""
        return await self.cache.remove(key)

    async def clear_cache(self) -> None:
        """清空缓存"""
        await self.cache.clear()

    def get_cache_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        return self.cache.get_stats().to_dict()


def conditional_cache(condition_func: Callable[[Any], bool]):
    """条件缓存装饰器

    Args:
        condition_func: 条件函数,返回True时才缓存
    """
    return advanced_cache(condition=condition_func)


def lru_cache(max_size: int = 1000, ttl: Optional[float] = None):
    """LRU缓存装饰器"""
    return advanced_cache(strategy="lru", max_size=max_size, ttl=ttl)


def lfu_cache(max_size: int = 1000, ttl: Optional[float] = None):
    """LFU缓存装饰器"""
    return advanced_cache(strategy="lfu", max_size=max_size, ttl=ttl)


def fifo_cache(max_size: int = 1000, ttl: Optional[float] = None):
    """FIFO缓存装饰器"""
    return advanced_cache(strategy="fifo", max_size=max_size, ttl=ttl)


def ttl_cache(ttl: float, max_size: int = 1000):
    """TTL缓存装饰器"""
    return advanced_cache(strategy="ttl", max_size=max_size, ttl=ttl)


def adaptive_cache(max_size: int = 1000, ttl: Optional[float] = None):
    """自适应缓存装饰器"""
    return advanced_cache(strategy="adaptive", max_size=max_size, ttl=ttl)

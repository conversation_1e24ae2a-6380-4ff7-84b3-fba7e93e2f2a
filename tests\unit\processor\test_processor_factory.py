#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 处理器工厂测试 - 验证FIX-1.3.1修复的延迟导入问题
"""

import unittest
from unittest.mock import patch

from miniboot.processor.factory import (BeanPostProcessorFactory,
                                        DefaultBeanPostProcessorFactory,
                                        ProcessorFactoryRegistry,
                                        get_processor_factory,
                                        register_processor_factory)


class MockBeanPostProcessor:
    """模拟Bean后置处理器"""

    def __init__(self, name: str):
        self.name = name

    def post_process_before_initialization(self, bean, _bean_name: str):
        return bean

    def post_process_after_initialization(self, bean, _bean_name: str):
        return bean


class MockBeanFactory:
    """模拟Bean工厂"""

    def __init__(self):
        self.name = "MockBeanFactory"


class TestProcessorFactory(unittest.TestCase):
    """处理器工厂测试"""

    def test_default_processor_factory_creation(self):
        """测试默认处理器工厂创建"""
        factory = DefaultBeanPostProcessorFactory()

        # 验证工厂创建成功
        self.assertIsInstance(factory, BeanPostProcessorFactory)

        # 验证处理器名称
        processor_names = factory.names()
        self.assertIn("AutowiredAnnotationProcessor", processor_names)
        self.assertIn("ValueAnnotationProcessor", processor_names)

    def test_create_core_processors(self):
        """测试创建核心处理器"""
        factory = DefaultBeanPostProcessorFactory()
        mock_bean_factory = MockBeanFactory()

        # 创建核心处理器
        processors = factory.core(mock_bean_factory)

        # 验证处理器创建成功
        self.assertIsInstance(processors, list)
        # 应该至少有一个处理器（即使某些导入失败）
        self.assertGreaterEqual(len(processors), 0)

    def test_processor_factory_registry(self):
        """测试处理器工厂注册表"""
        registry = ProcessorFactoryRegistry()

        # 验证默认工厂存在
        self.assertIn("default", registry.available())

        # 获取默认工厂
        default_factory = registry.factory("default")
        self.assertIsInstance(default_factory, DefaultBeanPostProcessorFactory)

    def test_custom_processor_factory_registration(self):
        """测试自定义处理器工厂注册"""

        class CustomProcessorFactory(BeanPostProcessorFactory):
            def core(self, _bean_factory):
                return [MockBeanPostProcessor("custom")]

            def names(self):
                return ["CustomProcessor"]

        registry = ProcessorFactoryRegistry()
        custom_factory = CustomProcessorFactory()

        # 注册自定义工厂
        registry.register("custom", custom_factory)

        # 验证注册成功
        self.assertIn("custom", registry.available())

        # 获取自定义工厂
        retrieved_factory = registry.factory("custom")
        self.assertIs(retrieved_factory, custom_factory)

        # 测试自定义工厂功能
        processors = retrieved_factory.core(MockBeanFactory())
        self.assertEqual(len(processors), 1)
        self.assertEqual(processors[0].name, "custom")

    def test_factory_not_found_error(self):
        """测试工厂不存在错误"""
        registry = ProcessorFactoryRegistry()

        with self.assertRaises(KeyError):
            registry.factory("nonexistent")

    def test_global_factory_functions(self):
        """测试全局工厂函数"""
        # 测试获取默认工厂
        factory = get_processor_factory()
        self.assertIsInstance(factory, DefaultBeanPostProcessorFactory)

        # 测试注册自定义工厂
        class TestFactory(BeanPostProcessorFactory):
            def create_core_processors(self, _bean_factory):
                return []

            def get_processor_names(self):
                return ["TestProcessor"]

        test_factory = TestFactory()
        register_processor_factory("test", test_factory)

        # 验证注册成功
        retrieved_factory = get_processor_factory("test")
        self.assertIs(retrieved_factory, test_factory)

    @patch("miniboot.processor.autowired.AutowiredAnnotationProcessor")
    def test_autowired_processor_creation_success(self, mock_autowired_class):
        """测试自动装配处理器创建成功"""
        mock_processor = MockBeanPostProcessor("autowired")
        mock_autowired_class.return_value = mock_processor

        factory = DefaultBeanPostProcessorFactory()
        mock_bean_factory = MockBeanFactory()

        # 创建处理器
        processor = factory._create_autowired_processor(mock_bean_factory)

        # 验证创建成功
        self.assertIs(processor, mock_processor)
        mock_autowired_class.assert_called_once_with(mock_bean_factory)

    @patch("miniboot.processor.autowired.AutowiredAnnotationProcessor", side_effect=ImportError("Module not found"))
    def test_autowired_processor_creation_failure(self, _mock_autowired_class):
        """测试自动装配处理器创建失败"""
        factory = DefaultBeanPostProcessorFactory()
        mock_bean_factory = MockBeanFactory()

        # 创建处理器应该返回None
        processor = factory._create_autowired_processor(mock_bean_factory)

        # 验证返回None
        self.assertIsNone(processor)

    @patch("miniboot.processor.value.ValueAnnotationProcessor")
    def test_value_processor_creation_success(self, mock_value_class):
        """测试值注入处理器创建成功"""
        mock_processor = MockBeanPostProcessor("value")
        mock_value_class.return_value = mock_processor

        factory = DefaultBeanPostProcessorFactory()

        # 创建处理器
        processor = factory._create_value_processor()

        # 验证创建成功
        self.assertIs(processor, mock_processor)
        mock_value_class.assert_called_once()

    @patch("miniboot.processor.value.ValueAnnotationProcessor", side_effect=ImportError("Module not found"))
    def test_value_processor_creation_failure(self, _mock_value_class):
        """测试值注入处理器创建失败"""
        factory = DefaultBeanPostProcessorFactory()

        # 创建处理器应该返回None
        processor = factory._create_value_processor()

        # 验证返回None
        self.assertIsNone(processor)

    def test_partial_processor_creation_failure(self):
        """测试部分处理器创建失败的情况"""
        factory = DefaultBeanPostProcessorFactory()
        mock_bean_factory = MockBeanFactory()

        # 模拟部分导入失败
        with (
            patch.object(factory, "_create_autowired_processor", return_value=None),
            patch.object(factory, "_create_value_processor", return_value=MockBeanPostProcessor("value")),
        ):
            processors = factory.create_core_processors(mock_bean_factory)

            # 应该只有一个处理器（值注入处理器）
            self.assertEqual(len(processors), 1)
            self.assertEqual(processors[0].name, "value")

    def test_all_processor_creation_failure(self):
        """测试所有处理器创建失败的情况"""
        factory = DefaultBeanPostProcessorFactory()
        mock_bean_factory = MockBeanFactory()

        # 模拟所有导入失败
        with (
            patch.object(factory, "_create_autowired_processor", return_value=None),
            patch.object(factory, "_create_value_processor", return_value=None),
        ):
            processors = factory.create_core_processors(mock_bean_factory)

            # 应该返回空列表
            self.assertEqual(len(processors), 0)

    def test_processor_creation_exception_handling(self):
        """测试处理器创建异常处理"""
        factory = DefaultBeanPostProcessorFactory()
        mock_bean_factory = MockBeanFactory()

        # 模拟创建过程中抛出异常
        with patch.object(factory, "_create_autowired_processor", side_effect=RuntimeError("Creation failed")):
            processors = factory.create_core_processors(mock_bean_factory)

            # 应该捕获异常并返回空列表或部分处理器
            self.assertIsInstance(processors, list)


if __name__ == "__main__":
    unittest.main()

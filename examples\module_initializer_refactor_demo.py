#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
模块初始化器重构演示

展示重构后的模块初始化器的新功能，包括：
- 插件化架构
- 异步优化
- 条件系统
- 生命周期管理
- 依赖解析
- 错误恢复
- 性能监控
"""

import asyncio
import time
from miniboot.context.module_initializer import (
    ModuleInitializer,
    ModuleInfo,
    ModulePriority,
    ModuleState,
    ModuleCondition,
    ModuleLifecycleHook,
    DependencyGraph
)


class MockEnvironment:
    """模拟环境配置"""
    
    def __init__(self):
        self.properties = {
            "miniboot.modules.max_concurrent": 3,
            "miniboot.demo.module1.enabled": True,
            "miniboot.demo.module2.enabled": True,
            "miniboot.demo.module3.enabled": False,
            "miniboot.demo.module4.enabled": True,
        }
    
    def get_property(self, key, default=None):
        return self.properties.get(key, default)


class MockApplicationContext:
    """模拟应用上下文"""
    
    def __init__(self):
        self.environment = MockEnvironment()
        self.beans = {}
    
    def get_environment(self):
        return self.environment
    
    def get_bean(self, name):
        return self.beans.get(name)


class CustomCondition(ModuleCondition):
    """自定义条件示例"""
    
    async def evaluate(self, environment, module):
        # 示例：只有在工作日才启用某些模块
        import datetime
        weekday = datetime.datetime.now().weekday()
        
        if "weekend_only" in module.tags:
            return weekday >= 5  # 周末
        elif "weekday_only" in module.tags:
            return weekday < 5   # 工作日
        
        return True
    
    def get_condition_name(self):
        return "CustomCondition"


class DemoLifecycleHook(ModuleLifecycleHook):
    """演示生命周期钩子"""
    
    async def before_initialization(self, module, context):
        print(f"🔧 [Hook] Before initialization: {module.name}")
    
    async def after_initialization(self, module, context):
        print(f"✅ [Hook] After initialization: {module.name}")
    
    async def before_startup(self, module, context):
        print(f"🚀 [Hook] Before startup: {module.name}")
    
    async def after_startup(self, module, context):
        print(f"🏃 [Hook] After startup: {module.name}")
    
    async def before_shutdown(self, module, context):
        print(f"🛑 [Hook] Before shutdown: {module.name}")
    
    async def after_shutdown(self, module, context):
        print(f"💤 [Hook] After shutdown: {module.name}")


async def demo_basic_functionality():
    """演示基本功能"""
    print("🧪 Testing Basic Functionality...")
    
    # 创建模拟环境
    context = MockApplicationContext()
    
    # 创建模块初始化器
    initializer = ModuleInitializer(context.get_environment(), context)
    
    # 添加自定义条件和钩子
    initializer.add_condition(CustomCondition())
    initializer.add_lifecycle_hook(DemoLifecycleHook())
    
    # 创建演示模块
    async def init_module1():
        print("  📦 Module1 initializing...")
        await asyncio.sleep(0.1)  # 模拟初始化时间
        print("  ✅ Module1 initialized")
    
    async def init_module2():
        print("  📦 Module2 initializing...")
        await asyncio.sleep(0.2)  # 模拟初始化时间
        print("  ✅ Module2 initialized")
    
    def init_module3():
        print("  📦 Module3 initializing...")
        time.sleep(0.05)  # 模拟同步初始化
        print("  ✅ Module3 initialized")
    
    async def start_module1():
        print("  🚀 Module1 starting...")
        await asyncio.sleep(0.05)
    
    async def health_check_module1():
        return {"healthy": True, "status": "running", "uptime": 100}
    
    # 注册模块
    module1 = ModuleInfo(
        name="demo_module1",
        description="演示模块1",
        config_key="miniboot.demo.module1.enabled",
        initializer=init_module1,
        starter=start_module1,
        health_checker=health_check_module1,
        priority=ModulePriority.HIGH,
        tags={"demo", "weekday_only"}
    )
    
    module2 = ModuleInfo(
        name="demo_module2",
        description="演示模块2",
        config_key="miniboot.demo.module2.enabled",
        initializer=init_module2,
        dependencies=["demo_module1"],  # 依赖模块1
        priority=ModulePriority.NORMAL,
        tags={"demo"}
    )
    
    module3 = ModuleInfo(
        name="demo_module3",
        description="演示模块3（禁用）",
        config_key="miniboot.demo.module3.enabled",
        initializer=init_module3,
        priority=ModulePriority.LOW,
        tags={"demo", "weekend_only"}
    )
    
    initializer.register_module(module1)
    initializer.register_module(module2)
    initializer.register_module(module3)
    
    # 执行初始化
    result = await initializer.initialize_modules()
    
    print(f"\n📊 Initialization Results:")
    print(f"  - Total modules: {result['statistics']['initialized_count'] + result['statistics']['failed_count'] + result['statistics']['disabled_count']}")
    print(f"  - Initialized: {result['statistics']['initialized_count']}")
    print(f"  - Failed: {result['statistics']['failed_count']}")
    print(f"  - Disabled: {result['statistics']['disabled_count']}")
    print(f"  - Success rate: {result['statistics']['success_rate']:.1%}")
    print(f"  - Total time: {result['statistics']['total_initialization_time']:.3f}s")
    
    # 测试模块启动
    print(f"\n🚀 Testing Module Startup...")
    success = await initializer.start_module("demo_module1")
    print(f"  - Module1 start result: {success}")
    
    # 测试健康检查
    print(f"\n🏥 Testing Health Check...")
    health = await initializer.check_module_health("demo_module1")
    print(f"  - Module1 health: {health}")
    
    # 测试模块状态
    print(f"\n📋 Module Status:")
    status = initializer.get_module_status()
    for name, state in status["module_states"].items():
        print(f"  - {name}: {state}")
    
    return True


async def demo_dependency_graph():
    """演示依赖图功能"""
    print("\n🧪 Testing Dependency Graph...")
    
    # 创建依赖图
    graph = DependencyGraph()
    
    # 添加依赖关系
    graph.add_dependency("web", "config")
    graph.add_dependency("web", "database")
    graph.add_dependency("api", "web")
    graph.add_dependency("scheduler", "database")
    graph.add_dependency("monitoring", "api")
    
    print("📊 Dependency Graph:")
    print(f"  - web depends on: {graph.get_dependencies('web')}")
    print(f"  - api depends on: {graph.get_dependencies('api')}")
    print(f"  - database is depended by: {graph.get_dependents('database')}")
    
    # 测试拓扑排序
    topo_order = graph.topological_sort()
    print(f"  - Topological order: {topo_order}")
    
    # 测试循环依赖检测
    graph.add_dependency("database", "monitoring")  # 创建循环
    cycle = graph.has_circular_dependency()
    if cycle:
        print(f"  - Circular dependency detected: {' -> '.join(cycle)}")
    else:
        print("  - No circular dependency found")
    
    return True


async def demo_concurrent_initialization():
    """演示并发初始化"""
    print("\n🧪 Testing Concurrent Initialization...")
    
    context = MockApplicationContext()
    context.environment.properties["miniboot.modules.max_concurrent"] = 2
    
    initializer = ModuleInitializer(context.get_environment(), context)
    
    # 创建多个模块
    async def slow_init(name, delay):
        print(f"  📦 {name} starting initialization...")
        await asyncio.sleep(delay)
        print(f"  ✅ {name} initialization completed")
    
    modules = []
    for i in range(5):
        module = ModuleInfo(
            name=f"concurrent_module_{i}",
            description=f"并发模块 {i}",
            config_key=f"miniboot.demo.module{i}.enabled",
            initializer=lambda name=f"Module{i}", delay=0.2: slow_init(name, delay),
            priority=ModulePriority.NORMAL
        )
        modules.append(module)
        initializer.register_module(module)
        # 启用所有模块
        context.environment.properties[f"miniboot.demo.module{i}.enabled"] = True
    
    start_time = time.time()
    result = await initializer.initialize_modules()
    end_time = time.time()
    
    print(f"📊 Concurrent Initialization Results:")
    print(f"  - Total time: {end_time - start_time:.3f}s")
    print(f"  - Expected sequential time: ~1.0s")
    print(f"  - Speedup: ~{1.0 / (end_time - start_time):.1f}x")
    print(f"  - Initialized modules: {len(result['initialized_modules'])}")
    
    return True


async def demo_error_handling():
    """演示错误处理"""
    print("\n🧪 Testing Error Handling...")
    
    context = MockApplicationContext()
    initializer = ModuleInitializer(context.get_environment(), context)
    
    # 创建会失败的模块
    async def failing_init():
        print("  📦 Failing module initializing...")
        await asyncio.sleep(0.1)
        raise RuntimeError("Simulated initialization failure")
    
    async def normal_init():
        print("  📦 Normal module initializing...")
        await asyncio.sleep(0.1)
        print("  ✅ Normal module initialized")
    
    failing_module = ModuleInfo(
        name="failing_module",
        description="会失败的模块",
        config_key="miniboot.demo.failing.enabled",
        initializer=failing_init,
        priority=ModulePriority.NORMAL
    )
    
    normal_module = ModuleInfo(
        name="normal_module",
        description="正常模块",
        config_key="miniboot.demo.normal.enabled",
        initializer=normal_init,
        priority=ModulePriority.NORMAL
    )
    
    initializer.register_module(failing_module)
    initializer.register_module(normal_module)
    
    # 启用模块
    context.environment.properties["miniboot.demo.failing.enabled"] = True
    context.environment.properties["miniboot.demo.normal.enabled"] = True
    
    result = await initializer.initialize_modules()
    
    print(f"📊 Error Handling Results:")
    print(f"  - Failed modules: {result['failed_modules']}")
    print(f"  - Initialized modules: {result['initialized_modules']}")
    print(f"  - System continued despite failures: ✅")
    
    return True


async def main():
    """主测试函数"""
    print("🚀 Module Initializer Refactor Demo")
    print("=" * 50)
    
    tests = [
        ("Basic Functionality", demo_basic_functionality),
        ("Dependency Graph", demo_dependency_graph),
        ("Concurrent Initialization", demo_concurrent_initialization),
        ("Error Handling", demo_error_handling),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} Test...")
            success = await test_func()
            results.append((test_name, success))
            print(f"✅ {test_name} Test: PASSED")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ {test_name} Test: FAILED - {e}")
    
    # 总结
    print(f"\n📋 Test Summary:")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  - {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Module initializer refactor is working correctly!")
    else:
        print("⚠️  Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

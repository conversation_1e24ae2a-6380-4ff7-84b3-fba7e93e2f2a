#!/usr/bin/env python
# encoding: utf-8
"""
Processor模块集成示例

展示Processor模块与新异常处理架构的集成使用,
包括自定义处理器、异常处理、性能监控和配置管理.
"""

import time
from typing import Any

# 导入注解
from miniboot.annotations import Autowired, Component

# 导入Bean模块
from miniboot.bean import BeanDefinition, BeanScope, create_bean_factory

# 导入异常处理架构
from miniboot.errors import BeanProcessingError, create_bean_processing_error, get_decorator_metrics, performance_monitor, timeout_handler

# 导入Processor模块
from miniboot.processor import BeanPostProcessor, BeanPostProcessorManager, ProcessorConfig

# ============================================================================
# 业务Bean定义
# ============================================================================


@Component
class UserService:
    """用户服务"""

    def __init__(self):
        self.users = {}
        print("🔧 UserService 已创建")

    def create_user(self, name: str, email: str):
        user_id = f"user_{len(self.users) + 1}"
        user = {"id": user_id, "name": name, "email": email}
        self.users[user_id] = user
        print(f"👤 创建用户: {user}")
        return user

    def get_user_count(self):
        return len(self.users)


@Component
class NotificationService:
    """通知服务"""

    def __init__(self):
        self.user_service = None
        print("📧 NotificationService 已创建")

    @Autowired
    def set_user_service(self, user_service: UserService):
        self.user_service = user_service

    def send_welcome_notification(self, user_id: str):
        print(f"📨 发送欢迎通知给用户: {user_id}")


@Component
class OrderService:
    """订单服务"""

    def __init__(self):
        self.user_service = None
        self.notification_service = None
        print("🛒 OrderService 已创建")

    @Autowired
    def set_dependencies(self, user_service: UserService, notification_service: NotificationService):
        self.user_service = user_service
        self.notification_service = notification_service

    def create_order(self, user_id: str, product: str):
        order_id = f"order_{int(time.time())}"
        order = {"id": order_id, "user_id": user_id, "product": product}
        print(f"📦 创建订单: {order}")
        return order


# ============================================================================
# 自定义处理器
# ============================================================================


class LoggingProcessor(BeanPostProcessor):
    """日志记录处理器 - 记录Bean的创建过程"""

    def get_order(self) -> int:
        return 100  # 较低优先级

    def _do_post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        print(f"📝 [LoggingProcessor] Bean '{bean_name}' 初始化前处理")
        return bean

    def _do_post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        print(f"📝 [LoggingProcessor] Bean '{bean_name}' 初始化后处理")
        return bean


class ValidationProcessor(BeanPostProcessor):
    """验证处理器 - 验证Bean的有效性"""

    def get_order(self) -> int:
        return 50  # 中等优先级

    def _do_post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        print(f"✅ [ValidationProcessor] 验证Bean '{bean_name}'")

        # 模拟验证逻辑
        if hasattr(bean, "__dict__"):
            if not bean.__dict__:
                print(f"⚠️  Bean '{bean_name}' 没有属性,可能需要注意")

        return bean

    def _do_post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        print(f"✅ [ValidationProcessor] Bean '{bean_name}' 验证完成")
        return bean


class PerformanceMonitoringProcessor(BeanPostProcessor):
    """性能监控处理器 - 为Bean添加性能监控"""

    def get_order(self) -> int:
        return 10  # 高优先级

    def _do_post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        print(f"📊 [PerformanceProcessor] 为Bean '{bean_name}' 添加性能监控")

        # 为Bean添加创建时间戳
        if hasattr(bean, "__dict__"):
            bean.__dict__["_creation_time"] = time.time()

        return bean

    def _do_post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        print(f"📊 [PerformanceProcessor] Bean '{bean_name}' 性能监控设置完成")

        # 计算初始化耗时
        if hasattr(bean, "_creation_time"):
            init_time = time.time() - bean._creation_time
            print(f"⏱️  Bean '{bean_name}' 初始化耗时: {init_time:.3f}秒")

        return bean


class ErrorSimulationProcessor(BeanPostProcessor):
    """错误模拟处理器 - 用于测试异常处理"""

    def get_order(self) -> int:
        return 200  # 最低优先级

    def _do_post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
        # 模拟处理某些Bean时出错
        if bean_name == "errorBean":
            raise create_bean_processing_error(bean_name=bean_name, processor_name=self.__class__.__name__, message="模拟处理错误")

        return bean

    def _do_post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
        return bean


# ============================================================================
# 主程序
# ============================================================================


def main():
    print("🚀 启动Processor模块集成示例")

    # 1. 创建Bean工厂
    print("\n📦 创建Bean工厂...")
    bean_factory = create_bean_factory()

    # 2. 创建处理器管理器
    print("\n🔧 创建处理器管理器...")
    processor_manager = BeanPostProcessorManager()

    # 3. 注册自定义处理器
    print("\n📋 注册自定义处理器...")

    # 性能监控处理器 - 高优先级
    perf_config = ProcessorConfig(timeout_seconds=5.0, retry_count=2, retry_delay=0.1)
    processor_manager.register_processor(PerformanceMonitoringProcessor(), perf_config)

    # 验证处理器 - 中等优先级
    validation_config = ProcessorConfig(timeout_seconds=3.0, retry_count=1)
    processor_manager.register_processor(ValidationProcessor(), validation_config)

    # 日志处理器 - 低优先级
    logging_config = ProcessorConfig(timeout_seconds=2.0, error_threshold=5)
    processor_manager.register_processor(LoggingProcessor(), logging_config)

    # 错误模拟处理器 - 最低优先级
    error_config = ProcessorConfig(
        timeout_seconds=1.0,
        retry_count=0,  # 不重试,直接失败
    )
    processor_manager.register_processor(ErrorSimulationProcessor(), error_config)

    print(f"✅ 已注册 {processor_manager.get_processor_count()} 个处理器")

    # 4. 注册Bean定义
    print("\n🏗️  注册Bean定义...")

    # 注册UserService
    user_service_def = BeanDefinition(bean_name="userService", bean_class=UserService, scope=BeanScope.SINGLETON)
    bean_factory._registry.register_bean_definition("userService", user_service_def)

    # 注册NotificationService
    notification_service_def = BeanDefinition(bean_name="notificationService", bean_class=NotificationService, scope=BeanScope.SINGLETON)
    bean_factory._registry.register_bean_definition("notificationService", notification_service_def)

    # 注册OrderService
    order_service_def = BeanDefinition(bean_name="orderService", bean_class=OrderService, scope=BeanScope.SINGLETON)
    bean_factory._registry.register_bean_definition("orderService", order_service_def)

    # 5. 创建Bean实例(触发处理器执行)
    print("\n🔨 创建Bean实例...")

    try:
        # 创建UserService
        print("\n--- 创建 UserService ---")
        user_service = bean_factory.get_bean("userService")

        # 创建NotificationService
        print("\n--- 创建 NotificationService ---")
        notification_service = bean_factory.get_bean("notificationService")

        # 创建OrderService
        print("\n--- 创建 OrderService ---")
        order_service = bean_factory.get_bean("orderService")

        print("\n✅ 所有Bean创建成功!")

    except Exception as e:
        print(f"\n❌ Bean创建失败: {e}")

    # 6. 测试业务功能
    print("\n🧪 测试业务功能...")

    try:
        # 创建用户
        user = user_service.create_user("张三", "<EMAIL>")

        # 发送通知
        notification_service.send_welcome_notification(user["id"])

        # 创建订单
        order = order_service.create_order(user["id"], "笔记本电脑")

        print(f"📊 当前用户数量: {user_service.get_user_count()}")

    except Exception as e:
        print(f"❌ 业务功能测试失败: {e}")

    # 7. 测试错误处理
    print("\n🚨 测试错误处理...")

    try:
        # 尝试创建一个会触发错误的Bean
        error_bean_def = BeanDefinition(
            bean_name="errorBean",
            bean_class=str,  # 简单的字符串类
            scope=BeanScope.SINGLETON,
        )
        bean_factory._registry.register_bean_definition("errorBean", error_bean_def)

        error_bean = bean_factory.get_bean("errorBean")
        print(f"⚠️  意外成功创建了errorBean: {error_bean}")

    except BeanProcessingError as e:
        print(f"✅ 成功捕获Bean处理异常: {e}")
    except Exception as e:
        print(f"❌ 捕获到意外异常: {e}")

    # 8. 显示处理器状态
    print("\n📊 处理器状态统计:")

    for processor_name in ["PerformanceMonitoringProcessor", "ValidationProcessor", "LoggingProcessor", "ErrorSimulationProcessor"]:
        state = processor_manager.get_processor_state(processor_name)
        if state:
            print(f"  - {processor_name}: {state.value if hasattr(state, 'value') else state}")
            # 注意:ProcessorState 可能是枚举值,不是对象
            # 如果需要更详细的状态信息,需要从管理器获取

    # 9. 显示异常处理指标
    print("\n📈 异常处理指标:")
    metrics = get_decorator_metrics()

    print("函数调用统计:")
    for func_name, count in list(metrics["function_calls"].items())[:5]:  # 显示前5个
        print(f"  - {func_name}: {count} 次")

    print("异常统计:")
    for exception_type, count in metrics["exceptions"].items():
        print(f"  - {exception_type}: {count} 次")

    print("执行时间统计:")
    for func_name, times in list(metrics["execution_times"].items())[:3]:  # 显示前3个
        if times:
            avg_time = sum(times) / len(times)
            print(f"  - {func_name}: 平均 {avg_time:.3f}s")

    print("\n🎉 Processor模块集成示例运行完成!")


if __name__ == "__main__":
    main()

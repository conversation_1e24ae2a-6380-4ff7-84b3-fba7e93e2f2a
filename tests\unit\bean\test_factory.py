#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean工厂单元测试
"""

import unittest
from typing import Any
from unittest.mock import Mock, patch

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.utils import BeanCreationError, NoSuchBeanDefinitionError


class TestBean:
    """测试用Bean类"""

    def __init__(self, name: str = "test"):
        self.name = name
        self.initialized = False
        self.destroyed = False

    def init(self):
        """初始化方法"""
        self.initialized = True

    def destroy(self):
        """销毁方法"""
        self.destroyed = True


class TestDependentBean:
    """有依赖的测试Bean"""

    def __init__(self, dependency: TestBean):
        self.dependency = dependency
        self.name = "dependent"


class TestDefaultBeanFactory(unittest.TestCase):
    """DefaultBeanFactory测试"""

    def setUp(self):
        """测试前置设置"""
        self.factory = DefaultBeanFactory()

    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.factory, 'shutdown'):
            self.factory.shutdown()

    def test_factory_creation(self):
        """测试工厂创建"""
        self.assertIsNotNone(self.factory)
        self.assertIsInstance(self.factory, DefaultBeanFactory)

    def test_register_singleton(self):
        """测试注册单例Bean"""
        # 通过registry注册Bean定义
        bean_definition = BeanDefinition(
            bean_name="testBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("testBean", bean_definition)

        # 验证Bean已注册
        self.assertTrue(self.factory.contains_bean("testBean"))

        # 获取Bean
        retrieved_bean = self.factory.get_bean("testBean")
        self.assertIsInstance(retrieved_bean, TestBean)

        # 多次获取应该返回同一个实例
        retrieved_bean2 = self.factory.get_bean("testBean")
        self.assertIs(retrieved_bean, retrieved_bean2)

    def test_register_bean_definition(self):
        """测试注册Bean定义"""
        bean_definition = BeanDefinition(
            bean_name="testBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )

        self.factory._registry.register_bean_definition("testBean", bean_definition)

        # 验证Bean定义已注册
        self.assertTrue(self.factory._registry.contains_bean_definition("testBean"))

        # 获取Bean定义
        retrieved_def = self.factory._registry.get_bean_definition("testBean")
        self.assertEqual(retrieved_def, bean_definition)

    def test_get_bean_by_name(self):
        """测试按名称获取Bean"""
        bean_definition = BeanDefinition(
            bean_name="testBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )

        self.factory._registry.register_bean_definition("testBean", bean_definition)

        # 获取Bean
        bean = self.factory.get_bean("testBean")
        self.assertIsInstance(bean, TestBean)
        self.assertEqual(bean.name, "test")  # 默认构造函数参数

    def test_get_bean_by_type(self):
        """测试按类型获取Bean"""
        bean_definition = BeanDefinition(
            bean_name="testBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )

        self.factory._registry.register_bean_definition("testBean", bean_definition)

        # 按类型获取Bean
        bean = self.factory.get_bean("testBean", TestBean)
        self.assertIsInstance(bean, TestBean)

    def test_get_nonexistent_bean(self):
        """测试获取不存在的Bean"""
        with self.assertRaises(KeyError):  # DefaultBeanFactory抛出KeyError
            self.factory.get_bean("nonexistentBean")

    def test_singleton_scope(self):
        """测试单例作用域"""
        bean_definition = BeanDefinition(
            bean_name="singletonBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )

        self.factory._registry.register_bean_definition("singletonBean", bean_definition)

        # 多次获取应该返回同一个实例
        bean1 = self.factory.get_bean("singletonBean")
        bean2 = self.factory.get_bean("singletonBean")

        self.assertIs(bean1, bean2)
        self.assertTrue(self.factory.is_singleton("singletonBean"))

    def test_prototype_scope(self):
        """测试原型作用域"""
        bean_definition = BeanDefinition(
            bean_name="prototypeBean",
            bean_class=TestBean,
            scope=BeanScope.PROTOTYPE
        )

        self.factory._registry.register_bean_definition("prototypeBean", bean_definition)

        # 多次获取应该返回不同实例
        bean1 = self.factory.get_bean("prototypeBean")
        bean2 = self.factory.get_bean("prototypeBean")

        self.assertIsNot(bean1, bean2)
        self.assertFalse(self.factory.is_singleton("prototypeBean"))

    def test_bean_with_init_method(self):
        """测试带初始化方法的Bean"""
        bean_definition = BeanDefinition(
            bean_name="initBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )

        self.factory._registry.register_bean_definition("initBean", bean_definition)

        # 获取Bean，应该自动调用初始化方法
        bean = self.factory.get_bean("initBean")
        self.assertTrue(bean.initialized)

    def test_bean_with_destroy_method(self):
        """测试带销毁方法的Bean"""
        bean_definition = BeanDefinition(
            bean_name="destroyBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON,
            destroy_method_name="destroy"
        )

        self.factory._registry.register_bean_definition("destroyBean", bean_definition)

        # 获取Bean
        bean = self.factory.get_bean("destroyBean")
        self.assertFalse(bean.destroyed)

        # 关闭工厂，应该调用销毁方法
        self.factory.destroy_singletons()  # 使用正确的方法名
        self.assertTrue(bean.destroyed)

    def test_get_bean_names(self):
        """测试获取Bean名称列表"""
        # 注册几个Bean
        bean_def1 = BeanDefinition(
            bean_name="bean1",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("bean1", bean_def1)

        bean_def2 = BeanDefinition(
            bean_name="bean2",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("bean2", bean_def2)

        # 获取Bean名称
        bean_names = self.factory.get_bean_names()
        self.assertIn("bean1", bean_names)
        self.assertIn("bean2", bean_names)
        self.assertGreaterEqual(len(bean_names), 2)

    def test_get_bean_names_by_type(self):
        """测试按类型获取Bean名称"""
        # 注册TestBean类型的Bean
        bean_def1 = BeanDefinition(
            bean_name="testBean1",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("testBean1", bean_def1)

        bean_def2 = BeanDefinition(
            bean_name="testBean2",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("testBean2", bean_def2)

        # 注册其他类型的Bean
        mock_def = BeanDefinition(
            bean_name="mockBean",
            bean_class=Mock,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("mockBean", mock_def)

        # 按类型获取Bean - 使用实际存在的方法
        test_beans = self.factory.get_beans_of_type(TestBean)
        self.assertIn("testBean1", test_beans)
        self.assertIn("testBean2", test_beans)
        self.assertNotIn("mockBean", test_beans)

    def test_get_type(self):
        """测试获取Bean类型"""
        bean_definition = BeanDefinition(
            bean_name="testBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )

        self.factory._registry.register_bean_definition("testBean", bean_definition)

        # 获取Bean类型
        bean_type = self.factory.get_type("testBean")
        self.assertEqual(bean_type, TestBean)

    def test_contains_bean(self):
        """测试检查Bean是否存在"""
        self.assertFalse(self.factory.contains_bean("nonexistent"))

        bean_def = BeanDefinition(
            bean_name="existingBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("existingBean", bean_def)
        self.assertTrue(self.factory.contains_bean("existingBean"))

    def test_bean_creation_error_handling(self):
        """测试Bean创建错误处理"""
        # 创建一个会抛出异常的Bean类
        class ErrorBean:
            def __init__(self):
                raise ValueError("Creation error")

        bean_definition = BeanDefinition(
            bean_name="errorBean",
            bean_class=ErrorBean,
            scope=BeanScope.SINGLETON
        )

        self.factory._registry.register_bean_definition("errorBean", bean_definition)

        # 获取Bean应该抛出异常（可能是ValueError或其他异常）
        with self.assertRaises(Exception):  # 使用更通用的异常类型
            self.factory.get_bean("errorBean")

    def test_factory_shutdown(self):
        """测试工厂关闭"""
        # 注册一些Bean
        bean_def1 = BeanDefinition(
            bean_name="bean1",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )
        self.factory._registry.register_bean_definition("bean1", bean_def1)

        bean_def2 = BeanDefinition(
            bean_name="bean2",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON,
            destroy_method_name="destroy"
        )
        self.factory._registry.register_bean_definition("bean2", bean_def2)

        # 获取Bean以确保它们被创建
        bean2 = self.factory.get_bean("bean2")

        # 关闭工厂
        self.factory.destroy_singletons()

        # 验证销毁方法被调用
        self.assertTrue(bean2.destroyed)

    @patch('miniboot.bean.factory.DefaultBeanFactory._create_bean')
    def test_bean_creation_caching(self, mock_create_bean):
        """测试Bean创建缓存"""
        mock_bean = TestBean()
        mock_create_bean.return_value = mock_bean

        bean_definition = BeanDefinition(
            bean_name="cachedBean",
            bean_class=TestBean,
            scope=BeanScope.SINGLETON
        )

        self.factory._registry.register_bean_definition("cachedBean", bean_definition)

        # 第一次获取Bean
        bean1 = self.factory.get_bean("cachedBean")

        # 第二次获取Bean
        bean2 = self.factory.get_bean("cachedBean")

        # 验证_create_bean只被调用一次（因为有缓存）
        mock_create_bean.assert_called_once()

        # 验证返回的是同一个实例
        self.assertIs(bean1, bean2)


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
WebAutoConfiguration 集成测试

测试 Actuator Web 自动配置类与实际组件的集成，包括：
- 条件化配置的实际效果
- Bean 创建和依赖注入
- Web 集成组件的协同工作
"""

import unittest
from unittest.mock import Mock, patch

from miniboot.starters.actuator.autoconfigure.web import WebAutoConfiguration
from miniboot.starters.actuator.properties import ActuatorProperties
from miniboot.starters.actuator.web.integration import WebIntegrationChecker
from miniboot.starters.actuator.web.routes import ActuatorRouteRegistrar


class TestWebAutoConfigurationIntegration(unittest.TestCase):
    """WebAutoConfiguration 集成测试类"""

    def setUp(self):
        """设置测试环境"""
        self.config = WebAutoConfiguration()

        # 创建真实的 ActuatorProperties
        self.actuator_properties = ActuatorProperties()
        self.actuator_properties.enabled = True
        self.actuator_properties.web.enabled = True
        self.actuator_properties.web.base_path = "/actuator"
        self.actuator_properties.web.endpoints.health = True
        self.actuator_properties.web.endpoints.info = True

    def test_web_integration_checker_real_creation(self):
        """测试真实的 WebIntegrationChecker 创建"""
        checker = self.config.web_integration_checker()

        # 验证返回的是真实的 WebIntegrationChecker 实例
        self.assertIsInstance(checker, WebIntegrationChecker)

        # 验证基本功能
        self.assertTrue(callable(checker.is_fastapi_available))
        self.assertTrue(callable(checker.is_web_module_enabled))
        self.assertTrue(callable(checker.should_enable_web_integration))
        self.assertTrue(callable(checker.get_integration_status))

        # 验证状态获取
        status = checker.get_integration_status()
        self.assertIsNotNone(status)
        self.assertIsInstance(status.fastapi_available, bool)
        self.assertIsInstance(status.web_module_enabled, bool)
        self.assertIsInstance(status.should_integrate, bool)

    def test_actuator_route_registrar_with_enabled_integration(self):
        """测试启用 Web 集成时的路由注册器创建"""
        # 创建真实的 WebIntegrationChecker
        checker = WebIntegrationChecker()

        # 模拟启用 Web 集成的条件
        with patch.object(checker, 'should_enable_web_integration', return_value=True):
            registrar = self.config.actuator_route_registrar(
                self.actuator_properties,
                checker
            )

            # 验证返回的是真实的 ActuatorRouteRegistrar 实例
            self.assertIsInstance(registrar, ActuatorRouteRegistrar)

            # 验证基本属性
            self.assertEqual(registrar._base_path, "/actuator")
            self.assertIsNotNone(registrar._actuator_context)

            # 验证基本功能
            self.assertTrue(callable(registrar.register_routes))
            self.assertTrue(callable(registrar.register_endpoint_route))
            self.assertTrue(callable(registrar.unregister_endpoint_routes))
            self.assertTrue(callable(registrar.get_route_status))

    def test_actuator_route_registrar_with_disabled_integration(self):
        """测试禁用 Web 集成时不创建路由注册器"""
        # 创建真实的 WebIntegrationChecker
        checker = WebIntegrationChecker()

        # 模拟禁用 Web 集成的条件
        with patch.object(checker, 'should_enable_web_integration', return_value=False):
            registrar = self.config.actuator_route_registrar(
                self.actuator_properties,
                checker
            )

            # 验证返回 None
            self.assertIsNone(registrar)

    def test_full_configuration_workflow(self):
        """测试完整的配置工作流程"""
        # 1. 创建 WebIntegrationChecker
        checker = self.config.web_integration_checker()
        self.assertIsInstance(checker, WebIntegrationChecker)

        # 2. 获取集成状态
        status = checker.get_integration_status()
        self.assertIsNotNone(status)

        # 3. 根据集成状态决定是否创建路由注册器
        if status.should_integrate:
            # 如果应该集成，创建路由注册器
            with patch.object(checker, 'should_enable_web_integration', return_value=True):
                registrar = self.config.actuator_route_registrar(
                    self.actuator_properties,
                    checker
                )
                self.assertIsInstance(registrar, ActuatorRouteRegistrar)
        else:
            # 如果不应该集成，不创建路由注册器
            with patch.object(checker, 'should_enable_web_integration', return_value=False):
                registrar = self.config.actuator_route_registrar(
                    self.actuator_properties,
                    checker
                )
                self.assertIsNone(registrar)

    def test_actuator_context_creation_and_configuration(self):
        """测试 ActuatorContext 的创建和配置"""
        # 测试 _get_actuator_context 方法
        actuator_context = self.config._get_actuator_context(self.actuator_properties)

        # 验证 ActuatorContext 实例
        self.assertIsNotNone(actuator_context)
        self.assertEqual(actuator_context.__class__.__name__, 'ActuatorContext')

        # 验证配置传递
        self.assertIsNotNone(actuator_context.properties)
        self.assertEqual(actuator_context.properties.enabled, self.actuator_properties.enabled)
        self.assertEqual(actuator_context.properties.base_path, self.actuator_properties.web.base_path)

    def test_fastapi_app_handling(self):
        """测试 FastAPI 应用处理"""
        # 测试 _get_fastapi_app 方法
        app = self.config._get_fastapi_app()

        # 当前实现总是返回 None，这是预期的
        self.assertIsNone(app)

    def test_configuration_with_different_properties(self):
        """测试不同配置属性的处理"""
        # 测试不同的基础路径
        test_properties = ActuatorProperties()
        test_properties.enabled = True
        test_properties.web.enabled = True
        test_properties.web.base_path = "/custom-actuator"

        # 创建 ActuatorContext
        actuator_context = self.config._get_actuator_context(test_properties)
        self.assertEqual(actuator_context.properties.base_path, "/custom-actuator")

        # 创建路由注册器
        checker = WebIntegrationChecker()
        with patch.object(checker, 'should_enable_web_integration', return_value=True):
            registrar = self.config.actuator_route_registrar(test_properties, checker)
            self.assertEqual(registrar._base_path, "/custom-actuator")

    def test_error_handling_in_configuration(self):
        """测试配置过程中的错误处理"""
        # 测试无效的配置属性
        invalid_properties = None

        with self.assertRaises(AttributeError):
            self.config._get_actuator_context(invalid_properties)

    def test_middleware_configuration_placeholder(self):
        """测试中间件配置（占位符）"""
        middleware = self.config.actuator_web_middleware()

        # 当前实现返回 None，这是预期的
        self.assertIsNone(middleware)


if __name__ == '__main__':
    unittest.main()

#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置基类测试
"""

from unittest.mock import Mock

import pytest

from miniboot.annotations import Bean
from miniboot.autoconfigure.base import AutoConfiguration
from miniboot.autoconfigure.metadata import AutoConfigurationMetadata
from miniboot.context import ApplicationContext
from miniboot.errors.autoconfigure import AutoConfigurationException


class TestAutoConfiguration:
    """自动配置基类测试"""

    def setup_method(self):
        """测试前置设置"""
        self.context = Mock(spec=ApplicationContext)
        self.context.get_environment.return_value = Mock()
        self.context.get_bean_factory.return_value = Mock()
        self.context.contains_bean.return_value = False

    def test_get_metadata_abstract_method(self):
        """测试get_metadata抽象方法"""
        # 不能直接实例化抽象类
        with pytest.raises(TypeError):
            AutoConfiguration()

    def test_should_configure_with_no_conditions(self):
        """测试无条件时应该配置"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

        config = TestConfig()
        assert config.should_configure(self.context) is True

    def test_should_configure_with_property_condition_true(self):
        """测试属性条件为真时应该配置"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration", conditions=["property:test.enabled"])

        # 模拟环境返回属性值
        env_mock = Mock()
        env_mock.get_property.return_value = "true"
        self.context.get_environment.return_value = env_mock

        config = TestConfig()
        assert config.should_configure(self.context) is True

    def test_should_configure_with_property_condition_false(self):
        """测试属性条件为假时不应该配置"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration", conditions=["property:test.enabled"])

        # 模拟环境返回None
        env_mock = Mock()
        env_mock.get_property.return_value = None
        self.context.get_environment.return_value = env_mock

        config = TestConfig()
        assert config.should_configure(self.context) is False

    def test_should_configure_with_class_condition_true(self):
        """测试类存在条件为真时应该配置"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration", conditions=["class:os.path"])

        config = TestConfig()
        assert config.should_configure(self.context) is True

    def test_should_configure_with_class_condition_false(self):
        """测试类不存在条件为假时不应该配置"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration", conditions=["class:non.existent.module"])

        config = TestConfig()
        assert config.should_configure(self.context) is False

    def test_should_configure_with_bean_condition_true(self):
        """测试Bean存在条件为真时应该配置"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration", conditions=["bean:test_bean"])

        # 模拟容器包含Bean
        self.context.contains_bean.return_value = True

        config = TestConfig()
        assert config.should_configure(self.context) is True

    def test_should_configure_with_bean_condition_false(self):
        """测试Bean不存在条件为假时不应该配置"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration", conditions=["bean:test_bean"])

        # 模拟容器不包含Bean
        self.context.contains_bean.return_value = False

        config = TestConfig()
        assert config.should_configure(self.context) is False

    def test_should_configure_with_conflicts(self):
        """测试存在冲突时不应该配置"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration", conflicts_with=["conflicting-config"])

            def _has_configuration(self, config_name, context):
                return config_name == "conflicting-config"

        config = TestConfig()
        assert config.should_configure(self.context) is False

    def test_configure_success(self):
        """测试配置成功"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

            @Bean
            def test_bean(self):
                return "test_value"

        # 为test_bean方法添加Bean注解属性
        TestConfig.test_bean._bean_annotation = True
        TestConfig.test_bean._bean_name = "test_bean"

        config = TestConfig()

        # 模拟Bean工厂
        bean_factory_mock = Mock()
        self.context.get_bean_factory.return_value = bean_factory_mock

        config.configure(self.context)

        # 验证Bean被注册
        bean_factory_mock.register_singleton.assert_called()

    def test_configure_failure(self):
        """测试配置失败"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

            def _do_configure(self, context):
                raise Exception("Configuration failed")

        config = TestConfig()

        with pytest.raises(AutoConfigurationException) as exc_info:
            config.configure(self.context)

        assert "test-config" in str(exc_info.value)

    def test_get_configuration_properties(self):
        """测试获取配置属性"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration", properties_prefix="test.config")

        # 模拟环境返回属性
        env_mock = Mock()
        env_mock.get_properties_with_prefix.return_value = {"enabled": True, "timeout": 30}
        self.context.get_environment.return_value = env_mock

        config = TestConfig()
        properties = config.get_configuration_properties(self.context)

        assert properties == {"enabled": True, "timeout": 30}
        env_mock.get_properties_with_prefix.assert_called_with("test.config")

    def test_evaluate_condition_invalid(self):
        """测试无效条件评估"""

        class TestConfig(AutoConfiguration):
            def get_metadata(self):
                return AutoConfigurationMetadata(name="test-config", description="Test configuration")

            def test_invalid_condition(self, context):
                return self._evaluate_condition("invalid:condition", context)

        config = TestConfig()
        # 无效条件应该返回True（默认行为）
        assert config.test_invalid_condition(self.context) is True

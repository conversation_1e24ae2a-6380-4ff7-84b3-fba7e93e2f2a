#!/usr/bin/env python
"""
* @author: cz
* @description: 配置相关注解测试

测试配置相关注解的功能，包括@Value、@ConfigurationProperties、@ComponentScan等。
"""

import unittest
from dataclasses import dataclass

from miniboot.annotations import (
    ComponentScan,
    Configuration,
    ConfigurationProperties,
    EnableAutoConfiguration,
    Import,
    Value,
    get_component_scan_metadata,
    get_config_prefix,
    get_configuration_properties_metadata,
    get_import_classes,
    get_scan_packages,
    get_value_expression,
    get_value_metadata,
    is_component_scan,
    is_configuration_properties,
    is_enable_auto_configuration,
    is_import,
    is_value_injection,
)


# 测试用的配置类
@dataclass
class DatabaseConfig:
    host: str = "localhost"
    port: int = 3306
    username: str = ""
    password: str = ""


@dataclass
class ServerConfig:
    host: str = "0.0.0.0"
    port: int = 8080


class TestConfigAnnotations(unittest.TestCase):
    """配置相关注解测试类"""

    def test_value_annotation_on_field(self):
        """测试@Value注解在字段上的使用"""

        # 创建一个带@Value注解的字段
        @Value("${app.name}")
        class AppNameField:
            pass

        # 验证字段注解属性
        self.assertTrue(hasattr(AppNameField, "__is_value_injection__"))
        self.assertTrue(AppNameField.__is_value_injection__)
        self.assertEqual(AppNameField.__value_expression__, "${app.name}")
        self.assertTrue(AppNameField.__value_required__)
        self.assertTrue(AppNameField.__is_field_value_injection__)

        # 验证元数据
        metadata = AppNameField.__value_metadata__
        self.assertEqual(metadata.value, "${app.name}")
        self.assertTrue(metadata.required)
        self.assertIsNone(metadata.default_value)

    def test_value_annotation_on_method(self):
        """测试@Value注解在方法上的使用"""

        class ServiceConfig:
            @Value("${service.timeout:30}")
            def set_timeout(self, timeout: int):
                self.timeout = timeout

        method = ServiceConfig.set_timeout

        # 验证方法注解属性
        self.assertTrue(method.__is_value_injection__)
        self.assertEqual(method.__value_expression__, "${service.timeout:30}")
        self.assertTrue(method.__value_required__)
        self.assertTrue(method.__is_setter_value_injection__)

        # 验证元数据
        metadata = method.__value_metadata__
        self.assertEqual(metadata.value, "${service.timeout:30}")
        self.assertTrue(metadata.required)

    def test_value_annotation_with_default(self):
        """测试@Value注解带默认值"""

        class OptionalConfig:
            @Value("${optional.feature}", required=False, default_value=False)
            def set_feature(self, enabled: bool):
                self.feature_enabled = enabled

        method = OptionalConfig.set_feature

        self.assertFalse(method.__value_required__)
        self.assertFalse(method.__value_default__)

        metadata = method.__value_metadata__
        self.assertFalse(metadata.required)
        self.assertFalse(metadata.default_value)

    def test_configuration_properties_annotation(self):
        """测试@ConfigurationProperties注解"""

        @ConfigurationProperties(prefix="database")
        class DatabaseProperties:
            host: str = "localhost"
            port: int = 3306
            username: str = ""
            password: str = ""

        # 验证注解属性
        self.assertTrue(hasattr(DatabaseProperties, "__is_configuration_properties__"))
        self.assertTrue(DatabaseProperties.__is_configuration_properties__)
        self.assertEqual(DatabaseProperties.__config_prefix__, "database")
        self.assertTrue(DatabaseProperties.__config_ignore_unknown__)
        self.assertFalse(DatabaseProperties.__config_ignore_invalid__)
        self.assertTrue(DatabaseProperties.__config_validate__)

        # 验证元数据
        metadata = DatabaseProperties.__configuration_properties_metadata__
        self.assertEqual(metadata.prefix, "database")
        self.assertTrue(metadata.ignore_unknown_fields)
        self.assertFalse(metadata.ignore_invalid_fields)
        self.assertTrue(metadata.validate)

    def test_configuration_properties_with_options(self):
        """测试@ConfigurationProperties注解带选项"""

        @ConfigurationProperties(prefix="service", ignore_unknown_fields=False, ignore_invalid_fields=True, validate=False)
        class ServiceProperties:
            timeout: int = 30
            retries: int = 3

        self.assertEqual(ServiceProperties.__config_prefix__, "service")
        self.assertFalse(ServiceProperties.__config_ignore_unknown__)
        self.assertTrue(ServiceProperties.__config_ignore_invalid__)
        self.assertFalse(ServiceProperties.__config_validate__)

        metadata = ServiceProperties.__configuration_properties_metadata__
        self.assertEqual(metadata.prefix, "service")
        self.assertFalse(metadata.ignore_unknown_fields)
        self.assertTrue(metadata.ignore_invalid_fields)
        self.assertFalse(metadata.validate)

    def test_component_scan_annotation_single_package(self):
        """测试@ComponentScan注解单个包"""

        @Configuration
        @ComponentScan("com.example.service")
        class AppConfig:
            pass

        # 验证注解属性
        self.assertTrue(AppConfig.__is_component_scan__)
        self.assertEqual(AppConfig.__scan_packages__, ["com.example.service"])
        self.assertEqual(AppConfig.__scan_include_filters__, [])
        self.assertEqual(AppConfig.__scan_exclude_filters__, [])
        self.assertFalse(AppConfig.__scan_lazy_init__)

        # 验证元数据
        metadata = AppConfig.__component_scan_metadata__
        self.assertEqual(metadata.base_packages, ["com.example.service"])
        self.assertEqual(metadata.include_filters, [])
        self.assertEqual(metadata.exclude_filters, [])
        self.assertFalse(metadata.lazy_init)

    def test_component_scan_annotation_multiple_packages(self):
        """测试@ComponentScan注解多个包"""

        @Configuration
        @ComponentScan(["com.example.service", "com.example.repository"])
        class AppConfig:
            pass

        self.assertEqual(AppConfig.__scan_packages__, ["com.example.service", "com.example.repository"])

        metadata = AppConfig.__component_scan_metadata__
        self.assertEqual(metadata.base_packages, ["com.example.service", "com.example.repository"])

    def test_component_scan_annotation_with_filters(self):
        """测试@ComponentScan注解带过滤器"""

        @Configuration
        @ComponentScan(base_packages="com.example", include_filters=["*Service", "*Repository"], exclude_filters=["*Test*"], lazy_init=True)
        class AppConfig:
            pass

        self.assertEqual(AppConfig.__scan_packages__, ["com.example"])
        self.assertEqual(AppConfig.__scan_include_filters__, ["*Service", "*Repository"])
        self.assertEqual(AppConfig.__scan_exclude_filters__, ["*Test*"])
        self.assertTrue(AppConfig.__scan_lazy_init__)

        metadata = AppConfig.__component_scan_metadata__
        self.assertEqual(metadata.base_packages, ["com.example"])
        self.assertEqual(metadata.include_filters, ["*Service", "*Repository"])
        self.assertEqual(metadata.exclude_filters, ["*Test*"])
        self.assertTrue(metadata.lazy_init)

    def test_enable_auto_configuration_annotation(self):
        """测试@EnableAutoConfiguration注解"""

        @Configuration
        @EnableAutoConfiguration
        class AppConfig:
            pass

        # 验证注解属性
        self.assertTrue(AppConfig.__is_enable_auto_configuration__)
        self.assertEqual(AppConfig.__auto_config_exclude__, [])

        # 验证元数据
        metadata = AppConfig.__enable_auto_configuration_metadata__
        self.assertEqual(metadata.exclude, [])

    def test_enable_auto_configuration_with_exclude(self):
        """测试@EnableAutoConfiguration注解带排除"""

        @Configuration
        @EnableAutoConfiguration(exclude=["DatabaseAutoConfiguration", "CacheAutoConfiguration"])
        class AppConfig:
            pass

        self.assertEqual(AppConfig.__auto_config_exclude__, ["DatabaseAutoConfiguration", "CacheAutoConfiguration"])

        metadata = AppConfig.__enable_auto_configuration_metadata__
        self.assertEqual(metadata.exclude, ["DatabaseAutoConfiguration", "CacheAutoConfiguration"])

    def test_import_annotation_single_class(self):
        """测试@Import注解单个类"""

        @Configuration
        @Import(DatabaseConfig)
        class AppConfig:
            pass

        # 验证注解属性
        self.assertTrue(AppConfig.__is_import__)
        self.assertEqual(AppConfig.__import_classes__, [DatabaseConfig])

        # 验证元数据
        metadata = AppConfig.__import_metadata__
        self.assertEqual(metadata.import_classes, [DatabaseConfig])

    def test_import_annotation_multiple_classes(self):
        """测试@Import注解多个类"""

        @Configuration
        @Import(DatabaseConfig, ServerConfig)
        class AppConfig:
            pass

        self.assertEqual(AppConfig.__import_classes__, [DatabaseConfig, ServerConfig])

        metadata = AppConfig.__import_metadata__
        self.assertEqual(metadata.import_classes, [DatabaseConfig, ServerConfig])

    def test_combined_config_annotations(self):
        """测试组合使用多个配置注解"""

        @Configuration
        @ComponentScan("com.example")
        @EnableAutoConfiguration(exclude=["TestAutoConfiguration"])
        @Import(DatabaseConfig)
        class ComplexAppConfig:
            @Value("${app.name}")
            def set_app_name(self, name: str):
                self.app_name = name

        # 验证类级别注解
        self.assertTrue(ComplexAppConfig.__is_component_scan__)
        self.assertTrue(ComplexAppConfig.__is_enable_auto_configuration__)
        self.assertTrue(ComplexAppConfig.__is_import__)

        # 验证方法级别注解
        method = ComplexAppConfig.set_app_name
        self.assertTrue(method.__is_value_injection__)
        self.assertEqual(method.__value_expression__, "${app.name}")

    def test_utility_functions(self):
        """测试工具函数"""

        @ConfigurationProperties(prefix="test")
        @ComponentScan("com.test")
        @EnableAutoConfiguration
        @Import(DatabaseConfig)
        class TestConfig:
            @Value("${test.value}")
            def set_value(self, value: str):
                self.value = value

        # 测试类级别检查函数
        self.assertTrue(is_configuration_properties(TestConfig))
        self.assertTrue(is_component_scan(TestConfig))
        self.assertTrue(is_enable_auto_configuration(TestConfig))
        self.assertTrue(is_import(TestConfig))

        # 测试方法级别检查函数
        method = TestConfig.set_value
        self.assertTrue(is_value_injection(method))

        # 测试获取元数据函数
        config_metadata = get_configuration_properties_metadata(TestConfig)
        self.assertIsNotNone(config_metadata)
        self.assertEqual(config_metadata.prefix, "test")

        scan_metadata = get_component_scan_metadata(TestConfig)
        self.assertIsNotNone(scan_metadata)
        self.assertEqual(scan_metadata.base_packages, ["com.test"])

        value_metadata = get_value_metadata(method)
        self.assertIsNotNone(value_metadata)
        self.assertEqual(value_metadata.value, "${test.value}")

        # 测试获取属性函数
        self.assertEqual(get_config_prefix(TestConfig), "test")
        self.assertEqual(get_scan_packages(TestConfig), ["com.test"])
        self.assertEqual(get_import_classes(TestConfig), [DatabaseConfig])
        self.assertEqual(get_value_expression(method), "${test.value}")

    def test_utility_functions_with_no_annotations(self):
        """测试工具函数在没有注解的情况下"""

        class PlainConfig:
            def plain_method(self):
                pass

        # 测试类级别检查函数
        self.assertFalse(is_configuration_properties(PlainConfig))
        self.assertFalse(is_component_scan(PlainConfig))
        self.assertFalse(is_enable_auto_configuration(PlainConfig))
        self.assertFalse(is_import(PlainConfig))

        # 测试方法级别检查函数
        method = PlainConfig.plain_method
        self.assertFalse(is_value_injection(method))

        # 测试获取元数据函数
        self.assertIsNone(get_configuration_properties_metadata(PlainConfig))
        self.assertIsNone(get_component_scan_metadata(PlainConfig))
        self.assertIsNone(get_value_metadata(method))

        # 测试获取属性函数
        self.assertIsNone(get_config_prefix(PlainConfig))
        self.assertEqual(get_scan_packages(PlainConfig), [])
        self.assertEqual(get_import_classes(PlainConfig), [])
        self.assertIsNone(get_value_expression(method))


if __name__ == "__main__":
    unittest.main()

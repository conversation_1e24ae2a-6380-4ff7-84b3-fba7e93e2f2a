#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean处理器模块错误处理和异常恢复测试
"""

import unittest
from unittest.mock import Mock, patch

from miniboot.errors import BeanProcessingError
from miniboot.processor.autowired import AutowiredAnnotationProcessor
from miniboot.processor.base import BeanPostProcessor
from miniboot.processor.configuration import ConfigurationPropertiesProcessor
from miniboot.processor.event import EventListenerProcessor
from miniboot.processor.lifecycle import LifecycleAnnotationProcessor
from miniboot.processor.manager import (BeanPostProcessorManager,
                                        ProcessorConfig)
from miniboot.processor.registry import BeanPostProcessorRegistry
from miniboot.processor.schedule import ScheduledAnnotationProcessor
from miniboot.processor.value import ValueAnnotationProcessor


class ErrorBean:
    """错误测试Bean"""

    def __init__(self):
        self.value = None
        self.repository = None


class TestProcessorErrorHandling(unittest.TestCase):
    """处理器错误处理测试类"""

    def setUp(self):
        """测试前置设置"""
        self.bean = ErrorBean()
        self.bean_name = "errorBean"

    def test_autowired_processor_bean_factory_error(self):
        """测试自动装配处理器Bean工厂错误"""
        processor = AutowiredAnnotationProcessor()

        # 模拟Bean工厂抛出异常
        mock_bean_factory = Mock()
        mock_bean_factory.get_bean.side_effect = RuntimeError("Bean not found")
        processor.set_bean_factory(mock_bean_factory)

        # 创建有@Autowired注解的Bean
        test_bean = Mock()
        test_bean.__class__.__name__ = "TestBean"

        # 模拟@Autowired字段和注解检查
        with patch.object(processor, "_get_autowired_fields") as mock_get_fields, patch.object(processor, "_has_annotations") as mock_has_annotations:
            mock_get_fields.return_value = [("repository", str, None)]
            mock_has_annotations.return_value = True  # 模拟有@Autowired注解

            # 应该抛出BeanProcessingError
            with self.assertRaises(BeanProcessingError) as context:
                processor.post_process_before_initialization(test_bean, self.bean_name)

            # 验证错误信息
            self.assertIn("Failed to process @Autowired annotations", str(context.exception))
            self.assertEqual(context.exception.get_bean_name(), self.bean_name)
            self.assertEqual(context.exception.get_processor_name(), "AutowiredAnnotationProcessor")

    def test_value_processor_environment_error(self):
        """测试值注入处理器环境错误"""
        processor = ValueAnnotationProcessor()

        # 模拟环境抛出异常
        mock_environment = Mock()
        mock_environment.get_property.side_effect = RuntimeError("Property not found")
        processor.set_environment(mock_environment)

        # 创建有@Value注解的Bean
        test_bean = Mock()
        test_bean.__class__.__name__ = "TestBean"

        # 模拟@Value字段
        with patch.object(processor, "_get_value_fields") as mock_get_fields:
            mock_get_fields.return_value = [("config_value", str, "${test.property}", None)]

            # 应该抛出BeanProcessingError
            with self.assertRaises(BeanProcessingError) as context:
                processor.post_process_before_initialization(test_bean, self.bean_name)

            # 验证错误信息
            self.assertIn("Failed to process @Value annotations", str(context.exception))

    def test_config_processor_binding_error(self):
        """测试配置属性处理器绑定错误"""
        processor = ConfigurationPropertiesProcessor()

        # 没有环境的情况下，处理器应该不支持任何Bean
        test_bean = Mock()
        test_bean.__class__.__name__ = "TestBean"

        # 验证处理器不支持此Bean（因为没有环境）
        self.assertFalse(processor.supports(test_bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_before_initialization(test_bean, self.bean_name)
        self.assertIs(result, test_bean)

    def test_lifecycle_processor_method_execution_error(self):
        """测试生命周期处理器方法执行错误"""
        processor = LifecycleAnnotationProcessor()

        # 创建没有生命周期注解的Bean
        test_bean = Mock()
        test_bean.__class__.__name__ = "TestBean"

        # 验证处理器不支持此Bean（因为没有生命周期注解）
        self.assertFalse(processor.supports(test_bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_after_initialization(test_bean, self.bean_name)
        self.assertIs(result, test_bean)

    def test_event_processor_registration_error(self):
        """测试事件监听处理器注册错误"""
        processor = EventListenerProcessor()

        # 没有事件发布器的情况下，处理器应该不支持任何Bean
        test_bean = Mock()
        test_bean.__class__.__name__ = "TestBean"

        # 验证处理器不支持此Bean（因为没有事件发布器）
        self.assertFalse(processor.supports(test_bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_after_initialization(test_bean, self.bean_name)
        self.assertIs(result, test_bean)

    def test_scheduled_processor_task_creation_error(self):
        """测试定时任务处理器任务创建错误"""
        processor = ScheduledAnnotationProcessor()

        # 没有任务调度器的情况下，处理器应该不支持任何Bean
        test_bean = Mock()
        test_bean.__class__.__name__ = "TestBean"

        # 验证处理器不支持此Bean（因为没有任务调度器）
        self.assertFalse(processor.supports(test_bean, self.bean_name))

        # 处理应该直接返回原Bean
        result = processor.post_process_after_initialization(test_bean, self.bean_name)
        self.assertIs(result, test_bean)

    def test_processor_registry_exception_propagation(self):
        """测试处理器注册表异常传播"""
        registry = BeanPostProcessorRegistry()

        # 创建会抛出异常的处理器
        failing_processor = Mock(spec=BeanPostProcessor)
        failing_processor.__class__.__name__ = "FailingProcessor"
        failing_processor.get_order.return_value = 100
        failing_processor.supports.return_value = True
        failing_processor.post_process_before_initialization.side_effect = RuntimeError("Processor failed")

        registry.register_processor(failing_processor)

        # 异常应该被包装为BeanProcessingError
        with self.assertRaises(BeanProcessingError) as context:
            registry.apply_before_initialization(self.bean, self.bean_name)

        # 验证原始异常被包装
        self.assertIn("初始化前处理失败", str(context.exception))
        self.assertIsInstance(context.exception.__cause__, RuntimeError)

    def test_processor_manager_error_metrics_collection(self):
        """测试处理器管理器错误指标收集"""
        manager = BeanPostProcessorManager()

        # 创建会抛出异常的处理器
        failing_processor = Mock(spec=BeanPostProcessor)
        failing_processor.__class__.__name__ = "FailingProcessor"
        failing_processor.supports.return_value = True
        failing_processor.post_process_before_initialization.side_effect = RuntimeError("Test error")

        manager.register_processor(failing_processor)

        # 执行处理，应该抛出异常
        with self.assertRaises(BeanProcessingError):
            manager.apply_before_initialization(self.bean, self.bean_name)

        # 验证错误指标被收集
        metrics = manager.get_processor_metrics("FailingProcessor")
        self.assertIsNotNone(metrics)
        self.assertEqual(metrics.error_count, 1)
        self.assertEqual(metrics.total_executions, 1)
        self.assertIsNotNone(metrics.last_error_time)
        self.assertIsNotNone(metrics.last_error_message)

    def test_processor_manager_circuit_breaker_recovery(self):
        """测试处理器管理器熔断器恢复"""
        manager = BeanPostProcessorManager()

        # 配置熔断器
        config = ProcessorConfig(circuit_breaker_enabled=True, error_threshold=2, circuit_breaker_timeout=0.1)

        # 创建可控制失败的处理器
        controllable_processor = Mock(spec=BeanPostProcessor)
        controllable_processor.__class__.__name__ = "ControllableProcessor"
        controllable_processor.supports.return_value = True

        # 初始设置为失败
        controllable_processor.post_process_before_initialization.side_effect = RuntimeError("Controlled error")

        manager.register_processor(controllable_processor, config)

        # 触发足够的错误来打开熔断器
        import contextlib

        for _ in range(3):
            with contextlib.suppress(Exception):
                manager.apply_before_initialization(self.bean, self.bean_name)

        # 验证熔断器已打开
        from miniboot.processor.manager import ProcessorState

        state = manager.get_processor_state("ControllableProcessor")
        self.assertEqual(state, ProcessorState.CIRCUIT_OPEN)

        # 等待熔断器超时
        import time

        time.sleep(0.2)

        # 修复处理器（不再抛出异常）
        controllable_processor.post_process_before_initialization.side_effect = None
        controllable_processor.post_process_before_initialization.return_value = self.bean

        # 熔断器应该在下次调用时重置
        result = manager.apply_before_initialization(self.bean, self.bean_name)
        self.assertIs(result, self.bean)

        # 验证熔断器已重置
        state = manager.get_processor_state("ControllableProcessor")
        self.assertEqual(state, ProcessorState.ACTIVE)

    def test_processor_registry_partial_failure_recovery(self):
        """测试处理器注册表部分失败恢复"""
        registry = BeanPostProcessorRegistry()

        # 注册多个处理器，其中一个会失败
        success_processor1 = Mock(spec=BeanPostProcessor)
        success_processor1.__class__.__name__ = "SuccessProcessor1"
        success_processor1.get_order.return_value = 100
        success_processor1.supports.return_value = True
        success_processor1.post_process_before_initialization.return_value = self.bean

        failing_processor = Mock(spec=BeanPostProcessor)
        failing_processor.__class__.__name__ = "FailingProcessor"
        failing_processor.get_order.return_value = 200
        failing_processor.supports.return_value = True
        failing_processor.post_process_before_initialization.side_effect = RuntimeError("Middle failure")

        success_processor2 = Mock(spec=BeanPostProcessor)
        success_processor2.__class__.__name__ = "SuccessProcessor2"
        success_processor2.get_order.return_value = 300
        success_processor2.supports.return_value = True
        success_processor2.post_process_before_initialization.return_value = self.bean

        registry.register_processor(success_processor1)
        registry.register_processor(failing_processor)
        registry.register_processor(success_processor2)

        # 执行处理，应该在中间失败并被包装为BeanProcessingError
        with self.assertRaises(BeanProcessingError):
            registry.apply_before_initialization(self.bean, self.bean_name)

        # 验证第一个处理器被调用
        success_processor1.post_process_before_initialization.assert_called_once()

        # 验证失败的处理器被调用
        failing_processor.post_process_before_initialization.assert_called_once()

        # 验证第三个处理器没有被调用（因为中间失败了）
        success_processor2.post_process_before_initialization.assert_not_called()

    def test_bean_processing_error_chaining(self):
        """测试Bean处理错误链式传播"""
        # 创建原始异常
        original_error = ValueError("Original error")

        # 创建Bean处理错误
        processing_error = BeanProcessingError("Processing failed", bean_name="testBean", processor_name="TestProcessor", cause=original_error)

        # 验证异常链
        self.assertEqual(processing_error.get_bean_name(), "testBean")
        self.assertEqual(processing_error.get_processor_name(), "TestProcessor")
        self.assertIs(processing_error.__cause__, original_error)

        # 验证字符串表示包含所有信息
        error_str = str(processing_error)
        self.assertIn("Processing failed", error_str)
        self.assertIn("testBean", error_str)
        self.assertIn("TestProcessor", error_str)


class TestProcessorRecovery(unittest.TestCase):
    """处理器恢复测试类"""

    def test_processor_state_recovery_after_error(self):
        """测试处理器错误后状态恢复"""
        manager = BeanPostProcessorManager()

        # 创建可控制的处理器
        processor = Mock(spec=BeanPostProcessor)
        processor.__class__.__name__ = "RecoveryProcessor"
        processor.supports.return_value = True

        # 初始设置为成功
        processor.post_process_before_initialization.return_value = ErrorBean()

        manager.register_processor(processor)

        # 验证初始状态
        from miniboot.processor.manager import ProcessorState

        state = manager.get_processor_state("RecoveryProcessor")
        self.assertEqual(state, ProcessorState.ACTIVE)

        # 成功执行
        bean = ErrorBean()
        result = manager.apply_before_initialization(bean, "recoveryBean")
        self.assertIsNotNone(result)

        # 设置为失败
        processor.post_process_before_initialization.side_effect = RuntimeError("Temporary error")

        # 执行失败
        with self.assertRaises(BeanProcessingError):
            manager.apply_before_initialization(bean, "recoveryBean")

        # 验证错误指标
        metrics = manager.get_processor_metrics("RecoveryProcessor")
        self.assertEqual(metrics.error_count, 1)

        # 恢复处理器
        processor.post_process_before_initialization.side_effect = None
        processor.post_process_before_initialization.return_value = bean

        # 再次成功执行
        result = manager.apply_before_initialization(bean, "recoveryBean")
        self.assertIs(result, bean)

        # 验证指标更新
        metrics = manager.get_processor_metrics("RecoveryProcessor")
        self.assertEqual(metrics.total_executions, 3)
        self.assertEqual(metrics.error_count, 1)


if __name__ == "__main__":
    unittest.main()

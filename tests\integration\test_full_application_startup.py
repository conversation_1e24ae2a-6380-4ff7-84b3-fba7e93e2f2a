#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: 完整应用启动集成测试
"""

import asyncio
import time
import unittest
from typing import Any, Dict, List
from unittest.mock import Mock, patch

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import Default<PERSON>eanFactory
from miniboot.context.application import DefaultApplicationContext


class DatabaseService:
    """数据库服务"""

    def __init__(self, connection_string: str = "sqlite:///:memory:"):
        self.connection_string = connection_string
        self.connected = False
        self.tables_created = False

    def connect(self):
        """连接数据库"""
        print(f"Connecting to database: {self.connection_string}")
        self.connected = True

    def create_tables(self):
        """创建表"""
        if not self.connected:
            raise RuntimeError("Database not connected")
        print("Creating database tables...")
        self.tables_created = True

    def is_ready(self) -> bool:
        """检查是否就绪"""
        return self.connected and self.tables_created


class CacheService:
    """缓存服务"""

    def __init__(self, redis_url: str = "redis://localhost:6379"):
        self.redis_url = redis_url
        self.connected = False
        self.cache_data = {}

    def connect(self):
        """连接缓存"""
        print(f"Connecting to cache: {self.redis_url}")
        self.connected = True

    def set(self, key: str, value: Any):
        """设置缓存"""
        if not self.connected:
            raise RuntimeError("Cache not connected")
        self.cache_data[key] = value

    def get(self, key: str) -> Any:
        """获取缓存"""
        if not self.connected:
            raise RuntimeError("Cache not connected")
        return self.cache_data.get(key)

    def is_ready(self) -> bool:
        """检查是否就绪"""
        return self.connected


class MessageQueueService:
    """消息队列服务"""

    def __init__(self, broker_url: str = "amqp://localhost:5672"):
        self.broker_url = broker_url
        self.connected = False
        self.queues = {}

    def connect(self):
        """连接消息队列"""
        print(f"Connecting to message queue: {self.broker_url}")
        self.connected = True

    def create_queue(self, queue_name: str):
        """创建队列"""
        if not self.connected:
            raise RuntimeError("Message queue not connected")
        self.queues[queue_name] = []

    def send_message(self, queue_name: str, message: Any):
        """发送消息"""
        if queue_name not in self.queues:
            self.create_queue(queue_name)
        self.queues[queue_name].append(message)

    def is_ready(self) -> bool:
        """检查是否就绪"""
        return self.connected


class UserService:
    """用户服务"""

    def __init__(self):
        self.database = None
        self.cache = None
        self.initialized = False

    def set_database(self, database: DatabaseService):
        self.database = database

    def set_cache(self, cache: CacheService):
        self.cache = cache

    def init(self):
        """初始化服务"""
        if not self.database or not self.cache:
            raise RuntimeError("Dependencies not injected")
        if not self.database.is_ready() or not self.cache.is_ready():
            raise RuntimeError("Dependencies not ready")

        print("Initializing user service...")
        self.initialized = True

    def create_user(self, username: str, email: str) -> Dict[str, Any]:
        """创建用户"""
        if not self.initialized:
            raise RuntimeError("Service not initialized")

        user = {
            "id": len(self.get_all_users()) + 1,
            "username": username,
            "email": email,
            "created_at": time.time()
        }

        # 缓存用户信息
        self.cache.set(f"user:{user['id']}", user)

        return user

    def get_all_users(self) -> List[Dict[str, Any]]:
        """获取所有用户"""
        # 模拟从数据库获取
        return []


class NotificationService:
    """通知服务"""

    def __init__(self):
        self.message_queue = None
        self.user_service = None
        self.initialized = False

    def set_message_queue(self, mq: MessageQueueService):
        self.message_queue = mq

    def set_user_service(self, user_service: UserService):
        self.user_service = user_service

    def init(self):
        """初始化服务"""
        if not self.message_queue or not self.user_service:
            raise RuntimeError("Dependencies not injected")
        if not self.message_queue.is_ready() or not self.user_service.initialized:
            raise RuntimeError("Dependencies not ready")

        print("Initializing notification service...")
        self.message_queue.create_queue("notifications")
        self.initialized = True

    def send_notification(self, user_id: int, message: str):
        """发送通知"""
        if not self.initialized:
            raise RuntimeError("Service not initialized")

        notification = {
            "user_id": user_id,
            "message": message,
            "timestamp": time.time()
        }

        self.message_queue.send_message("notifications", notification)


class WebController:
    """Web控制器"""

    def __init__(self):
        self.user_service = None
        self.notification_service = None
        self.initialized = False

    def set_user_service(self, user_service: UserService):
        self.user_service = user_service

    def set_notification_service(self, notification_service: NotificationService):
        self.notification_service = notification_service

    def init(self):
        """初始化控制器"""
        if not self.user_service or not self.notification_service:
            raise RuntimeError("Dependencies not injected")
        if not self.user_service.initialized or not self.notification_service.initialized:
            raise RuntimeError("Dependencies not ready")

        print("Initializing web controller...")
        self.initialized = True

    def handle_user_registration(self, username: str, email: str) -> Dict[str, Any]:
        """处理用户注册"""
        if not self.initialized:
            raise RuntimeError("Controller not initialized")

        # 创建用户
        user = self.user_service.create_user(username, email)

        # 发送欢迎通知
        self.notification_service.send_notification(
            user["id"],
            f"Welcome {username}! Your account has been created."
        )

        return {
            "status": "success",
            "user": user,
            "message": "User registered successfully"
        }


class TestFullApplicationStartup(unittest.TestCase):
    """完整应用启动集成测试"""

    def setUp(self):
        """测试前置设置"""
        self.context = DefaultApplicationContext()
        self.startup_time = None

    def tearDown(self):
        """测试后置清理"""
        if hasattr(self.context, 'close'):
            self.context.close()

    def test_complete_application_startup(self):
        """测试完整应用启动"""
        start_time = time.time()

        # 1. 注册基础设施Bean
        self._register_infrastructure_beans()

        # 2. 注册业务服务Bean
        self._register_business_service_beans()

        # 3. 注册控制器Bean
        self._register_controller_beans()

        # 4. 启动应用上下文
        print("Starting application context...")
        self.context.refresh()

        # 5. 验证所有Bean都已正确创建和初始化
        self._verify_beans_initialization()

        # 6. 测试完整业务流程
        self._test_business_workflow()

        # 7. 记录启动时间
        self.startup_time = time.time() - start_time
        print(f"Application startup completed in {self.startup_time:.3f} seconds")

        # 验证启动时间合理（应该在5秒内）
        self.assertLess(self.startup_time, 5.0)

    def _register_infrastructure_beans(self):
        """注册基础设施Bean"""
        # 数据库服务
        db_def = BeanDefinition(
            bean_name="databaseService",
            bean_class=DatabaseService,
            scope=BeanScope.SINGLETON,
            init_method_name="connect"
        )
        db_def.add_constructor_arg(0, value="postgresql://localhost:5432/testdb")
        self.context.register_bean_definition("databaseService", db_def)

        # 缓存服务
        cache_def = BeanDefinition(
            bean_name="cacheService",
            bean_class=CacheService,
            scope=BeanScope.SINGLETON,
            init_method_name="connect"
        )
        cache_def.add_constructor_arg(0, value="redis://localhost:6379/0")
        self.context.register_bean_definition("cacheService", cache_def)

        # 消息队列服务
        mq_def = BeanDefinition(
            bean_name="messageQueueService",
            bean_class=MessageQueueService,
            scope=BeanScope.SINGLETON,
            init_method_name="connect"
        )
        mq_def.add_constructor_arg(0, value="amqp://localhost:5672/")
        self.context.register_bean_definition("messageQueueService", mq_def)

    def _register_business_service_beans(self):
        """注册业务服务Bean"""
        # 用户服务
        user_service_def = BeanDefinition(
            bean_name="userService",
            bean_class=UserService,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        user_service_def.add_property_value("database", ref="databaseService")
        user_service_def.add_property_value("cache", ref="cacheService")
        self.context.register_bean_definition("userService", user_service_def)

        # 通知服务
        notification_service_def = BeanDefinition(
            bean_name="notificationService",
            bean_class=NotificationService,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        notification_service_def.add_property_value("message_queue", ref="messageQueueService")
        notification_service_def.add_property_value("user_service", ref="userService")
        self.context.register_bean_definition("notificationService", notification_service_def)

    def _register_controller_beans(self):
        """注册控制器Bean"""
        # Web控制器
        controller_def = BeanDefinition(
            bean_name="webController",
            bean_class=WebController,
            scope=BeanScope.SINGLETON,
            init_method_name="init"
        )
        controller_def.add_property_value("user_service", ref="userService")
        controller_def.add_property_value("notification_service", ref="notificationService")
        self.context.register_bean_definition("webController", controller_def)

    def _verify_beans_initialization(self):
        """验证Bean初始化"""
        # 验证基础设施Bean
        db_service = self.context.get_bean("databaseService")
        self.assertTrue(db_service.connected)

        cache_service = self.context.get_bean("cacheService")
        self.assertTrue(cache_service.connected)

        mq_service = self.context.get_bean("messageQueueService")
        self.assertTrue(mq_service.connected)

        # 验证业务服务Bean
        user_service = self.context.get_bean("userService")
        self.assertTrue(user_service.initialized)

        notification_service = self.context.get_bean("notificationService")
        self.assertTrue(notification_service.initialized)

        # 验证控制器Bean
        web_controller = self.context.get_bean("webController")
        self.assertTrue(web_controller.initialized)

        print("All beans initialized successfully!")

    def _test_business_workflow(self):
        """测试业务工作流"""
        # 获取控制器
        controller = self.context.get_bean("webController")

        # 测试用户注册流程
        result = controller.handle_user_registration("testuser", "<EMAIL>")

        # 验证结果
        self.assertEqual(result["status"], "success")
        self.assertIn("user", result)
        self.assertEqual(result["user"]["username"], "testuser")
        self.assertEqual(result["user"]["email"], "<EMAIL>")

        # 验证缓存中有用户数据
        cache_service = self.context.get_bean("cacheService")
        cached_user = cache_service.get(f"user:{result['user']['id']}")
        self.assertIsNotNone(cached_user)
        self.assertEqual(cached_user["username"], "testuser")

        # 验证通知队列中有消息
        mq_service = self.context.get_bean("messageQueueService")
        notifications = mq_service.queues.get("notifications", [])
        self.assertEqual(len(notifications), 1)
        self.assertEqual(notifications[0]["user_id"], result["user"]["id"])

        print("Business workflow test completed successfully!")

    def test_application_shutdown(self):
        """测试应用关闭"""
        # 先启动应用
        self._register_infrastructure_beans()
        self._register_business_service_beans()
        self._register_controller_beans()

        self.context.refresh()

        # 验证应用正在运行
        self.assertTrue(self.context.is_active())

        # 关闭应用
        print("Shutting down application...")
        shutdown_start = time.time()
        self.context.close()
        shutdown_time = time.time() - shutdown_start

        # 验证应用已关闭
        self.assertFalse(self.context.is_active())

        # 验证关闭时间合理（应该在2秒内）
        self.assertLess(shutdown_time, 2.0)
        print(f"Application shutdown completed in {shutdown_time:.3f} seconds")

    def test_application_restart(self):
        """测试应用重启"""
        # 第一次启动
        self._register_infrastructure_beans()
        self.context.refresh()

        first_db_service = self.context.get_bean("databaseService")
        self.assertTrue(first_db_service.connected)

        # 关闭应用
        self.context.close()
        self.assertFalse(self.context.is_active())

        # 重新创建上下文并启动
        self.context = DefaultApplicationContext()
        self._register_infrastructure_beans()
        self.context.refresh()

        # 验证重启后的服务
        second_db_service = self.context.get_bean("databaseService")
        self.assertTrue(second_db_service.connected)
        self.assertIsNot(first_db_service, second_db_service)  # 应该是新实例

        print("Application restart test completed successfully!")

    def test_bean_dependency_resolution_order(self):
        """测试Bean依赖解析顺序"""
        creation_order = []

        # 创建记录创建顺序的Bean类
        class OrderTrackingService:
            def __init__(self, name: str):
                self.name = name
                creation_order.append(name)

        # 注册有依赖关系的Bean
        # A依赖B，B依赖C
        c_def = BeanDefinition("serviceC", OrderTrackingService, BeanScope.SINGLETON)
        c_def.add_constructor_arg(0, value="C")
        self.context.register_bean_definition("serviceC", c_def)

        b_def = BeanDefinition("serviceB", OrderTrackingService, BeanScope.SINGLETON)
        b_def.add_constructor_arg(0, value="B")
        b_def.add_property_value("dependency", ref="serviceC")
        self.context.register_bean_definition("serviceB", b_def)

        a_def = BeanDefinition("serviceA", OrderTrackingService, BeanScope.SINGLETON)
        a_def.add_constructor_arg(0, value="A")
        a_def.add_property_value("dependency", ref="serviceB")
        self.context.register_bean_definition("serviceA", a_def)

        # 启动上下文
        self.context.refresh()

        # 验证创建顺序：C -> B -> A
        self.assertEqual(creation_order, ["C", "B", "A"])
        print(f"Bean creation order: {' -> '.join(creation_order)}")


if __name__ == '__main__':
    unittest.main()

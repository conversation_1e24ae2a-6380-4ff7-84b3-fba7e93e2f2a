#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
简化的应用上下文演示

展示更新后的默认应用上下文的核心功能，专注于：
- 重构后的模块初始化器集成
- 基本的生命周期管理
- 状态监控
"""

import asyncio
import time
from pathlib import Path

from miniboot.context.application import DefaultApplicationContext


async def demo_basic_functionality():
    """演示基本功能"""
    print("🧪 Testing Basic Application Context Functionality...")
    
    # 创建应用上下文
    context = DefaultApplicationContext(
        config_path=None,  # 不使用配置文件
        packages_to_scan=[],  # 不扫描包
        auto_detect=True
    )
    
    try:
        # 启动应用上下文
        print("🚀 Starting application context...")
        start_time = time.time()
        await context.start()
        startup_time = time.time() - start_time
        
        print(f"✅ Application context started in {startup_time:.3f}s")
        
        # 检查应用状态
        print(f"📊 Application state: {context.get_state().value}")
        print(f"🏃 Is running: {context.is_running()}")
        
        # 获取详细状态信息
        status = context.get_application_status()
        print(f"\n📋 Application Status Summary:")
        print(f"  - State: {status['application']['state']}")
        print(f"  - Uptime: {status['application']['uptime']:.3f}s")
        print(f"  - Module initializer available: {status['components']['module_initializer_available']}")
        
        # 等待一段时间
        await asyncio.sleep(1)
        
        return context, True
        
    except Exception as e:
        print(f"❌ Failed to start application context: {e}")
        import traceback
        traceback.print_exc()
        return context, False


async def demo_graceful_shutdown(context):
    """演示优雅关闭"""
    print("\n🧪 Testing Graceful Shutdown...")
    
    try:
        print("🛑 Stopping application context...")
        stop_start_time = time.time()
        
        await context.stop()
        
        stop_time = time.time() - stop_start_time
        print(f"✅ Application context stopped in {stop_time:.3f}s")
        
        # 检查最终状态
        print(f"📊 Final state: {context.get_state().value}")
        print(f"🏃 Is running: {context.is_running()}")
        
        return True
        
    except Exception as e:
        print(f"❌ Graceful shutdown test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def demo_error_handling():
    """演示错误处理"""
    print("\n🧪 Testing Error Handling...")
    
    try:
        # 创建一个配置错误的上下文
        context = DefaultApplicationContext(
            config_path="non_existent_config.properties",
            packages_to_scan=["non.existent.package"],
            auto_detect=True
        )
        
        try:
            await context.start()
            print("⚠️ Expected startup to fail but it succeeded")
            await context.stop()
            return False
        except Exception as e:
            print(f"✅ Correctly handled startup error: {type(e).__name__}")
            return True
            
    except Exception as e:
        print(f"❌ Error handling test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Simple Application Context Demo")
    print("=" * 50)
    
    context = None
    tests = [
        ("Basic Functionality", demo_basic_functionality),
        ("Error Handling", demo_error_handling),
    ]
    
    # 运行基础测试
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🧪 Running {test_name} Test...")
            if test_name == "Basic Functionality":
                context, success = await test_func()
                results.append((test_name, success))
            else:
                success = await test_func()
                results.append((test_name, success))
            
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{status} {test_name} Test")
        except Exception as e:
            results.append((test_name, False))
            print(f"❌ FAILED {test_name} Test: {e}")
    
    # 如果基础功能测试成功，运行关闭测试
    if context and context.is_running():
        try:
            print(f"\n🧪 Running Graceful Shutdown Test...")
            success = await demo_graceful_shutdown(context)
            results.append(("Graceful Shutdown", success))
            
            status = "✅ PASSED" if success else "❌ FAILED"
            print(f"{status} Graceful Shutdown Test")
        except Exception as e:
            results.append(("Graceful Shutdown", False))
            print(f"❌ FAILED Graceful Shutdown Test: {e}")
    
    # 总结
    print(f"\n📋 Test Summary:")
    passed = sum(1 for _, success in results if success)
    total = len(results)
    
    for test_name, success in results:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"  - {test_name}: {status}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! Updated application context is working correctly!")
    else:
        print("⚠️ Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

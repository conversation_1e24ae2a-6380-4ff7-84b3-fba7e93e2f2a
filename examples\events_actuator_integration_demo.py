#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
Events Actuator 集成自动配置演示

展示如何使用 EventsActuatorAutoConfiguration 自动配置 Actuator 与事件系统的集成，
包括事件监控、事件发布管理、事件监听器管理等功能。

功能特性:
- 自动检测事件系统环境
- 自动创建事件相关端点
- 事件发布监控集成
- 事件监听器状态监控
- 事件统计指标收集
- 事件系统配置管理
"""

import asyncio
import time

# DEPRECATED: from miniboot.actuator.autoconfigure.events_actuator_auto_configuration import (
#     EventsActuatorAutoConfiguration,
#     EventsIntegrationStatus,
#     EventPublisherEndpoint,
#     EventListenersEndpoint,
#     EventTypesEndpoint
# )


class MockApplicationContext:
    """模拟应用上下文"""

    def __init__(self):
        self._beans = {}
        self._environment = MockEnvironment()
        self._event_publisher = None

    def get_bean(self, name):
        return self._beans.get(name)

    def get_bean_factory(self):
        return self

    def register_singleton(self, name, instance):
        self._beans[name] = instance
        print(f"📦 Registered bean: {name}")


class MockEnvironment:
    """模拟环境配置"""

    def get_property(self, key, default=None):
        # 演示配置
        config = {
            "actuator.enabled": True,
            "actuator.base-path": "/actuator",
            "actuator.events.enabled": True,
            "actuator.security.enabled": False,
        }
        return config.get(key, default)


async def create_demo_event_publisher():
    """创建演示事件发布器"""
    try:
        from miniboot.events.base import ApplicationEvent, Event
        from miniboot.events.publisher import EventPublisher

        # 创建事件发布器
        publisher = EventPublisher()

        # 创建一些演示事件类型
        class UserCreatedEvent(Event):
            def __init__(self, user_id, username, **kwargs):
                super().__init__(**kwargs)
                self.user_id = user_id
                self.username = username

            def get_event_type(self) -> str:
                return "UserCreatedEvent"

        class OrderProcessedEvent(ApplicationEvent):
            def __init__(self, order_id, amount, **kwargs):
                super().__init__(**kwargs)
                self.order_id = order_id
                self.amount = amount

            def get_event_type(self) -> str:
                return "OrderProcessedEvent"

        class SystemStartedEvent(ApplicationEvent):
            def get_event_type(self) -> str:
                return "SystemStartedEvent"

        # 创建一些演示事件监听器
        def handle_user_created(event):
            print(f"🔔 User created: {event.username} (ID: {event.user_id})")

        def handle_order_processed(event):
            print(f"💰 Order processed: {event.order_id} (Amount: ${event.amount})")

        async def handle_system_started_async(event):
            print(f"🚀 System started at {event.timestamp}")
            await asyncio.sleep(0.1)  # 模拟异步操作

        # 注册事件监听器
        publisher.subscribe(UserCreatedEvent, handle_user_created, order=1)
        publisher.subscribe(OrderProcessedEvent, handle_order_processed, order=2)
        publisher.subscribe(SystemStartedEvent, handle_system_started_async, async_exec=True, order=0)

        # 发布一些演示事件
        user_event = UserCreatedEvent(user_id=123, username="john_doe", source="user_service")
        order_event = OrderProcessedEvent(order_id="ORD-001", amount=99.99, source="order_service")
        system_event = SystemStartedEvent(source="system")

        publisher.publish(user_event)
        publisher.publish(order_event)
        await publisher.publish_async(system_event)

        print("✅ Demo event publisher created with 3 event types and listeners")
        return publisher

    except ImportError as e:
        print(f"⚠️  Events module not available: {e}")
        return None
    except Exception as e:
        print(f"❌ Failed to create demo event publisher: {e}")
        return None


async def test_events_actuator_auto_configuration():
    """测试 Events Actuator 自动配置"""
    print("🧪 Testing Events Actuator Auto Configuration...")

    try:
        # 1. 创建模拟应用上下文
        app_context = MockApplicationContext()

        # 2. 创建演示事件发布器
        publisher = await create_demo_event_publisher()
        if publisher:
            app_context._event_publisher = publisher

        # 3. 创建 Events 自动配置实例
        events_config = EventsActuatorAutoConfiguration(app_context)
        print(f"📊 Initial status: {events_config.status.value}")

        # 4. 执行自动配置
        print("🔧 Executing Events Actuator auto configuration...")
        await events_config.configure()

        # 5. 检查配置结果
        print(f"📊 Final status: {events_config.status.value}")

        # 6. 获取集成状态
        status_info = events_config.get_integration_status()
        print("\n📈 Integration Status:")
        for key, value in status_info.items():
            if key != "metrics":
                print(f"  - {key}: {value}")

        print("📊 Metrics:")
        for key, value in status_info["metrics"].items():
            print(f"  - {key}: {value}")

        # 7. 测试端点功能
        await test_events_endpoints(events_config)

        # 8. 刷新指标
        print("\n🔄 Refreshing metrics...")
        await events_config.refresh_metrics()
        updated_status = events_config.get_integration_status()
        print(f"📊 Updated metrics:")
        for key, value in updated_status["metrics"].items():
            print(f"  - {key}: {value}")

        print("\n✅ Events Actuator Auto Configuration test completed successfully!")
        return True

    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False


async def test_events_endpoints(events_config):
    """测试事件端点功能"""
    print("\n🔌 Testing Events Endpoints...")

    try:
        event_publisher = events_config.get_event_publisher()
        if not event_publisher:
            print("⚠️  No event publisher available for endpoint testing")
            return

        # 测试事件发布器端点
        publisher_endpoint = EventPublisherEndpoint(event_publisher)

        print("📡 Testing EventPublisher endpoint:")
        publisher_info = publisher_endpoint.invoke(publisher_endpoint.operations()[0].operation_type)
        print(f"  - Available: {publisher_info.get('available', False)}")
        print(f"  - Type: {publisher_info.get('type', 'unknown')}")
        stats = publisher_info.get('statistics', {})
        print(f"  - Published events: {stats.get('published_events', 0)}")
        print(f"  - Handled events: {stats.get('handled_events', 0)}")

        # 测试发布测试事件
        print("\n📤 Testing test event publishing:")
        publish_result = publisher_endpoint._publish_test_event()
        print(f"  - Publish success: {publish_result.get('success', False)}")
        print(f"  - Event ID: {publish_result.get('event_id', 'unknown')}")

        # 测试事件监听器端点
        listeners_endpoint = EventListenersEndpoint(event_publisher)

        print("\n👂 Testing EventListeners endpoint:")
        listeners_info = listeners_endpoint.invoke(listeners_endpoint.operations()[0].operation_type)
        summary = listeners_info.get('summary', {})
        print(f"  - Total listeners: {summary.get('total_listeners', 0)}")
        print(f"  - Event types: {summary.get('event_types', 0)}")
        print(f"  - Event type names: {summary.get('event_type_names', [])}")

        # 测试事件类型端点
        types_endpoint = EventTypesEndpoint(event_publisher)

        print("\n📋 Testing EventTypes endpoint:")
        types_info = types_endpoint.invoke(types_endpoint.operations()[0].operation_type)
        types_summary = types_info.get('summary', {})
        print(f"  - Total types: {types_summary.get('total_types', 0)}")
        print(f"  - Type names: {types_summary.get('type_names', [])}")

        print("✅ Events endpoints tested successfully")

    except Exception as e:
        print(f"❌ Endpoint testing failed: {e}")


async def test_integration_components():
    """测试集成组件"""
    print("\n🧪 Testing Integration Components...")

    try:
        # 测试状态枚举
        print("📊 Integration Status Values:")
        for status in EventsIntegrationStatus:
            print(f"  - {status.name}: {status.value}")

        # 测试导入的组件
        # DEPRECATED: from miniboot.actuator.autoconfigure.events_actuator_auto_configuration import \
            EventsMetrics

        print("✅ All components imported successfully!")
        print("  - EventsActuatorAutoConfiguration: ✓")
        print("  - EventPublisherEndpoint: ✓")
        print("  - EventListenersEndpoint: ✓")
        print("  - EventTypesEndpoint: ✓")
        print("  - EventsIntegrationStatus: ✓")
        print("  - EventsMetrics: ✓")

        return True

    except Exception as e:
        print(f"❌ Component test failed: {e}")
        return False


async def main():
    """主测试函数"""
    print("🚀 Starting Events Actuator Integration Tests...\n")

    # 测试组件导入
    component_test = await test_integration_components()

    # 测试自动配置
    config_test = await test_events_actuator_auto_configuration()

    # 总结
    print(f"\n📋 Test Summary:")
    print(f"  - Component Import Test: {'✅ PASSED' if component_test else '❌ FAILED'}")
    print(f"  - Auto Configuration Test: {'✅ PASSED' if config_test else '❌ FAILED'}")

    if component_test and config_test:
        print("\n🎉 All tests passed! Events Actuator integration is working correctly!")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")


if __name__ == "__main__":
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("\n👋 Test interrupted by user")
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        import traceback
        traceback.print_exc()

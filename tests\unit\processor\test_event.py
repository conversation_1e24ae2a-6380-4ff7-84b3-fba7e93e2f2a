#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: 事件监听处理器单元测试 - 简化版本
"""

import unittest
from unittest.mock import Mock

from miniboot.processor.base import ProcessorOrder
from miniboot.processor.event import EventListenerProcessor


class MockEvent:
    """模拟事件类"""

    def __init__(self, data=None):
        self.data = data or {}


class MockEventListenerMethod:
    """模拟@EventListener方法"""

    def __init__(self, event_type=MockEvent, condition=None, order=0, async_exec=False):
        self.__event_listener_metadata__ = type(
            "EventListenerMetadata",
            (),
            {"method": "handle_event", "event_type": event_type, "condition": condition, "order": order, "async_exec": async_exec},
        )()

    def __call__(self, instance, event):
        # 模拟方法调用
        if hasattr(instance, "events_handled"):
            instance.events_handled.append(event)


class TestBeanWithEventListener:
    """测试Bean - 有@EventListener方法"""

    def __init__(self):
        self.events_handled = []

    def handle_event(self, event: MockEvent):
        """@EventListener方法"""
        self.events_handled.append(event)


# 为测试Bean添加@EventListener注解
TestBeanWithEventListener.handle_event.__event_listener_metadata__ = type(
    "EventListenerMetadata", (), {"method": "handle_event", "event_type": MockEvent, "condition": None, "order": 0, "async_exec": False}
)()


class TestBeanWithMultipleListeners:
    """测试Bean - 多个@EventListener方法"""

    def __init__(self):
        self.events_handled = []
        self.priority_events = []

    def handle_event1(self, event: MockEvent):
        """第一个@EventListener方法"""
        self.events_handled.append(f"handler1: {event}")

    def handle_event2(self, event: MockEvent):
        """第二个@EventListener方法"""
        self.priority_events.append(f"handler2: {event}")


# 为多个监听器方法添加注解
TestBeanWithMultipleListeners.handle_event1.__event_listener_metadata__ = type(
    "EventListenerMetadata", (), {"method": "handle_event1", "event_type": MockEvent, "condition": None, "order": 1, "async_exec": False}
)()

TestBeanWithMultipleListeners.handle_event2.__event_listener_metadata__ = type(
    "EventListenerMetadata", (), {"method": "handle_event2", "event_type": MockEvent, "condition": None, "order": 0, "async_exec": False}
)()


class TestBeanWithoutEventListener:
    """测试Bean - 无@EventListener注解"""

    def __init__(self):
        self.value = "test"


class TestEventProcessor(unittest.TestCase):
    """事件监听处理器测试类"""

    def setUp(self):
        """测试前置设置"""
        self.mock_event_publisher = Mock()
        self.processor = EventListenerProcessor(self.mock_event_publisher)

        # 设置模拟事件发布器的返回值
        self.mock_event_publisher.subscribe.return_value = "handler_id_123"

    def test_processor_order(self):
        """测试处理器执行顺序"""
        self.assertEqual(self.processor.get_order(), ProcessorOrder.LIFECYCLE_PROCESSOR + 20)

    def test_supports_bean_with_event_listener(self):
        """测试支持有@EventListener注解的Bean"""
        bean = TestBeanWithEventListener()
        self.assertTrue(self.processor.supports(bean, "testBean"))

    def test_supports_bean_without_event_listener(self):
        """测试不支持无@EventListener注解的Bean"""
        bean = TestBeanWithoutEventListener()
        self.assertFalse(self.processor.supports(bean, "testBean"))

    def test_supports_none_bean(self):
        """测试不支持None Bean"""
        self.assertFalse(self.processor.supports(None, "testBean"))

    def test_event_listener_registration(self):
        """测试事件监听器注册"""
        bean = TestBeanWithEventListener()

        # 执行处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)

        # 验证事件发布器被调用
        self.mock_event_publisher.subscribe.assert_called_once()

        # 验证调用参数
        call_args = self.mock_event_publisher.subscribe.call_args
        self.assertEqual(call_args[1]["event_type"], MockEvent)
        self.assertEqual(call_args[1]["instance"], bean)
        self.assertEqual(call_args[1]["order"], 0)
        self.assertEqual(call_args[1]["async_exec"], False)
        self.assertIsNone(call_args[1]["condition"])

    def test_multiple_event_listeners_registration(self):
        """测试多个事件监听器注册"""
        bean = TestBeanWithMultipleListeners()

        # 执行处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)

        # 验证事件发布器被调用两次
        self.assertEqual(self.mock_event_publisher.subscribe.call_count, 2)

    def test_no_event_publisher(self):
        """测试没有事件发布器的情况"""
        processor = EventListenerProcessor()  # 没有设置事件发布器
        bean = TestBeanWithEventListener()

        # 执行处理（不应该抛出异常）
        result = processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)

    def test_already_processed_bean(self):
        """测试已处理的Bean不会重复处理"""
        bean = TestBeanWithEventListener()

        # 第一次处理
        self.processor.post_process_after_initialization(bean, "testBean")

        # 重置mock调用记录
        self.mock_event_publisher.reset_mock()

        # 第二次处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证没有再次调用事件发布器
        self.mock_event_publisher.subscribe.assert_not_called()
        self.assertIs(result, bean)

    def test_post_process_before_initialization(self):
        """测试初始化前处理（应该直接返回原Bean）"""
        bean = TestBeanWithEventListener()

        result = self.processor.post_process_before_initialization(bean, "testBean")

        self.assertIs(result, bean)
        # 事件发布器不应该被调用
        self.mock_event_publisher.subscribe.assert_not_called()

    def test_destroy_bean(self):
        """测试Bean销毁时取消注册监听器"""
        bean = TestBeanWithEventListener()

        # 先注册监听器
        self.processor.post_process_after_initialization(bean, "testBean")

        # 销毁Bean
        self.processor.destroy_bean(bean, "testBean")

        # 验证取消注册被调用
        self.mock_event_publisher.unsubscribe.assert_called_once_with("handler_id_123")

    def test_none_bean_handling(self):
        """测试None Bean的处理"""
        result = self.processor.post_process_after_initialization(None, "testBean")
        self.assertIsNone(result)

        # 销毁None Bean不应该抛出异常
        self.processor.destroy_bean(None, "testBean")

    def test_processed_beans_count(self):
        """测试已处理的Bean数量"""
        initial_count = self.processor.get_processed_beans_count()

        bean1 = TestBeanWithEventListener()
        bean2 = TestBeanWithEventListener()

        self.processor.post_process_after_initialization(bean1, "testBean1")
        self.processor.post_process_after_initialization(bean2, "testBean2")

        self.assertEqual(self.processor.get_processed_beans_count(), initial_count + 2)

    def test_registered_listeners_count(self):
        """测试已注册的监听器数量"""
        initial_count = self.processor.get_registered_listeners_count()

        bean1 = TestBeanWithEventListener()
        bean2 = TestBeanWithMultipleListeners()

        self.processor.post_process_after_initialization(bean1, "testBean1")
        self.processor.post_process_after_initialization(bean2, "testBean2")

        # bean1有1个监听器，bean2有2个监听器
        self.assertEqual(self.processor.get_registered_listeners_count(), initial_count + 3)

    def test_bean_without_event_listener_not_processed(self):
        """测试没有@EventListener注解的Bean不被处理"""
        bean = TestBeanWithoutEventListener()

        # 执行处理
        result = self.processor.post_process_after_initialization(bean, "testBean")

        # 验证结果
        self.assertIs(result, bean)
        # 验证事件发布器没有被调用
        self.mock_event_publisher.subscribe.assert_not_called()


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: SchedulerState枚举测试 - 验证FIX-3.3.3修复的状态定义规范化
"""

import unittest
from enum import Enum

from miniboot.schedule.scheduler import SchedulerState


class SchedulerStateTestCase(unittest.TestCase):
    """SchedulerState枚举测试"""

    def test_scheduler_state_is_enum(self):
        """测试SchedulerState是Enum类型"""
        self.assertTrue(issubclass(SchedulerState, Enum))
        self.assertIsInstance(SchedulerState.STOPPED, SchedulerState)

    def test_scheduler_state_values(self):
        """测试SchedulerState的值"""
        self.assertEqual(SchedulerState.STOPPED.value, "stopped")
        self.assertEqual(SchedulerState.STARTING.value, "starting")
        self.assertEqual(SchedulerState.RUNNING.value, "running")
        self.assertEqual(SchedulerState.PAUSED.value, "paused")
        self.assertEqual(SchedulerState.STOPPING.value, "stopping")

    def test_scheduler_state_comparison(self):
        """测试SchedulerState的比较"""
        # 测试相等性
        self.assertEqual(SchedulerState.RUNNING, SchedulerState.RUNNING)
        self.assertNotEqual(SchedulerState.RUNNING, SchedulerState.STOPPED)

        # 测试与字符串比较（应该不相等）
        self.assertNotEqual(SchedulerState.RUNNING, "running")
        self.assertEqual(SchedulerState.RUNNING.value, "running")

    def test_scheduler_state_membership(self):
        """测试SchedulerState的成员检查"""
        active_states = [SchedulerState.RUNNING, SchedulerState.STARTING]
        self.assertIn(SchedulerState.RUNNING, active_states)
        self.assertNotIn(SchedulerState.STOPPED, active_states)

    def test_is_active_method(self):
        """测试is_active方法"""
        self.assertTrue(SchedulerState.RUNNING.is_active())
        self.assertTrue(SchedulerState.STARTING.is_active())
        self.assertFalse(SchedulerState.STOPPED.is_active())
        self.assertFalse(SchedulerState.PAUSED.is_active())
        self.assertFalse(SchedulerState.STOPPING.is_active())

    def test_can_schedule_task_method(self):
        """测试can_schedule_task方法"""
        self.assertTrue(SchedulerState.RUNNING.can_schedule_task())
        self.assertTrue(SchedulerState.PAUSED.can_schedule_task())
        self.assertFalse(SchedulerState.STOPPED.can_schedule_task())
        self.assertFalse(SchedulerState.STARTING.can_schedule_task())
        self.assertFalse(SchedulerState.STOPPING.can_schedule_task())

    def test_can_transition_to_method(self):
        """测试can_transition_to方法"""
        # 从STOPPED状态的转换
        self.assertTrue(SchedulerState.STOPPED.can_transition_to(SchedulerState.STARTING))
        self.assertFalse(SchedulerState.STOPPED.can_transition_to(SchedulerState.RUNNING))
        self.assertFalse(SchedulerState.STOPPED.can_transition_to(SchedulerState.PAUSED))
        self.assertFalse(SchedulerState.STOPPED.can_transition_to(SchedulerState.STOPPING))

        # 从STARTING状态的转换
        self.assertTrue(SchedulerState.STARTING.can_transition_to(SchedulerState.RUNNING))
        self.assertTrue(SchedulerState.STARTING.can_transition_to(SchedulerState.STOPPED))
        self.assertFalse(SchedulerState.STARTING.can_transition_to(SchedulerState.PAUSED))
        self.assertFalse(SchedulerState.STARTING.can_transition_to(SchedulerState.STOPPING))

        # 从RUNNING状态的转换
        self.assertTrue(SchedulerState.RUNNING.can_transition_to(SchedulerState.PAUSED))
        self.assertTrue(SchedulerState.RUNNING.can_transition_to(SchedulerState.STOPPING))
        self.assertFalse(SchedulerState.RUNNING.can_transition_to(SchedulerState.STOPPED))
        self.assertFalse(SchedulerState.RUNNING.can_transition_to(SchedulerState.STARTING))

        # 从PAUSED状态的转换
        self.assertTrue(SchedulerState.PAUSED.can_transition_to(SchedulerState.RUNNING))
        self.assertTrue(SchedulerState.PAUSED.can_transition_to(SchedulerState.STOPPING))
        self.assertFalse(SchedulerState.PAUSED.can_transition_to(SchedulerState.STOPPED))
        self.assertFalse(SchedulerState.PAUSED.can_transition_to(SchedulerState.STARTING))

        # 从STOPPING状态的转换
        self.assertTrue(SchedulerState.STOPPING.can_transition_to(SchedulerState.STOPPED))
        self.assertFalse(SchedulerState.STOPPING.can_transition_to(SchedulerState.STARTING))
        self.assertFalse(SchedulerState.STOPPING.can_transition_to(SchedulerState.RUNNING))
        self.assertFalse(SchedulerState.STOPPING.can_transition_to(SchedulerState.PAUSED))

    def test_state_iteration(self):
        """测试状态枚举的迭代"""
        all_states = list(SchedulerState)
        expected_states = [SchedulerState.STOPPED, SchedulerState.STARTING, SchedulerState.RUNNING, SchedulerState.PAUSED, SchedulerState.STOPPING]
        self.assertEqual(all_states, expected_states)

    def test_state_string_representation(self):
        """测试状态的字符串表示"""
        self.assertEqual(str(SchedulerState.RUNNING), "SchedulerState.RUNNING")
        self.assertEqual(repr(SchedulerState.RUNNING), "<SchedulerState.RUNNING: 'running'>")

    def test_state_hash(self):
        """测试状态的哈希值"""
        # Enum成员应该是可哈希的
        state_set = {SchedulerState.RUNNING, SchedulerState.STOPPED}
        self.assertEqual(len(state_set), 2)

        # 相同状态应该有相同的哈希值
        self.assertEqual(hash(SchedulerState.RUNNING), hash(SchedulerState.RUNNING))

    def test_state_documentation(self):
        """测试状态的文档字符串"""
        self.assertIsNotNone(SchedulerState.__doc__)
        self.assertIn("调度器状态枚举", SchedulerState.__doc__)

    def test_state_transition_chains(self):
        """测试状态转换链"""
        # 测试完整的启动-运行-停止链
        # STOPPED -> STARTING -> RUNNING -> STOPPING -> STOPPED
        self.assertTrue(SchedulerState.STOPPED.can_transition_to(SchedulerState.STARTING))
        self.assertTrue(SchedulerState.STARTING.can_transition_to(SchedulerState.RUNNING))
        self.assertTrue(SchedulerState.RUNNING.can_transition_to(SchedulerState.STOPPING))
        self.assertTrue(SchedulerState.STOPPING.can_transition_to(SchedulerState.STOPPED))

        # 测试暂停-恢复链
        # RUNNING -> PAUSED -> RUNNING
        self.assertTrue(SchedulerState.RUNNING.can_transition_to(SchedulerState.PAUSED))
        self.assertTrue(SchedulerState.PAUSED.can_transition_to(SchedulerState.RUNNING))

        # 测试暂停-停止链
        # PAUSED -> STOPPING -> STOPPED
        self.assertTrue(SchedulerState.PAUSED.can_transition_to(SchedulerState.STOPPING))
        self.assertTrue(SchedulerState.STOPPING.can_transition_to(SchedulerState.STOPPED))

    def test_invalid_state_transitions(self):
        """测试无效的状态转换"""
        # 测试自转换（应该都不允许）
        for state in SchedulerState:
            self.assertFalse(state.can_transition_to(state), f"State {state} should not transition to itself")

        # 测试跳跃式转换（应该不允许）
        self.assertFalse(SchedulerState.STOPPED.can_transition_to(SchedulerState.RUNNING))
        self.assertFalse(SchedulerState.STOPPED.can_transition_to(SchedulerState.PAUSED))
        self.assertFalse(SchedulerState.RUNNING.can_transition_to(SchedulerState.STOPPED))

    def test_type_safety(self):
        """测试类型安全性"""

        # 测试类型检查
        def check_scheduler_state(state: SchedulerState) -> bool:
            return state.is_active()

        # 应该接受SchedulerState枚举
        self.assertTrue(check_scheduler_state(SchedulerState.RUNNING))
        self.assertFalse(check_scheduler_state(SchedulerState.STOPPED))

        # 字符串不应该被接受（在类型检查器中会报错）
        with self.assertRaises(AttributeError):
            check_scheduler_state("running")  # type: ignore

    def test_enum_uniqueness(self):
        """测试枚举值的唯一性"""
        values = [state.value for state in SchedulerState]
        self.assertEqual(len(values), len(set(values)), "SchedulerState values should be unique")

    def test_enum_completeness(self):
        """测试枚举的完整性"""
        # 确保所有预期的状态都存在
        expected_states = {"stopped", "starting", "running", "paused", "stopping"}
        actual_states = {state.value for state in SchedulerState}
        self.assertEqual(actual_states, expected_states)

    def test_state_logic_consistency(self):
        """测试状态逻辑的一致性"""
        # 活动状态应该包含所有可以调度任务的状态
        for state in SchedulerState:
            if state.is_active():
                # 活动状态中，只有RUNNING和STARTING，但只有RUNNING和PAUSED可以调度任务
                if state == SchedulerState.RUNNING:
                    self.assertTrue(state.can_schedule_task())
                elif state == SchedulerState.STARTING:
                    self.assertFalse(state.can_schedule_task())

        # 可以调度任务的状态应该是有限的
        schedulable_states = [state for state in SchedulerState if state.can_schedule_task()]
        expected_schedulable = [SchedulerState.RUNNING, SchedulerState.PAUSED]
        self.assertEqual(set(schedulable_states), set(expected_schedulable))


if __name__ == "__main__":
    unittest.main()

#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
@author: cz
@description: Bean工厂处理器集成测试 - 简化版本
"""

import unittest

from miniboot.bean.definition import BeanDefinition, BeanScope
from miniboot.bean.factory import DefaultBeanFactory
from miniboot.bean.registry import DefaultBeanDefinitionRegistry
from miniboot.bean.utils import DependencyGraph


class MockRepository:
    """模拟仓库类"""

    def __init__(self, name="mock_repo"):
        self.name = name


class TestServiceWithAutowired:
    """测试服务类 - 有@Autowired字段"""

    def __init__(self):
        self.repository = None

    # 模拟@Autowired字段元数据
    __autowired_fields__ = {"repository": type("AutowiredMetadata", (), {"required": True, "name": None, "qualifier": None})()}
    __annotations__ = {"repository": MockRepository}


class TestServiceWithoutAutowired:
    """测试服务类 - 无@Autowired注解"""

    def __init__(self):
        self.value = "test"


class TestIntegration(unittest.TestCase):
    """Bean工厂处理器集成测试类"""

    def setUp(self):
        """测试前置设置"""
        self.registry = DefaultBeanDefinitionRegistry()
        self.dependency_graph = DependencyGraph()
        self.bean_factory = DefaultBeanFactory(self.registry, self.dependency_graph)

        # 注册MockRepository Bean
        repo_definition = BeanDefinition(bean_name="mockRepository", bean_class=MockRepository, scope=BeanScope.SINGLETON)
        self.registry.register_bean_definition("mockRepository", repo_definition)

        # 注册TestServiceWithAutowired Bean
        service_definition = BeanDefinition(bean_name="testService", bean_class=TestServiceWithAutowired, scope=BeanScope.SINGLETON)
        self.registry.register_bean_definition("testService", service_definition)

    def test_processor_count(self):
        """测试Bean后置处理器数量"""
        # 检查处理器注册表是否可用
        if self.bean_factory._bean_post_processor_registry is None:
            self.skipTest("Bean后置处理器模块不可用")

        # Bean工厂只注册核心处理器：自动装配处理器、值注入处理器
        # 框架特性处理器（配置属性、生命周期、事件监听、定时任务）由应用上下文注册
        self.assertEqual(self.bean_factory.get_bean_post_processor_count(), 2)

    def test_autowired_injection(self):
        """测试自动装配注入"""
        # 获取服务Bean，应该自动注入repository
        service = self.bean_factory.get_bean("testService")

        # 验证自动装配是否成功
        self.assertIsNotNone(service.repository)
        self.assertIsInstance(service.repository, MockRepository)
        self.assertEqual(service.repository.name, "mock_repo")

    def test_bean_without_autowired(self):
        """测试没有@Autowired注解的Bean不受影响"""
        # 注册没有@Autowired的Bean
        simple_definition = BeanDefinition(bean_name="simpleService", bean_class=TestServiceWithoutAutowired, scope=BeanScope.SINGLETON)
        self.registry.register_bean_definition("simpleService", simple_definition)

        # 获取Bean
        service = self.bean_factory.get_bean("simpleService")

        # 验证Bean正常创建
        self.assertIsNotNone(service)
        self.assertEqual(service.value, "test")

    def test_multiple_beans_autowired(self):
        """测试多个Bean的自动装配"""
        # 注册另一个有@Autowired的Bean
        service2_definition = BeanDefinition(bean_name="testService2", bean_class=TestServiceWithAutowired, scope=BeanScope.SINGLETON)
        self.registry.register_bean_definition("testService2", service2_definition)

        # 获取两个服务Bean
        service1 = self.bean_factory.get_bean("testService")
        service2 = self.bean_factory.get_bean("testService2")

        # 验证两个Bean都正确注入了repository
        self.assertIsNotNone(service1.repository)
        self.assertIsNotNone(service2.repository)

        # 验证它们共享同一个repository实例（单例）
        self.assertIs(service1.repository, service2.repository)

    def test_processor_manager_integration(self):
        """测试处理器管理器集成"""
        from miniboot.processor.manager import BeanPostProcessorManager

        # 获取Bean工厂的处理器注册表
        registry = self.bean_factory._bean_post_processor_registry

        # 检查处理器注册表是否可用
        if registry is None:
            self.skipTest("Bean后置处理器模块不可用")

        # 创建处理器管理器
        manager = BeanPostProcessorManager(registry)

        # 验证管理器可以获取处理器数量
        processor_count = manager.get_processor_count()
        self.assertEqual(processor_count, 2)  # Bean工厂只有2个核心处理器

        # 创建一个Bean来触发处理器执行
        service = self.bean_factory.get_bean("testService")
        self.assertIsNotNone(service)

        # 验证可以获取处理器指标（处理器管理器需要手动初始化指标）
        # 由于处理器管理器是独立创建的，需要手动注册处理器来获取指标
        processors = registry.get_processors()
        for processor in processors:
            processor_name = processor.__class__.__name__
            metrics = manager.get_processor_metrics(processor_name)
            # 如果指标不存在，说明处理器管理器是独立创建的
            if metrics is None:
                # 这是正常的，因为处理器管理器是独立创建的
                pass


if __name__ == "__main__":
    unittest.main()

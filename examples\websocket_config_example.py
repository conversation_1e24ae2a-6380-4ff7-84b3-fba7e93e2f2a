#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: WebSocket 配置使用示例
"""

from miniboot.context import ApplicationContext
from miniboot.starters.websocket import WebSocketProperties


def main():
    """配置使用示例"""
    print("🔧 WebSocket 配置使用示例")
    print("=" * 50)

    # 创建应用上下文
    context = ApplicationContext()

    try:
        # 获取 WebSocket 配置
        websocket_properties = context.get_bean(WebSocketProperties)

        print("📋 当前 WebSocket 配置:")
        print(f"  启用状态: {websocket_properties.enabled}")
        print(f"  端点路径: {websocket_properties.path}")
        print(f"  最大消息大小: {websocket_properties.max_message_size} 字节")

        print("\n🖥️ 服务器配置:")
        print(f"  主机地址: {websocket_properties.server.host}")
        print(f"  端口: {websocket_properties.server.port}")
        print(f"  读取超时: {websocket_properties.server.read_timeout} 秒")
        print(f"  写入超时: {websocket_properties.server.write_timeout} 秒")
        print(f"  空闲超时: {websocket_properties.server.idle_timeout} 秒")

        print("\n🔒 安全配置:")
        print(f"  允许的源: {websocket_properties.security.allowed_origins}")
        print(f"  认证启用: {websocket_properties.security.auth.enabled}")
        print(f"  JWT启用: {websocket_properties.security.jwt.enabled}")

        print("\n📦 压缩配置:")
        print(f"  压缩启用: {websocket_properties.compression.enabled}")
        print(f"  压缩级别: {websocket_properties.compression.level}")
        print(f"  压缩算法: {websocket_properties.compression.algorithm}")
        print(f"  最小压缩大小: {websocket_properties.compression.min_size} 字节")

        print("\n⏱️ 超时配置:")
        print(f"  连接超时: {websocket_properties.timeout.connect} 秒")
        print(f"  读取超时: {websocket_properties.timeout.read} 秒")
        print(f"  写入超时: {websocket_properties.timeout.write} 秒")
        print(f"  心跳间隔: {websocket_properties.timeout.heartbeat} 秒")

        print("\n🔗 连接限制配置:")
        print(f"  连接限制启用: {websocket_properties.connection_limit.enabled}")
        print(f"  每用户最大连接数: {websocket_properties.connection_limit.max_connections_per_user}")
        print(f"  总最大连接数: {websocket_properties.connection_limit.max_total_connections}")
        print(f"  每IP最大连接数: {websocket_properties.connection_limit.max_connections_per_ip}")

        print("\n🏊 连接池配置:")
        print(f"  连接池启用: {websocket_properties.pool.enabled}")
        print(f"  最小连接数: {websocket_properties.pool.min_connections}")
        print(f"  最大连接数: {websocket_properties.pool.max_connections}")
        print(f"  最大空闲时间: {websocket_properties.pool.max_idle_time} 秒")
        print(f"  连接验证启用: {websocket_properties.pool.enable_validation}")

        print("\n📊 监控配置:")
        print(f"  指标收集启用: {websocket_properties.metrics.enabled}")
        print(f"  导出格式: {websocket_properties.metrics.export_format}")
        print(f"  收集间隔: {websocket_properties.metrics.collection_interval} 秒")

        print("\n🩺 健康检查配置:")
        print(f"  健康检查启用: {websocket_properties.health.enabled}")
        print(f"  检查间隔: {websocket_properties.health.check_interval} 秒")
        print(f"  最大错误率: {websocket_properties.health.max_error_rate}")
        print(f"  最大响应时间: {websocket_properties.health.max_response_time} 毫秒")
        print(f"  最小成功率: {websocket_properties.health.min_success_rate}")

        print("\n✅ 配置加载成功!")

        # 展示如何根据配置创建服务
        print("\n🚀 根据配置创建 WebSocket 服务:")

        if websocket_properties.enabled:
            print("  ✅ WebSocket 功能已启用")
            print(f"  🌐 服务将在 {websocket_properties.server.host}:{websocket_properties.server.port}{websocket_properties.path} 启动")

            if websocket_properties.compression.enabled:
                print(f"  📦 消息压缩已启用 ({websocket_properties.compression.algorithm})")

            if websocket_properties.security.auth.enabled:
                print("  🔒 认证功能已启用")

            if websocket_properties.pool.enabled:
                print(f"  🏊 连接池已启用 (最大连接数: {websocket_properties.pool.max_connections})")

            if websocket_properties.metrics.enabled:
                print("  📊 指标收集已启用")

            if websocket_properties.health.enabled:
                print("  🩺 健康检查已启用")
        else:
            print("  ❌ WebSocket 功能已禁用")

    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        print("请检查 application.yml 文件中的 WebSocket 配置")

    finally:
        # 关闭应用上下文
        context.close()


def show_config_examples():
    """展示配置示例"""
    print("\n📝 配置文件示例:")
    print("=" * 50)

    print("🔧 基础配置 (application.yml):")
    print("""
miniboot:
    starters:
        websocket:
            enabled: true
            path: "/ws"
            max-message-size: 524288

            server:
                host: 0.0.0.0
                port: 8080
                idle-timeout: 60

            compression:
                enabled: true
                level: 6
                algorithm: "gzip"

            pool:
                enabled: true
                max-connections: 50
    """)

    print("🛠️ 开发环境配置 (application-dev.yml):")
    print("""
miniboot:
    starters:
        websocket:
            server:
                host: localhost
                port: 8080

            security:
                allowed-origins: ["http://localhost:3000"]
                auth:
                    enabled: false

            compression:
                level: 1  # 低压缩级别,优先速度

            connection-limit:
                max-connections-per-user: 10
    """)

    print("🏭 生产环境配置 (application-prod.yml):")
    print("""
miniboot:
    starters:
        websocket:
            server:
                host: 0.0.0.0
                port: 443

            security:
                allowed-origins: ["https://yourdomain.com"]
                auth:
                    enabled: true
                jwt:
                    enabled: true
                    secret-key: "${JWT_SECRET_KEY}"

            compression:
                level: 9  # 高压缩级别,节省带宽

            connection-limit:
                max-total-connections: 10000
                max-connections-per-ip: 100

            pool:
                max-connections: 200
                enable-validation: true
    """)


if __name__ == "__main__":
    main()
    show_config_examples()

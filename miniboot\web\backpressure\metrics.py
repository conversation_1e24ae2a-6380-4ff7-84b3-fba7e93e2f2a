#!/usr/bin/env python
"""
背压控制指标收集器

提供背压控制系统的性能指标收集、统计分析和监控功能.

主要功能:
- 背压控制事件统计 (触发次数、持续时间、恢复时间)
- 系统保护效果分析 (请求拒绝率、响应时间改善)
- 组件性能监控 (熔断器状态、降级策略执行)
- 实时指标导出和历史数据分析
- 性能报告生成和优化建议
"""

import time
from collections import defaultdict, deque
from dataclasses import dataclass, field
from enum import Enum
from threading import Lock
from typing import Any, Dict, List, Optional

from loguru import logger


class BackpressureEventType(Enum):
    """背压控制事件类型"""

    CIRCUIT_BREAKER_OPEN = "circuit_breaker_open"
    CIRCUIT_BREAKER_CLOSE = "circuit_breaker_close"
    CIRCUIT_BREAKER_HALF_OPEN = "circuit_breaker_half_open"
    DEGRADATION_ACTIVATED = "degradation_activated"
    DEGRADATION_DEACTIVATED = "degradation_deactivated"
    CONCURRENCY_LIMITED = "concurrency_limited"
    LOAD_SHEDDING = "load_shedding"
    REQUEST_REJECTED = "request_rejected"
    REQUEST_THROTTLED = "request_throttled"


@dataclass
class BackpressureEvent:
    """背压控制事件"""

    event_type: BackpressureEventType
    timestamp: float
    component: str  # 触发组件名称
    details: Dict[str, Any] = field(default_factory=dict)
    duration: Optional[float] = None  # 事件持续时间(如果适用)


@dataclass
class ComponentMetrics:
    """组件指标"""

    component_name: str
    total_events: int = 0
    active_events: int = 0
    last_event_time: Optional[float] = None
    avg_event_duration: float = 0.0
    success_rate: float = 100.0
    performance_impact: float = 0.0  # 性能影响百分比


@dataclass
class MetricsSnapshot:
    """背压控制指标快照"""

    timestamp: float
    total_events: int
    events_per_minute: float
    active_protections: int
    system_protection_rate: float  # 系统保护率
    request_rejection_rate: float  # 请求拒绝率
    avg_response_time_improvement: float  # 响应时间改善
    component_metrics: Dict[str, ComponentMetrics]
    recent_events: List[BackpressureEvent]


class BackpressureMetrics:
    """背压控制指标收集器

    收集和分析背压控制系统的各种性能指标,提供实时监控和历史分析功能.
    支持事件统计、性能分析、组件监控和报告生成.
    """

    def __init__(self, max_events_history: int = 1000, max_snapshots: int = 100):
        """初始化指标收集器

        Args:
            max_events_history: 最大事件历史记录数
            max_snapshots: 最大快照历史记录数
        """
        # 配置参数
        self._max_events_history = max_events_history
        self._max_snapshots = max_snapshots

        # 事件存储
        self._events_history: deque = deque(maxlen=max_events_history)
        self._snapshots_history: deque = deque(maxlen=max_snapshots)

        # 组件指标
        self._component_metrics: Dict[str, ComponentMetrics] = {}

        # 统计数据
        self._event_counters: Dict[BackpressureEventType, int] = defaultdict(int)
        self._active_events: Dict[str, BackpressureEvent] = {}  # 正在进行的事件

        # 性能统计
        self._total_requests = 0
        self._rejected_requests = 0
        self._throttled_requests = 0
        self._response_times_before: deque = deque(maxlen=1000)
        self._response_times_after: deque = deque(maxlen=1000)

        # 线程安全锁
        self._metrics_lock = Lock()

        # 启动时间
        self._start_time = time.time()

        logger.info("BackpressureMetrics initialized")

    def record_event(self, event_type: BackpressureEventType, component: str, details: Optional[Dict[str, Any]] = None) -> str:
        """记录背压控制事件

        Args:
            event_type: 事件类型
            component: 触发组件名称
            details: 事件详细信息

        Returns:
            事件ID,用于后续更新事件状态
        """
        event_id = f"{component}_{event_type.value}_{time.time()}"
        event = BackpressureEvent(event_type=event_type, timestamp=time.time(), component=component, details=details or {})

        with self._metrics_lock:
            # 记录事件
            self._events_history.append(event)
            self._event_counters[event_type] += 1

            # 如果是开始事件,记录为活跃事件
            if event_type in [
                BackpressureEventType.CIRCUIT_BREAKER_OPEN,
                BackpressureEventType.DEGRADATION_ACTIVATED,
                BackpressureEventType.CONCURRENCY_LIMITED,
            ]:
                self._active_events[event_id] = event

            # 更新组件指标
            self._update_component_metrics(component, event_type)

        logger.debug(f"Recorded backpressure event: {event_type.value} from {component}")
        return event_id

    def complete_event(self, event_id: str, success: bool = True) -> None:
        """完成背压控制事件

        Args:
            event_id: 事件ID
            success: 事件是否成功完成
        """
        with self._metrics_lock:
            if event_id in self._active_events:
                event = self._active_events[event_id]
                event.duration = time.time() - event.timestamp

                # 更新组件指标
                component_metrics = self._get_or_create_component_metrics(event.component)
                component_metrics.active_events -= 1

                if component_metrics.avg_event_duration == 0:
                    component_metrics.avg_event_duration = event.duration
                else:
                    # 计算移动平均
                    component_metrics.avg_event_duration = component_metrics.avg_event_duration * 0.9 + event.duration * 0.1

                # 更新成功率
                if success:
                    component_metrics.success_rate = min(100.0, component_metrics.success_rate + 0.1)
                else:
                    component_metrics.success_rate = max(0.0, component_metrics.success_rate - 1.0)

                del self._active_events[event_id]

    def record_request_metrics(self, total_requests: int, rejected_requests: int = 0, throttled_requests: int = 0) -> None:
        """记录请求指标

        Args:
            total_requests: 总请求数
            rejected_requests: 拒绝请求数
            throttled_requests: 限流请求数
        """
        with self._metrics_lock:
            self._total_requests += total_requests
            self._rejected_requests += rejected_requests
            self._throttled_requests += throttled_requests

    def record_response_time(self, response_time: float, is_protected: bool = False) -> None:
        """记录响应时间

        Args:
            response_time: 响应时间(秒)
            is_protected: 是否在背压保护下
        """
        with self._metrics_lock:
            if is_protected:
                self._response_times_after.append(response_time)
            else:
                self._response_times_before.append(response_time)

    def get_current_snapshot(self) -> MetricsSnapshot:
        """获取当前指标快照"""
        with self._metrics_lock:
            current_time = time.time()

            # 计算每分钟事件数
            recent_events = [e for e in self._events_history if current_time - e.timestamp <= 60]
            events_per_minute = len(recent_events)

            # 计算请求拒绝率
            rejection_rate = 0.0
            if self._total_requests > 0:
                rejection_rate = (self._rejected_requests / self._total_requests) * 100

            # 计算系统保护率
            protection_rate = 0.0
            if self._total_requests > 0:
                protected_requests = self._rejected_requests + self._throttled_requests
                protection_rate = (protected_requests / self._total_requests) * 100

            # 计算响应时间改善
            response_time_improvement = 0.0
            if self._response_times_before and self._response_times_after:
                avg_before = sum(self._response_times_before) / len(self._response_times_before)
                avg_after = sum(self._response_times_after) / len(self._response_times_after)
                if avg_before > 0:
                    response_time_improvement = ((avg_before - avg_after) / avg_before) * 100

            snapshot = MetricsSnapshot(
                timestamp=current_time,
                total_events=len(self._events_history),
                events_per_minute=events_per_minute,
                active_protections=len(self._active_events),
                system_protection_rate=protection_rate,
                request_rejection_rate=rejection_rate,
                avg_response_time_improvement=response_time_improvement,
                component_metrics=self._component_metrics.copy(),
                recent_events=recent_events[-10:],  # 最近10个事件
            )

            # 保存快照到历史记录
            self._snapshots_history.append(snapshot)

            return snapshot

    def get_event_statistics(self) -> Dict[str, Any]:
        """获取事件统计信息"""
        with self._metrics_lock:
            total_events = sum(self._event_counters.values())

            event_stats = {}
            for event_type, count in self._event_counters.items():
                percentage = (count / total_events * 100) if total_events > 0 else 0
                event_stats[event_type.value] = {"count": count, "percentage": round(percentage, 2)}

            return {
                "total_events": total_events,
                "active_events": len(self._active_events),
                "event_breakdown": event_stats,
                "uptime_hours": round((time.time() - self._start_time) / 3600, 2),
            }

    def get_component_performance(self) -> Dict[str, Dict[str, Any]]:
        """获取组件性能统计"""
        with self._metrics_lock:
            performance = {}
            for component, metrics in self._component_metrics.items():
                performance[component] = {
                    "total_events": metrics.total_events,
                    "active_events": metrics.active_events,
                    "avg_event_duration": round(metrics.avg_event_duration, 3),
                    "success_rate": round(metrics.success_rate, 2),
                    "performance_impact": round(metrics.performance_impact, 2),
                    "last_event_ago": (round(time.time() - metrics.last_event_time, 1) if metrics.last_event_time else None),
                }
            return performance

    def get_protection_effectiveness(self) -> Dict[str, Any]:
        """获取保护效果分析"""
        with self._metrics_lock:
            if self._total_requests == 0:
                return {"total_requests": 0, "protection_summary": "No requests processed yet"}

            rejection_rate = (self._rejected_requests / self._total_requests) * 100
            throttling_rate = (self._throttled_requests / self._total_requests) * 100

            # 响应时间分析
            response_time_analysis = {}
            if self._response_times_before:
                avg_before = sum(self._response_times_before) / len(self._response_times_before)
                response_time_analysis["avg_response_time_before"] = round(avg_before, 3)

            if self._response_times_after:
                avg_after = sum(self._response_times_after) / len(self._response_times_after)
                response_time_analysis["avg_response_time_after"] = round(avg_after, 3)

                if self._response_times_before:
                    improvement = ((avg_before - avg_after) / avg_before) * 100
                    response_time_analysis["improvement_percentage"] = round(improvement, 2)

            return {
                "total_requests": self._total_requests,
                "rejected_requests": self._rejected_requests,
                "throttled_requests": self._throttled_requests,
                "rejection_rate": round(rejection_rate, 2),
                "throttling_rate": round(throttling_rate, 2),
                "response_time_analysis": response_time_analysis,
            }

    def generate_performance_report(self) -> Dict[str, Any]:
        """生成性能报告"""
        current_snapshot = self.get_current_snapshot()
        event_stats = self.get_event_statistics()
        component_performance = self.get_component_performance()
        protection_effectiveness = self.get_protection_effectiveness()

        # 生成建议
        recommendations = []

        if current_snapshot.request_rejection_rate > 10:
            recommendations.append("High request rejection rate detected. Consider scaling resources.")

        if current_snapshot.active_protections > 5:
            recommendations.append("Multiple active protections. System may be under stress.")

        if current_snapshot.avg_response_time_improvement < 0:
            recommendations.append("Response time not improving. Review backpressure configuration.")

        for component, metrics in component_performance.items():
            if metrics["success_rate"] < 90:
                recommendations.append(f"Component {component} has low success rate: {metrics['success_rate']}%")

        return {
            "report_timestamp": time.time(),
            "summary": {
                "total_events": event_stats["total_events"],
                "active_protections": current_snapshot.active_protections,
                "system_protection_rate": round(current_snapshot.system_protection_rate, 2),
                "response_time_improvement": round(current_snapshot.avg_response_time_improvement, 2),
            },
            "event_statistics": event_stats,
            "component_performance": component_performance,
            "protection_effectiveness": protection_effectiveness,
            "recommendations": recommendations,
        }

    def reset_metrics(self) -> None:
        """重置所有指标"""
        with self._metrics_lock:
            self._events_history.clear()
            self._snapshots_history.clear()
            self._component_metrics.clear()
            self._event_counters.clear()
            self._active_events.clear()

            self._total_requests = 0
            self._rejected_requests = 0
            self._throttled_requests = 0
            self._response_times_before.clear()
            self._response_times_after.clear()

            self._start_time = time.time()

        logger.info("BackpressureMetrics reset")

    def _update_component_metrics(self, component: str, event_type: BackpressureEventType) -> None:
        """更新组件指标"""
        metrics = self._get_or_create_component_metrics(component)
        metrics.total_events += 1
        metrics.last_event_time = time.time()

        # 如果是开始事件,增加活跃事件计数
        if event_type in [
            BackpressureEventType.CIRCUIT_BREAKER_OPEN,
            BackpressureEventType.DEGRADATION_ACTIVATED,
            BackpressureEventType.CONCURRENCY_LIMITED,
        ]:
            metrics.active_events += 1

    def _get_or_create_component_metrics(self, component: str) -> ComponentMetrics:
        """获取或创建组件指标"""
        if component not in self._component_metrics:
            self._component_metrics[component] = ComponentMetrics(component_name=component)
        return self._component_metrics[component]

    def get_metrics_summary(self) -> Dict[str, Any]:
        """获取指标摘要"""
        snapshot = self.get_current_snapshot()
        return {
            "total_events": snapshot.total_events,
            "events_per_minute": snapshot.events_per_minute,
            "active_protections": snapshot.active_protections,
            "protection_rate": round(snapshot.system_protection_rate, 2),
            "rejection_rate": round(snapshot.request_rejection_rate, 2),
            "response_improvement": round(snapshot.avg_response_time_improvement, 2),
            "components_count": len(snapshot.component_metrics),
        }

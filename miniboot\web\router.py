#!/usr/bin/env python
"""
智能路由处理器模块

集成背压控制、请求限流和熔断机制的智能路由处理系统.

主要功能:
- SmartRouteHandler - 智能路由处理器
- 路由级别的背压控制
- 请求限流和熔断机制
- 与智能调度器深度集成
- 路由性能监控和优化
"""

import asyncio
import time
from collections import defaultdict
from dataclasses import dataclass, field
from enum import Enum
from typing import Any, Callable, Dict, List, Optional, Set

from fastapi import HTTPException, Request, Response
from loguru import logger

from ..errors import (circuit_breaker, handle_context_exceptions,
                      performance_monitor, timeout_handler)
from .backpressure.breaker import (CircuitBreaker, CircuitBreakerConfig,
                                   CircuitBreakerState)
from .backpressure.limiter import (ConcurrencyConfig, ConcurrencyLimiter,
                                   RequestPriority)
from .registry import RouteInfo as SmartRouteInfo
from .scheduling.scheduler import TaskRequest, TaskScheduler


class RouteOptimizationLevel(Enum):
    """路由优化级别"""

    NONE = "none"  # 无优化
    BASIC = "basic"  # 基础优化
    ADVANCED = "advanced"  # 高级优化
    INTELLIGENT = "intelligent"  # 智能优化


@dataclass
class RouteBackpressureConfig:
    """路由背压配置"""

    enabled: bool = True
    max_concurrent: int = 50
    queue_size: int = 200
    timeout: float = 30.0

    # 限流配置
    rate_limit_enabled: bool = True
    requests_per_second: float = 100.0
    burst_size: int = 20

    # 熔断配置
    circuit_breaker_enabled: bool = True
    failure_threshold: int = 5
    success_threshold: int = 3
    timeout_seconds: float = 60.0

    # 智能优化配置
    optimization_level: RouteOptimizationLevel = RouteOptimizationLevel.INTELLIGENT
    adaptive_limits: bool = True
    performance_monitoring: bool = True


@dataclass
class RoutePerformanceMetrics:
    """路由性能指标"""

    total_requests: int = 0
    successful_requests: int = 0
    failed_requests: int = 0
    rejected_requests: int = 0

    avg_response_time: float = 0.0
    min_response_time: float = float("inf")
    max_response_time: float = 0.0

    current_concurrent: int = 0
    max_concurrent_reached: int = 0

    circuit_breaker_trips: int = 0
    rate_limit_hits: int = 0

    last_request_time: float = 0.0
    last_success_time: float = 0.0
    last_failure_time: float = 0.0

    def update_request(self, response_time: float, success: bool):
        """更新请求指标"""
        self.total_requests += 1
        self.last_request_time = time.time()

        if success:
            self.successful_requests += 1
            self.last_success_time = time.time()
        else:
            self.failed_requests += 1
            self.last_failure_time = time.time()

        # 更新响应时间统计
        total_time = self.avg_response_time * (self.total_requests - 1) + response_time
        self.avg_response_time = total_time / self.total_requests
        self.min_response_time = min(self.min_response_time, response_time)
        self.max_response_time = max(self.max_response_time, response_time)


class RouteBackpressureController:
    """路由级别的背压控制器"""

    def __init__(self, route_path: str, config: RouteBackpressureConfig):
        self.route_path = route_path
        self.config = config

        # 并发限制器
        if config.enabled:
            concurrency_config = ConcurrencyConfig(max_concurrent=config.max_concurrent, queue_size=config.queue_size, timeout=config.timeout)
            self.concurrency_limiter = ConcurrencyLimiter(concurrency_config)
        else:
            self.concurrency_limiter = None

        # 熔断器
        if config.circuit_breaker_enabled:
            circuit_config = CircuitBreakerConfig(
                failure_threshold=config.failure_threshold, success_threshold=config.success_threshold, timeout=config.timeout_seconds
            )
            self.circuit_breaker = CircuitBreaker(f"route_{route_path}", circuit_config)
        else:
            self.circuit_breaker = None

        # 性能指标
        self.metrics = RoutePerformanceMetrics()

        logger.debug(f"RouteBackpressureController initialized for {route_path}")

    async def can_process_request(self, request: Request) -> bool:
        """检查是否可以处理请求

        Args:
            request: HTTP请求

        Returns:
            bool: 是否可以处理请求
        """
        try:
            # 检查熔断器状态
            if not await self._check_circuit_breaker():
                return False

            # 检查并发限制
            if not await self._check_concurrency_limit():
                return False

            return True

        except Exception as e:
            logger.error(f"Error checking request processing capability: {e}")
            return False

    async def _check_circuit_breaker(self) -> bool:
        """检查熔断器状态

        Returns:
            bool: 熔断器是否允许请求通过
        """
        if self.circuit_breaker:
            if self.circuit_breaker.state == CircuitBreakerState.OPEN:
                self.metrics.rejected_requests += 1
                logger.warning(f"Request rejected by circuit breaker for route {self.route_path}")
                return False
        return True

    async def _check_concurrency_limit(self) -> bool:
        """检查并发限制

        Returns:
            bool: 是否在并发限制内
        """
        if self.concurrency_limiter:
            if not await self.concurrency_limiter.can_acquire():
                self.metrics.rejected_requests += 1
                logger.warning(f"Request rejected by concurrency limiter for route {self.route_path}")
                return False
        return True

    async def acquire_resources(self, request: Request, priority: RequestPriority = RequestPriority.NORMAL) -> bool:
        """获取处理资源"""
        try:
            if self.concurrency_limiter:
                acquired = await self.concurrency_limiter.acquire(priority=priority)
                if acquired:
                    self.metrics.current_concurrent += 1
                    self.metrics.max_concurrent_reached = max(self.metrics.max_concurrent_reached, self.metrics.current_concurrent)
                return acquired
            return True

        except Exception as e:
            logger.error(f"Error acquiring resources: {e}")
            return False

    async def release_resources(self):
        """释放处理资源"""
        try:
            if self.concurrency_limiter:
                await self.concurrency_limiter.release()
                self.metrics.current_concurrent = max(0, self.metrics.current_concurrent - 1)

        except Exception as e:
            logger.error(f"Error releasing resources: {e}")

    async def record_result(self, success: bool, response_time: float, error: Optional[Exception] = None):
        """记录处理结果"""
        try:
            # 更新性能指标
            self.metrics.update_request(response_time, success)

            # 更新熔断器
            if self.circuit_breaker:
                if success:
                    await self.circuit_breaker.record_success()
                else:
                    await self.circuit_breaker.record_failure()
                    if self.circuit_breaker.state == CircuitBreakerState.OPEN:
                        self.metrics.circuit_breaker_trips += 1

        except Exception as e:
            logger.error(f"Error recording result: {e}")

    def get_metrics(self) -> Dict[str, Any]:
        """获取性能指标"""
        return {
            "route_path": self.route_path,
            "total_requests": self.metrics.total_requests,
            "successful_requests": self.metrics.successful_requests,
            "failed_requests": self.metrics.failed_requests,
            "rejected_requests": self.metrics.rejected_requests,
            "success_rate": (self.metrics.successful_requests / max(1, self.metrics.total_requests)) * 100,
            "avg_response_time": self.metrics.avg_response_time,
            "current_concurrent": self.metrics.current_concurrent,
            "max_concurrent_reached": self.metrics.max_concurrent_reached,
            "circuit_breaker_state": self.circuit_breaker.state.value if self.circuit_breaker else "disabled",
            "circuit_breaker_trips": self.metrics.circuit_breaker_trips,
            "rate_limit_hits": self.metrics.rate_limit_hits,
        }


class RouteHandler:
    """智能路由处理器
    集成背压控制、请求限流和熔断机制的智能路由处理系统."""

    def __init__(self, smart_scheduler: Optional[TaskScheduler] = None):
        """初始化智能路由处理器

        Args:
            smart_scheduler: 智能调度器实例
        """
        self.smart_scheduler = smart_scheduler

        # 路由背压控制器
        self._route_controllers: Dict[str, RouteBackpressureController] = {}

        # 路由配置
        self._route_configs: Dict[str, RouteBackpressureConfig] = {}

        # 智能路由信息
        self._smart_routes: Dict[str, SmartRouteInfo] = {}

        # 全局统计
        self._global_stats = {"total_routes": 0, "active_routes": 0, "total_requests": 0, "total_rejections": 0, "avg_response_time": 0.0}

        logger.info("RouteHandler initialized")

    def register_route(self, route_path: str, smart_route_info: Optional[SmartRouteInfo] = None, config: Optional[RouteBackpressureConfig] = None):
        """注册路由

        Args:
            route_path: 路由路径
            smart_route_info: 智能路由信息
            config: 背压配置
        """
        try:
            # 使用默认配置或提供的配置
            if config is None:
                config = self._generate_smart_config(smart_route_info)

            # 创建路由背压控制器
            controller = RouteBackpressureController(route_path, config)

            # 保存
            self._route_controllers[route_path] = controller
            self._route_configs[route_path] = config
            if smart_route_info:
                self._smart_routes[route_path] = smart_route_info

            # 更新统计
            self._global_stats["total_routes"] += 1
            self._global_stats["active_routes"] += 1

            logger.info(f"Route registered: {route_path}")

        except Exception as e:
            logger.error(f"Failed to register route {route_path}: {e}")

    def _generate_smart_config(self, smart_route_info: Optional[SmartRouteInfo]) -> RouteBackpressureConfig:
        """基于智能路由信息生成配置"""
        config = RouteBackpressureConfig()

        if smart_route_info:
            # 基于任务类型调整配置
            if smart_route_info.task_type.value == "cpu_bound":
                config.max_concurrent = 20  # CPU密集型任务限制并发
                config.requests_per_second = 50.0
            elif smart_route_info.task_type.value == "io_bound":
                config.max_concurrent = 100  # IO密集型任务允许更多并发
                config.requests_per_second = 200.0
            elif smart_route_info.task_type.value == "lightweight":
                config.max_concurrent = 200  # 轻量级任务允许大量并发
                config.requests_per_second = 500.0

            # 基于复杂度调整超时
            if smart_route_info.complexity_score > 0.7:
                config.timeout = 60.0
                config.timeout_seconds = 120.0
            elif smart_route_info.complexity_score > 0.3:
                config.timeout = 30.0
                config.timeout_seconds = 60.0
            else:
                config.timeout = 15.0
                config.timeout_seconds = 30.0

            # 基于预估执行时间调整熔断阈值
            if smart_route_info.estimated_duration > 1.0:
                config.failure_threshold = 3  # 慢接口更容易熔断
            else:
                config.failure_threshold = 5

        return config

    @handle_context_exceptions
    @performance_monitor(slow_threshold=2.0)
    @timeout_handler(timeout_seconds=30.0, timeout_message="Route request handling timed out")
    async def handle_request(self, route_path: str, request: Request, handler: Callable, *args, **kwargs) -> Any:
        """处理路由请求

        Args:
            route_path: 路由路径
            request: HTTP请求
            handler: 处理函数
            *args, **kwargs: 处理函数参数

        Returns:
            处理结果
        """
        start_time = time.time()
        controller = self._route_controllers.get(route_path)

        if not controller:
            # 如果没有注册控制器,直接处理
            return await self._execute_handler(handler, *args, **kwargs)

        # 验证和准备请求
        await self._validate_request(route_path, request, controller)

        # 获取处理资源
        await self._acquire_resources(route_path, request, controller)

        try:
            # 执行请求处理
            result = await self._execute_request(route_path, handler, start_time, controller, *args, **kwargs)
            return result
        except Exception as e:
            # 处理请求失败
            await self._handle_request_failure(e, start_time, controller)
            raise
        finally:
            # 清理请求资源
            await self._cleanup_request(controller)

    async def _validate_request(self, route_path: str, request: Request, controller: RouteBackpressureController) -> None:
        """验证请求是否可以处理

        Args:
            route_path: 路由路径
            request: HTTP请求
            controller: 路由控制器
        """
        if not await controller.can_process_request(request):
            raise HTTPException(status_code=503, detail="Service temporarily unavailable")

    async def _acquire_resources(self, route_path: str, request: Request, controller: RouteBackpressureController) -> None:
        """获取处理资源

        Args:
            route_path: 路由路径
            request: HTTP请求
            controller: 路由控制器
        """
        priority = self._determine_request_priority(request, route_path)
        if not await controller.acquire_resources(request, priority):
            raise HTTPException(status_code=503, detail="Service overloaded")

    async def _execute_request(
        self, route_path: str, handler: Callable, start_time: float, controller: RouteBackpressureController, *args, **kwargs
    ) -> Any:
        """执行请求处理

        Args:
            route_path: 路由路径
            handler: 处理函数
            start_time: 开始时间
            controller: 路由控制器
            *args, **kwargs: 处理函数参数

        Returns:
            处理结果
        """
        # 执行处理
        if self.smart_scheduler and route_path in self._smart_routes:
            # 使用智能调度器
            result = await self._execute_with_scheduler(route_path, handler, *args, **kwargs)
        else:
            # 直接执行
            result = await self._execute_handler(handler, *args, **kwargs)

        # 记录成功
        response_time = time.time() - start_time
        await controller.record_result(True, response_time)

        # 更新全局统计
        self._update_global_stats(response_time, True)

        return result

    async def _handle_request_failure(self, error: Exception, start_time: float, controller: RouteBackpressureController) -> None:
        """处理请求失败

        Args:
            error: 发生的异常
            start_time: 开始时间
            controller: 路由控制器
        """
        # 记录失败
        response_time = time.time() - start_time
        await controller.record_result(False, response_time, error)

        # 更新全局统计
        self._update_global_stats(response_time, False)

    async def _cleanup_request(self, controller: RouteBackpressureController) -> None:
        """清理请求资源

        Args:
            controller: 路由控制器
        """
        # 释放资源
        await controller.release_resources()

    async def _execute_handler(self, handler: Callable, *args, **kwargs) -> Any:
        """执行处理函数"""
        if asyncio.iscoroutinefunction(handler):
            return await handler(*args, **kwargs)
        else:
            return handler(*args, **kwargs)

    async def _execute_with_scheduler(self, route_path: str, handler: Callable, *args, **kwargs) -> Any:
        """使用智能调度器执行"""
        smart_route = self._smart_routes[route_path]

        # 创建任务请求
        task_request = TaskRequest(
            task_id=f"route_{route_path}_{id(args)}",
            function=handler,
            args=args,
            kwargs=kwargs,
            metadata={
                "route_path": route_path,
                "task_type": smart_route.task_type.value,
                "complexity_score": smart_route.complexity_score,
                "estimated_duration": smart_route.estimated_duration,
            },
        )

        # 使用调度器执行
        return await self.smart_scheduler.schedule_task(task_request)

    def _determine_request_priority(self, request: Request, route_path: str) -> RequestPriority:
        """确定请求优先级"""
        # 基于路径确定优先级
        if "/admin/" in route_path:
            return RequestPriority.HIGH
        elif "/api/critical/" in route_path:
            return RequestPriority.CRITICAL
        elif "/health" in route_path or "/status" in route_path:
            return RequestPriority.HIGH
        else:
            return RequestPriority.NORMAL

    def _update_global_stats(self, response_time: float, success: bool):
        """更新全局统计"""
        self._global_stats["total_requests"] += 1

        if not success:
            self._global_stats["total_rejections"] += 1

        # 更新平均响应时间
        total_requests = self._global_stats["total_requests"]
        current_avg = self._global_stats["avg_response_time"]
        new_avg = (current_avg * (total_requests - 1) + response_time) / total_requests
        self._global_stats["avg_response_time"] = new_avg

    def get_route_metrics(self, route_path: str) -> Optional[Dict[str, Any]]:
        """获取路由指标"""
        controller = self._route_controllers.get(route_path)
        return controller.get_metrics() if controller else None

    def get_global_metrics(self) -> Dict[str, Any]:
        """获取全局指标"""
        return {
            **self._global_stats,
            "route_count": len(self._route_controllers),
            "routes": {path: controller.get_metrics() for path, controller in self._route_controllers.items()},
        }

    def get_optimization_report(self) -> Dict[str, Any]:
        """获取优化报告"""
        routes_performance = []

        for path, controller in self._route_controllers.items():
            metrics = controller.get_metrics()
            routes_performance.append(
                {
                    "route": path,
                    "performance_score": self._calculate_performance_score(metrics),
                    "recommendations": self._generate_recommendations(metrics),
                }
            )

        # 按性能评分排序
        routes_performance.sort(key=lambda x: x["performance_score"])

        return {
            "global_stats": self._global_stats,
            "worst_performing_routes": routes_performance[:5],
            "best_performing_routes": routes_performance[-5:],
            "optimization_suggestions": self._generate_global_suggestions(),
        }

    def _calculate_performance_score(self, metrics: Dict[str, Any]) -> float:
        """计算性能评分 (0-100)"""
        success_rate = metrics.get("success_rate", 0)
        avg_response_time = metrics.get("avg_response_time", 0)
        rejection_rate = (metrics.get("rejected_requests", 0) / max(1, metrics.get("total_requests", 1))) * 100

        # 综合评分
        score = success_rate * 0.4 + (100 - min(100, avg_response_time * 10)) * 0.4 + (100 - rejection_rate) * 0.2

        return max(0, min(100, score))

    def _generate_recommendations(self, metrics: Dict[str, Any]) -> List[str]:
        """生成优化建议"""
        recommendations = []

        success_rate = metrics.get("success_rate", 0)
        avg_response_time = metrics.get("avg_response_time", 0)
        rejection_rate = (metrics.get("rejected_requests", 0) / max(1, metrics.get("total_requests", 1))) * 100

        if success_rate < 95:
            recommendations.append("Consider implementing retry logic or improving error handling")

        if avg_response_time > 1.0:
            recommendations.append("Optimize response time - consider caching or async processing")

        if rejection_rate > 5:
            recommendations.append("High rejection rate - consider increasing concurrency limits")

        if metrics.get("circuit_breaker_trips", 0) > 0:
            recommendations.append("Circuit breaker activated - investigate underlying issues")

        return recommendations

    def _generate_global_suggestions(self) -> List[str]:
        """生成全局优化建议"""
        suggestions = []

        total_requests = self._global_stats["total_requests"]
        total_rejections = self._global_stats["total_rejections"]

        if total_requests > 0:
            rejection_rate = (total_rejections / total_requests) * 100
            if rejection_rate > 5:
                suggestions.append("Global rejection rate is high - consider scaling resources")

        avg_response_time = self._global_stats["avg_response_time"]
        if avg_response_time > 0.5:
            suggestions.append("Global response time is high - consider performance optimization")

        return suggestions

#!/usr/bin/env python
"""
* @author: cz
* @description: 线程池管理系统集成测试
"""

import time
import unittest
from concurrent.futures import as_completed

from miniboot.asyncs import PoolStrategy, ThreadPoolConfigFactory, ThreadPoolManager, create_pool_config, get_preset_config, submit_task


class ThreadPoolIntegrationTestCase(unittest.TestCase):
    """线程池管理系统集成测试"""

    def setUp(self):
        """设置测试"""
        self.manager = ThreadPoolManager()
        # 清理现有池
        for pool_name in list(self.manager.list_pools()):
            self.manager.remove_pool(pool_name)
        # 重置全局指标
        self.manager._global_metrics = {"total_pools": 0, "active_pools": 0, "total_tasks": 0, "completed_tasks": 0, "failed_tasks": 0}

    def tearDown(self):
        """清理测试"""
        self.manager.shutdown_all()

    def test_complete_workflow(self):
        """测试完整工作流程"""
        # 1. 使用配置工厂创建不同类型的线程池
        io_config = ThreadPoolConfigFactory.for_io_tasks("io-pool", 20)
        cpu_config = ThreadPoolConfigFactory.for_cpu_tasks("cpu-pool")
        bg_config = ThreadPoolConfigFactory.for_background_tasks("bg-pool", 3)

        io_pool = self.manager.create_pool("io-pool", io_config)
        cpu_pool = self.manager.create_pool("cpu-pool", cpu_config)
        bg_pool = self.manager.create_pool("bg-pool", bg_config)

        # 2. 验证池创建成功
        self.assertEqual(len(self.manager.list_pools()), 3)
        self.assertIn("io-pool", self.manager.list_pools())
        self.assertIn("cpu-pool", self.manager.list_pools())
        self.assertIn("bg-pool", self.manager.list_pools())

        # 3. 提交不同类型的任务
        def io_task(delay):
            time.sleep(delay)
            return f"IO task completed after {delay}s"

        def cpu_task(n):
            # 模拟CPU密集型任务
            result = sum(i * i for i in range(n))
            return f"CPU task result: {result}"

        def bg_task(message):
            return f"Background: {message}"

        # 提交IO任务
        io_futures = []
        for _ in range(5):
            future = io_pool.submit(io_task, 0.1)
            io_futures.append(future)

        # 提交CPU任务
        cpu_futures = []
        for i in range(3):
            future = cpu_pool.submit(cpu_task, 1000 * (i + 1))
            cpu_futures.append(future)

        # 提交后台任务
        bg_futures = []
        for i in range(10):
            future = bg_pool.submit(bg_task, f"message-{i}")
            bg_futures.append(future)

        # 4. 等待所有任务完成
        all_futures = io_futures + cpu_futures + bg_futures
        results = []

        for future in as_completed(all_futures, timeout=10):
            result = future.result()
            results.append(result)

        # 5. 验证结果
        self.assertEqual(len(results), 18)  # 5 + 3 + 10

        # 验证IO任务结果
        io_results = [r for r in results if r.startswith("IO task")]
        self.assertEqual(len(io_results), 5)

        # 验证CPU任务结果
        cpu_results = [r for r in results if r.startswith("CPU task")]
        self.assertEqual(len(cpu_results), 3)

        # 验证后台任务结果
        bg_results = [r for r in results if r.startswith("Background")]
        self.assertEqual(len(bg_results), 10)

        # 6. 检查指标
        metrics = self.manager.get_all_metrics()
        self.assertEqual(metrics["global_metrics"]["total_pools"], 3)
        self.assertEqual(metrics["global_metrics"]["active_pools"], 3)
        self.assertEqual(metrics["global_metrics"]["total_tasks"], 18)
        self.assertEqual(metrics["global_metrics"]["completed_tasks"], 18)

    def test_config_builder_integration(self):
        """测试配置构建器集成"""
        # 使用构建器创建自定义配置
        config = (
            create_pool_config("custom-pool")
            .strategy(PoolStrategy.CACHED)
            .core_size(5)
            .max_size(15)
            .for_io_tasks()
            .thread_name_prefix("custom-io")
            .build()
        )

        pool = self.manager.create_pool("custom-pool", config)

        # 验证配置应用正确
        self.assertEqual(pool.config.name, "custom-pool")
        self.assertEqual(pool.config.core_size, 10)  # for_io_tasks覆盖了之前的设置
        self.assertEqual(pool.config.max_size, 50)
        self.assertEqual(pool.config.thread_name_prefix, "custom-io")

        # 提交任务验证功能
        future = pool.submit(lambda x: x * 2, 21)
        result = future.result(timeout=5)
        self.assertEqual(result, 42)

    def test_preset_config_integration(self):
        """测试预设配置集成"""
        # 使用预设配置创建池
        config = get_preset_config("medium", "preset-pool")
        self.assertIsNotNone(config)

        pool = self.manager.create_pool("preset-pool", config)

        # 验证预设配置
        self.assertEqual(pool.config.core_size, 5)
        self.assertEqual(pool.config.max_size, 20)
        self.assertEqual(pool.config.queue_capacity, 200)

        # 提交批量任务测试
        def batch_task(batch_id, items):
            return f"Batch {batch_id}: processed {len(items)} items"

        futures = []
        for i in range(10):
            items = list(range(i * 10, (i + 1) * 10))
            future = pool.submit(batch_task, i, items)
            futures.append(future)

        results = [future.result(timeout=5) for future in futures]
        self.assertEqual(len(results), 10)

        # 验证所有批次都处理成功
        for i, result in enumerate(results):
            self.assertIn(f"Batch {i}", result)
            self.assertIn("processed 10 items", result)

    def test_global_submit_function(self):
        """测试全局提交函数"""
        # 测试提交到默认池
        future1 = submit_task(lambda x: x + 10, 5)
        result1 = future1.result(timeout=5)
        self.assertEqual(result1, 15)

        # 创建命名池
        config = ThreadPoolConfigFactory.for_io_tasks("named-pool", 10)
        self.manager.create_pool("named-pool", config)

        # 测试提交到命名池
        future2 = submit_task(lambda x: x * 3, 7, pool_name="named-pool")
        result2 = future2.result(timeout=5)
        self.assertEqual(result2, 21)

        # 验证池中有任务记录
        self.assertIn("default", self.manager.list_pools())
        self.assertIn("named-pool", self.manager.list_pools())

    def test_concurrent_pool_operations(self):
        """测试并发池操作"""
        import threading

        # 创建多个线程同时操作池
        def create_and_use_pool(pool_id):
            pool_name = f"concurrent-pool-{pool_id}"
            config = ThreadPoolConfigFactory.for_background_tasks(pool_name, 2)

            try:
                pool = self.manager.create_pool(pool_name, config)

                # 提交一些任务
                futures = []
                for i in range(5):
                    future = pool.submit(lambda x: x**2, i)
                    futures.append(future)

                # 等待完成
                results = [future.result(timeout=5) for future in futures]
                return len(results)

            except ValueError:
                # 池已存在，忽略
                return 0

        # 启动多个线程
        threads = []
        for i in range(5):
            thread = threading.Thread(target=create_and_use_pool, args=(i,))
            threads.append(thread)
            thread.start()

        # 等待所有线程完成
        for thread in threads:
            thread.join(timeout=10)

        # 验证池创建成功
        pools = self.manager.list_pools()
        concurrent_pools = [p for p in pools if p.startswith("concurrent-pool-")]
        self.assertGreater(len(concurrent_pools), 0)
        self.assertLessEqual(len(concurrent_pools), 5)

    def test_pool_lifecycle_management(self):
        """测试池生命周期管理"""
        # 创建池
        config = ThreadPoolConfigFactory.for_cpu_tasks("lifecycle-pool")
        pool = self.manager.create_pool("lifecycle-pool", config)

        # 验证池状态 - 延迟初始化，创建时为 created 状态
        self.assertFalse(pool.is_shutdown())
        metrics = pool.get_metrics()
        self.assertEqual(metrics.state.value, "created")

        # 提交任务 - 这会触发初始化并变为 running 状态
        future = pool.submit(lambda: "lifecycle test")
        result = future.result(timeout=5)
        self.assertEqual(result, "lifecycle test")

        # 现在应该是 running 状态
        metrics = pool.get_metrics()
        self.assertEqual(metrics.state.value, "running")

        # 关闭特定池
        self.manager.shutdown_pool("lifecycle-pool")
        self.assertTrue(pool.is_shutdown())

        # 验证无法再提交任务
        with self.assertRaises(RuntimeError):
            pool.submit(lambda: "should fail")

        # 验证池已被自动移除
        self.assertNotIn("lifecycle-pool", self.manager.list_pools())

    def test_health_monitoring(self):
        """测试健康监控"""
        # 创建多个池
        configs = [
            ThreadPoolConfigFactory.for_io_tasks("health-io", 10),
            ThreadPoolConfigFactory.for_cpu_tasks("health-cpu"),
            ThreadPoolConfigFactory.for_background_tasks("health-bg", 3),
        ]

        pools = []
        for config in configs:
            pool = self.manager.create_pool(config.name, config)
            pools.append(pool)

        # 检查健康状态
        health = self.manager.health_check()
        self.assertEqual(health["status"], "healthy")
        self.assertEqual(health["total_pools"], 3)
        self.assertEqual(health["healthy_pools"], 3)
        self.assertEqual(health["unhealthy_pools"], 0)

        # 关闭一个池
        pools[0].shutdown()

        # 再次检查健康状态
        health = self.manager.health_check()
        self.assertEqual(health["status"], "degraded")
        self.assertEqual(health["total_pools"], 3)
        self.assertEqual(health["healthy_pools"], 2)
        self.assertEqual(health["unhealthy_pools"], 1)

        # 获取详细指标
        all_metrics = self.manager.get_all_metrics()
        self.assertIn("global_metrics", all_metrics)
        self.assertIn("pool_metrics", all_metrics)

        pool_metrics = all_metrics["pool_metrics"]
        self.assertEqual(len(pool_metrics), 3)

        # 验证关闭的池状态
        health_io_metrics = pool_metrics["health-io"]
        self.assertEqual(health_io_metrics["state"], "terminated")


if __name__ == "__main__":
    unittest.main()

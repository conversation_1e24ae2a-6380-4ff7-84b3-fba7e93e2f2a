#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
监控组件发现机制

实现基于接口的监控组件自动发现，支持：
- Bean 工厂扫描：从应用上下文的 Bean 工厂中发现组件
- 类型检查：基于接口类型进行组件识别
- 条件过滤：支持条件化的组件发现
- 扩展性：支持自定义发现策略

设计原则：
- 非侵入式：不需要修改现有组件代码
- 高性能：缓存发现结果，避免重复扫描
- 容错性：发现过程中的异常不影响应用启动
"""

from typing import List, Type, Any, Dict, Optional, Set
from loguru import logger

from .interfaces import (
    MonitoringContext,
    EndpointProvider,
    MetricsCollector,
    HealthIndicator,
    MonitoringEventListener
)


class MonitoringComponentDiscovery:
    """监控组件发现器
    
    负责从应用上下文中自动发现各种监控组件实现。
    使用接口驱动的方式，避免硬编码依赖。
    """
    
    def __init__(self, application_context):
        """初始化发现器
        
        Args:
            application_context: 应用上下文实例
        """
        self.application_context = application_context
        self._discovery_cache: Dict[Type, List[Any]] = {}
        self._cache_enabled = True
        
    def discover_monitoring_contexts(self) -> List[MonitoringContext]:
        """发现所有监控上下文实现
        
        Returns:
            List[MonitoringContext]: 监控上下文实现列表
        """
        return self._discover_implementations(MonitoringContext)
    
    def discover_endpoint_providers(self) -> List[EndpointProvider]:
        """发现所有端点提供者
        
        Returns:
            List[EndpointProvider]: 端点提供者列表
        """
        return self._discover_implementations(EndpointProvider)
    
    def discover_metrics_collectors(self) -> List[MetricsCollector]:
        """发现所有指标收集器
        
        Returns:
            List[MetricsCollector]: 指标收集器列表
        """
        return self._discover_implementations(MetricsCollector)
    
    def discover_health_indicators(self) -> List[HealthIndicator]:
        """发现所有健康检查指示器
        
        Returns:
            List[HealthIndicator]: 健康检查指示器列表
        """
        indicators = self._discover_implementations(HealthIndicator)
        # 按执行顺序排序
        return sorted(indicators, key=lambda x: x.get_order())
    
    def discover_event_listeners(self) -> List[MonitoringEventListener]:
        """发现所有监控事件监听器
        
        Returns:
            List[MonitoringEventListener]: 事件监听器列表
        """
        return self._discover_implementations(MonitoringEventListener)
    
    def discover_all_components(self) -> Dict[str, List[Any]]:
        """发现所有监控组件
        
        Returns:
            Dict[str, List[Any]]: 组件类型到组件列表的映射
        """
        return {
            "monitoring_contexts": self.discover_monitoring_contexts(),
            "endpoint_providers": self.discover_endpoint_providers(),
            "metrics_collectors": self.discover_metrics_collectors(),
            "health_indicators": self.discover_health_indicators(),
            "event_listeners": self.discover_event_listeners()
        }
    
    def _discover_implementations(self, interface_class: Type) -> List[Any]:
        """通过 Bean 工厂扫描接口实现
        
        Args:
            interface_class: 要发现的接口类型
            
        Returns:
            List[Any]: 接口实现列表
        """
        # 检查缓存
        if self._cache_enabled and interface_class in self._discovery_cache:
            logger.debug(f"Using cached discovery result for {interface_class.__name__}")
            return self._discovery_cache[interface_class].copy()
        
        implementations = []
        discovered_names = set()
        
        try:
            # 策略1：从 Bean 工厂的单例缓存中扫描
            implementations.extend(
                self._scan_from_singleton_cache(interface_class, discovered_names)
            )
            
            # 策略2：从 Bean 定义注册表中扫描
            implementations.extend(
                self._scan_from_bean_registry(interface_class, discovered_names)
            )
            
            # 策略3：从自动配置 Bean 中扫描
            implementations.extend(
                self._scan_from_auto_configuration(interface_class, discovered_names)
            )
            
            # 缓存结果
            if self._cache_enabled:
                self._discovery_cache[interface_class] = implementations.copy()
            
            logger.info(f"Discovered {len(implementations)} {interface_class.__name__} implementations")
            
        except Exception as e:
            logger.warning(f"Error discovering {interface_class.__name__} implementations: {e}")
            
        return implementations
    
    def _scan_from_singleton_cache(self, interface_class: Type, discovered_names: Set[str]) -> List[Any]:
        """从单例缓存中扫描"""
        implementations = []
        
        try:
            if hasattr(self.application_context, '_bean_factory'):
                bean_factory = self.application_context._bean_factory
                
                if hasattr(bean_factory, 'get_singleton_names'):
                    for bean_name in bean_factory.get_singleton_names():
                        if bean_name in discovered_names:
                            continue
                            
                        try:
                            bean = bean_factory.get_singleton(bean_name)
                            if isinstance(bean, interface_class):
                                implementations.append(bean)
                                discovered_names.add(bean_name)
                                logger.debug(f"Found {interface_class.__name__} implementation: {bean_name}")
                        except Exception as e:
                            logger.debug(f"Error getting singleton bean {bean_name}: {e}")
                            continue
                            
        except Exception as e:
            logger.debug(f"Error scanning singleton cache: {e}")
            
        return implementations
    
    def _scan_from_bean_registry(self, interface_class: Type, discovered_names: Set[str]) -> List[Any]:
        """从 Bean 定义注册表中扫描"""
        implementations = []
        
        try:
            if hasattr(self.application_context, '_bean_registry'):
                registry = self.application_context._bean_registry
                
                if hasattr(registry, 'get_bean_definition_names'):
                    for bean_name in registry.get_bean_definition_names():
                        if bean_name in discovered_names:
                            continue
                            
                        try:
                            bean = self.application_context.get_bean(bean_name)
                            if isinstance(bean, interface_class):
                                implementations.append(bean)
                                discovered_names.add(bean_name)
                                logger.debug(f"Found {interface_class.__name__} implementation from registry: {bean_name}")
                        except Exception as e:
                            logger.debug(f"Error getting bean from registry {bean_name}: {e}")
                            continue
                            
        except Exception as e:
            logger.debug(f"Error scanning bean registry: {e}")
            
        return implementations
    
    def _scan_from_auto_configuration(self, interface_class: Type, discovered_names: Set[str]) -> List[Any]:
        """从自动配置中扫描"""
        implementations = []
        
        try:
            # 尝试通过类型获取 Bean
            if hasattr(self.application_context, 'get_beans_of_type'):
                beans = self.application_context.get_beans_of_type(interface_class)
                for bean_name, bean in beans.items():
                    if bean_name not in discovered_names:
                        implementations.append(bean)
                        discovered_names.add(bean_name)
                        logger.debug(f"Found {interface_class.__name__} implementation from auto-config: {bean_name}")
                        
        except Exception as e:
            logger.debug(f"Error scanning auto configuration: {e}")
            
        return implementations
    
    def clear_cache(self) -> None:
        """清除发现缓存"""
        self._discovery_cache.clear()
        logger.debug("Discovery cache cleared")
    
    def disable_cache(self) -> None:
        """禁用缓存"""
        self._cache_enabled = False
        self.clear_cache()
        logger.debug("Discovery cache disabled")
    
    def enable_cache(self) -> None:
        """启用缓存"""
        self._cache_enabled = True
        logger.debug("Discovery cache enabled")
    
    def get_discovery_statistics(self) -> Dict[str, Any]:
        """获取发现统计信息"""
        stats = {
            "cache_enabled": self._cache_enabled,
            "cached_types": len(self._discovery_cache),
            "total_cached_components": sum(len(components) for components in self._discovery_cache.values())
        }
        
        # 添加每种类型的统计
        for interface_type, components in self._discovery_cache.items():
            stats[f"{interface_type.__name__}_count"] = len(components)
            
        return stats

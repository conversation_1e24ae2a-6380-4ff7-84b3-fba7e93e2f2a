#!/usr/bin/env python
# encoding: utf-8
"""
* @author: cz
* @description: 自动配置发现器测试
"""

import os
import tempfile
from unittest.mock import patch

from miniboot.autoconfigure import (AutoConfiguration,
                                    AutoConfigurationDiscovery,
                                    AutoConfigurationLoader,
                                    AutoConfigurationMetadata,
                                    AutoConfigurationRegistry)


class MockDiscoveryConfigA(AutoConfiguration):
    """测试发现配置A"""

    def get_metadata(self):
        return AutoConfigurationMetadata(name="discovery-config-a", description="Discovery test configuration A")


class MockDiscoveryConfigB(AutoConfiguration):
    """测试发现配置B"""

    def get_metadata(self):
        return AutoConfigurationMetadata(name="discovery-config-b", description="Discovery test configuration B")


class TestAutoConfigurationDiscovery:
    """自动配置发现器测试"""

    def setup_method(self):
        """测试前置设置"""
        self.registry = AutoConfigurationRegistry()
        self.discovery = AutoConfigurationDiscovery(self.registry)

    def test_discover_from_modules(self):
        """测试从模块发现配置"""
        # 使用当前测试模块
        module_name = __name__

        discovered = self.discovery.discover_modules([module_name])

        # 应该发现MockDiscoveryConfigA和MockDiscoveryConfigB
        config_names = [config.__name__ for config in discovered]
        assert "MockDiscoveryConfigA" in config_names
        assert "MockDiscoveryConfigB" in config_names

    def test_discover_from_nonexistent_module(self):
        """测试从不存在的模块发现配置"""
        discovered = self.discovery.discover_modules(["nonexistent.module"])

        # 应该返回空列表，不抛出异常
        assert len(discovered) == 0

    def test_discover_from_file(self):
        """测试从配置文件发现配置"""
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False, encoding="utf-8") as f:
            f.write(f"{__name__}.MockDiscoveryConfigA\n")
            f.write(f"{__name__}.MockDiscoveryConfigB\n")
            f.write("# This is a comment\n")  # 使用英文注释避免编码问题
            f.write("\n")  # 空行
            f.write("nonexistent.module.Config\n")  # 不存在的类
            temp_file = f.name

        try:
            discovered = self.discovery.discover_from_file(temp_file)

            # 应该发现两个有效的配置类
            config_names = [config.__name__ for config in discovered]
            assert "MockDiscoveryConfigA" in config_names
            assert "MockDiscoveryConfigB" in config_names
            assert len(discovered) == 2

        finally:
            os.unlink(temp_file)



    def test_discover_and_register_classes(self):
        """测试发现并注册指定的配置类"""
        classes = [MockDiscoveryConfigA, MockDiscoveryConfigB]

        count = self.discovery.load_and_register(classes=classes)

        assert count == 2
        assert "discovery-config-a" in self.registry.get_all_configurations()
        assert "discovery-config-b" in self.registry.get_all_configurations()

    def test_discover_and_register_modules(self):
        """测试发现并注册模块中的配置"""
        count = self.discovery.load_and_register(modules=[__name__])

        # 应该注册至少2个配置
        assert count >= 2
        registered_configs = self.registry.get_all_configurations()
        assert "discovery-config-a" in registered_configs
        assert "discovery-config-b" in registered_configs



    def test_discover_and_register_duplicate_classes(self):
        """测试发现并注册重复的配置类"""
        classes = [MockDiscoveryConfigA, MockDiscoveryConfigA]  # 重复的类

        count = self.discovery.load_and_register(classes=classes)

        # 应该只注册一次
        assert count == 1
        assert len(self.registry.get_all_configurations()) == 1

    def test_get_discovered_classes(self):
        """测试获取已发现的配置类"""
        classes = [MockDiscoveryConfigA, MockDiscoveryConfigB]
        self.discovery.discover_and_register(classes=classes)

        discovered = self.discovery.get_discovered_classes()

        assert MockDiscoveryConfigA in discovered
        assert MockDiscoveryConfigB in discovered
        assert len(discovered) == 2



    @patch("miniboot.autoconfigure.discovery.importlib.import_module")
    def test_discover_from_package_import_error(self, mock_import):
        """测试包导入错误处理"""
        mock_import.side_effect = ImportError("Module not found")

        discovered = self.discovery.discover_package("nonexistent.package")

        # 应该返回空列表，不抛出异常
        assert len(discovered) == 0




class TestAutoConfigurationLoader:
    """自动配置加载器测试"""

    def setup_method(self):
        """测试前置设置"""
        self.loader = AutoConfigurationLoader()

    def test_loader_initialization(self):
        """测试加载器初始化"""
        assert self.loader.registry is not None
        assert self.loader.discovery is not None
        assert isinstance(self.loader.registry, AutoConfigurationRegistry)
        assert isinstance(self.loader.discovery, AutoConfigurationDiscovery)

    def test_load_from_packages(self):
        """测试从包加载配置"""
        # 使用当前测试模块所在的包
        package_name = __name__.rsplit(".", 1)[0]  # 去掉模块名，保留包名

        count = self.loader.load_from_packages([package_name])

        # 应该加载到一些配置
        assert count >= 0  # 可能没有配置类在包级别

    def test_load_from_config_file(self):
        """测试从配置文件加载"""
        # 创建临时配置文件
        with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False, encoding="utf-8") as f:
            f.write(f"{__name__}.MockDiscoveryConfigA\n")
            temp_file = f.name

        try:
            count = self.loader.load_config(temp_file)

            assert count == 1
            registry = self.loader.get_registry()
            assert "discovery-config-a" in registry.get_all_configurations()

        finally:
            os.unlink(temp_file)

    def test_get_registry_and_discovery(self):
        """测试获取注册表和发现器实例"""
        registry = self.loader.get_registry()
        discovery = self.loader.get_discovery()

        assert registry is self.loader.registry
        assert discovery is self.loader.discovery

    def test_load_default_configurations(self):
        """测试加载默认配置"""
        # 这个测试可能会加载实际的miniboot配置，所以只检查不抛出异常
        try:
            count = self.loader.load_default_configurations()
            assert count >= 0
        except Exception as e:
            # 如果miniboot.autoconfigure包不存在或有问题，应该优雅处理
            assert "miniboot.autoconfigure" in str(e) or "import" in str(e).lower()

    def test_multiple_operations(self):
        """测试多次操作"""
        # 先从类加载
        self.loader.discovery.load_and_register(classes=[MockDiscoveryConfigA])

        # 再从配置文件加载
        with tempfile.NamedTemporaryFile(mode="w", suffix=".txt", delete=False, encoding="utf-8") as f:
            f.write(f"{__name__}.MockDiscoveryConfigB\n")
            temp_file = f.name

        try:
            self.loader.load_config(temp_file)

            # 应该总共有2个配置
            registry = self.loader.get_registry()
            all_configs = registry.get_all_configurations()
            assert len(all_configs) == 2
            assert "discovery-config-a" in all_configs
            assert "discovery-config-b" in all_configs

        finally:
            os.unlink(temp_file)


class TestAutoConfigurationLoaderEdgeCases:
    """自动配置加载器边界情况测试"""

    def setup_method(self):
        """测试前置设置"""
        self.loader = AutoConfigurationLoader()

    def test_load_from_nonexistent_config_file(self):
        """测试从不存在的配置文件加载"""
        count = self.loader.load_from_config_file("/nonexistent/file.txt")
        assert count == 0

    def test_load_from_config_file_with_duplicates(self):
        """测试加载重复配置"""
        # 创建临时配置文件
        content = """
tests.unit.autoconfigure.test_discovery.MockDiscoveryConfigA
tests.unit.autoconfigure.test_discovery.MockDiscoveryConfigA
        """

        with tempfile.NamedTemporaryFile(mode="w", delete=False, suffix=".txt") as f:
            f.write(content)
            temp_file = f.name

        try:
            count = self.loader.load_from_config_file(temp_file)
            # 应该去重，只加载一次
            assert count == 1
        finally:
            os.unlink(temp_file)

    def test_get_registry(self):
        """测试获取注册表"""
        registry = self.loader.get_registry()
        assert registry is not None
        assert isinstance(registry, AutoConfigurationRegistry)

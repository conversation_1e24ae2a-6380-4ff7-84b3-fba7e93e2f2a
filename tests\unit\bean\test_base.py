#!/usr/bin/env python
# encoding: utf-8
"""
 * @author: cz
 * @description: Bean基础接口单元测试
"""

import unittest
from abc import ABC
from typing import Any
from unittest.mock import Mock, patch

from miniboot.bean.base import (ApplicationContextAware,
                                BeanDefinitionRegistry, BeanFactory,
                                BeanFactoryAware, BeanNameAware,
                                BeanPostProcessor, DisposableBean,
                                InitializingBean, Lifecycle, SmartLifecycle)


class TestBeanFactory(unittest.TestCase):
    """BeanFactory接口测试"""

    def test_bean_factory_is_abstract(self):
        """测试BeanFactory是抽象类"""
        self.assertTrue(issubclass(BeanFactory, ABC))

        # 不能直接实例化抽象类
        with self.assertRaises(TypeError):
            BeanFactory()

    def test_bean_factory_methods_are_abstract(self):
        """测试BeanFactory的抽象方法"""
        # 创建一个不完整的实现类
        class IncompleteBeanFactory(BeanFactory):
            pass

        # 不能实例化不完整的实现
        with self.assertRaises(TypeError):
            IncompleteBeanFactory()

    def test_bean_factory_complete_implementation(self):
        """测试BeanFactory的完整实现"""
        class CompleteBeanFactory(BeanFactory):
            def get_bean(self, name: str, required_type: type = None) -> Any:
                return Mock()

            def contains_bean(self, name: str) -> bool:
                return True

            def is_singleton(self, name: str) -> bool:
                return True

            def get_type(self, name: str) -> type:
                return Mock

            def get_aliases(self, name: str) -> list:
                return []

        # 可以成功实例化完整实现
        factory = CompleteBeanFactory()
        self.assertIsInstance(factory, BeanFactory)


class TestBeanDefinitionRegistry(unittest.TestCase):
    """BeanDefinitionRegistry接口测试"""

    def test_registry_is_abstract(self):
        """测试BeanDefinitionRegistry是抽象类"""
        self.assertTrue(issubclass(BeanDefinitionRegistry, ABC))

        with self.assertRaises(TypeError):
            BeanDefinitionRegistry()

    def test_registry_complete_implementation(self):
        """测试BeanDefinitionRegistry的完整实现"""
        from miniboot.bean.definition import BeanDefinition

        class CompleteRegistry(BeanDefinitionRegistry):
            def register_bean_definition(self, bean_name: str, bean_definition: "BeanDefinition") -> None:
                pass

            def remove_bean_definition(self, bean_name: str) -> "BeanDefinition":
                return Mock()

            def get_bean_definition(self, bean_name: str) -> "BeanDefinition":
                return Mock()

            def contains_bean_definition(self, bean_name: str) -> bool:
                return True

            def get_bean_definition_names(self) -> list:
                return []

            def get_bean_definition_count(self) -> int:
                return 0

        registry = CompleteRegistry()
        self.assertIsInstance(registry, BeanDefinitionRegistry)


class TestBeanPostProcessor(unittest.TestCase):
    """BeanPostProcessor接口测试"""

    def test_post_processor_is_abstract(self):
        """测试BeanPostProcessor是抽象基类"""
        self.assertTrue(issubclass(BeanPostProcessor, ABC))

        # BeanPostProcessor提供了默认实现，所以可以实例化
        # 但通常应该被子类化
        processor = BeanPostProcessor()
        self.assertIsInstance(processor, BeanPostProcessor)

    def test_post_processor_implementation(self):
        """测试BeanPostProcessor的实现"""
        class TestPostProcessor(BeanPostProcessor):
            def __init__(self):
                self.before_calls = []
                self.after_calls = []

            def post_process_before_initialization(self, bean: Any, bean_name: str) -> Any:
                self.before_calls.append((bean, bean_name))
                return bean

            def post_process_after_initialization(self, bean: Any, bean_name: str) -> Any:
                self.after_calls.append((bean, bean_name))
                return bean

            def get_order(self) -> int:
                return 100

        processor = TestPostProcessor()
        self.assertIsInstance(processor, BeanPostProcessor)

        # 测试方法调用
        test_bean = Mock()
        result = processor.post_process_before_initialization(test_bean, "testBean")
        self.assertEqual(result, test_bean)
        self.assertEqual(len(processor.before_calls), 1)

        result = processor.post_process_after_initialization(test_bean, "testBean")
        self.assertEqual(result, test_bean)
        self.assertEqual(len(processor.after_calls), 1)

        self.assertEqual(processor.get_order(), 100)


class TestLifecycleInterfaces(unittest.TestCase):
    """生命周期接口测试"""

    def test_initializing_bean(self):
        """测试InitializingBean接口"""
        self.assertTrue(issubclass(InitializingBean, ABC))

        class TestInitializingBean(InitializingBean):
            def __init__(self):
                self.initialized = False

            def after_properties_set(self) -> None:
                self.initialized = True

        bean = TestInitializingBean()
        self.assertFalse(bean.initialized)

        bean.after_properties_set()
        self.assertTrue(bean.initialized)

    def test_disposable_bean(self):
        """测试DisposableBean接口"""
        self.assertTrue(issubclass(DisposableBean, ABC))

        class TestDisposableBean(DisposableBean):
            def __init__(self):
                self.destroyed = False

            def destroy(self) -> None:
                self.destroyed = True

        bean = TestDisposableBean()
        self.assertFalse(bean.destroyed)

        bean.destroy()
        self.assertTrue(bean.destroyed)

    def test_lifecycle(self):
        """测试Lifecycle接口"""
        self.assertTrue(issubclass(Lifecycle, ABC))

        class TestLifecycle(Lifecycle):
            def __init__(self):
                self._running = False

            def start(self) -> None:
                self._running = True

            def stop(self) -> None:
                self._running = False

            def is_running(self) -> bool:
                return self._running

        lifecycle = TestLifecycle()
        self.assertFalse(lifecycle.is_running())

        lifecycle.start()
        self.assertTrue(lifecycle.is_running())

        lifecycle.stop()
        self.assertFalse(lifecycle.is_running())

    def test_smart_lifecycle(self):
        """测试SmartLifecycle接口"""
        self.assertTrue(issubclass(SmartLifecycle, Lifecycle))

        class TestSmartLifecycle(SmartLifecycle):
            def __init__(self, phase: int = 0, auto_startup: bool = True):
                self._running = False
                self._phase = phase
                self._auto_startup = auto_startup

            def start(self) -> None:
                self._running = True

            def stop(self) -> None:
                self._running = False

            def is_running(self) -> bool:
                return self._running

            def get_phase(self) -> int:
                return self._phase

            def is_auto_startup(self) -> bool:
                return self._auto_startup

        smart_lifecycle = TestSmartLifecycle(phase=100, auto_startup=False)
        self.assertEqual(smart_lifecycle.get_phase(), 100)
        self.assertFalse(smart_lifecycle.is_auto_startup())
        self.assertFalse(smart_lifecycle.is_running())

        smart_lifecycle.start()
        self.assertTrue(smart_lifecycle.is_running())


class TestAwareInterfaces(unittest.TestCase):
    """感知接口测试"""

    def test_bean_name_aware(self):
        """测试BeanNameAware接口"""
        self.assertTrue(issubclass(BeanNameAware, ABC))

        class TestBeanNameAware(BeanNameAware):
            def __init__(self):
                self.bean_name = None

            def set_bean_name(self, name: str) -> None:
                self.bean_name = name

        bean = TestBeanNameAware()
        self.assertIsNone(bean.bean_name)

        bean.set_bean_name("testBean")
        self.assertEqual(bean.bean_name, "testBean")

    def test_bean_factory_aware(self):
        """测试BeanFactoryAware接口"""
        self.assertTrue(issubclass(BeanFactoryAware, ABC))

        class TestBeanFactoryAware(BeanFactoryAware):
            def __init__(self):
                self.bean_factory = None

            def set_bean_factory(self, bean_factory: BeanFactory) -> None:
                self.bean_factory = bean_factory

        bean = TestBeanFactoryAware()
        factory = Mock(spec=BeanFactory)

        self.assertIsNone(bean.bean_factory)

        bean.set_bean_factory(factory)
        self.assertEqual(bean.bean_factory, factory)

    def test_application_context_aware(self):
        """测试ApplicationContextAware接口"""
        self.assertTrue(issubclass(ApplicationContextAware, ABC))

        class TestApplicationContextAware(ApplicationContextAware):
            def __init__(self):
                self.application_context = None

            def set_application_context(self, application_context: Any) -> None:
                self.application_context = application_context

        bean = TestApplicationContextAware()
        context = Mock()

        self.assertIsNone(bean.application_context)

        bean.set_application_context(context)
        self.assertEqual(bean.application_context, context)


if __name__ == '__main__':
    unittest.main()
